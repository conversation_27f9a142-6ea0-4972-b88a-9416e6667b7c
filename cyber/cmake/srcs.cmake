#========================
# libs
#========================

set(CMAKE_POSITION_INDEPENDENT_CODE ON)

set(CUR_SRCS "")
LIST(APPEND CUR_INCLUDES "include")

set(CUR_SUB_DIR "")
LIST(APPEND CUR_SUB_DIR include)
LIST(APPEND CUR_SUB_DIR src)

list(APPEND CUR_SRCS cyber/base/macros.h)
list(APPEND CUR_SRCS cyber/logger/async_logger.cc)
list(APPEND CUR_SRCS cyber/logger/async_logger.h)
list(APPEND CUR_SRCS cyber/logger/log_file_object.cc)
list(APPEND CUR_SRCS cyber/logger/log_file_object.h)
list(APPEND CUR_SRCS cyber/logger/logger_util.cc)
list(APPEND CUR_SRCS cyber/logger/logger_util.h)
list(APPEND CUR_SRCS cyber/glog_wrapper/glog_wrapper.cpp)
list(APPEND CUR_SRCS cyber/glog_wrapper/glog_wrapper.h)
list(APPEND CUR_SRCS cyber/common/log.h)
list(APPEND CUR_SRCS cyber/init.cc)
list(APPEND CUR_SRCS cyber/state.cc)

if(WIN32)
        add_library(${CUR_LIB} STATIC ${CUR_SRCS})
else()
        add_library(${CUR_LIB} SHARED ${CUR_SRCS})
endif()

target_include_directories(${CUR_LIB}
        PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}
        ${GLOG_INCLUDE_DIR}
        )
target_link_libraries(${CUR_LIB}
        PUBLIC
        ${GLOG_LIBRARY}
        )
# Windows 特定依赖
if(WIN32)
    target_link_libraries(${CUR_LIB} PRIVATE ws2_32)
endif()

# 设置模块名称
target_compile_definitions(${CUR_LIB} PRIVATE MODULE_NAME="cyber")

#add_executable(cyber_recorder tools/cyber_recorder/main.cc)
#target_link_libraries(cyber_recorder cyber)
#
#add_executable(cyber_monitor tools/cyber_monitor/main.cc)
#target_link_libraries(cyber_monitor cyber)