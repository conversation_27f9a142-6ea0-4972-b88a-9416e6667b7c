/******************************************************************************
 * Copyright 2018 The Apollo Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *****************************************************************************/

#include <csignal>
#include "cyber/init.h"
#include "cyber/logger/async_logger.h"

#ifdef _WIN32
#include <windows.h>
#else
#include <unistd.h>
#endif

namespace apollo {
namespace cyber {
namespace {

bool g_atexit_registered = false;
std::mutex g_mutex;

logger::AsyncLogger* async_logger = nullptr;

void SignalhHandle(const char* data, int size) {
  AERROR << std::string(data, size);
  if (async_logger) {
    async_logger->Flush();
  }
}

#ifdef _WIN32
LONG WINAPI MyExceptionHandler(EXCEPTION_POINTERS* ExceptionInfo) {
  AERROR << "Unhandled exception occurred!";
  if (async_logger) {
    async_logger->Flush();
  }
  return EXCEPTION_EXECUTE_HANDLER;
}
#endif

void InitLogger(const char* binary_name) {
  // Init glog
  google::InitGoogleLogging(binary_name);

#ifdef _WIN32
  // 设置 Windows 异常处理函数
    SetUnhandledExceptionFilter(MyExceptionHandler);
#else
  // 在非 Windows 平台上使用 InstallFailureSignalHandler
  // google::InstallFailureSignalHandler();
  // google::InstallFailureWriter(&SignalhHandle);
#endif

  // Init async logger
  async_logger = new ::apollo::cyber::logger::AsyncLogger(
      google::base::GetLogger(FLAGS_minloglevel));
  google::base::SetLogger(FLAGS_minloglevel, async_logger);
  async_logger->Start();
}

void StopLogger() { delete async_logger; }

}  // namespace

#ifdef _WIN32
BOOL WINAPI OnShutdown(DWORD sig) {
  if (GetState() != STATE_SHUTDOWN) {
    SetState(STATE_SHUTTING_DOWN);
  }
  return TRUE;
}
#else
void OnShutdown(int sig) {
  (void)sig;
  if (GetState() != STATE_SHUTDOWN) {
    SetState(STATE_SHUTTING_DOWN);
  }
}
#endif

void ExitHandle() { Clear(); }

bool Init(const char* binary_name) {
  std::lock_guard<std::mutex> lg(g_mutex);
  if (GetState() != STATE_UNINITIALIZED) {
    return false;
  }

  InitLogger(binary_name);
#ifdef _WIN32
  if (!SetConsoleCtrlHandler(OnShutdown, TRUE)) {
    AERROR << "Failed to register shutdown handler";
    return false;
  }
#else
  std::signal(SIGINT, OnShutdown);
#endif
  // Register exit handlers
  if (!g_atexit_registered) {
    if (std::atexit(ExitHandle) != 0) {
      AERROR << "Register exit handle failed";
      return false;
    }
    AINFO << "Register exit handle succ.";
    g_atexit_registered = true;
  }
  SetState(STATE_INITIALIZED);

  return true;
}

void Clear() {
  std::lock_guard<std::mutex> lg(g_mutex);
  if (GetState() == STATE_SHUTDOWN || GetState() == STATE_UNINITIALIZED) {
    return;
  }
  StopLogger();
  SetState(STATE_SHUTDOWN);
}

}  // namespace cyber
}  // namespace apollo
