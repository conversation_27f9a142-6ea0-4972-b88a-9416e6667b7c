/******************************************************************************
 * Copyright 2018 The Apollo Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *****************************************************************************/

/**
 * @file
 */

#ifndef CYBER_LOGGER_LOGGER_UTIL_H_
#define CYBER_LOGGER_LOGGER_UTIL_H_

#ifdef _WIN32
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN  /* We always want minimal includes */
#endif
#include <windows.h>
#else
#include <sys/time.h>
#include <sys/utsname.h>
#endif

#include "cyber/common/log.h"

namespace apollo {
namespace cyber {
namespace logger {

inline int64_t CycleClock_Now() {
#ifdef _WIN32
  FILETIME ft;
  GetSystemTimeAsFileTime(&ft);
  ULARGE_INTEGER uli;
  uli.LowPart = ft.dwLowDateTime;
  uli.HighPart = ft.dwHighDateTime;
  // Convert to microseconds since 1601-01-01
  int64_t usec = (uli.QuadPart - 116444736000000000ULL) / 10;
  return usec;
#else
  struct timeval tv;
  gettimeofday(&tv, nullptr);
  return static_cast<int64_t>(tv.tv_sec) * 1000000 + tv.tv_usec;
#endif
}

inline int64_t UsecToCycles(int64_t usec) { return usec; }

void GetHostName(std::string* hostname);

int32_t GetMainThreadPid();

bool PidHasChanged();

inline int32_t MaxLogSize() {
  return 1;  // 100MB
}

inline void FindModuleName(std::string* log_message, std::string* module_name) {
  auto lpos = log_message->find(LEFT_BRACKET);
  if (lpos != std::string::npos) {
    auto rpos = log_message->find(RIGHT_BRACKET, lpos);
    if (rpos != std::string::npos) {
      module_name->assign(*log_message, lpos + 1, rpos - lpos - 1);
      auto cut_length = rpos - lpos + 1;
      log_message->erase(lpos, cut_length);
    }
  }
  if (module_name->empty()) {
    *module_name = "UNKNOWN";
  }
}

}  // namespace logger
}  // namespace cyber
}  // namespace apollo

#endif  // CYBER_LOGGER_LOGGER_UTIL_H_
