/******************************************************************************
 * Copyright 2018 The Apollo Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *****************************************************************************/

#include "cyber/logger/logger_util.h"
#ifdef _WIN32
  #ifndef WIN32_LEAN_AND_MEAN
  #define WIN32_LEAN_AND_MEAN  /* We always want minimal includes */
  #endif

  #include <windows.h>
  #include <winsock2.h>
#else
  #include <sys/time.h>
  #include <sys/utsname.h>
  #include <unistd.h>
#endif

namespace apollo {
namespace cyber {
namespace logger {

#ifdef _WIN32
static int32_t g_main_thread_pid = GetCurrentProcessId();
#else
static int32_t g_main_thread_pid = getpid();
#endif

void GetHostName(std::string* hostname) {
#ifdef _WIN32
  char name[256];
  if (gethostname(name, sizeof(name)) == 0) {
      *hostname = name;
  } else {
      *hostname = "UNKNOWN";
  }
#else
  struct utsname buf;
  if (0 != uname(&buf)) {
    *hostname = "UNKNOWN";
  } else {
    *hostname = buf.nodename;
  }
#endif
}

int32_t GetMainThreadPid() { return g_main_thread_pid; }

bool PidHasChanged() {
#ifdef _WIN32
  int32_t pid = GetCurrentProcessId();
#else
  int32_t pid = getpid();
#endif
  if (g_main_thread_pid == pid) {
    return false;
  }
  g_main_thread_pid = pid;
  return true;
}

}  // namespace logger
}  // namespace cyber
}  // namespace apollo
