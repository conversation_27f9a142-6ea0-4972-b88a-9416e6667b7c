/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "hyper_vision/common/basic_type/basic_type.h"
#include "hyper_vision/interface/interface.h"
#include "rally/utils/utils.h"
#include <csignal>
#include <filesystem>

#ifdef _WIN32
#include <chrono>
#include <thread>
#endif

bool start_ = true;

/**
 * @brief  signal handler
 * @note   will be called if receive ctrl+c signal from keyboard during the
 * progress (all the threads in progress will be stopped and the progress end)
 * @param  sig: the input signal
 * @retval None
 */
static void sigHandler(int sig) { start_ = false; }

int main(int argc, char **argv) {
  signal(SIGINT, sigHandler);

  // std::cout << "run here 1" << std::endl;
  auto config_file_fs =
      std::filesystem::path(PROJECT_PATH) / "config" / "usr_config.yaml";
  if (argc == 2) {
    std::string folderPath = argv[1];
    config_file_fs =
        std::filesystem::path(folderPath) / "Config" / "usr_config.yaml";
  }
  // std::cout << "run here 2" << std::endl;
  SetPlayMode(1);
  // std::cout << "run here 3" << std::endl;
  std::string config_file = config_file_fs.string();
  OnInitialize(config_file.data(), true);
  // std::cout << "run here 4" << std::endl;
  // SetSocketReceiveDataCb();
  // std::cout << "run here 5" << std::endl;
  OnStart();
  // std::cout << "run here 6" << std::endl;

  while (start_) {
#ifdef _WIN32
    std::this_thread::sleep_for(std::chrono::seconds(1));
#else
    sleep(1);
#endif
  }
  OnDestroy();

  return 0;
}
