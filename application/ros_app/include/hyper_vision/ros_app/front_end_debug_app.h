/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef HYPER_VISION_ROS_APP_FRONT_END_DEBUG_APP_H_
#define HYPER_VISION_ROS_APP_FRONT_END_DEBUG_APP_H_

#include <ros/ros.h>
#include <sensor_msgs/Imu.h>
#include <sensor_msgs/PointCloud2.h>
#include <pcl_conversions/pcl_conversions.h>
#include <cv_bridge/cv_bridge.h>
#include <image_transport/image_transport.h>
#include <nav_msgs/Odometry.h>
#include <nav_msgs/Path.h>
#include "hyper_vision/front_end/front_end.h"

namespace robosense {

class FrontEndDebugApp {
 public:
  FrontEndDebugApp(const std::string &config_file, front_end::RunMode run_mode);

  ~FrontEndDebugApp() { Stop(); }

  void Start() { impl_ptr_->Start(); }

  void Stop() { impl_ptr_->Stop(); }

 private:
  std::string name() { return "FrontEndDebugApp"; }

  struct timeval RostimeToTimeval(const ros::Time &ros_time) {
    struct timeval tv;
    tv.tv_sec = ros_time.sec;  // ROS时间的秒部分
    tv.tv_usec = ros_time.nsec / 1000;  // ROS时间的纳秒部分转换为微秒
    return tv;
  }

  void LidarCallback(const sensor_msgs::PointCloud2::ConstPtr &msg);

  void ImuCallback(const sensor_msgs::Imu::ConstPtr &imu_msg_ptr);

  void ImageCallback(const sensor_msgs::ImageConstPtr &image_ptr);

  ros::NodeHandle nh_;
  ros::Subscriber lidar_sub_, imu_sub_, image_sub_;
  image_transport::Publisher img_pub_;
  ros::Publisher pub_pointcloud_, pub_slam_pointcloud_, pub_path_;
  nav_msgs::Path path_;

  front_end::FrontEnd::Ptr impl_ptr_;
};

}  // namespace robosense

#endif  // HYPER_VISION_ROS_APP_FRONT_END_DEBUG_APP_H_
