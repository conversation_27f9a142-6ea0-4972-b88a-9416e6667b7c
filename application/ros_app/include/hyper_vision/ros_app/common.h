/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef HYPER_VISION_ROS_APP_COMMON_H_
#define HYPER_VISION_ROS_APP_COMMON_H_

#include <iostream>
#include <string>
#include <map>
#include <vector>
#include <sstream>

namespace robosense {

inline std::string Encode(const std::map<std::string, std::string>& data) {
  std::stringstream ss;
  for (const auto& pair : data) {
    ss << pair.first << "=" << pair.second << ",";
  }
  std::string result = ss.str();
  if (!result.empty()) {
    result.pop_back(); // 去掉最后一个逗号
  }
  return result;
}

inline std::map<std::string, std::string> Decode(const std::string& encoded) {
  std::map<std::string, std::string> result;
  std::stringstream ss(encoded);
  std::string token;
  while (std::getline(ss, token, ',')) {
    size_t pos = token.find('=');
    if (pos != std::string::npos) {
      std::string key = token.substr(0, pos);
      std::string value = token.substr(pos + 1);
      result[key] = value;
    }
  }
  return result;
}

}  // namespace robosense

#endif  // HYPER_VISION_ROS_APP_COMMON_H_
