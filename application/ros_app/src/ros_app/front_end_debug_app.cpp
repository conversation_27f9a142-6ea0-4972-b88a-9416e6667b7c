/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "hyper_vision/ros_app/front_end_debug_app.h"

namespace robosense {

FrontEndDebugApp::FrontEndDebugApp(const std::string &config_file, front_end::RunMode run_mode) {
  impl_ptr_.reset(new front_end::FrontEnd());

  rally::init(config_file, "");
  YAML::Node cfg_node, super_sensor_node;
  rally::ConfigureManager::getInstance().setConfigFile(config_file);
  cfg_node = rally::ConfigureManager::getInstance().getCfgNode();
  std::string config_path = rally::ConfigureManager::getInstance().getWorkRoot();

  rally::yamlSubNode(cfg_node, "super_sensor", super_sensor_node);

  impl_ptr_->Init(config_path, super_sensor_node, run_mode);

  std::string lid_topic = "/livox/lidar";
  std::string imu_topic = "/livox/imu";
  std::string img_topic = "/left_camera/image";
  // subscriber, impl by ROS
  std::string rs_meta_file = config_path + "/RS_META.yaml";
  auto rs_meta_node = YAML::LoadFile(rs_meta_file);
  yamlRead(rs_meta_node["topic"], "lid_topic", lid_topic);
  yamlRead(rs_meta_node["topic"], "imu_topic", imu_topic);
  yamlRead(rs_meta_node["topic"], "img_topic", img_topic);
  RINFO << "topic: lidar: " << lid_topic << " imu: " << imu_topic << " camera: " << img_topic;

  lidar_sub_ = nh_.subscribe(lid_topic, 200000, &FrontEndDebugApp::LidarCallback, this);
  imu_sub_ = nh_.subscribe(imu_topic, 200000, &FrontEndDebugApp::ImuCallback, this);
  image_sub_ = nh_.subscribe(img_topic, 200000, &FrontEndDebugApp::ImageCallback, this);
  image_transport::ImageTransport it(nh_);

  img_pub_ = it.advertise("/rgb_img", 1);
  pub_pointcloud_ = nh_.advertise<sensor_msgs::PointCloud2>("/cloud_registered", 1);
  pub_slam_pointcloud_ = nh_.advertise<sensor_msgs::PointCloud2>("/slam_cloud_registered", 1);
  pub_path_ = nh_.advertise<nav_msgs::Path>("/path", 1);

  auto func = [this](const front_end::FrontEndOutputMsg::Ptr& msg_ptr) {
    {  // pub pose
      auto pos_msg = msg_ptr->odom.ToPosition();
      geometry_msgs::PoseStamped msg_body_pose;
      msg_body_pose.pose.position.x = pos_msg.x;
      msg_body_pose.pose.position.y = pos_msg.y;
      msg_body_pose.pose.position.z = pos_msg.z;
      path_.poses.emplace_back(msg_body_pose);
      path_.header.frame_id = "camera_init";
      this->pub_path_.publish(path_);
    }
    {  // pub pointcloud
      auto pc_msg = msg_ptr->point_cloud.ToOutPointCloud();
      pcl::PointCloud<pcl::PointXYZRGB>::Ptr color_cloud_ptr(new pcl::PointCloud<pcl::PointXYZRGB>);
      color_cloud_ptr->resize(pc_msg.size);
      for (int i = 0; i < pc_msg.size; ++i) {
        auto& out_pt = color_cloud_ptr->points[i];
        const auto& in_pt = pc_msg.data[i];
        out_pt.x = in_pt.x;
        out_pt.y = in_pt.y;
        out_pt.z = in_pt.z;
        out_pt.r = in_pt.r;
        out_pt.g = in_pt.g;
        out_pt.b = in_pt.b;
      }
      sensor_msgs::PointCloud2 point_cloud_msg;
      pcl::toROSMsg(*color_cloud_ptr, point_cloud_msg);
      point_cloud_msg.header.stamp = ros::Time::now();
      point_cloud_msg.header.frame_id = "camera_init";
      this->pub_pointcloud_.publish(point_cloud_msg);
    }
    {  // pub slam pointcloud
      auto slam_pc_msg = msg_ptr->slam_point_cloud.ToOutPointCloud();
      pcl::PointCloud<pcl::PointXYZRGB>::Ptr color_cloud_ptr(new pcl::PointCloud<pcl::PointXYZRGB>);
      color_cloud_ptr->resize(slam_pc_msg.size);
      for (int i = 0; i < slam_pc_msg.size; ++i) {
        auto& out_pt = color_cloud_ptr->points[i];
        const auto& in_pt = slam_pc_msg.data[i];
        out_pt.x = in_pt.x;
        out_pt.y = in_pt.y;
        out_pt.z = in_pt.z;
        out_pt.r = in_pt.r;
        out_pt.g = in_pt.g;
        out_pt.b = in_pt.b;
      }
      sensor_msgs::PointCloud2 point_cloud_msg;
      pcl::toROSMsg(*color_cloud_ptr, point_cloud_msg);
      point_cloud_msg.header.stamp = ros::Time::now();
      point_cloud_msg.header.frame_id = "camera_init";
      this->pub_slam_pointcloud_.publish(point_cloud_msg);
    }
    {
      auto image_msg = msg_ptr->rgb_image.ToOutRgbImage();
      if (image_msg.height <= 0 || image_msg.width <= 0) {
        RINFO << name() << ": no image data!";
      } else {
        const auto& height = image_msg.height;
        const auto& width = image_msg.width;
        const auto& data = image_msg.data;
        cv::Mat img_data = cv::Mat(height, width, CV_8UC3);
        for (int row = 0; row < height; ++row) {
          for (int col = 0; col < width; ++col) {
            img_data.at<cv::Vec3b>(row, col)[0] = data[(row * width + col) * 3 + 2];
            img_data.at<cv::Vec3b>(row, col)[1] = data[(row * width + col) * 3 + 1];
            img_data.at<cv::Vec3b>(row, col)[2] = data[(row * width + col) * 3 + 0];
          }
        }
        cv_bridge::CvImage out_msg;
        out_msg.header.stamp = ros::Time::now();
        out_msg.encoding = sensor_msgs::image_encodings::BGR8;
        out_msg.image = img_data;
        this->img_pub_.publish(out_msg.toImageMsg());
      }
    }
  };
  impl_ptr_->SetCallback(func);
}

void FrontEndDebugApp::LidarCallback(const sensor_msgs::PointCloud2::ConstPtr &msg) {
  std::shared_ptr<common::DepthFrame> msg_ptr(new common::DepthFrame);
  msg_ptr->capture_time = RostimeToTimeval(msg->header.stamp);

  pcl::PointCloud<robosense::Point>::Ptr pl_orig_ptr(new pcl::PointCloud<robosense::Point>);
  pcl::fromROSMsg(*msg, *pl_orig_ptr);
  msg_ptr->point_nums = pl_orig_ptr->size();
  msg_ptr->points = std::shared_ptr<robosense::common::CloudPointXYZIRT>(
      new robosense::common::CloudPointXYZIRT[msg_ptr->point_nums],
      std::default_delete<robosense::common::CloudPointXYZIRT[]>());
  for (size_t i = 0; i < pl_orig_ptr->size(); ++i) {
    const auto &in_pt = pl_orig_ptr->points[i];
    msg_ptr->points.get()[i].x = in_pt.x;
    msg_ptr->points.get()[i].y = in_pt.y;
    msg_ptr->points.get()[i].z = in_pt.z;
    msg_ptr->points.get()[i].intensity = in_pt.intensity;
    msg_ptr->points.get()[i].ring = in_pt.ring;
    msg_ptr->points.get()[i].timestamp = in_pt.timestamp;
  }
  impl_ptr_->AddData(msg_ptr);
}

void FrontEndDebugApp::ImuCallback(const sensor_msgs::Imu::ConstPtr &imu_msg_ptr) {
  std::shared_ptr<common::MotionFrame> msg_ptr(new common::MotionFrame);
  msg_ptr->capture_time = RostimeToTimeval(imu_msg_ptr->header.stamp);
  msg_ptr->accel.x = imu_msg_ptr->linear_acceleration.x;
  msg_ptr->accel.y = imu_msg_ptr->linear_acceleration.y;
  msg_ptr->accel.z = imu_msg_ptr->linear_acceleration.z;
  msg_ptr->gyro.x = imu_msg_ptr->angular_velocity.x;
  msg_ptr->gyro.y = imu_msg_ptr->angular_velocity.y;
  msg_ptr->gyro.z = imu_msg_ptr->angular_velocity.z;
  impl_ptr_->AddData(msg_ptr);
}

void FrontEndDebugApp::ImageCallback(const sensor_msgs::ImageConstPtr &image_ptr) {
  std::shared_ptr<common::ImageFrame> msg_ptr(new common::ImageFrame);
  msg_ptr->capture_time = RostimeToTimeval(image_ptr->header.stamp);
  std::shared_ptr<cv::Mat> cv_img_ptr(new cv::Mat);
  *cv_img_ptr = cv_bridge::toCvCopy(image_ptr, "bgr8")->image;
  cv::cvtColor(*cv_img_ptr, *cv_img_ptr, cv::COLOR_BGR2RGB);

  msg_ptr->width = cv_img_ptr->cols;
  msg_ptr->height = cv_img_ptr->rows;
  msg_ptr->frame_format = robosense::common::FRAME_FORMAT_RGB24;
  msg_ptr->step = cv_img_ptr->step;
  msg_ptr->data_bytes = cv_img_ptr->step * cv_img_ptr->rows;
  msg_ptr->data = std::shared_ptr<uint8_t>(
      new uint8_t[msg_ptr->data_bytes], std::default_delete<uint8_t[]>());
  memcpy(msg_ptr->data.get(), cv_img_ptr->data,
         sizeof(uint8_t) * msg_ptr->data_bytes);

  this->impl_ptr_->AddData(msg_ptr);
}

}  // namespace robosense
