find_package(catkin REQUIRED COMPONENTS
        pcl_ros
        roscpp
        rosbag
        cv_bridge
        sensor_msgs
        image_transport
        )

#========================
# libs
#========================

set(CUR_SRCS "")
LIST(APPEND CUR_INCLUDES include)

set(CUR_SUB_DIR "")
LIST(APPEND CUR_SUB_DIR include)
LIST(APPEND CUR_SUB_DIR src)

foreach (dir ${CUR_SUB_DIR})
    file(GLOB_RECURSE tmp_srcs ${dir}/*.cpp ${dir}/*.h)
    list(APPEND CUR_SRCS ${tmp_srcs})
endforeach ()

add_library(${CUR_LIB} SHARED
        ${CUR_SRCS}
        )

target_include_directories(${CUR_LIB}
        PUBLIC
        ${CUR_INCLUDES}
        ${catkin_INCLUDE_DIRS}
        )
target_link_libraries(${CUR_LIB}
        PUBLIC
        ${catkin_LIBRARIES}
        slam
        interface
        )