/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include <signal.h>
#include "hyper_vision/common/basic_type/basic_type.h"
#include "hyper_vision/interface/interface.h"
#include "rally/utils/utils.h"
#include "hyper_vision/ros_app/ros_app.h"

std::atomic<bool> running{true};

void signalHandler(int signum) {
  RINFO << "Received signal: " << signum;
  running = false;
  ros::shutdown();
}

std::string select_uuid;

void getDevices() {
  bool closed = true;
  while (running) {
    // RINFO << "GetDeviceInfo()...";
    auto handle = [](void *devices, unsigned int size) {
      for (unsigned int i = 0; i < size; i++) {
        TagDeviceEvent device_event = static_cast<TagDeviceEvent *>(devices)[i];
        auto str_size = device_event.uuid_size;
        RINFO << "str size: " << str_size;
        std::string uuid = std::string(device_event.uuid, str_size);
        RINFO << "device uuid: " << uuid;
        if (select_uuid.empty()) {
          select_uuid = uuid;
        }
      }
    };
    GetDeviceInfo(handle);
    if (select_uuid.empty()) {
      RERROR << "no device found!";
      closed = true;
      std::this_thread::sleep_for(std::chrono::seconds(1));
      continue;
    }
    if (closed) {
      auto operator_type =
          robosense::common::DeviceOperatorType_t::DEVICE_OPERATOR_OPEN;
      RWARN << "open device uuid: " << select_uuid;
      if (0 == OperatorDevice(select_uuid.c_str(), operator_type)) {
        RINFO << "device is opened";
        closed = false;
      } else {
        RERROR << "operator device failed";
      }
    }
    select_uuid.clear();
    std::this_thread::sleep_for(std::chrono::seconds(2));
  }
}

image_transport::Publisher img_pub_;
ros::Publisher pub_pointcloud_, pub_slam_pointcloud_, pub_path_;

int main(int argc, char **argv) {
  ros::init(argc, argv, "front_end_ros_app_node");
  ros::NodeHandle nh_;
// 注册信号处理函数
  signal(SIGINT, signalHandler);
  signal(SIGTERM, signalHandler);

  image_transport::ImageTransport it(nh_);
  img_pub_ = it.advertise("/rgb_img", 1);
  pub_pointcloud_ = nh_.advertise<sensor_msgs::PointCloud2>("/cloud_registered", 1);
  pub_slam_pointcloud_ = nh_.advertise<sensor_msgs::PointCloud2>("/slam_cloud_registered", 1);
  pub_path_ = nh_.advertise<nav_msgs::Path>("/path", 1);

  std::string config_file =
      std::string(PROJECT_PATH) + "/config/usr_config.yaml";

  SetRunMode(2);

  OnInitialize(config_file.data());
  auto th1 = std::thread(getDevices);
  auto func = [](void *pos_msg, void *cloud_msg, void *slam_cloud_msg, void *triangle_msg, void *rgb_msg, void *depth_msg) {
    auto position_ptr = static_cast<TagPosition *>(pos_msg);
    auto pc_ptr = static_cast<TagPointCloud *>(cloud_msg);
    auto slam_pc_ptr = static_cast<TagPointCloud *>(slam_cloud_msg);
    auto tri_ptr = static_cast<TagTriangleFacet *>(triangle_msg);
    auto rgb_image_ptr = static_cast<TagRgbImage *>(rgb_msg);
    auto depth_image_ptr = static_cast<TagDepthImage *>(depth_msg);
    if (cloud_msg != nullptr) {  // pub pointcloud
      pcl::PointCloud<pcl::PointXYZRGB>::Ptr color_cloud_ptr(new pcl::PointCloud<pcl::PointXYZRGB>);
      color_cloud_ptr->resize(pc_ptr->size);
      for (int i = 0; i < pc_ptr->size; ++i) {
        auto& out_pt = color_cloud_ptr->points[i];
        const auto& in_pt = pc_ptr->data[i];
        out_pt.x = in_pt.x;
        out_pt.y = in_pt.y;
        out_pt.z = in_pt.z;
        out_pt.r = in_pt.r;
        out_pt.g = in_pt.g;
        out_pt.b = in_pt.b;
      }
      sensor_msgs::PointCloud2 point_cloud_msg;
      pcl::toROSMsg(*color_cloud_ptr, point_cloud_msg);
      point_cloud_msg.header.stamp = ros::Time::now();
      point_cloud_msg.header.frame_id = "camera_init";
      pub_pointcloud_.publish(point_cloud_msg);
    }
    if (slam_cloud_msg != nullptr) {  // pub slam pointcloud
      pcl::PointCloud<pcl::PointXYZRGB>::Ptr color_cloud_ptr(new pcl::PointCloud<pcl::PointXYZRGB>);
      color_cloud_ptr->resize(slam_pc_ptr->size);
      for (int i = 0; i < slam_pc_ptr->size; ++i) {
        auto& out_pt = color_cloud_ptr->points[i];
        const auto& in_pt = slam_pc_ptr->data[i];
        out_pt.x = in_pt.x;
        out_pt.y = in_pt.y;
        out_pt.z = in_pt.z;
        out_pt.r = in_pt.r;
        out_pt.g = in_pt.g;
        out_pt.b = in_pt.b;
      }
      sensor_msgs::PointCloud2 point_cloud_msg;
      pcl::toROSMsg(*color_cloud_ptr, point_cloud_msg);
      point_cloud_msg.header.stamp = ros::Time::now();
      point_cloud_msg.header.frame_id = "camera_init";
      pub_slam_pointcloud_.publish(point_cloud_msg);
    }
    if (rgb_msg != nullptr) {
      if (rgb_image_ptr->height <= 0 || rgb_image_ptr->width <= 0) {
        RINFO << "no image data!";
      } else {
        const auto& height = rgb_image_ptr->height;
        const auto& width = rgb_image_ptr->width;
        const auto& data = rgb_image_ptr->data;
        cv::Mat img_data = cv::Mat(height, width, CV_8UC3);
        for (int row = 0; row < height; ++row) {
          for (int col = 0; col < width; ++col) {
            img_data.at<cv::Vec3b>(row, col)[0] = data[(row * width + col) * 3 + 2];
            img_data.at<cv::Vec3b>(row, col)[1] = data[(row * width + col) * 3 + 1];
            img_data.at<cv::Vec3b>(row, col)[2] = data[(row * width + col) * 3 + 0];
          }
        }
        cv_bridge::CvImage out_msg;
        out_msg.header.stamp = ros::Time::now();
        out_msg.encoding = sensor_msgs::image_encodings::BGR8;
        out_msg.image = img_data;
        img_pub_.publish(out_msg.toImageMsg());
      }
    }
  };

  SetReceiveDataHandler(func);
  OnStart();

  ros::Rate loop_rate(50);
  while (ros::ok()) {
    ros::spinOnce();
    loop_rate.sleep();
  }

  OnDestroy();

  if (th1.joinable()) {
    th1.join();
  }

  return 0;
}
