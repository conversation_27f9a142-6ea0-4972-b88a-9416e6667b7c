/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "hyper_vision/ros_app/ros_app.h"
#include "hyper_vision/common/basic_type/basic_type.h"
#include "hyper_vision/interface/interface_impl.h"
#include "rally/utils/utils.h"
#include <csignal>

bool start_ = true;
std::string select_uuid;

/**
 * @brief  signal handler
 * @note   will be called if receive ctrl+c signal from keyboard during the
 * progress (all the threads in progress will be stopped and the progress end)
 * @param  sig: the input signal
 * @retval None
 */
//static void sigHandler(int sig) { start_ = false; }

robosense::driver::SuperSensor::Ptr driver_ptr_;
ros::Publisher pub_imu_, pub_cloud_;
image_transport::Publisher pub_image_;
std::mutex g_mutex_;

void getDevices() {
  bool closed = true;
  while (start_) {
    {
      auto device_infos = driver_ptr_->GetDeviceUuids();
      auto devices = new TagDeviceEvent[device_infos.size()];
      int index = 0;
      for(auto iterSet = device_infos.begin(); iterSet != device_infos.end(); iterSet++, index++) {
        auto& device = devices[index];
        const auto& info = *iterSet;
        auto type = static_cast<uint8_t>(robosense::common::DeviceEventType_t::DEVICE_EVENT_ATTACH);
        device.event_type = type;
        device.uuid_size = info.size();
        memcpy(device.uuid, info.c_str(), info.size());
      }
      for (unsigned int i = 0; i < device_infos.size(); i++) {
        TagDeviceEvent device_event = static_cast<TagDeviceEvent *>(devices)[i];
        auto str_size = device_event.uuid_size;
        RINFO << "str size: " << str_size;
        std::string uuid = std::string(device_event.uuid, str_size);
        RINFO << "device uuid: " << uuid;
        if (select_uuid.empty()) {
          select_uuid = uuid;
        }
      }
      delete[] devices;
    }

    if (select_uuid.empty()) {
      RERROR << "no device found!";
      closed = true;
      std::this_thread::sleep_for(std::chrono::seconds(1));
      continue;
    }
    if (closed) {
      auto operator_type =
          robosense::common::DeviceOperatorType_t::DEVICE_OPERATOR_OPEN;
      RWARN << "open device uuid: " << select_uuid;
      robosense::common::DeviceOperator device_operator;
      device_operator.operation_type = static_cast<robosense::common::DeviceOperatorType_t>(operator_type);
      std::string str_uuid(select_uuid);
      device_operator.uuid_size = str_uuid.size();
      memcpy(device_operator.uuid, str_uuid.c_str(), str_uuid.size());

      if (0 == driver_ptr_->OperatorDevice(device_operator)) {
        RINFO << "device is opened";
        closed = false;
      } else {
        RERROR << "operator device failed";
      }
    }
    select_uuid.clear();
    std::this_thread::sleep_for(std::chrono::seconds(2));
  }
}

void TransTime(const struct timeval& tv, std_msgs::Header& header) {
  header.stamp.sec = tv.tv_sec;
  header.stamp.nsec = tv.tv_usec * 1000;
}

int main(int argc, char **argv) {
//  signal(SIGINT, sigHandler);
  ros::init(argc, argv, "driver_data_publisher");
  ros::NodeHandle nh;
  image_transport::ImageTransport it(nh);

  std::string config_file = std::string(PROJECT_PATH) + "/config/usr_config.yaml";
  rally::init(config_file, "");
  YAML::Node cfg_node, ros_bag_writer_node;
  rally::ConfigureManager::getInstance().setConfigFile(config_file);
  cfg_node = rally::ConfigureManager::getInstance().getCfgNode();

  rally::yamlSubNode(cfg_node, "ros_bag_writer", ros_bag_writer_node);
  YAML::Node driver_cfg_node;
  std::string write_dir;
  rally::yamlRead(ros_bag_writer_node, "write_dir", write_dir);
  rally::yamlSubNode(ros_bag_writer_node, "driver", driver_cfg_node);

  pub_cloud_ = nh.advertise<sensor_msgs::PointCloud2>("/rslidar_points_meta", 100);
  pub_image_ = it.advertise("/image_rgb", 1);
  pub_imu_ = nh.advertise<sensor_msgs::Imu>("/rslidar_imuData_meta", 100);

  driver_ptr_.reset(new robosense::driver::SuperSensor());

  driver_ptr_->Init(driver_cfg_node);
  {
    auto motion_func = [](const std::shared_ptr<robosense::common::MotionFrame> &msg_ptr) {
      RINFO << "receive motion!";
      std::lock_guard<std::mutex> lock_(g_mutex_);
      sensor_msgs::Imu::Ptr imu_ptr(new sensor_msgs::Imu);
      std_msgs::Header header;
      TransTime(msg_ptr->capture_time, header);
      imu_ptr->header = header;
      imu_ptr->linear_acceleration.x = msg_ptr->accel.x;
      imu_ptr->linear_acceleration.y = msg_ptr->accel.y;
      imu_ptr->linear_acceleration.z = msg_ptr->accel.z;
      imu_ptr->angular_velocity.x = msg_ptr->gyro.x;
      imu_ptr->angular_velocity.y = msg_ptr->gyro.y;
      imu_ptr->angular_velocity.z = msg_ptr->gyro.z;
      pub_imu_.publish(imu_ptr);
    };
    driver_ptr_->SetCallback(motion_func);

    auto depth_func = [](const std::shared_ptr<robosense::common::DepthFrame> &msg_ptr) {
      RINFO << "receive depth!";
      std::lock_guard<std::mutex> lock_(g_mutex_);
      std_msgs::Header header;
      TransTime(msg_ptr->capture_time, header);
      pcl::PointCloud<robosense::Point>::Ptr cloud_msg_ptr(new pcl::PointCloud<robosense::Point>);
      cloud_msg_ptr->height = 1;
      cloud_msg_ptr->width = msg_ptr->point_nums;
      cloud_msg_ptr->points.resize(msg_ptr->point_nums);
      for (size_t i = 0; i < cloud_msg_ptr->points.size(); ++i) {
        const auto& in_pt = msg_ptr->points.get()[i];
        auto& out_pt = cloud_msg_ptr->points[i];
        out_pt.x = in_pt.x;
        out_pt.y = in_pt.y;
        out_pt.z = in_pt.z;
        out_pt.intensity = in_pt.intensity;
        out_pt.timestamp = in_pt.timestamp;
        out_pt.ring = in_pt.ring;
      }

      sensor_msgs::PointCloud2 laserCloudmsg;
      pcl::toROSMsg(*cloud_msg_ptr, laserCloudmsg);
      laserCloudmsg.header = header;
      laserCloudmsg.header.frame_id = "camera_init";
      pub_cloud_.publish(laserCloudmsg);
    };
    driver_ptr_->SetCallback(depth_func);

    auto image_func = [](const std::shared_ptr<robosense::common::ImageFrame> &msg_ptr) {
      RINFO << "receive image!";
      std::lock_guard<std::mutex> lock_(g_mutex_);
      std_msgs::Header header;
      TransTime(msg_ptr->capture_time, header);
      std::shared_ptr<cv::Mat> img_msg_ptr(new cv::Mat(cv::Size(msg_ptr->width, msg_ptr->height), CV_8UC3, msg_ptr->data.get()));
      cv::cvtColor(*img_msg_ptr, *img_msg_ptr, cv::COLOR_RGB2BGR);

      cv_bridge::CvImage out_msg;
      out_msg.header = header;
      out_msg.encoding = sensor_msgs::image_encodings::BGR8;
      out_msg.image = *img_msg_ptr;
      pub_image_.publish(out_msg.toImageMsg());
    };
    driver_ptr_->SetCallback(image_func);
  }
  driver_ptr_->Start();

  auto th1 = std::thread(getDevices);

  ros::spin();
  start_ = false;
  driver_ptr_->Stop();
  if (th1.joinable()) {
    th1.join();
  }

  return 0;
}
