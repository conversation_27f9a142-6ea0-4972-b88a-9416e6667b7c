/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include <QApplication>
#include <QWidget>
#include <QPushButton>
#include <QVBoxLayout>
#include <QDebug>
#include <ros/ros.h>
#include <std_msgs/String.h>
#include "hyper_vision/ros_app/ros_app.h"

class MainWindow : public QWidget {
 Q_OBJECT

 public:
  MainWindow(QWidget *parent = nullptr) : QWidget(parent) {
    pub_ = nh_.advertise<std_msgs::String>("chatter", 10);

    // 设置窗口标题
    setWindowTitle("ROS + OpenCV + PCL UI Demo");

    // 创建一个按钮
    QPushButton * single_frame_button = new QPushButton("single_frame", this);
    QPushButton * slam_frame_button = new QPushButton("slam_frame", this);
    QPushButton * online_mode_button = new QPushButton("online_mode", this);
    QPushButton * offline_mode_button = new QPushButton("offline_mode", this);

    // 创建一个垂直布局
    QVBoxLayout * layout = new QVBoxLayout(this);
    layout->addWidget(single_frame_button);
    layout->addWidget(slam_frame_button);
    layout->addWidget(online_mode_button);
    layout->addWidget(offline_mode_button);
    setLayout(layout);

    // 连接按钮的点击信号到槽函数
    connect(single_frame_button, &QPushButton::clicked, this, &MainWindow::onSingleFrameButtonClicked);
    connect(slam_frame_button, &QPushButton::clicked, this, &MainWindow::onSlamFrameButtonClicked);
    connect(online_mode_button, &QPushButton::clicked, this, &MainWindow::onOnlineModeButtonClicked);
    connect(offline_mode_button, &QPushButton::clicked, this, &MainWindow::onOfflineModeButtonClicked);
  }

 private slots:
  void onSingleFrameButtonClicked() {
    slam_mode_ = false;
    UpdateMsg();
    pub_.publish(msg_);
  }

  void onSlamFrameButtonClicked() {
    slam_mode_ = true;
    UpdateMsg();
    pub_.publish(msg_);
  }

  void onOnlineModeButtonClicked() {
    online_mode_ = true;
    UpdateMsg();
    pub_.publish(msg_);
  }

  void onOfflineModeButtonClicked() {
    online_mode_ = false;
    UpdateMsg();
    pub_.publish(msg_);
  }

  void UpdateMsg() {
    std::map<std::string, std::string> info_keys;
    if (slam_mode_) {
      info_keys["slam"] = "true";
    } else {
      info_keys["slam"] = "false";
    }

    if (online_mode_) {
      info_keys["online"] = "true";
    } else {
      info_keys["online"] = "false";
    }

    msg_.data = robosense::Encode(info_keys);
 }

  std_msgs::String msg_;
  ros::NodeHandle nh_;
  ros::Publisher pub_;

  bool slam_mode_{true};
  bool online_mode_{true};
};

int main(int argc, char *argv[]) {
  ros::init(argc, argv, "qt_ui_demo");

  // 创建Qt应用程序
  QApplication app(argc, argv);

  // 创建主窗口
  MainWindow window;
  window.show();

  // 进入事件循环
  return app.exec();
}

#include "qt_ros_ui_node.moc"