# Prevent multiple inclusions
if(Eigen3_FOUND)
    return()
endif()

# Set default search path for Eigen source (relative to project root)
set(EIGEN3_SEARCH_PATHS
        "${CMAKE_SOURCE_DIR}/third_party/eigen-3.4.0"
        )

# Find Eigen include directory
find_path(EIGEN3_INCLUDE_DIR
        NAMES Eigen/Core
        PATHS ${EIGEN3_SEARCH_PATHS}
        DOC "Eigen3 include directory"
        )

# Check if Eigen was found
if(EIGEN3_INCLUDE_DIR)
    set(Eigen3_FOUND TRUE)
    message(STATUS "Found Eigen3: ${EIGEN3_INCLUDE_DIR}")
else()
    set(Eigen3_FOUND FALSE)
    message(WARNING "Eigen3 not found in expected paths: ${EIGEN3_SEARCH_PATHS}")
endif()

# Define Eigen3::Eigen target if found
if(Eigen3_FOUND)
    if(NOT TARGET Eigen3::Eigen)
        add_library(Eigen3::Eigen INTERFACE IMPORTED)
        set_target_properties(Eigen3::Eigen PROPERTIES
                INTERFACE_INCLUDE_DIRECTORIES "${EIGEN3_INCLUDE_DIR}"
                )
    endif()

    # Set variables for compatibility
    set(EIGEN3_INCLUDE_DIRS ${EIGEN3_INCLUDE_DIR})
    set(Eigen3_DIR ${EIGEN3_INCLUDE_DIR})
    set(EIGEN3_VERSION "3.4.0")  # 可根据实际版本调整
    set(Eigen3_VERSION "3.4.0")  # 可根据实际版本调整
endif()

# Handle QUIETLY and REQUIRED arguments
include(FindPackageHandleStandardArgs)
find_package_handle_standard_args(Eigen3
        REQUIRED_VARS EIGEN3_INCLUDE_DIR
        VERSION_VAR EIGEN3_VERSION
        )

mark_as_advanced(EIGEN3_INCLUDE_DIR)