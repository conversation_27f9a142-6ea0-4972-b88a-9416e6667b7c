# 查找 Git
find_package(Git REQUIRED)

# 读取 VERSION 文件
file(READ "${CMAKE_SOURCE_DIR}/VERSION" VERSION_CONTENT)

# 使用正则表达式提取版本号
string(REGEX MATCH "Version: ([0-9]+\\.[0-9]+\\.[0-9]+)" VERSION_MATCH "${VERSION_CONTENT}")
set(PROJECT_VERSION "${CMAKE_MATCH_1}")

# 获取当前 commit ID
execute_process(
    COMMAND ${GIT_EXECUTABLE} rev-parse --short HEAD
    WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
    OUTPUT_VARIABLE GIT_COMMIT_HASH
    OUTPUT_STRIP_TRAILING_WHITESPACE
)

# 打印版本号和 commit ID
message(STATUS "Project Version: ${PROJECT_VERSION}")
message(STATUS "Git Commit Hash: ${GIT_COMMIT_HASH}")

# 将版本号和 commit ID 传递给源代码
set(INTERFACE_INCLUDE_DIR ${CMAKE_SOURCE_DIR}/modules/interface/include)
configure_file(
    ${INTERFACE_INCLUDE_DIR}/project_version/version.h.in
    ${INTERFACE_INCLUDE_DIR}/project_version/version.h
)

# 将版本号和 commit ID 传递给输出目录
configure_file(
    ${CMAKE_SOURCE_DIR}/VERSION.in
    ${CMAKE_BINARY_DIR}/VERSION
)