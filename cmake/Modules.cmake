list(APPEND CMAKE_MODULE_PATH "${CMAKE_SOURCE_DIR}/cmake")
set(THIRD_PARTY_DIR ${PROJECT_SOURCE_DIR}/third_party)

set(USE_SRC TRUE)

#========================
# driver part
#========================
if(WIN32)
        add_library(rsbag STATIC IMPORTED GLOBAL)
        set_target_properties(rsbag PROPERTIES
                INTERFACE_INCLUDE_DIRECTORIES ${THIRD_PARTY_DIR}/librsbag/include)     
        set_target_properties(rsbag PROPERTIES
                IMPORTED_LOCATION_RELEASE ${THIRD_PARTY_DIR}/librsbag/lib/windows/rsbag.lib
                ) 
        
        add_library(rsros STATIC IMPORTED GLOBAL)
        set_target_properties(rsros PROPERTIES
                INTERFACE_INCLUDE_DIRECTORIES ${THIRD_PARTY_DIR}/librsros/include)     
        set_target_properties(rsros PROPERTIES
                IMPORTED_LOCATION_RELEASE ${THIRD_PARTY_DIR}/librsros/lib/windows/rsros.lib
                ) 
                        
        add_library(rsmsg STATIC IMPORTED GLOBAL)
        set_target_properties(rsmsg PROPERTIES
                INTERFACE_INCLUDE_DIRECTORIES ${THIRD_PARTY_DIR}/librsmsg/include)     
        set_target_properties(rsmsg PROPERTIES
                IMPORTED_LOCATION_RELEASE ${THIRD_PARTY_DIR}/librsmsg/lib/windows/rsmsg.lib
        )
        message("Sytem Type: Windows")
elseif(UNIX AND NOT APPLE)
        add_library(supersensor SHARED IMPORTED GLOBAL)
        set_target_properties(supersensor PROPERTIES
                INTERFACE_INCLUDE_DIRECTORIES ${THIRD_PARTY_DIR}/libsupersensor/include
                )
        set_target_properties(supersensor PROPERTIES
                IMPORTED_IMPLIB_RELEASE ${THIRD_PARTY_DIR}/libsupersensor/lib/linux/libmfsensor.so
                IMPORTED_LOCATION_RELEASE ${THIRD_PARTY_DIR}/libsupersensor/lib/linux/libmfsensor.so
                )   

        add_library(rsbag SHARED IMPORTED GLOBAL)
        set_target_properties(rsbag PROPERTIES
		INTERFACE_INCLUDE_DIRECTORIES ${THIRD_PARTY_DIR}/librsbag/include)     
	if(CMAKE_SYSTEM_PROCESSOR MATCHES "x86_64")
		set_target_properties(rsbag PROPERTIES
                	IMPORTED_IMPLIB_RELEASE ${THIRD_PARTY_DIR}/librsbag/lib/linux/x86/librsbag.so
                	IMPORTED_LOCATION_RELEASE ${THIRD_PARTY_DIR}/librsbag/lib/linux/x86/librsbag.so
                )
	else()
		set_target_properties(rsbag PROPERTIES
                        IMPORTED_IMPLIB_RELEASE ${THIRD_PARTY_DIR}/librsbag/lib/linux/arm/librsbag.so
                        IMPORTED_LOCATION_RELEASE ${THIRD_PARTY_DIR}/librsbag/lib/linux/arm/librsbag.so
                )
	endif()	

        add_library(rsros SHARED IMPORTED GLOBAL)
        set_target_properties(rsros PROPERTIES
                INTERFACE_INCLUDE_DIRECTORIES ${THIRD_PARTY_DIR}/librsros/include)     
       if(CMAKE_SYSTEM_PROCESSOR MATCHES "x86_64")
       		set_target_properties(rsros PROPERTIES
        	        IMPORTED_IMPLIB_RELEASE ${THIRD_PARTY_DIR}/librsros/lib/linux/x86/librsros.so
                	IMPORTED_LOCATION_RELEASE ${THIRD_PARTY_DIR}/librsros/lib/linux/x86/librsros.so
                ) 
	else()
		set_target_properties(rsros PROPERTIES
                        IMPORTED_IMPLIB_RELEASE ${THIRD_PARTY_DIR}/librsros/lib/linux/arm/librsros.so
                        IMPORTED_LOCATION_RELEASE ${THIRD_PARTY_DIR}/librsros/lib/linux/arm/librsros.so
                )

	endif()
                
        add_library(rsmsg SHARED IMPORTED GLOBAL)
        set_target_properties(rsmsg PROPERTIES
                INTERFACE_INCLUDE_DIRECTORIES ${THIRD_PARTY_DIR}/librsmsg/include)     
       if(CMAKE_SYSTEM_PROCESSOR MATCHES "x86_64")       
		set_target_properties(rsmsg PROPERTIES
                	IMPORTED_IMPLIB_RELEASE ${THIRD_PARTY_DIR}/librsmsg/lib/linux/x86/librsmsg.so
                	IMPORTED_LOCATION_RELEASE ${THIRD_PARTY_DIR}/librsmsg/lib/linux/x86/librsmsg.so
        	)
	else()
		set_target_properties(rsmsg PROPERTIES
                        IMPORTED_IMPLIB_RELEASE ${THIRD_PARTY_DIR}/librsmsg/lib/linux/arm/librsmsg.so
                        IMPORTED_LOCATION_RELEASE ${THIRD_PARTY_DIR}/librsmsg/lib/linux/arm/librsmsg.so
                ) 
	endif()	

        if(CMAKE_SYSTEM_PROCESSOR MATCHES "x86_64")
                set(FFMPEG_LIB_DIR "/usr/lib/x86_64-linux-gnu") 
                set(FFMPEG_LIBS  ${FFMPEG_LIB_DIR}/libavcodec.so ${FFMPEG_LIB_DIR}/libavformat.so ${FFMPEG_LIB_DIR}/libavutil.so ${FFMPEG_LIB_DIR}/libswscale.so)
	else()
		set(FFMPEG_LIB_DIR "/usr/lib/aarch64-linux-gnu")
                set(FFMPEG_LIBS  ${FFMPEG_LIB_DIR}/libavcodec.so ${FFMPEG_LIB_DIR}/libavformat.so ${FFMPEG_LIB_DIR}/libavutil.so ${FFMPEG_LIB_DIR}/libswscale.so)	
        endif (CMAKE_SYSTEM_PROCESSOR MATCHES "x86_64") 
        message("System Type: Linux")
elseif(APPLE)
        set(MAC_PLATFORM_DIR "")
        if(CMAKE_HOST_SYSTEM_PROCESSOR MATCHES "arm64")
                message(STATUS "Building on Apple Silicon")
                set(MAC_PLATFORM_DIR "mac_arm64") 
        else()
                message(STATUS "Building on Intel")
                set(MAC_PLATFORM_DIR "mac")
        endif()

        add_library(rsbag SHARED IMPORTED GLOBAL)
        set_target_properties(rsbag PROPERTIES
                INTERFACE_INCLUDE_DIRECTORIES ${THIRD_PARTY_DIR}/librsbag/include)     
        set_target_properties(rsbag PROPERTIES
                IMPORTED_IMPLIB_RELEASE ${THIRD_PARTY_DIR}/librsbag/lib/${MAC_PLATFORM_DIR}/librsbag.dylib
                IMPORTED_LOCATION_RELEASE ${THIRD_PARTY_DIR}/librsbag/lib/${MAC_PLATFORM_DIR}/librsbag.dylib
                ) 
        
        add_library(rsros SHARED IMPORTED GLOBAL)
        set_target_properties(rsros PROPERTIES
                INTERFACE_INCLUDE_DIRECTORIES ${THIRD_PARTY_DIR}/librsros/include)     
        set_target_properties(rsros PROPERTIES
                IMPORTED_IMPLIB_RELEASE ${THIRD_PARTY_DIR}/librsros/lib/${MAC_PLATFORM_DIR}/librsros.dylib
                IMPORTED_LOCATION_RELEASE ${THIRD_PARTY_DIR}/librsros/lib/${MAC_PLATFORM_DIR}/librsros.dylib
                ) 
                        
        add_library(rsmsg SHARED IMPORTED GLOBAL)
        set_target_properties(rsmsg PROPERTIES
                INTERFACE_INCLUDE_DIRECTORIES ${THIRD_PARTY_DIR}/librsmsg/include)     
        set_target_properties(rsmsg PROPERTIES
                IMPORTED_IMPLIB_RELEASE ${THIRD_PARTY_DIR}/librsmsg/lib/${MAC_PLATFORM_DIR}/librsmsg.dylib
                IMPORTED_LOCATION_RELEASE ${THIRD_PARTY_DIR}/librsmsg/lib/${MAC_PLATFORM_DIR}/librsmsg.dylib
        )

        set(FFMPEG_LIB_DIR "/opt/homebrew/lib") 
        set(FFMPEG_LIBS  ${FFMPEG_LIB_DIR}/libavcodec.dylib ${FFMPEG_LIB_DIR}/libavformat.dylib ${FFMPEG_LIB_DIR}/libavutil.dylib ${FFMPEG_LIB_DIR}/libswscale.dylib)

        message("System Type: Apple")
else()
        message(FATAL_ERROR "Not Support System Type")
endif()


if(WIN32)
        if (DEFINED ENV{VCPKG_ROOT})
                message(STATUS "VCPKG_ROOT: $ENV{VCPKG_ROOT}")
                list(APPEND CMAKE_PREFIX_PATH "$ENV{VCPKG_ROOT}/installed/x64-windows/share")
        else()
                message(FATAL_ERROR "NO VCPKG_ROOT, Please manually add the missing dependency libraries, or install and configure the dependencies via vcpkg.")
        endif()
endif()

#========================
# eigen part
#========================

find_package(Eigen3 REQUIRED)

#========================
# glog part
#========================

if (USE_SRC)
    add_subdirectory(${CMAKE_CURRENT_SOURCE_DIR}/third_party/glog_src)
    set(GLOG_LIBRARY glog)
else()
    find_package(Glog REQUIRED)
endif ()

##========================
## sophus part
##========================

if (USE_SRC)
    add_subdirectory(${CMAKE_CURRENT_SOURCE_DIR}/third_party/sophus_src)
    set(SOPHUS_LIBRARY Sophus)
else()
    find_package(Sophus REQUIRED)
endif ()

##========================
## Ceres part
##========================

if (USE_SRC)
    add_subdirectory(${CMAKE_CURRENT_SOURCE_DIR}/third_party/ceres_src)
    set(CERES_LIBRARY ceres)
else()
    find_package(Ceres REQUIRED)
endif ()

#========================
# yaml-cpp part
#========================

find_package(yaml-cpp REQUIRED)

#========================
# PCL part
#========================

if(APPLE)
      find_package(JSONCPP REQUIRED)
endif()

find_package(PCL REQUIRED COMPONENTS common io filters features)

#========================
# OpenCV part
#========================
find_package(OpenCV REQUIRED)

#========================
# GTSAM part
#========================
find_package(Boost 1.65 COMPONENTS serialization;system;filesystem;thread;program_options;date_time;timer;chrono;regex)
find_package(GtSam4 REQUIRED)

#========================
# libusb 
#========================
include_directories(${THIRD_PARTY_DIR}/libusb) 
include_directories(${THIRD_PARTY_DIR}/libusb/libusb) 
add_subdirectory(third_party/libusb)

#
# libuvc 
#
include_directories(${THIRD_PARTY_DIR}/libuvc) 
include_directories(${THIRD_PARTY_DIR}/libuvc/include) 
add_subdirectory(third_party/libuvc)

add_definitions(-DENABLE_USB)
add_definitions(-DDISABLE_PCAP_PARSE)

#
# nlohmann_json
#
find_package(nlohmann_json REQUIRED)

# Protobuf
if(WIN32)
    find_package(protobuf CONFIG REQUIRED)
    add_definitions(-DENABLE_PROTOBUF_COMPRESS=0)
    message("Windows System Disable Protobuf Compress !") 
else() 
    find_package       (Protobuf REQUIRED) 
    include_directories(${PROTOBUF_INCLUDE_DIRS}) 
    link_directories   (${PROTOBUF_LIBRARY}) 
    add_definitions(-DENABLE_PROTOBUF_COMPRESS=1)
    message("Linux/Mac System Enable Protobuf Compress !")
endif() 

#find Boost 
find_package        (Boost REQUIRED) 
include_directories (${Boost_INCLUDE_DIRS})
link_directories    (${Boost_LIBRARY_DIRS})

#find OpenSSL 
find_package(OpenSSL REQUIRED)

# ========================
#  OpenMp part
# ========================
message("Current CPU archtecture: ${CMAKE_SYSTEM_PROCESSOR}")
if(CMAKE_SYSTEM_PROCESSOR MATCHES "(x86)|(X86)|(amd64)|(AMD64)" )
    include(ProcessorCount)
    ProcessorCount(N)
    message("Processer number:  ${N}")
    if(N GREATER 9)
        add_definitions(-DMP_EN)
        add_definitions(-DMP_PROC_NUM=8)
        message("core for MP:  ${PROC_NUM}/${N}")
    elseif(N GREATER 7)
        add_definitions(-DMP_EN)
        add_definitions(-DMP_PROC_NUM=6)
        message("core for MP:  ${PROC_NUM}/${N}")
    elseif(N GREATER 5)
        add_definitions(-DMP_EN)
        add_definitions(-DMP_PROC_NUM=4)
        message("core for MP:  4")
    elseif(N GREATER 3)
        math(EXPR PROC_NUM "${N} - 2")
        add_definitions(-DMP_EN)
        add_definitions(-DMP_PROC_NUM="${PROC_NUM}")
        message("core for MP:  ${PROC_NUM}")
    else()
        add_definitions(-DMP_EN)
        add_definitions(-DMP_PROC_NUM=4)
    endif()
else()
    add_definitions(-DMP_EN)
    add_definitions(-DMP_PROC_NUM=4)
endif()

find_package(OpenMP QUIET)
if(WIN32)
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${OpenMP_CXX_FLAGS}")
        set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS}   ${OpenMP_C_FLAGS}")
elseif(UNIX AND NOT APPLE)
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${OpenMP_CXX_FLAGS}")
        set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS}   ${OpenMP_C_FLAGS}")
elseif(APPLE)
        message("System Type: Apple, OpenMP_CXX_FLAGS = ${OpenMP_CXX_FLAGS}")
        message("System Type: Apple, OpenMP_C_FLAGS = ${OpenMP_C_FLAGS}")

        if(CMAKE_HOST_SYSTEM_PROCESSOR MATCHES "arm64")
                set(OMP_INCLUDE_DIR "/opt/homebrew/opt/libomp/include")
        endif()
        find_library(OMP_LIBRARY omp PATHS /usr/local/opt/libomp/lib /opt/homebrew/opt/libomp/lib)
endif()

#========================
# CUDA 
#========================
set(enable_use_cuda_gpu false)
if(enable_use_cuda_gpu)
    find_package(CUDA QUIET)
    if(CUDA_FOUND)
        add_definitions(-DENABLE_USE_CUDA)
        message(STATUS "CUDA found - version: ${CUDA_VERSION}")
        include_directories(${CUDA_INCLUDE_DIRS})
        if(WIN32)

        elseif(UNIX AND NOT APPLE)
            add_library(gpujpeg SHARED IMPORTED GLOBAL)
            set_target_properties(gpujpeg PROPERTIES
                INTERFACE_INCLUDE_DIRECTORIES ${THIRD_PARTY_DIR}/libgpujpeg/include)     
            set_target_properties(gpujpeg PROPERTIES
                IMPORTED_IMPLIB_RELEASE ${THIRD_PARTY_DIR}/libgpujpeg/lib/linux/libgpujpeg.so.0
                IMPORTED_LOCATION_RELEASE ${THIRD_PARTY_DIR}/libgpujpeg/lib/linux/libgpujpeg.so.0
                ) 
        elseif(APPLE)

        else() 

        endif()
    endif(CUDA_FOUND)  
else() 
     message("disable gpu !")
endif(enable_use_cuda_gpu) 

#===================================
# ROS2 
#=================================== 
set(ENABLE_ROS2 false)
set(ROS2_LIBS "")
if(ENABLE_ROS2)
   message("Enable Ros2 !")
   add_definitions(-DROS2_FOUND)


   set(CUR_SUB_DIR ${THIRD_PARTY_DIR}/libros2)
   if(WIN32)
        link_directories(${THIRD_PARTY_DIR}/libros2_windows/lib/)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/ament_index_cpp)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/ament_index_cpp.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/builtin_interfaces)     
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/builtin_interfaces__rosidl_generator_c.lib)  
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/builtin_interfaces__rosidl_generator_py.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/builtin_interfaces__rosidl_typesupport_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/builtin_interfaces__rosidl_typesupport_cpp.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/builtin_interfaces__rosidl_typesupport_fastrtps_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/builtin_interfaces__rosidl_typesupport_fastrtps_cpp.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/builtin_interfaces__rosidl_typesupport_introspection_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/builtin_interfaces__rosidl_typesupport_introspection_cpp.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/class_loader)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/class_loader.lib)
        # include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/console_bridge_vendor)
        # list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/console_bridge.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/fastcdr)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/fastcdr-1.0.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/fastrtps)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/fastrtps-2.6.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/geometry_msgs)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/geometry_msgs__rosidl_generator_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/geometry_msgs__rosidl_generator_py.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/geometry_msgs__rosidl_typesupport_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/geometry_msgs__rosidl_typesupport_cpp.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/geometry_msgs__rosidl_typesupport_fastrtps_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/geometry_msgs__rosidl_typesupport_fastrtps_cpp.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/geometry_msgs__rosidl_typesupport_introspection_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/geometry_msgs__rosidl_typesupport_introspection_cpp.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/hyper_vision_msgs)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/hyper_vision_msgs__rosidl_generator_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/hyper_vision_msgs__rosidl_generator_py.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/hyper_vision_msgs__rosidl_typesupport_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/hyper_vision_msgs__rosidl_typesupport_cpp.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/hyper_vision_msgs__rosidl_typesupport_fastrtps_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/hyper_vision_msgs__rosidl_typesupport_fastrtps_cpp.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/hyper_vision_msgs__rosidl_typesupport_introspection_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/hyper_vision_msgs__rosidl_typesupport_introspection_cpp.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/keyboard_handler)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/keyboard_handler.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/libstatistics_collector)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/libstatistics_collector.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/message_filters)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/message_filters.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/rcl)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rcl.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/rcl_interfaces)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rcl_interfaces__rosidl_generator_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rcl_interfaces__rosidl_generator_py.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rcl_interfaces__rosidl_typesupport_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rcl_interfaces__rosidl_typesupport_cpp.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rcl_interfaces__rosidl_typesupport_fastrtps_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rcl_interfaces__rosidl_typesupport_fastrtps_cpp.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rcl_interfaces__rosidl_typesupport_introspection_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rcl_interfaces__rosidl_typesupport_introspection_cpp.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/rcl_logging_interface)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rcl_logging_interface.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/rcl_logging_spdlog)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rcl_logging_spdlog.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/rcl_yaml_param_parser)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rcl_yaml_param_parser.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/rclcpp)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rclcpp.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/rcpputils)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rcpputils.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/rcutils)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rcutils.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/rmw)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rmw.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/rmw_dds_common)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rmw_dds_common__rosidl_generator_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rmw_dds_common__rosidl_generator_py.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rmw_dds_common__rosidl_typesupport_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rmw_dds_common__rosidl_typesupport_cpp.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rmw_dds_common__rosidl_typesupport_fastrtps_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rmw_dds_common__rosidl_typesupport_fastrtps_cpp.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rmw_dds_common__rosidl_typesupport_introspection_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rmw_dds_common__rosidl_typesupport_introspection_cpp.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/rmw_fastrtps_cpp)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rmw_fastrtps_cpp.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/rmw_fastrtps_shared_cpp)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rmw_fastrtps_shared_cpp.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/rmw_implementation)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rmw_implementation.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/rosbag2_compression)
        #list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/fake_plugin.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rosbag2_compression.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/rosbag2_compression_zstd)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rosbag2_compression_zstd.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/rosbag2_cpp)
        #list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/converter_test_plugins.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rosbag2_cpp.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/rosbag2_interfaces)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rosbag2_interfaces__rosidl_generator_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rosbag2_interfaces__rosidl_generator_py.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rosbag2_interfaces__rosidl_typesupport_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rosbag2_interfaces__rosidl_typesupport_cpp.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rosbag2_interfaces__rosidl_typesupport_fastrtps_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rosbag2_interfaces__rosidl_typesupport_fastrtps_cpp.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rosbag2_interfaces__rosidl_typesupport_introspection_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rosbag2_interfaces__rosidl_typesupport_introspection_cpp.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/rosbag2_storage)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rosbag2_storage.lib)
        #list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/test_plugin.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/rosbag2_storage_default_plugins)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rosbag2_storage_default_plugins.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/rosbag2_storage_mcap)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rosbag2_storage_mcap.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/rosbag2_transport)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rosbag2_transport.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/rosgraph_msgs)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rosgraph_msgs__rosidl_generator_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rosgraph_msgs__rosidl_generator_py.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rosgraph_msgs__rosidl_typesupport_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rosgraph_msgs__rosidl_typesupport_cpp.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rosgraph_msgs__rosidl_typesupport_fastrtps_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rosgraph_msgs__rosidl_typesupport_fastrtps_cpp.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rosgraph_msgs__rosidl_typesupport_introspection_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rosgraph_msgs__rosidl_typesupport_introspection_cpp.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/rosidl_runtime_c)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rosidl_runtime_c.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/rosidl_runtime_cpp)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/rosidl_typesupport_c)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rosidl_typesupport_c.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/rosidl_typesupport_cpp)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rosidl_typesupport_cpp.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/rosidl_typesupport_fastrtps_c)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rosidl_typesupport_fastrtps_c.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/rosidl_typesupport_fastrtps_cpp)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rosidl_typesupport_fastrtps_cpp.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/rosidl_typesupport_interface)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/rosidl_typesupport_introspection_c)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rosidl_typesupport_introspection_c.lib)

        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/rosidl_typesupport_introspection_cpp)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rosidl_typesupport_introspection_cpp.lib)
        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/rscamera_msg)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rscamera_msg__rosidl_generator_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rscamera_msg__rosidl_generator_py.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rscamera_msg__rosidl_typesupport_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rscamera_msg__rosidl_typesupport_cpp.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rscamera_msg__rosidl_typesupport_fastrtps_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rscamera_msg__rosidl_typesupport_fastrtps_cpp.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rscamera_msg__rosidl_typesupport_introspection_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/rscamera_msg__rosidl_typesupport_introspection_cpp.lib)

        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/sensor_msgs)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/sensor_msgs__rosidl_generator_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/sensor_msgs__rosidl_generator_py.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/sensor_msgs__rosidl_typesupport_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/sensor_msgs__rosidl_typesupport_cpp.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/sensor_msgs__rosidl_typesupport_fastrtps_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/sensor_msgs__rosidl_typesupport_fastrtps_cpp.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/sensor_msgs__rosidl_typesupport_introspection_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/sensor_msgs__rosidl_typesupport_introspection_cpp.lib)

        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/statistics_msgs)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/statistics_msgs__rosidl_generator_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/statistics_msgs__rosidl_generator_py.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/statistics_msgs__rosidl_typesupport_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/statistics_msgs__rosidl_typesupport_cpp.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/statistics_msgs__rosidl_typesupport_fastrtps_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/statistics_msgs__rosidl_typesupport_fastrtps_cpp.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/statistics_msgs__rosidl_typesupport_introspection_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/statistics_msgs__rosidl_typesupport_introspection_cpp.lib)

        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/std_msgs)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/std_msgs__rosidl_generator_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/std_msgs__rosidl_generator_py.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/std_msgs__rosidl_typesupport_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/std_msgs__rosidl_typesupport_cpp.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/std_msgs__rosidl_typesupport_fastrtps_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/std_msgs__rosidl_typesupport_fastrtps_cpp.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/std_msgs__rosidl_typesupport_introspection_c.lib)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/std_msgs__rosidl_typesupport_introspection_cpp.lib)

        include_directories(${THIRD_PARTY_DIR}/libros2_windows/include/tracetools)
        #list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/tracetools_status.lib)
        #list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2_windows/lib/tracetools.lib)

        # 可能需要添加Windows特定的系统库
        list(APPEND ROS2_LIBS
        ws2_32.lib  # 网络库
        shell32.lib # Shell API
        rpcrt4.lib  # RPC运行时
        )
   elseif(UNIX AND NOT APPLE)
        # foreach (dir ${CUR_SUB_DIR})
        #         file(GLOB_RECURSE tmp_srcs ${dir}/*.so)
        #         list(APPEND ROS2_LIBS ${tmp_srcs})
        # endforeach ()
        include_directories(${THIRD_PARTY_DIR}/libros2/ament_index_cpp/include/ament_index_cpp)
        link_directories(${THIRD_PARTY_DIR}/libros2/ament_index_cpp/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/ament_index_cpp/lib/libament_index_cpp.so)

        include_directories(${THIRD_PARTY_DIR}/libros2/builtin_interfaces/include/builtin_interfaces)
        link_directories(${THIRD_PARTY_DIR}/libros2/builtin_interfaces/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/builtin_interfaces/lib/libbuiltin_interfaces__rosidl_generator_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/builtin_interfaces/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/builtin_interfaces/lib/libbuiltin_interfaces__rosidl_generator_py.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/builtin_interfaces/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/builtin_interfaces/lib/libbuiltin_interfaces__rosidl_typesupport_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/builtin_interfaces/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/builtin_interfaces/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/builtin_interfaces/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/builtin_interfaces/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/builtin_interfaces/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/builtin_interfaces/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/builtin_interfaces/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/builtin_interfaces/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/builtin_interfaces/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/builtin_interfaces/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so)

        include_directories(${THIRD_PARTY_DIR}/libros2/class_loader/include/class_loader)
        link_directories(${THIRD_PARTY_DIR}/libros2/class_loader/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/class_loader/lib/libclass_loader.so)

        include_directories(${THIRD_PARTY_DIR}/libros2/console_bridge_vendor/include/console_bridge_vendor)
        link_directories(${THIRD_PARTY_DIR}/libros2/console_bridge_vendor/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/console_bridge_vendor/lib/libconsole_bridge.so.1.0)

        include_directories(${THIRD_PARTY_DIR}/libros2/fastcdr/include/fastcdr)
        link_directories(${THIRD_PARTY_DIR}/libros2/fastcdr/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/fastcdr/lib/libfastcdr.so.1.0.24)

        include_directories(${THIRD_PARTY_DIR}/libros2/fastrtps/include/fastrtps)
        link_directories(${THIRD_PARTY_DIR}/libros2/fastrtps/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/fastrtps/lib/libfastrtps.so.2.6.9)
      
        include_directories(${THIRD_PARTY_DIR}/libros2/geometry_msgs/include/geometry_msgs)
        link_directories(${THIRD_PARTY_DIR}/libros2/geometry_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/geometry_msgs/lib/libgeometry_msgs__rosidl_generator_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/geometry_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/geometry_msgs/lib/libgeometry_msgs__rosidl_generator_py.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/geometry_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/geometry_msgs/lib/libgeometry_msgs__rosidl_typesupport_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/geometry_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/geometry_msgs/lib/libgeometry_msgs__rosidl_typesupport_cpp.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/geometry_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/geometry_msgs/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/geometry_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/geometry_msgs/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_cpp.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/geometry_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/geometry_msgs/lib/libgeometry_msgs__rosidl_typesupport_introspection_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/geometry_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/geometry_msgs/lib/libgeometry_msgs__rosidl_typesupport_introspection_cpp.so)
        
        include_directories(${THIRD_PARTY_DIR}/libros2/hyper_vision_msgs/include/hyper_vision_msgs)
        link_directories(${THIRD_PARTY_DIR}/libros2/hyper_vision_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/hyper_vision_msgs/lib/libhyper_vision_msgs__rosidl_generator_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/hyper_vision_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/hyper_vision_msgs/lib/libhyper_vision_msgs__rosidl_generator_py.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/hyper_vision_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/hyper_vision_msgs/lib/libhyper_vision_msgs__rosidl_typesupport_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/hyper_vision_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/hyper_vision_msgs/lib/libhyper_vision_msgs__rosidl_typesupport_cpp.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/hyper_vision_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/hyper_vision_msgs/lib/libhyper_vision_msgs__rosidl_typesupport_fastrtps_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/hyper_vision_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/hyper_vision_msgs/lib/libhyper_vision_msgs__rosidl_typesupport_fastrtps_cpp.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/hyper_vision_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/hyper_vision_msgs/lib/libhyper_vision_msgs__rosidl_typesupport_introspection_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/hyper_vision_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/hyper_vision_msgs/lib/libhyper_vision_msgs__rosidl_typesupport_introspection_cpp.so)


        include_directories(${THIRD_PARTY_DIR}/libros2/keyboard_handler/include/keyboard_handler)
        link_directories(${THIRD_PARTY_DIR}/libros2/keyboard_handler/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/keyboard_handler/lib/libkeyboard_handler.so)

        include_directories(${THIRD_PARTY_DIR}/libros2/libstatistics_collector/include/libstatistics_collector)
        link_directories(${THIRD_PARTY_DIR}/libros2/libstatistics_collector/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/libstatistics_collector/lib/liblibstatistics_collector.so)

        include_directories(${THIRD_PARTY_DIR}/libros2/message_filters/include/message_filters)
        link_directories(${THIRD_PARTY_DIR}/libros2/message_filters/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/message_filters/lib/libmessage_filters.so)

        # pcl_conversions2
        include_directories(${THIRD_PARTY_DIR}/libros2)

        include_directories(${THIRD_PARTY_DIR}/libros2/rcl/include/rcl)
        link_directories(${THIRD_PARTY_DIR}/libros2/rcl/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rcl/lib/librcl.so)

        include_directories(${THIRD_PARTY_DIR}/libros2/rcl_interfaces/include/rcl_interfaces)
        link_directories(${THIRD_PARTY_DIR}/libros2/rcl_interfaces/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rcl_interfaces/lib/librcl_interfaces__rosidl_generator_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rcl_interfaces/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rcl_interfaces/lib/librcl_interfaces__rosidl_generator_py.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rcl_interfaces/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rcl_interfaces/lib/librcl_interfaces__rosidl_typesupport_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rcl_interfaces/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rcl_interfaces/lib/librcl_interfaces__rosidl_typesupport_cpp.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rcl_interfaces/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rcl_interfaces/lib/librcl_interfaces__rosidl_typesupport_fastrtps_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rcl_interfaces/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rcl_interfaces/lib/librcl_interfaces__rosidl_typesupport_fastrtps_cpp.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rcl_interfaces/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rcl_interfaces/lib/librcl_interfaces__rosidl_typesupport_introspection_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rcl_interfaces/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rcl_interfaces/lib/librcl_interfaces__rosidl_typesupport_introspection_cpp.so)

        include_directories(${THIRD_PARTY_DIR}/libros2/rcl_logging_interface/include/rcl_logging_interface)
        link_directories(${THIRD_PARTY_DIR}/libros2/rcl_logging_interface/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rcl_logging_interface/lib/librcl_logging_interface.so)

        include_directories(${THIRD_PARTY_DIR}/libros2/rcl_logging_spdlog/include/rcl_logging_spdlog)
        link_directories(${THIRD_PARTY_DIR}/libros2/rcl_logging_spdlog/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rcl_logging_spdlog/lib/librcl_logging_spdlog.so)

        include_directories(${THIRD_PARTY_DIR}/libros2/rcl_yaml_param_parser/include/rcl_yaml_param_parser)
        link_directories(${THIRD_PARTY_DIR}/libros2/rcl_yaml_param_parser/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rcl_yaml_param_parser/lib/librcl_yaml_param_parser.so)

        include_directories(${THIRD_PARTY_DIR}/libros2/rclcpp/include/rclcpp)
        link_directories(${THIRD_PARTY_DIR}/libros2/rclcpp/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rclcpp/lib/librclcpp.so)

        include_directories(${THIRD_PARTY_DIR}/libros2/rcpputils/include/rcpputils)
        link_directories(${THIRD_PARTY_DIR}/libros2/rcpputils/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rcpputils/lib/librcpputils.so)

        include_directories(${THIRD_PARTY_DIR}/libros2/rcutils/include/rcutils)
        link_directories(${THIRD_PARTY_DIR}/libros2/rcutils/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rcutils/lib/librcutils.so)

        include_directories(${THIRD_PARTY_DIR}/libros2/rmw/include/rmw)
        link_directories(${THIRD_PARTY_DIR}/libros2/rmw/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rmw/lib/librmw.so)

        include_directories(${THIRD_PARTY_DIR}/libros2/rmw_dds_common/include/rmw_dds_common)
        link_directories(${THIRD_PARTY_DIR}/libros2/rmw_dds_common/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rmw_dds_common/lib/librmw_dds_common__rosidl_generator_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rmw_dds_common/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rmw_dds_common/lib/librmw_dds_common__rosidl_generator_py.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rmw_dds_common/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rmw_dds_common/lib/librmw_dds_common__rosidl_typesupport_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rmw_dds_common/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rmw_dds_common/lib/librmw_dds_common__rosidl_typesupport_cpp.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rmw_dds_common/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rmw_dds_common/lib/librmw_dds_common__rosidl_typesupport_fastrtps_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rmw_dds_common/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rmw_dds_common/lib/librmw_dds_common__rosidl_typesupport_fastrtps_cpp.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rmw_dds_common/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rmw_dds_common/lib/librmw_dds_common__rosidl_typesupport_introspection_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rmw_dds_common/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rmw_dds_common/lib/librmw_dds_common__rosidl_typesupport_introspection_cpp.so)


        include_directories(${THIRD_PARTY_DIR}/libros2/rmw_fastrtps_cpp/include/rmw_fastrtps_cpp)
        link_directories(${THIRD_PARTY_DIR}/libros2/rmw_fastrtps_cpp/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rmw_fastrtps_cpp/lib/librmw_fastrtps_cpp.so)

        include_directories(${THIRD_PARTY_DIR}/libros2/rmw_fastrtps_shared_cpp/include/rmw_fastrtps_shared_cpp)
        link_directories(${THIRD_PARTY_DIR}/libros2/rmw_fastrtps_shared_cpp/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rmw_fastrtps_shared_cpp/lib/librmw_fastrtps_shared_cpp.so)

        include_directories(${THIRD_PARTY_DIR}/libros2/rmw_implementation/include/rmw_implementation)
        link_directories(${THIRD_PARTY_DIR}/libros2/rmw_implementation/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rmw_implementation/lib/librmw_implementation.so)

        include_directories(${THIRD_PARTY_DIR}/libros2/rosbag2_compression/include/rosbag2_compression)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosbag2_compression/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosbag2_compression/lib/libfake_plugin.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosbag2_compression/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosbag2_compression/lib/librosbag2_compression.so)

        include_directories(${THIRD_PARTY_DIR}/libros2/rosbag2_compression_zstd/include/rosbag2_compression_zstd)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosbag2_compression_zstd/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosbag2_compression_zstd/lib/librosbag2_compression_zstd.so)

        include_directories(${THIRD_PARTY_DIR}/libros2/rosbag2_cpp/include/rosbag2_cpp)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosbag2_cpp/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosbag2_cpp/lib/libconverter_test_plugins.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosbag2_cpp/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosbag2_cpp/lib/librosbag2_cpp.so)

        include_directories(${THIRD_PARTY_DIR}/libros2/rosbag2_interfaces/include/rosbag2_interfaces)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosbag2_interfaces/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosbag2_interfaces/lib/librosbag2_interfaces__rosidl_generator_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosbag2_interfaces/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosbag2_interfaces/lib/librosbag2_interfaces__rosidl_generator_py.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosbag2_interfaces/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosbag2_interfaces/lib/librosbag2_interfaces__rosidl_typesupport_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosbag2_interfaces/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosbag2_interfaces/lib/librosbag2_interfaces__rosidl_typesupport_cpp.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosbag2_interfaces/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosbag2_interfaces/lib/librosbag2_interfaces__rosidl_typesupport_fastrtps_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosbag2_interfaces/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosbag2_interfaces/lib/librosbag2_interfaces__rosidl_typesupport_fastrtps_cpp.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosbag2_interfaces/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosbag2_interfaces/lib/librosbag2_interfaces__rosidl_typesupport_introspection_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosbag2_interfaces/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosbag2_interfaces/lib/librosbag2_interfaces__rosidl_typesupport_introspection_cpp.so)


        include_directories(${THIRD_PARTY_DIR}/libros2/rosbag2_storage/include/rosbag2_storage)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosbag2_storage/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosbag2_storage/lib/librosbag2_storage.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosbag2_storage/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosbag2_storage/lib/libtest_plugin.so)

        include_directories(${THIRD_PARTY_DIR}/libros2/rosbag2_storage_default_plugins/include/rosbag2_storage_default_plugins)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosbag2_storage_default_plugins/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosbag2_storage_default_plugins/lib/librosbag2_storage_default_plugins.so)

        include_directories(${THIRD_PARTY_DIR}/libros2/rosbag2_storage_mcap/include/rosbag2_storage_mcap)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosbag2_storage_mcap/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosbag2_storage_mcap/lib/librosbag2_storage_mcap.so)

        include_directories(${THIRD_PARTY_DIR}/libros2/rosbag2_transport/include/rosbag2_transport)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosbag2_transport/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosbag2_transport/lib/librosbag2_transport.so)

        include_directories(${THIRD_PARTY_DIR}/libros2/rosgraph_msgs/include/rosgraph_msgs)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosgraph_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosgraph_msgs/lib/librosgraph_msgs__rosidl_generator_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosgraph_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosgraph_msgs/lib/librosgraph_msgs__rosidl_generator_py.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosgraph_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosgraph_msgs/lib/librosgraph_msgs__rosidl_typesupport_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosgraph_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosgraph_msgs/lib/librosgraph_msgs__rosidl_typesupport_cpp.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosgraph_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosgraph_msgs/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosgraph_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosgraph_msgs/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_cpp.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosgraph_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosgraph_msgs/lib/librosgraph_msgs__rosidl_typesupport_introspection_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosgraph_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosgraph_msgs/lib/librosgraph_msgs__rosidl_typesupport_introspection_cpp.so)

        include_directories(${THIRD_PARTY_DIR}/libros2/rosidl_runtime_c/include/rosidl_runtime_c)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosidl_runtime_c/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosidl_runtime_c/lib/librosidl_runtime_c.so)

        include_directories(${THIRD_PARTY_DIR}/libros2/rosidl_runtime_cpp/include/rosidl_runtime_cpp)

        include_directories(${THIRD_PARTY_DIR}/libros2/rosidl_typesupport_c/include/rosidl_typesupport_c)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosidl_typesupport_c/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosidl_typesupport_c/lib/librosidl_typesupport_c.so)
        
        include_directories(${THIRD_PARTY_DIR}/libros2/rosidl_typesupport_cpp/include/rosidl_typesupport_cpp)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosidl_typesupport_cpp/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosidl_typesupport_cpp/lib/librosidl_typesupport_cpp.so)

        include_directories(${THIRD_PARTY_DIR}/libros2/rosidl_typesupport_fastrtps_c/include/rosidl_typesupport_fastrtps_c)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosidl_typesupport_fastrtps_c/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosidl_typesupport_fastrtps_c/lib/librosidl_typesupport_fastrtps_c.so)

        include_directories(${THIRD_PARTY_DIR}/libros2/rosidl_typesupport_fastrtps_cpp/include/rosidl_typesupport_fastrtps_cpp)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosidl_typesupport_fastrtps_cpp/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosidl_typesupport_fastrtps_cpp/lib/librosidl_typesupport_fastrtps_cpp.so)

        include_directories(${THIRD_PARTY_DIR}/libros2/rosidl_typesupport_interface/include/rosidl_typesupport_interface)

        include_directories(${THIRD_PARTY_DIR}/libros2/rosidl_typesupport_introspection_c/include/rosidl_typesupport_introspection_c)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosidl_typesupport_introspection_c/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosidl_typesupport_introspection_c/lib/librosidl_typesupport_introspection_c.so)

        include_directories(${THIRD_PARTY_DIR}/libros2/rosidl_typesupport_introspection_cpp/include/rosidl_typesupport_introspection_cpp)
        link_directories(${THIRD_PARTY_DIR}/libros2/rosidl_typesupport_introspection_cpp/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rosidl_typesupport_introspection_cpp/lib/librosidl_typesupport_introspection_cpp.so)
        
        include_directories(${THIRD_PARTY_DIR}/libros2/rscamera_msg/include/rscamera_msg)
        link_directories(${THIRD_PARTY_DIR}/libros2/rscamera_msg/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rscamera_msg/lib/librscamera_msg__rosidl_generator_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rscamera_msg/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rscamera_msg/lib/librscamera_msg__rosidl_generator_py.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rscamera_msg/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rscamera_msg/lib/librscamera_msg__rosidl_typesupport_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rscamera_msg/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rscamera_msg/lib/librscamera_msg__rosidl_typesupport_cpp.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rscamera_msg/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rscamera_msg/lib/librscamera_msg__rosidl_typesupport_fastrtps_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rscamera_msg/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rscamera_msg/lib/librscamera_msg__rosidl_typesupport_fastrtps_cpp.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rscamera_msg/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rscamera_msg/lib/librscamera_msg__rosidl_typesupport_introspection_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/rscamera_msg/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/rscamera_msg/lib/librscamera_msg__rosidl_typesupport_introspection_cpp.so)

        include_directories(${THIRD_PARTY_DIR}/libros2/sensor_msgs/include/sensor_msgs)
        link_directories(${THIRD_PARTY_DIR}/libros2/sensor_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/sensor_msgs/lib/libsensor_msgs__rosidl_generator_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/sensor_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/sensor_msgs/lib/libsensor_msgs__rosidl_generator_py.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/sensor_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/sensor_msgs/lib/libsensor_msgs__rosidl_typesupport_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/sensor_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/sensor_msgs/lib/libsensor_msgs__rosidl_typesupport_cpp.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/sensor_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/sensor_msgs/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/sensor_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/sensor_msgs/lib/libsensor_msgs__rosidl_typesupport_fastrtps_cpp.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/sensor_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/sensor_msgs/lib/libsensor_msgs__rosidl_typesupport_introspection_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/sensor_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/sensor_msgs/lib/libsensor_msgs__rosidl_typesupport_introspection_cpp.so)

        include_directories(${THIRD_PARTY_DIR}/libros2/statistics_msgs/include/statistics_msgs)
        link_directories(${THIRD_PARTY_DIR}/libros2/statistics_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/statistics_msgs/lib/libstatistics_msgs__rosidl_generator_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/statistics_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/statistics_msgs/lib/libstatistics_msgs__rosidl_generator_py.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/statistics_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/statistics_msgs/lib/libstatistics_msgs__rosidl_typesupport_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/statistics_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/statistics_msgs/lib/libstatistics_msgs__rosidl_typesupport_cpp.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/statistics_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/statistics_msgs/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/statistics_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/statistics_msgs/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_cpp.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/statistics_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/statistics_msgs/lib/libstatistics_msgs__rosidl_typesupport_introspection_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/statistics_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/statistics_msgs/lib/libstatistics_msgs__rosidl_typesupport_introspection_cpp.so)


        include_directories(${THIRD_PARTY_DIR}/libros2/std_msgs/include/std_msgs)
        link_directories(${THIRD_PARTY_DIR}/libros2/std_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/std_msgs/lib/libstd_msgs__rosidl_generator_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/std_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/std_msgs/lib/libstd_msgs__rosidl_generator_py.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/std_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/std_msgs/lib/libstd_msgs__rosidl_typesupport_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/std_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/std_msgs/lib/libstd_msgs__rosidl_typesupport_cpp.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/std_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/std_msgs/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/std_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/std_msgs/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/std_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/std_msgs/lib/libstd_msgs__rosidl_typesupport_introspection_c.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/std_msgs/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/std_msgs/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so)


        include_directories(${THIRD_PARTY_DIR}/libros2/tracetools/include/tracetools)
        link_directories(${THIRD_PARTY_DIR}/libros2/tracetools/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/tracetools/lib/libtracetools_status.so)
        link_directories(${THIRD_PARTY_DIR}/libros2/tracetools/lib/)
        list(APPEND ROS2_LIBS ${THIRD_PARTY_DIR}/libros2/tracetools/lib/libtracetools.so)

   elseif(APPLE)

   else() 
        
   endif() 
   message("ROS2_LIBS = ${ROS2_LIBS}")
else() 
   message("Disble ROS2 !")
endif()

