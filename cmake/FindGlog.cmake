# FindGlog.cmake
# 查找 Google Logging (glog) 的头文件和库路径

# Set default search path for glog source (relative to project root)
set(GLOG_SEARCH_PATHS "${CMAKE_SOURCE_DIR}/third_party/glog")

# 平台特定的路径设置
if(WIN32)
    # Windows 平台
    set(GLOG_LIBRARY_NAME glog)
    set(GLOG_LIBRARY_SUFFIX .lib)
    set(GLOG_LIBRARY_PREFIX "")
    set(GLOG_INCLUDE_FIND_PATH ${GLOG_SEARCH_PATHS}/include/windows)
elseif(UNIX AND NOT APPLE)
    # Linux 平台
    set(GLOG_LIBRARY_NAME glog)
    set(GLOG_LIBRARY_SUFFIX .a)
    set(GLOG_LIBRARY_PREFIX lib)
    set(GLOG_INCLUDE_FIND_PATH ${GLOG_SEARCH_PATHS}/include/ubuntu)
elseif(APPLE)
    # macOS 平台
    set(GLOG_LIBRARY_NAME glog)
    set(GLOG_LIBRARY_SUFFIX .dylib)
    set(GLOG_LIBRARY_PREFIX lib)
    set(GLOG_INCLUDE_FIND_PATH ${GLOG_SEARCH_PATHS}/include/mac)
endif()

# Find glog include directory
find_path(GLOG_INCLUDE_DIR
        NAMES glog/logging.h
        PATHS ${GLOG_SEARCH_PATHS}/include
        NO_DEFAULT_PATH
        DOC "Glog include directory"
        )

# 查找 glog 的库文件
find_library(GLOG_LIBRARY
        NAMES ${GLOG_LIBRARY_PREFIX}${GLOG_LIBRARY_NAME}${GLOG_LIBRARY_SUFFIX}
        PATHS ${GLOG_SEARCH_PATHS}/lib
        NO_DEFAULT_PATH
        )

# 设置 Glog_FOUND
if(GLOG_INCLUDE_DIR AND GLOG_LIBRARY)
    set(Glog_FOUND TRUE)
    message(STATUS "Found Glog: ${GLOG_SEARCH_PATHS}")
else()
    set(Glog_FOUND FALSE)
    message(WARNING "Glog not found in expected paths: ${GLOG_SEARCH_PATHS}")
endif()

# 设置 Glog 的 include 目录和库文件
if(Glog_FOUND)
    set(GLOG_INCLUDE_DIRS ${GLOG_INCLUDE_DIR})
    set(GLOG_LIBRARIES ${GLOG_LIBRARY})
endif()

# 标记为高级变量
mark_as_advanced(GLOG_INCLUDE_DIR GLOG_LIBRARY)