# Set default search path for Eigen source (relative to project root)
set(SOPHUS_SEARCH_PATHS "${CMAKE_SOURCE_DIR}/third_party/sophus")

# 平台特定的路径设置
if(WIN32)
    # Windows 平台
    set(SOPHUS_LIBRARY_NAME Sophus)
    set(SOPHUS_LIBRARY_SUFFIX .lib)
    set(SOPHUS_LIBRARY_PREFIX "")
elseif(UNIX AND NOT APPLE)
    # Linux 平台
    set(SOPHUS_LIBRARY_NAME Sophus)
    set(SOPHUS_LIBRARY_SUFFIX .a)
    set(SOPHUS_LIBRARY_PREFIX lib)
elseif(APPLE)
    # macOS 平台
    set(SOPHUS_LIBRARY_NAME Sophus)
    set(SOPHUS_LIBRARY_SUFFIX .dylib)
    set(SOPHUS_LIBRARY_PREFIX lib)
endif()

# Find Sophus include directory
find_path(SOPHUS_INCLUDE_DIR
        NAMES sophus/so2.h
        PATHS ${SOPHUS_SEARCH_PATHS}/include
        NO_DEFAULT_PATH
        DOC "Sophus include directory"
        )

# 查找 Sophus 的库文件
find_library(SOPHUS_LIBRARY
        NAMES ${SOPHUS_LIBRARY_PREFIX}${SOPHUS_LIBRARY_NAME}${SOPHUS_LIBRARY_SUFFIX}
        PATHS ${SOPHUS_SEARCH_PATHS}/lib
        NO_DEFAULT_PATH
        )

# 设置 Sophus_FOUND
if(SOPHUS_INCLUDE_DIR AND SOPHUS_LIBRARY)
    set(Sophus_FOUND TRUE)
    message(STATUS "Found Sophus: ${SOPHUS_SEARCH_PATHS}")
else()
    set(Sophus_FOUND FALSE)
    message(WARNING "Sophus not found in expected paths: ${SOPHUS_SEARCH_PATHS}")
endif()

# 设置 Sophus 的 include 目录和库文件
if(SOPHUS_FOUND)
    set(SOPHUS_INCLUDE_DIRS ${SOPHUS_INCLUDE_DIR})
    set(SOPHUS_LIBRARIES ${SOPHUS_LIBRARY})
endif()

# 标记为高级变量
mark_as_advanced(SOPHUS_INCLUDE_DIR SOPHUS_LIBRARY)