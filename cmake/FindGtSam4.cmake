# FindGtSam4.cmake
# 查找 gtsam 的头文件和库路径

# Set default search path for gtsam source (relative to project root)
set(GTSAM_SEARCH_PATHS "${CMAKE_SOURCE_DIR}/third_party/gtsam")

# 平台特定的路径设置
if(WIN32)
    # # Windows 平台
    set(GTSAM_LIBRARY_NAME Gtsam)
    set(GTSAM_LIBRARY_SUFFIX .lib)
    set(GTSAM_LIBRARY_PREFIX "")
    # set(GTSAM_INCLUDE_FIND_PATH ${GTSAM_SEARCH_PATHS}/include/windows)
elseif(UNIX AND NOT APPLE)
    # Linux 平台
    set(GTSAM_LIBRARY_NAME gtsam)
    set(GTSAM_LIBRARY_SUFFIX .a)
    set(GTSAM_LIBRARY_PREFIX lib)
    # set(GTSAM_INCLUDE_FIND_PATH ${GTSAM_SEARCH_PATHS}/include/ubuntu)
elseif(APPLE)
    # # macOS 平台
    set(GTSAM_LIBRARY_NAME Gtsam)
    set(GTSAM_LIBRARY_SUFFIX .dylib)
    set(GTSAM_LIBRARY_PREFIX lib)
    # set(GTSAM_INCLUDE_FIND_PATH ${GTSAM_SEARCH_PATHS}/include/mac)
endif()

# Find gtsam include directory
find_path(GTSAM_INCLUDE_DIR
        # NAMES Gtsam/logging.h
        PATHS ${GTSAM_SEARCH_PATHS}/include
        NO_DEFAULT_PATH
        DOC "Gtsam include directory"
        )

# 查找 gtsam 的库文件
find_library(GTSAM_LIBRARIES
        NAMES ${GTSAM_LIBRARY_PREFIX}${GTSAM_LIBRARY_NAME}${GTSAM_LIBRARY_SUFFIX}
        PATHS ${GTSAM_SEARCH_PATHS}/lib
        NO_DEFAULT_PATH
        )

# 设置 GTSAM_FOUND
if(GTSAM_INCLUDE_DIR AND GTSAM_LIBRARIES)
    set(GTSAM_FOUND TRUE)
    message(STATUS "Found Gtsam: ${GTSAM_SEARCH_PATHS}")
else()
    set(GTSAM_FOUND FALSE)
    message(WARNING "Gtsam not found in expected paths: ${GTSAM_SEARCH_PATHS}")
endif()

# 设置 Gtsam 的 include 目录和库文件
if(GTSAM_FOUND)
    set(GTSAM_INCLUDE_DIR ${GTSAM_INCLUDE_DIR})
    set(GTSAM_LIBRARIES ${GTSAM_LIBRARIES})
endif()

# 标记为高级变量
mark_as_advanced(GTSAM_INCLUDE_DIR GTSAM_LIBRARIES)