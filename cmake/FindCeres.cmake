# FindCeres.cmake
# 查找 Ceres Solver 的头文件和库路径

# Set default search path for Ceres source (relative to project root)
set(CERES_SEARCH_PATHS "${CMAKE_SOURCE_DIR}/third_party/ceres")

# 平台特定的路径设置
if(WIN32)
    # Windows 平台
    set(CERES_LIBRARY_NAME ceres)
    set(CERES_LIBRARY_SUFFIX .lib)
    set(CERES_LIBRARY_PREFIX "")
elseif(UNIX AND NOT APPLE)
    # Linux 平台
    set(CERES_LIBRARY_NAME ceres)
    set(CERES_LIBRARY_SUFFIX .a)
    set(CERES_LIBRARY_PREFIX lib)
elseif(APPLE)
    # macOS 平台
    set(CERES_LIBRARY_NAME ceres)
    set(CERES_LIBRARY_SUFFIX .dylib)
    set(CERES_LIBRARY_PREFIX lib)
endif()

# Find Ceres include directory
find_path(CERES_INCLUDE_DIR
        NAMES ceres/ceres.h
        PATHS ${CERES_SEARCH_PATHS}/include
        NO_DEFAULT_PATH
        DOC "Ceres include directory"
        )

# 查找 Ceres 的库文件
find_library(CERES_LIBRARY
        NAMES ${CERES_LIBRARY_PREFIX}${CERES_LIBRARY_NAME}${CERES_LIBRARY_SUFFIX}
        PATHS ${CERES_SEARCH_PATHS}/lib
        NO_DEFAULT_PATH
        )

# 设置 Ceres_FOUND
if(CERES_INCLUDE_DIR AND CERES_LIBRARY)
    set(Ceres_FOUND TRUE)
    message(STATUS "Found Ceres: ${CERES_SEARCH_PATHS}")
else()
    set(Ceres_FOUND FALSE)
    message(WARNING "Ceres not found in expected paths: ${CERES_SEARCH_PATHS}")
endif()

# 设置 Ceres 的 include 目录和库文件
if(Ceres_FOUND)
    set(CERES_INCLUDE_DIRS ${CERES_INCLUDE_DIR})
    set(CERES_LIBRARIES ${CERES_LIBRARY})
endif()

# 标记为高级变量
mark_as_advanced(CERES_INCLUDE_DIR CERES_LIBRARY)