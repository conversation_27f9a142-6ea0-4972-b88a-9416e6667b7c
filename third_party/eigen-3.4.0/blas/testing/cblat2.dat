'cblat2.summ'     NAME OF <PERSON>UMMARY OUTPUT FILE
6                 UNIT NUMBER OF SUMMARY FILE
'cblat2.snap'     NAME OF SNAPSHOT OUTPUT FILE
-1                UNIT NUMBER OF SNAPSHOT FILE (NOT USED IF .LT. 0)
F        LOGICAL FLAG, T TO REWIND SNAPSHOT FILE AFTER EACH RECORD.
F        LOGICAL FLAG, T TO STOP ON FAILURES.
T        LOGICAL FLAG, T TO TEST ERROR EXITS.
16.0     THRESHOLD VALUE OF TEST RATIO
6                 NUMBER OF VALUES OF N
0 1 2 3 5 9       VALUES OF N
4                 NUMBER OF VALUES OF K
0 1 2 4           VALUES OF K
4                 NUMBER OF VALUES OF INCX AND INCY
1 2 -1 -2         VALUES OF INCX AND INCY
3                 NUMBER OF VALUES OF ALPHA
(0.0,0.0) (1.0,0.0) (0.7,-0.9)       VALUES OF ALPHA
3                 NUMBER OF VALUES OF BETA
(0.0,0.0) (1.0,0.0) (1.3,-1.1)       VALUES OF BETA
CGEMV  T PUT F FOR NO TEST. SAME COLUMNS.
CGBMV  T PUT F FOR NO TEST. SAME COLUMNS.
CHEMV  T PUT F FOR NO TEST. SAME COLUMNS.
CHBMV  T PUT F FOR NO TEST. SAME COLUMNS.
CHPMV  T PUT F FOR NO TEST. SAME COLUMNS.
CTRMV  T PUT F FOR NO TEST. SAME COLUMNS.
CTBMV  T PUT F FOR NO TEST. SAME COLUMNS.
CTPMV  T PUT F FOR NO TEST. SAME COLUMNS.
CTRSV  T PUT F FOR NO TEST. SAME COLUMNS.
CTBSV  T PUT F FOR NO TEST. SAME COLUMNS.
CTPSV  T PUT F FOR NO TEST. SAME COLUMNS.
CGERC  T PUT F FOR NO TEST. SAME COLUMNS.
CGERU  T PUT F FOR NO TEST. SAME COLUMNS.
CHER   T PUT F FOR NO TEST. SAME COLUMNS.
CHPR   T PUT F FOR NO TEST. SAME COLUMNS.
CHER2  T PUT F FOR NO TEST. SAME COLUMNS.
CHPR2  T PUT F FOR NO TEST. SAME COLUMNS.
