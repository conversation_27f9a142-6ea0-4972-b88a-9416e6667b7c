'sblat2.summ'     NAME OF <PERSON>UMMARY OUTPUT FILE
6                 UNIT NUMBER OF SUMMARY FILE
'sblat2.snap'     NAME OF SNAPSHOT OUTPUT FILE
-1                UNIT NUMBER OF SNAPSHOT FILE (NOT USED IF .LT. 0)
F        LOGICAL FLAG, T TO REWIND SNAPSHOT FILE AFTER EACH RECORD.
F        LOGICAL FLAG, T TO STOP ON FAILURES.
T        LOGICAL FLAG, T TO TEST ERROR EXITS.
16.0     THRESHOLD VALUE OF TEST RATIO
6                 NUMBER OF VALUES OF N
0 1 2 3 5 9       VALUES OF N
4                 NUMBER OF VALUES OF K
0 1 2 4           VALUES OF K
4                 NUMBER OF VALUES OF INCX AND INCY
1 2 -1 -2         VALUES OF INCX AND INCY
3                 NUMBER OF VALUES OF ALPHA
0.0 1.0 0.7       VALUES OF ALPHA
3                 NUMBER OF VALUES OF BETA
0.0 1.0 0.9       VALUES OF BETA
SGEMV  T PUT F FOR NO TEST. SAME COLUMNS.
S<PERSON>MV  T PUT F FOR NO TEST. SAME COLUMNS.
SSYMV  T PUT F FOR NO TEST. SAME COLUMNS.
SSBMV  T PUT F FOR NO TEST. SAME COLUMNS.
SSPMV  T PUT F FOR NO TEST. SAME COLUMNS.
STRMV  T PUT F FOR NO TEST. SAME COLUMNS.
STBMV  T PUT F FOR NO TEST. SAME COLUMNS.
STPMV  T PUT F FOR NO TEST. SAME COLUMNS.
STRSV  T PUT F FOR NO TEST. SAME COLUMNS.
STBSV  T PUT F FOR NO TEST. SAME COLUMNS.
STPSV  T PUT F FOR NO TEST. SAME COLUMNS.
SGER   T PUT F FOR NO TEST. SAME COLUMNS.
SSYR   T PUT F FOR NO TEST. SAME COLUMNS.
SSPR   T PUT F FOR NO TEST. SAME COLUMNS.
SSYR2  T PUT F FOR NO TEST. SAME COLUMNS.
SSPR2  T PUT F FOR NO TEST. SAME COLUMNS.
