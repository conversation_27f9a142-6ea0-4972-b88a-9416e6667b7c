#!/bin/bash

# CLIST[((g++))]="g++-3.4 -O3 -DNDEBUG"
# CLIST[((g++))]="g++-3.4 -O3 -DNDEBUG -finline-limit=20000"

# CLIST[((g++))]="g++-4.1 -O3 -DNDEBUG"
#CLIST[((g++))]="g++-4.1 -O3 -DNDEBUG -finline-limit=20000"

# CLIST[((g++))]="g++-4.2 -O3 -DNDEBUG"
#CLIST[((g++))]="g++-4.2 -O3 -DNDEBUG -finline-limit=20000"
# CLIST[((g++))]="g++-4.2 -O3 -DNDEBUG -finline-limit=20000 -fprofile-generate"
# CLIST[((g++))]="g++-4.2 -O3 -DNDEBUG -finline-limit=20000 -fprofile-use"

# CLIST[((g++))]="g++-4.3 -O3 -DNDEBUG"
#CLIST[((g++))]="g++-4.3 -O3 -DNDEBUG -finline-limit=20000"
# CLIST[((g++))]="g++-4.3 -O3 -DNDEBUG -finline-limit=20000 -fprofile-generate"
# CLIST[((g++))]="g++-4.3 -O3 -DNDEBUG -finline-limit=20000 -fprofile-use"

# CLIST[((g++))]="icpc -fast -DNDEBUG -fno-exceptions -no-inline-max-size -prof-genx"
# CLIST[((g++))]="icpc -fast -DNDEBUG -fno-exceptions -no-inline-max-size -prof-use"

#CLIST[((g++))]="/opt/intel/Compiler/11.1/072/bin/intel64/icpc -fast -DNDEBUG -fno-exceptions -no-inline-max-size -lrt"
CLIST[((g++))]="/home/<USER>/svn/llvm/Release/bin/clang++ -O3 -DNDEBUG -DEIGEN_DONT_VECTORIZE -lrt"
CLIST[((g++))]="/home/<USER>/svn/llvm/Release/bin/clang++ -O3 -DNDEBUG -lrt"
CLIST[((g++))]="g++-4.4.4 -O3 -DNDEBUG -DEIGEN_DONT_VECTORIZE -lrt"
CLIST[((g++))]="g++-4.4.4 -O3 -DNDEBUG -lrt"
CLIST[((g++))]="g++-4.5.0 -O3 -DNDEBUG -DEIGEN_DONT_VECTORIZE -lrt"
CLIST[((g++))]="g++-4.5.0 -O3 -DNDEBUG -lrt"
