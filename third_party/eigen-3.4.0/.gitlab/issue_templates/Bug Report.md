<!--
Please read this!

Before opening a new issue, make sure to search for keywords in the issues
filtered by "bug::confirmed" or "bug::unconfirmed" and "bugzilla" label:

- https://gitlab.com/libeigen/eigen/-/issues?scope=all&utf8=%E2%9C%93&state=opened&label_name[]=bug%3A%3Aconfirmed
- https://gitlab.com/libeigen/eigen/-/issues?scope=all&utf8=%E2%9C%93&state=opened&label_name[]=bug%3A%3Aunconfirmed
- https://gitlab.com/libeigen/eigen/-/issues?scope=all&utf8=%E2%9C%93&state=opened&label_name[]=bugzilla

and verify the issue you're about to submit isn't a duplicate. -->

### Summary
<!-- Summarize the bug encountered concisely. -->

### Environment
<!-- Please provide your development environment here -->
- **Operating System** : Windows/Linux
- **Architecture** : x64/Arm64/PowerPC ...
- **Eigen Version** : 3.3.9
- **Compiler Version** : Gcc7.0
- **Compile Flags** : -O3 -march=native
- **Vector Extension** : SSE/AVX/NEON ...

### Minimal Example
<!-- If possible, please create a minimal example here that exhibits the problematic behavior.
You can also link to [godbolt](https://godbolt.org). But please note that you need to click 
the "Share" button in the top right-hand corner of the godbolt page where you reproduce the sample 
code to get the share link instead of in your browser address bar. 

You can read [the guidelines on stackoverflow](https://stackoverflow.com/help/minimal-reproducible-example)
on how to create a good minimal example. -->

```cpp
//show your code here
```

### Steps to reproduce
<!-- Describe how one can reproduce the issue - this is very important. Please use an ordered list. -->

1. first step
2. second step
3. ... 

### What is the current *bug* behavior?
<!-- Describe what actually happens. -->

### What is the expected *correct* behavior?
<!-- Describe what you should see instead. -->

### Relevant logs
<!-- Add relevant code snippets or program output within blocks marked by " ``` " -->

<!-- OPTIONAL: remove this section if you are not reporting a compilation warning issue.-->
### Warning Messages
<!-- Show us the warning messages you got! -->

<!-- OPTIONAL: remove this section if you are not reporting a performance issue. -->
### Benchmark scripts and results
<!-- Please share any benchmark scripts - either standalone, or using [Google Benchmark](https://github.com/google/benchmark). -->

### Anything else that might help
<!-- It will be better to provide us more information to help narrow down the cause. 
Including but not limited to the following: 
- lines of code that might help us diagnose the problem. 
- potential ways to address the issue.
- last known working/first broken version (release number or commit hash). --> 

- [ ] Have a plan to fix this issue.
