<?xml version="1.0"?>
<!--
  Copyright 2017 Google Inc. All rights reserved.
  http://ceres-solver.org/

  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions are met:

  * Redistributions of source code must retain the above copyright notice,
    this list of conditions and the following disclaimer.
  * Redistributions in binary form must reproduce the above copyright notice,
    this list of conditions and the following disclaimer in the documentation
    and/or other materials provided with the distribution.
  * Neither the name of Google Inc. nor the names of its contributors may be
    used to endorse or promote products derived from this software without
    specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
  ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE
  LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, <PERSON>XEMPLAR<PERSON>, OR
  CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
  SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
  INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
  CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
  POSSIBILITY OF SUCH DAMAGE.
-->

<package format="2">
  <name>ceres-solver</name>
  <version>2.1.0</version>
  <description>A large scale non-linear optimization library.</description>
  <maintainer email="<EMAIL>">
    The Ceres Solver Authors
  </maintainer>
  <license>New BSD</license>
  <url type="website">http://ceres-solver.org/</url>

  <buildtool_depend>cmake</buildtool_depend>

  <build_depend>atlas</build_depend>
  <build_depend>gfortran</build_depend>
  <build_depend>suitesparse</build_depend>
  <depend>eigen</depend>
  <depend>libgflags-dev</depend>
  <depend>libgoogle-glog-dev</depend>

  <export>
    <build_type>cmake</build_type>
  </export>
</package>
