{"context": {"date": "2018-03-23 13:34:44", "num_cpus": 8, "mhz_per_cpu": 2200, "cpu_scaling_enabled": false, "library_build_type": "release"}, "benchmarks": [{"name": "BM_MatrixVectorMultiply/1/1", "iterations": 75370933, "real_time": 8.924666861027045, "cpu_time": 8.924156478200953, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/1/2", "iterations": 79276096, "real_time": 9.176883483513434, "cpu_time": 9.173345266648855, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/1/3", "iterations": 86461383, "real_time": 8.133996132556364, "cpu_time": 8.131514620810538, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/1/4", "iterations": 80784766, "real_time": 8.617504196610222, "cpu_time": 8.616909282128765, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/1/8", "iterations": 62071595, "real_time": 11.623699777324967, "cpu_time": 11.622159862333158, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/1/12", "iterations": 46187548, "real_time": 14.648812380771005, "cpu_time": 14.647497632911776, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/1/15", "iterations": 43216546, "real_time": 15.979603784946462, "cpu_time": 15.978764244602067, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/2/1", "iterations": 85460694, "real_time": 8.455713617301397, "cpu_time": 8.451733378153941, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/2/2", "iterations": 73809298, "real_time": 9.356360914744819, "cpu_time": 9.356355076023073, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/2/3", "iterations": 60910879, "real_time": 11.561664410556345, "cpu_time": 11.56066061696467, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/2/4", "iterations": 57011614, "real_time": 12.235077136567469, "cpu_time": 12.233770473503897, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/2/8", "iterations": 37294746, "real_time": 17.008151843698663, "cpu_time": 17.008079368605955, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/2/12", "iterations": 36096615, "real_time": 20.23428276958798, "cpu_time": 20.23242345577272, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/2/15", "iterations": 29620477, "real_time": 24.192867556653738, "cpu_time": 24.189515921705162, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/3/1", "iterations": 70819380, "real_time": 10.241319961826111, "cpu_time": 10.236957736709932, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/3/2", "iterations": 49055678, "real_time": 14.430740841341054, "cpu_time": 14.428604982281607, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/3/3", "iterations": 46364678, "real_time": 14.935508190628967, "cpu_time": 14.931711593036367, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/3/4", "iterations": 41730007, "real_time": 16.495830781686326, "cpu_time": 16.49549208079452, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/3/8", "iterations": 32099490, "real_time": 21.89921241480335, "cpu_time": 21.896111122014723, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/3/12", "iterations": 26976615, "real_time": 25.065036735486377, "cpu_time": 25.063782094232344, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/3/15", "iterations": 23158717, "real_time": 30.120809500024972, "cpu_time": 30.119803268894366, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/4/1", "iterations": 54510341, "real_time": 12.223535017736792, "cpu_time": 12.219974921822656, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/4/2", "iterations": 45100187, "real_time": 15.694088963631776, "cpu_time": 15.693859539872824, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/4/3", "iterations": 39166098, "real_time": 17.640497530917788, "cpu_time": 17.634996470672178, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/4/4", "iterations": 34750664, "real_time": 21.33945386000327, "cpu_time": 21.335160674915432, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/4/8", "iterations": 30043950, "real_time": 23.893673667994594, "cpu_time": 23.885541015745137, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/4/12", "iterations": 21692445, "real_time": 32.236621367456465, "cpu_time": 32.233111574098835, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/4/15", "iterations": 17051627, "real_time": 39.89456454704372, "cpu_time": 39.89302604379036, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/6/1", "iterations": 43622404, "real_time": 16.067802864650357, "cpu_time": 16.06383270394725, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/6/2", "iterations": 33862065, "real_time": 19.549917288877438, "cpu_time": 19.54860106730053, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/6/3", "iterations": 32245731, "real_time": 21.78901535588535, "cpu_time": 21.77655702703712, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/6/4", "iterations": 31862790, "real_time": 22.5543953950883, "cpu_time": 22.547178071976678, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/6/8", "iterations": 20659998, "real_time": 32.157974121069905, "cpu_time": 32.154552967527074, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/6/12", "iterations": 15551719, "real_time": 45.013863291655184, "cpu_time": 44.9240370148149, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/6/15", "iterations": 12874274, "real_time": 54.00855333930289, "cpu_time": 54.001647005493055, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/8/1", "iterations": 35825968, "real_time": 19.189763050020964, "cpu_time": 19.188037012705493, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/8/2", "iterations": 28860743, "real_time": 23.957427986537024, "cpu_time": 23.950526845410646, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/8/3", "iterations": 25577503, "real_time": 26.716626640397276, "cpu_time": 26.707845562563413, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/8/4", "iterations": 27263982, "real_time": 26.87154124970238, "cpu_time": 26.86522460292142, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/8/8", "iterations": 18390176, "real_time": 38.055934538584296, "cpu_time": 38.05232750355422, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/8/12", "iterations": 13196592, "real_time": 57.00488156095191, "cpu_time": 57.00198960458878, "time_unit": "ns"}, {"name": "BM_MatrixVectorMultiply/8/15", "iterations": 10753844, "real_time": 65.6496894501078, "cpu_time": 65.64973417877368, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/1/1", "iterations": 87950748, "real_time": 8.161199322124496, "cpu_time": 8.160760611154787, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/1/2", "iterations": 82828474, "real_time": 8.197013468329587, "cpu_time": 8.194766451932905, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/1/3", "iterations": 79647729, "real_time": 9.123642959983377, "cpu_time": 9.122582766923601, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/1/4", "iterations": 60000343, "real_time": 11.50134168574961, "cpu_time": 11.497550939000405, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/1/8", "iterations": 42555778, "real_time": 16.328523285845055, "cpu_time": 16.32842900909947, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/1/12", "iterations": 34690560, "real_time": 20.90096219310777, "cpu_time": 20.898105997712126, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/1/15", "iterations": 22984807, "real_time": 30.876962381519814, "cpu_time": 30.874873128149208, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/2/1", "iterations": 82616342, "real_time": 8.497106250712969, "cpu_time": 8.494602678002884, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/2/2", "iterations": 66217648, "real_time": 10.427153785878781, "cpu_time": 10.426691687992145, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/2/3", "iterations": 52740629, "real_time": 13.438166219244954, "cpu_time": 13.437818498524338, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/2/4", "iterations": 44820940, "real_time": 16.29096069797702, "cpu_time": 16.288391095769057, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/2/8", "iterations": 28365116, "real_time": 24.753231118037846, "cpu_time": 24.752586945175814, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/2/12", "iterations": 20152990, "real_time": 33.99254398150672, "cpu_time": 33.98964620138281, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/2/15", "iterations": 17477847, "real_time": 41.15864562526114, "cpu_time": 41.14803156246859, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/3/1", "iterations": 85071217, "real_time": 8.663808265065606, "cpu_time": 8.662060165425922, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/3/2", "iterations": 56597671, "real_time": 12.28825938755658, "cpu_time": 12.287025026171113, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/3/3", "iterations": 44866043, "real_time": 16.032917745159793, "cpu_time": 16.028580902488002, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/3/4", "iterations": 40158108, "real_time": 17.25510905503397, "cpu_time": 17.254896570326416, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/3/8", "iterations": 25254165, "real_time": 28.276512487003924, "cpu_time": 28.273039318464768, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/3/12", "iterations": 18068759, "real_time": 39.51496757523838, "cpu_time": 39.513781771067336, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/3/15", "iterations": 14997033, "real_time": 47.08858685368697, "cpu_time": 47.085713554140796, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/4/1", "iterations": 78983594, "real_time": 8.950030002488138, "cpu_time": 8.948592539356973, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/4/2", "iterations": 50655629, "real_time": 13.881427511987113, "cpu_time": 13.880806810236136, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/4/3", "iterations": 42322156, "real_time": 16.61704278485427, "cpu_time": 16.61685193920661, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/4/4", "iterations": 35709549, "real_time": 19.691253563928413, "cpu_time": 19.687591125835635, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/4/8", "iterations": 20404356, "real_time": 33.67175479655679, "cpu_time": 33.668153996136844, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/4/12", "iterations": 15090728, "real_time": 47.1252733530214, "cpu_time": 47.1227763166894, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/4/15", "iterations": 11336950, "real_time": 62.45347222679662, "cpu_time": 62.45180582078902, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/6/1", "iterations": 65892276, "real_time": 10.683369458878103, "cpu_time": 10.683331078137206, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/6/2", "iterations": 45151386, "real_time": 15.743454386474488, "cpu_time": 15.741886638873094, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/6/3", "iterations": 35555194, "real_time": 20.272604644448467, "cpu_time": 20.265815453011015, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/6/4", "iterations": 28844688, "real_time": 24.8992656822192, "cpu_time": 24.89664648132097, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/6/8", "iterations": 16677944, "real_time": 42.613494320617384, "cpu_time": 42.61028817461015, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/6/12", "iterations": 10657572, "real_time": 67.21215817375764, "cpu_time": 67.2065832630554, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/6/15", "iterations": 8660580, "real_time": 80.84343484848371, "cpu_time": 80.84343080948615, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/8/1", "iterations": 57066458, "real_time": 12.319644088657304, "cpu_time": 12.319338270477425, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/8/2", "iterations": 38263912, "real_time": 18.003573181401965, "cpu_time": 17.997610908158087, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/8/3", "iterations": 29869088, "real_time": 24.13752475408301, "cpu_time": 24.137328866552775, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/8/4", "iterations": 22616613, "real_time": 30.444019799836454, "cpu_time": 30.442931485806785, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/8/8", "iterations": 12552902, "real_time": 54.10299992579167, "cpu_time": 54.09976115483072, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/8/12", "iterations": 9204229, "real_time": 77.71573457738165, "cpu_time": 77.7058024088722, "time_unit": "ns"}, {"name": "BM_MatrixTransposeVectorMultiply/8/15", "iterations": 7493764, "real_time": 93.483444364895, "cpu_time": 93.48346171563588, "time_unit": "ns"}]}