{"context": {"date": "2018-03-23 13:15:00", "num_cpus": 8, "mhz_per_cpu": 2200, "cpu_scaling_enabled": false, "library_build_type": "release"}, "benchmarks": [{"name": "BM_MatrixMatrixMultiplyDynamic/1/1/1", "iterations": 70805770, "real_time": 9.708577407608265, "cpu_time": 9.705353108934483, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/1/2", "iterations": 74727246, "real_time": 10.385020397774865, "cpu_time": 10.330810264304402, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/1/3", "iterations": 58161273, "real_time": 11.918587820938697, "cpu_time": 11.860538196954527, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/1/4", "iterations": 48633401, "real_time": 13.997796658213307, "cpu_time": 13.981090896768663, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/1/8", "iterations": 32240533, "real_time": 21.27802189006271, "cpu_time": 21.243600408219077, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/1/12", "iterations": 25863853, "real_time": 26.210347317374435, "cpu_time": 26.0190544695719, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/1/15", "iterations": 18352905, "real_time": 36.613193819894164, "cpu_time": 36.5470207577492, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/2/1", "iterations": 73026206, "real_time": 9.818615895342395, "cpu_time": 9.816667731581177, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/2/2", "iterations": 58211574, "real_time": 12.254592290921693, "cpu_time": 12.253937679128907, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/2/3", "iterations": 41788051, "real_time": 16.580228591773523, "cpu_time": 16.55332046952845, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/2/4", "iterations": 37355846, "real_time": 18.618565967193987, "cpu_time": 18.617300221229108, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/2/8", "iterations": 23951522, "real_time": 29.06457694159757, "cpu_time": 29.063497509678122, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/2/12", "iterations": 17955394, "real_time": 39.849777902603556, "cpu_time": 39.84490677286161, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/2/15", "iterations": 15072693, "real_time": 47.9368776381267, "cpu_time": 47.92202693971137, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/3/1", "iterations": 70635009, "real_time": 10.20255222271114, "cpu_time": 10.198639600937826, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/3/2", "iterations": 49235444, "real_time": 15.07087887852331, "cpu_time": 15.068494152302112, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/3/3", "iterations": 38174808, "real_time": 17.47522282143962, "cpu_time": 17.473879632872062, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/3/4", "iterations": 34106744, "real_time": 20.02940421434039, "cpu_time": 20.028736838673233, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/3/8", "iterations": 20587933, "real_time": 33.61730121248437, "cpu_time": 33.61478784684215, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/3/12", "iterations": 15313500, "real_time": 46.2733606968171, "cpu_time": 46.2550690567147, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/3/15", "iterations": 11989586, "real_time": 56.997383313299295, "cpu_time": 56.99220973935199, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/4/1", "iterations": 62076549, "real_time": 11.642173391475032, "cpu_time": 11.57379093351336, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/4/2", "iterations": 43844265, "real_time": 16.274509083373967, "cpu_time": 16.270428983129257, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/4/3", "iterations": 32460306, "real_time": 20.15131252794388, "cpu_time": 20.15064183313622, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/4/4", "iterations": 30627603, "real_time": 23.54295492837865, "cpu_time": 23.537787139267902, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/4/8", "iterations": 16957323, "real_time": 38.97610259689301, "cpu_time": 38.96446390742221, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/4/12", "iterations": 11970314, "real_time": 56.122851917381425, "cpu_time": 56.1075507292456, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/4/15", "iterations": 9639749, "real_time": 75.14967060482137, "cpu_time": 75.11554502093384, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/8/1", "iterations": 46089625, "real_time": 14.720880177102261, "cpu_time": 14.720059015450884, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/8/2", "iterations": 35845047, "real_time": 20.07598932844998, "cpu_time": 20.073735710264213, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/8/3", "iterations": 27662955, "real_time": 25.73441174573276, "cpu_time": 25.734018654189445, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/8/4", "iterations": 21879855, "real_time": 34.10628310831569, "cpu_time": 34.08971403146873, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/8/8", "iterations": 10406446, "real_time": 59.840372689427156, "cpu_time": 59.8305127418138, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/8/12", "iterations": 8903134, "real_time": 85.12601652511526, "cpu_time": 85.10475075406018, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/8/15", "iterations": 6940176, "real_time": 100.28962911611278, "cpu_time": 100.25855252085725, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/12/1", "iterations": 35671516, "real_time": 18.399173111134672, "cpu_time": 18.397255670322537, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/12/2", "iterations": 25982607, "real_time": 27.018409545851057, "cpu_time": 27.0118391122185, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/12/3", "iterations": 18737168, "real_time": 33.898883702445445, "cpu_time": 33.861787437674536, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/12/4", "iterations": 15761399, "real_time": 45.105465061861274, "cpu_time": 45.09904228679162, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/12/8", "iterations": 9303562, "real_time": 79.82569385590635, "cpu_time": 79.81115190074513, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/12/12", "iterations": 5956180, "real_time": 122.2593425637815, "cpu_time": 122.22196105557559, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/12/15", "iterations": 4506302, "real_time": 148.18435404415453, "cpu_time": 148.15784650030173, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/15/1", "iterations": 35319999, "real_time": 19.630753360924967, "cpu_time": 19.626416184213337, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/15/2", "iterations": 23526644, "real_time": 30.066773439214494, "cpu_time": 30.05571045322092, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/15/3", "iterations": 17598729, "real_time": 41.08397328399072, "cpu_time": 41.07080687474689, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/15/4", "iterations": 12271659, "real_time": 53.032831420852794, "cpu_time": 53.02852694977917, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/15/8", "iterations": 7952738, "real_time": 92.4296930265707, "cpu_time": 92.42213184943324, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/15/12", "iterations": 4950880, "real_time": 128.7566709347262, "cpu_time": 128.7435768994604, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/1/15/15", "iterations": 3689648, "real_time": 172.0110625662535, "cpu_time": 171.99635303963902, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/1/1", "iterations": 71110750, "real_time": 10.465632580976097, "cpu_time": 10.462187503295935, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/1/2", "iterations": 38512960, "real_time": 15.239588699711753, "cpu_time": 15.233105946673353, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/1/3", "iterations": 39388909, "real_time": 18.14961102468686, "cpu_time": 18.142772118923137, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/1/4", "iterations": 32544668, "real_time": 20.37446045157588, "cpu_time": 20.37086382322283, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/1/8", "iterations": 22523030, "real_time": 31.021126554300547, "cpu_time": 31.018428692764868, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/1/12", "iterations": 16353383, "real_time": 42.19129057642019, "cpu_time": 42.19090325225083, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/1/15", "iterations": 13760296, "real_time": 63.1056781749931, "cpu_time": 63.08418074727465, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/2/1", "iterations": 53321552, "real_time": 13.571924725902662, "cpu_time": 13.570760280946285, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/2/2", "iterations": 34553227, "real_time": 19.24002959150211, "cpu_time": 19.238347839407254, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/2/3", "iterations": 26606966, "real_time": 25.68407029220461, "cpu_time": 25.683123735340747, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/2/4", "iterations": 21813649, "real_time": 33.015750690106444, "cpu_time": 33.012312612163, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/2/8", "iterations": 13165814, "real_time": 53.96142980195838, "cpu_time": 53.95032923904316, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/2/12", "iterations": 9855407, "real_time": 77.3305759978306, "cpu_time": 77.2996995456403, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/2/15", "iterations": 7160320, "real_time": 93.05944733805121, "cpu_time": 93.03145669467345, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/3/1", "iterations": 44724434, "real_time": 15.451907116408652, "cpu_time": 15.450704194490172, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/3/2", "iterations": 31958363, "real_time": 22.13576850112504, "cpu_time": 22.13057658804359, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/3/3", "iterations": 22712598, "real_time": 28.76291197581539, "cpu_time": 28.760734461112644, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/3/4", "iterations": 19248749, "real_time": 33.782859186998586, "cpu_time": 33.780377103987405, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/3/8", "iterations": 11206634, "real_time": 60.28197823957636, "cpu_time": 60.263233366950296, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/3/12", "iterations": 7864550, "real_time": 85.90987786563406, "cpu_time": 85.90319853011204, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/3/15", "iterations": 6630295, "real_time": 108.15029754593648, "cpu_time": 108.1291254763157, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/4/1", "iterations": 40941658, "real_time": 15.922779899567644, "cpu_time": 15.922657553340848, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/4/2", "iterations": 27517887, "real_time": 24.6612308938423, "cpu_time": 24.659415165124962, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/4/3", "iterations": 21047188, "real_time": 32.587989757196986, "cpu_time": 32.58449537296852, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/4/4", "iterations": 17532786, "real_time": 40.90771426962989, "cpu_time": 40.89367200398113, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/4/8", "iterations": 10142723, "real_time": 70.52964376344715, "cpu_time": 70.52504539461458, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/4/12", "iterations": 7004763, "real_time": 100.97909736219086, "cpu_time": 100.97914804540859, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/4/15", "iterations": 4970108, "real_time": 139.61298447974306, "cpu_time": 139.5945520700979, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/8/1", "iterations": 29905201, "real_time": 21.529462050555786, "cpu_time": 21.529265093385952, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/8/2", "iterations": 21023483, "real_time": 33.44659155464615, "cpu_time": 33.43898820190702, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/8/3", "iterations": 15687962, "real_time": 44.66648854594782, "cpu_time": 44.65239015749736, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/8/4", "iterations": 12333715, "real_time": 58.456414226244306, "cpu_time": 58.45221816784337, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/8/8", "iterations": 6708579, "real_time": 102.54277962264361, "cpu_time": 102.54228205406758, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/8/12", "iterations": 4610116, "real_time": 152.92074472764102, "cpu_time": 152.90352780710978, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/8/15", "iterations": 3740855, "real_time": 188.36253878858312, "cpu_time": 188.3534646491259, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/12/1", "iterations": 26222827, "real_time": 26.458015643267686, "cpu_time": 26.45744488189604, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/12/2", "iterations": 16369904, "real_time": 42.28220098028351, "cpu_time": 42.27092596266876, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/12/3", "iterations": 12297530, "real_time": 56.84789922039083, "cpu_time": 56.84491113256149, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/12/4", "iterations": 9635768, "real_time": 74.95444088833227, "cpu_time": 74.95282161214355, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/12/8", "iterations": 4942805, "real_time": 141.90911294302057, "cpu_time": 141.89574543199674, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/12/12", "iterations": 2953823, "real_time": 242.87571296928039, "cpu_time": 242.8557838435149, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/12/15", "iterations": 2647384, "real_time": 266.5160399825901, "cpu_time": 266.51366027747, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/15/1", "iterations": 24894908, "real_time": 28.931135794891475, "cpu_time": 28.92479056359621, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/15/2", "iterations": 10000000, "real_time": 50.149341800715774, "cpu_time": 50.140900000000954, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/15/3", "iterations": 10387914, "real_time": 67.78976471853693, "cpu_time": 67.78887464798126, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/15/4", "iterations": 8232001, "real_time": 87.25451283687022, "cpu_time": 87.24245781797033, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/15/8", "iterations": 4078969, "real_time": 161.4062230091944, "cpu_time": 161.4027956574309, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/15/12", "iterations": 2913983, "real_time": 246.24524953984053, "cpu_time": 246.2214089787108, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/2/15/15", "iterations": 1980545, "real_time": 356.5268767027268, "cpu_time": 356.4261352304527, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/1/1", "iterations": 56068628, "real_time": 12.794681797167486, "cpu_time": 12.793286113582157, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/1/2", "iterations": 39670171, "real_time": 17.59898098171357, "cpu_time": 17.59833603943866, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/1/3", "iterations": 31158472, "real_time": 22.851131887169714, "cpu_time": 22.848938163591246, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/1/4", "iterations": 26125739, "real_time": 25.647778157089107, "cpu_time": 25.647810383468983, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/1/8", "iterations": 16180744, "real_time": 44.15816089478095, "cpu_time": 44.14889698520625, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/1/12", "iterations": 11614209, "real_time": 61.22041896774151, "cpu_time": 61.20373759418259, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/1/15", "iterations": 7775445, "real_time": 88.19223144035787, "cpu_time": 88.19135110594956, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/2/1", "iterations": 42433758, "real_time": 16.78901321878231, "cpu_time": 16.788143062888754, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/2/2", "iterations": 27240110, "real_time": 26.04770891464828, "cpu_time": 26.0457098007311, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/2/3", "iterations": 21036625, "real_time": 34.48331484121555, "cpu_time": 34.47515939462775, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/2/4", "iterations": 16349143, "real_time": 40.97252363805076, "cpu_time": 40.97254516643465, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/2/8", "iterations": 9121829, "real_time": 73.69065172807457, "cpu_time": 73.67820642110196, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/2/12", "iterations": 6573758, "real_time": 106.1135438832917, "cpu_time": 106.10658317510315, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/2/15", "iterations": 5281106, "real_time": 129.52993671592768, "cpu_time": 129.5226795296308, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/3/1", "iterations": 37443767, "real_time": 18.692536893444267, "cpu_time": 18.691949450491798, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/3/2", "iterations": 24253512, "real_time": 28.977756462325814, "cpu_time": 28.97704052097729, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/3/3", "iterations": 18031942, "real_time": 38.05257642492108, "cpu_time": 38.05031094265942, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/3/4", "iterations": 14793400, "real_time": 48.3895595345831, "cpu_time": 48.378871658984025, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/3/8", "iterations": 7509118, "real_time": 87.2850715536611, "cpu_time": 87.28162215589214, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/3/12", "iterations": 5617437, "real_time": 125.94982336650604, "cpu_time": 125.94996615004284, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/3/15", "iterations": 4235468, "real_time": 156.62988364866754, "cpu_time": 156.5893072501082, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/4/1", "iterations": 33582967, "real_time": 20.666642557179625, "cpu_time": 20.66494005726175, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/4/2", "iterations": 20330280, "real_time": 34.48259384384173, "cpu_time": 34.467995521949824, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/4/3", "iterations": 14817261, "real_time": 44.999856386574585, "cpu_time": 44.999882231946614, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/4/4", "iterations": 12703713, "real_time": 57.50843316863264, "cpu_time": 57.49807162677589, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/4/8", "iterations": 6803250, "real_time": 103.53664954199472, "cpu_time": 103.53022452504064, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/4/12", "iterations": 4761484, "real_time": 151.0559510261405, "cpu_time": 151.0163638059046, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/4/15", "iterations": 3477121, "real_time": 202.55288986918265, "cpu_time": 202.5529166226849, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/8/1", "iterations": 24105845, "real_time": 30.166071218608963, "cpu_time": 30.152521100172752, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/8/2", "iterations": 14274178, "real_time": 47.932456426425794, "cpu_time": 47.93242735238514, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/8/3", "iterations": 11208070, "real_time": 64.67097912631067, "cpu_time": 64.65662687688572, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/8/4", "iterations": 8661544, "real_time": 81.03635656313216, "cpu_time": 81.02977944809965, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/8/8", "iterations": 4408532, "real_time": 159.33889874913777, "cpu_time": 159.33898177443112, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/8/12", "iterations": 3025836, "real_time": 229.25236332785, "cpu_time": 229.21764431383914, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/8/15", "iterations": 2491999, "real_time": 286.4180679932564, "cpu_time": 286.3901630779148, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/12/1", "iterations": 19248855, "real_time": 35.61081810040978, "cpu_time": 35.608871280915714, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/12/2", "iterations": 12091481, "real_time": 58.460851325769724, "cpu_time": 58.46008441811202, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/12/3", "iterations": 8702571, "real_time": 82.07327822041766, "cpu_time": 82.06643760792123, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/12/4", "iterations": 6588049, "real_time": 104.95601975858659, "cpu_time": 104.9535302484852, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/12/8", "iterations": 3441545, "real_time": 206.8204977618395, "cpu_time": 206.80363034625137, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/12/12", "iterations": 1913520, "real_time": 352.0648924252789, "cpu_time": 351.97646222668027, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/12/15", "iterations": 1769187, "real_time": 392.55757818155735, "cpu_time": 392.5164496460796, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/15/1", "iterations": 17529493, "real_time": 40.35762328938747, "cpu_time": 40.355017683627686, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/15/2", "iterations": 10120287, "real_time": 68.4013657896837, "cpu_time": 68.39430541841396, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/15/3", "iterations": 7331839, "real_time": 95.76670095204322, "cpu_time": 95.76383223908698, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/15/4", "iterations": 5777437, "real_time": 124.17593458644886, "cpu_time": 124.17166989445052, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/15/8", "iterations": 2843390, "real_time": 245.4533497202779, "cpu_time": 245.43344388213094, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/15/12", "iterations": 2003899, "real_time": 349.96039020923655, "cpu_time": 349.96075151492676, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/3/15/15", "iterations": 1369917, "real_time": 534.0792551068274, "cpu_time": 534.0038849069114, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/1/1", "iterations": 43784206, "real_time": 15.567154102293838, "cpu_time": 15.566777664073646, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/1/2", "iterations": 33442579, "real_time": 21.8670375255653, "cpu_time": 21.86063461194106, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/1/3", "iterations": 26232949, "real_time": 27.43205195585781, "cpu_time": 27.423260724518492, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/1/4", "iterations": 21726783, "real_time": 33.09777508247426, "cpu_time": 33.09307226937404, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/1/8", "iterations": 12635379, "real_time": 55.52768064990924, "cpu_time": 55.52773684113523, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/1/12", "iterations": 7856077, "real_time": 88.06104052183609, "cpu_time": 88.05820004055361, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/1/15", "iterations": 5300460, "real_time": 126.61529226403675, "cpu_time": 126.60165344139702, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/2/1", "iterations": 34390768, "real_time": 20.377612358162786, "cpu_time": 20.377561792164304, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/2/2", "iterations": 20908879, "real_time": 34.18694928378928, "cpu_time": 34.171846324233805, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/2/3", "iterations": 15411815, "real_time": 45.325916440705925, "cpu_time": 45.313287240988785, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/2/4", "iterations": 12438032, "real_time": 54.548608170088684, "cpu_time": 54.54134544757455, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/2/8", "iterations": 7252233, "real_time": 95.60118655242721, "cpu_time": 95.59028233097638, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/2/12", "iterations": 4715647, "real_time": 141.06606686984432, "cpu_time": 141.05275479695544, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/2/15", "iterations": 4108608, "real_time": 171.7693398839505, "cpu_time": 171.7657172453587, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/3/1", "iterations": 30653086, "real_time": 23.378084249479144, "cpu_time": 23.371578313517723, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/3/2", "iterations": 18987479, "real_time": 36.60128350998847, "cpu_time": 36.600540809024764, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/3/3", "iterations": 14597262, "real_time": 48.92379214889819, "cpu_time": 48.915611708551786, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/3/4", "iterations": 11609779, "real_time": 61.78766082111031, "cpu_time": 61.783949548049065, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/3/8", "iterations": 6170445, "real_time": 115.3343227172265, "cpu_time": 115.32134230189035, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/3/12", "iterations": 3942551, "real_time": 170.28375181379528, "cpu_time": 170.2775690155913, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/3/15", "iterations": 3143680, "real_time": 211.63193262032627, "cpu_time": 211.53902432817358, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/4/1", "iterations": 26297796, "real_time": 27.622421285563462, "cpu_time": 27.61790379695828, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/4/2", "iterations": 16116963, "real_time": 42.68193467811245, "cpu_time": 42.67354836019698, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/4/3", "iterations": 11333463, "real_time": 59.49625926601928, "cpu_time": 59.488260560782976, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/4/4", "iterations": 9425832, "real_time": 72.42838690239867, "cpu_time": 72.42681600945119, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/4/8", "iterations": 5303754, "real_time": 136.3180173914495, "cpu_time": 136.3060956447039, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/4/12", "iterations": 3471792, "real_time": 208.9563467296624, "cpu_time": 208.8878020342208, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/4/15", "iterations": 2520089, "real_time": 285.73527243793194, "cpu_time": 285.68475160995524, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/8/1", "iterations": 19096621, "real_time": 36.68898377700184, "cpu_time": 36.686385512912835, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/8/2", "iterations": 11715481, "real_time": 59.865703340908404, "cpu_time": 59.86386730514972, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/8/3", "iterations": 8174992, "real_time": 82.27500796670499, "cpu_time": 82.2640560382157, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/8/4", "iterations": 6602839, "real_time": 105.13826369751193, "cpu_time": 105.13825946687413, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/8/8", "iterations": 3103153, "real_time": 229.33769686453937, "cpu_time": 229.22105355424063, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/8/12", "iterations": 2360909, "real_time": 300.08286085974277, "cpu_time": 300.0738274960951, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/8/15", "iterations": 1889522, "real_time": 367.64128913401316, "cpu_time": 367.59773106638625, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/12/1", "iterations": 15594924, "real_time": 45.34080224359472, "cpu_time": 45.33885513004067, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/12/2", "iterations": 9416323, "real_time": 77.93539443209376, "cpu_time": 77.92319783423049, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/12/3", "iterations": 6504911, "real_time": 107.15989640776804, "cpu_time": 107.13782248519193, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/12/4", "iterations": 5121452, "real_time": 141.5863993223798, "cpu_time": 141.56454068104333, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/12/8", "iterations": 2629809, "real_time": 267.29956094762065, "cpu_time": 267.2996403921329, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/12/12", "iterations": 1465266, "real_time": 480.5505504731605, "cpu_time": 480.45406090090495, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/12/15", "iterations": 1347579, "real_time": 507.5070952786226, "cpu_time": 507.4752574802682, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/15/1", "iterations": 13992164, "real_time": 51.035651877709, "cpu_time": 51.031848969179904, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/15/2", "iterations": 8114437, "real_time": 91.33802516920845, "cpu_time": 91.28926627935128, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/15/3", "iterations": 5584453, "real_time": 130.20937251867338, "cpu_time": 130.200934630483, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/15/4", "iterations": 4225191, "real_time": 167.7784750951636, "cpu_time": 167.76780031956187, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/15/8", "iterations": 2208097, "real_time": 314.20101474920136, "cpu_time": 314.17641525712475, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/15/12", "iterations": 1362239, "real_time": 471.934592999102, "cpu_time": 471.92966872919396, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/4/15/15", "iterations": 1013934, "real_time": 690.744626397405, "cpu_time": 690.6761189584317, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/1/1", "iterations": 35883083, "real_time": 19.658060568729933, "cpu_time": 19.65324439931763, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/1/2", "iterations": 24056388, "real_time": 29.5397966227179, "cpu_time": 29.535938645485025, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/1/3", "iterations": 18012176, "real_time": 37.624106551207404, "cpu_time": 37.62299457877833, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/1/4", "iterations": 14678345, "real_time": 45.91810091807816, "cpu_time": 45.91382747850593, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/1/8", "iterations": 7894084, "real_time": 89.4161504852266, "cpu_time": 89.4162007903609, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/1/12", "iterations": 5637614, "real_time": 126.66981333290803, "cpu_time": 126.65606407249378, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/1/15", "iterations": 3823507, "real_time": 187.3662096315493, "cpu_time": 187.30395942781817, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/2/1", "iterations": 24223046, "real_time": 28.497691000545316, "cpu_time": 28.491338372555116, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/2/2", "iterations": 15398423, "real_time": 45.79588779986548, "cpu_time": 45.79481937858277, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/2/3", "iterations": 11563176, "real_time": 62.808566696334694, "cpu_time": 62.80298769127104, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/2/4", "iterations": 9189970, "real_time": 79.18195804611466, "cpu_time": 79.17697228608651, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/2/8", "iterations": 5045518, "real_time": 141.16900465724606, "cpu_time": 141.1637021213732, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/2/12", "iterations": 3222317, "real_time": 216.23090867295952, "cpu_time": 216.1339185437043, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/2/15", "iterations": 2661132, "real_time": 257.6262751875314, "cpu_time": 257.6260779247309, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/3/1", "iterations": 21216879, "real_time": 31.72312864406536, "cpu_time": 31.721536423899945, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/3/2", "iterations": 12753475, "real_time": 50.89636408501422, "cpu_time": 50.893109525054015, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/3/3", "iterations": 10091108, "real_time": 72.62466302596921, "cpu_time": 72.61759560991611, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/3/4", "iterations": 7049416, "real_time": 91.07240372837474, "cpu_time": 91.06484849241389, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/3/8", "iterations": 4191818, "real_time": 171.44981293848105, "cpu_time": 171.4048653829872, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/3/12", "iterations": 2730067, "real_time": 258.22554940052413, "cpu_time": 258.21783860981856, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/3/15", "iterations": 2247191, "real_time": 320.2680537844319, "cpu_time": 320.21443660107224, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/4/1", "iterations": 18121429, "real_time": 38.54712224553353, "cpu_time": 38.52979806393881, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/4/2", "iterations": 11586335, "real_time": 60.85221668520172, "cpu_time": 60.84037791070456, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/4/3", "iterations": 7775445, "real_time": 83.48118506973684, "cpu_time": 83.47342177842908, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/4/4", "iterations": 5806575, "real_time": 109.83559688561705, "cpu_time": 109.80741659239386, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/4/8", "iterations": 3438553, "real_time": 210.37594070842158, "cpu_time": 210.31026713853169, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/4/12", "iterations": 2353234, "real_time": 303.5453550454598, "cpu_time": 303.5384496399492, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/4/15", "iterations": 1707234, "real_time": 412.12674833869283, "cpu_time": 412.0659499517819, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/8/1", "iterations": 13362349, "real_time": 52.982092895293206, "cpu_time": 52.980205800642366, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/8/2", "iterations": 8183210, "real_time": 86.80744035933353, "cpu_time": 86.79723482594034, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/8/3", "iterations": 5575956, "real_time": 120.95788793507765, "cpu_time": 120.92742482186435, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/8/4", "iterations": 4406784, "real_time": 160.9141916345332, "cpu_time": 160.9028261879893, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/8/8", "iterations": 2054986, "real_time": 324.1364223459572, "cpu_time": 324.0868794240019, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/8/12", "iterations": 1551202, "real_time": 443.036571677006, "cpu_time": 443.0054886468769, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/8/15", "iterations": 1191347, "real_time": 557.3665480852973, "cpu_time": 557.2742450352611, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/12/1", "iterations": 10931863, "real_time": 64.58630400278167, "cpu_time": 64.58322794568573, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/12/2", "iterations": 6480881, "real_time": 110.95950659776847, "cpu_time": 110.9134699433657, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/12/3", "iterations": 4402294, "real_time": 160.57101819994259, "cpu_time": 160.571056817198, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/12/4", "iterations": 3377482, "real_time": 212.60607102163365, "cpu_time": 212.59239871595997, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/12/8", "iterations": 1774438, "real_time": 406.20272672379355, "cpu_time": 406.17254589902825, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/12/12", "iterations": 1019947, "real_time": 691.9524965449832, "cpu_time": 691.883009607353, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/12/15", "iterations": 884553, "real_time": 758.3157300779525, "cpu_time": 758.2598216274052, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/15/1", "iterations": 9880168, "real_time": 70.87299801187136, "cpu_time": 70.8612444646686, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/15/2", "iterations": 5605516, "real_time": 130.12630738160823, "cpu_time": 130.10452561369684, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/15/3", "iterations": 3594887, "real_time": 187.27335103647246, "cpu_time": 187.2732021896575, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/15/4", "iterations": 2914275, "real_time": 250.0425152148694, "cpu_time": 250.01964468006324, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/15/8", "iterations": 1496673, "real_time": 459.0439975245002, "cpu_time": 459.04415994676094, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/15/12", "iterations": 1019264, "real_time": 692.6358970802343, "cpu_time": 692.5752307547401, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/6/15/15", "iterations": 703822, "real_time": 1027.412138392814, "cpu_time": 1026.9911994794356, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/1/1", "iterations": 28848967, "real_time": 24.540278929744005, "cpu_time": 24.540116115768196, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/1/2", "iterations": 16448953, "real_time": 41.712574414326596, "cpu_time": 41.70739620935218, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/1/3", "iterations": 14226312, "real_time": 49.027181393759875, "cpu_time": 49.02113773408125, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/1/4", "iterations": 11454941, "real_time": 59.97940198666364, "cpu_time": 59.9758654365848, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/1/8", "iterations": 6124502, "real_time": 116.6673618572873, "cpu_time": 116.63968760235049, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/1/12", "iterations": 3998583, "real_time": 167.6297020523875, "cpu_time": 167.5773642812978, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/1/15", "iterations": 2814308, "real_time": 249.54055349218976, "cpu_time": 249.52741490982092, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/2/1", "iterations": 18724488, "real_time": 35.9477515751645, "cpu_time": 35.94362633573708, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/2/2", "iterations": 11921590, "real_time": 59.207167255952854, "cpu_time": 59.207203066035085, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/2/3", "iterations": 8754158, "real_time": 82.11978067326183, "cpu_time": 82.10841065468692, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/2/4", "iterations": 6998810, "real_time": 102.14988562003887, "cpu_time": 102.14522183056609, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/2/8", "iterations": 3592562, "real_time": 194.37585320576306, "cpu_time": 194.3690881326563, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/2/12", "iterations": 2396431, "real_time": 280.26308038860464, "cpu_time": 280.25509601569246, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/2/15", "iterations": 2046951, "real_time": 351.1505844482723, "cpu_time": 351.0885214155089, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/3/1", "iterations": 16325388, "real_time": 39.79609574511122, "cpu_time": 39.79611388103171, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/3/2", "iterations": 10737679, "real_time": 65.90630871683184, "cpu_time": 65.90502472648092, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/3/3", "iterations": 7088320, "real_time": 93.00482864378753, "cpu_time": 92.97746151415653, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/3/4", "iterations": 5870267, "real_time": 120.0060395959379, "cpu_time": 120.00510368608558, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/3/8", "iterations": 3068143, "real_time": 236.26721927352122, "cpu_time": 236.23996665083703, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/3/12", "iterations": 2099580, "real_time": 335.83827954933315, "cpu_time": 335.81573457548717, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/3/15", "iterations": 1649656, "real_time": 424.2719894170351, "cpu_time": 424.24359987778223, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/4/1", "iterations": 15155944, "real_time": 46.88331482282913, "cpu_time": 46.86847615694623, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/4/2", "iterations": 8977697, "real_time": 78.23427165528497, "cpu_time": 78.22139686826429, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/4/3", "iterations": 6523279, "real_time": 107.60156249974737, "cpu_time": 107.59956764075524, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/4/4", "iterations": 4932391, "real_time": 146.28502665592592, "cpu_time": 146.24246131338307, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/4/8", "iterations": 2622518, "real_time": 270.04358482882026, "cpu_time": 270.027126601234, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/4/12", "iterations": 1776519, "real_time": 401.79950733230476, "cpu_time": 401.70074173144076, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/4/15", "iterations": 1306653, "real_time": 538.6278392865321, "cpu_time": 538.5262958107221, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/8/1", "iterations": 10522044, "real_time": 67.28194265498591, "cpu_time": 67.27932329497796, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/8/2", "iterations": 5683202, "real_time": 117.14747144106124, "cpu_time": 117.12921694495108, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/8/3", "iterations": 4012588, "real_time": 170.86801410540352, "cpu_time": 170.8284030157015, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/8/4", "iterations": 3210391, "real_time": 217.69298412875284, "cpu_time": 217.64763232889757, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/8/8", "iterations": 1386276, "real_time": 422.9821933109485, "cpu_time": 422.9698847848704, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/8/12", "iterations": 1229796, "real_time": 582.7028791240448, "cpu_time": 582.6844452250732, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/8/15", "iterations": 913278, "real_time": 722.9590333169817, "cpu_time": 722.6693296016911, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/12/1", "iterations": 8666906, "real_time": 82.82508347921514, "cpu_time": 82.81329000222279, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/12/2", "iterations": 4787962, "real_time": 158.93517973517132, "cpu_time": 158.90017506404547, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/12/3", "iterations": 3376993, "real_time": 212.0949353445854, "cpu_time": 212.08572241637498, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/12/4", "iterations": 2528052, "real_time": 283.61835277337747, "cpu_time": 283.56220520780306, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/12/8", "iterations": 1365161, "real_time": 515.6323246406517, "cpu_time": 515.6124442464964, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/12/12", "iterations": 774816, "real_time": 919.8208709045532, "cpu_time": 919.7693387850126, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/12/15", "iterations": 644128, "real_time": 1033.5456059649105, "cpu_time": 1033.429690993183, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/15/1", "iterations": 6749330, "real_time": 93.38964667345176, "cpu_time": 93.38571384122568, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/15/2", "iterations": 4076855, "real_time": 177.6880058406956, "cpu_time": 177.66268361275348, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/15/3", "iterations": 2646983, "real_time": 246.29957616369117, "cpu_time": 246.29398828781095, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/15/4", "iterations": 2202297, "real_time": 321.36069111042735, "cpu_time": 321.3517522840993, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/15/8", "iterations": 1097884, "real_time": 618.4788028425553, "cpu_time": 618.4524048077952, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/15/12", "iterations": 782910, "real_time": 909.1859856357228, "cpu_time": 908.9627160209242, "time_unit": "ns"}, {"name": "BM_MatrixMatrixMultiplyDynamic/8/15/15", "iterations": 527999, "real_time": 1386.975668682548, "cpu_time": 1386.4761107502002, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/1/1", "iterations": 71473060, "real_time": 10.218954287241552, "cpu_time": 10.190986645876869, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/1/2", "iterations": 56615982, "real_time": 11.715988235202534, "cpu_time": 11.687053312967441, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/1/3", "iterations": 41964917, "real_time": 15.662750051657548, "cpu_time": 15.6051303520978, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/1/4", "iterations": 43787767, "real_time": 16.259733979111644, "cpu_time": 16.25851347934711, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/1/6", "iterations": 36258346, "real_time": 19.466160398137074, "cpu_time": 19.460291983534194, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/1/8", "iterations": 29992202, "real_time": 23.46092654227273, "cpu_time": 23.460664875490462, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/2/1", "iterations": 64820817, "real_time": 11.30683139917828, "cpu_time": 11.305241647910986, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/2/2", "iterations": 42461300, "real_time": 16.090760857123335, "cpu_time": 16.089709924096166, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/2/3", "iterations": 34222131, "real_time": 20.463437795226742, "cpu_time": 20.461291554286287, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/2/4", "iterations": 28672426, "real_time": 24.036501725380685, "cpu_time": 24.036508107125368, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/2/6", "iterations": 22107753, "real_time": 32.36610599769808, "cpu_time": 32.339243160535105, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/2/8", "iterations": 17977851, "real_time": 38.30300050929419, "cpu_time": 38.302408891917274, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/3/1", "iterations": 47772113, "real_time": 15.04397211658807, "cpu_time": 15.041369428227512, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/3/2", "iterations": 34204071, "real_time": 20.490502315173504, "cpu_time": 20.488379877355843, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/3/3", "iterations": 24970214, "real_time": 27.615499291093524, "cpu_time": 27.611537490226628, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/3/4", "iterations": 21303857, "real_time": 32.18643431284643, "cpu_time": 32.18642520929471, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/3/6", "iterations": 15983487, "real_time": 43.9502749335588, "cpu_time": 43.94429075457319, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/3/8", "iterations": 13054830, "real_time": 53.51984237994798, "cpu_time": 53.51904237742041, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/4/1", "iterations": 38605567, "real_time": 17.140124584280613, "cpu_time": 17.139237975704773, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/4/2", "iterations": 24963892, "real_time": 26.611483778803432, "cpu_time": 26.610874618428802, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/4/3", "iterations": 20679041, "real_time": 33.41086416912856, "cpu_time": 33.41083370355468, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/4/4", "iterations": 16227632, "real_time": 41.82773728276579, "cpu_time": 41.81460363409571, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/4/6", "iterations": 12500000, "real_time": 55.14466775581241, "cpu_time": 55.142799999998715, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/4/8", "iterations": 10199175, "real_time": 71.09347903074051, "cpu_time": 71.08369059262493, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/8/1", "iterations": 23249326, "real_time": 29.23117650775301, "cpu_time": 29.228202142289398, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/8/2", "iterations": 15252472, "real_time": 45.1190203119924, "cpu_time": 45.11648996962465, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/8/3", "iterations": 11697861, "real_time": 59.49490380897046, "cpu_time": 59.49497946676409, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/8/4", "iterations": 9670378, "real_time": 75.50663190185617, "cpu_time": 75.4653023904557, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/8/6", "iterations": 6613757, "real_time": 106.50588401303291, "cpu_time": 106.50482018011422, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/8/8", "iterations": 5224816, "real_time": 134.7627675467789, "cpu_time": 134.7501998156505, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/12/1", "iterations": 16780897, "real_time": 39.5356941294348, "cpu_time": 39.5325708750808, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/12/2", "iterations": 11076475, "real_time": 62.615060207266964, "cpu_time": 62.613782814477034, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/12/3", "iterations": 7716475, "real_time": 93.23978564828977, "cpu_time": 93.2305230043558, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/12/4", "iterations": 6209031, "real_time": 112.62584290792275, "cpu_time": 112.60195028821359, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/12/6", "iterations": 4598547, "real_time": 156.99447042687075, "cpu_time": 156.98610887308206, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/12/8", "iterations": 3632307, "real_time": 195.46146239308518, "cpu_time": 195.45126554555083, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/15/1", "iterations": 14862532, "real_time": 48.93348199235388, "cpu_time": 48.92544554319409, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/15/2", "iterations": 9272751, "real_time": 75.06041444091942, "cpu_time": 75.05830793903924, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/15/3", "iterations": 6480341, "real_time": 109.79939651246349, "cpu_time": 109.79591969003172, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/15/4", "iterations": 4876180, "real_time": 137.06889409129832, "cpu_time": 137.0183217190566, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/15/6", "iterations": 3760125, "real_time": 186.51585653560488, "cpu_time": 186.50789534924448, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/1/15/8", "iterations": 2847137, "real_time": 240.58243597574796, "cpu_time": 240.55217574707797, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/1/1", "iterations": 68775115, "real_time": 10.161083190702918, "cpu_time": 10.160099332440879, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/1/2", "iterations": 49986789, "real_time": 14.43914673022805, "cpu_time": 14.437994806987415, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/1/3", "iterations": 39209975, "real_time": 17.90708785144897, "cpu_time": 17.90518866691479, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/1/4", "iterations": 33433954, "real_time": 21.57903962102919, "cpu_time": 21.575940434684497, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/1/6", "iterations": 23979419, "real_time": 29.08075562354776, "cpu_time": 29.078352565590922, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/1/8", "iterations": 18695882, "real_time": 37.289310238280216, "cpu_time": 37.28596489857995, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/2/1", "iterations": 46248885, "real_time": 15.789034589756211, "cpu_time": 15.788380627987841, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/2/2", "iterations": 31242050, "real_time": 23.16651599449695, "cpu_time": 23.1604200108517, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/2/3", "iterations": 23434728, "real_time": 30.07351452825994, "cpu_time": 30.0704151548064, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/2/4", "iterations": 18801991, "real_time": 38.63437143571334, "cpu_time": 38.63149386679461, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/2/6", "iterations": 13261846, "real_time": 53.97414998313208, "cpu_time": 53.973104498422586, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/2/8", "iterations": 10277341, "real_time": 68.221806885821, "cpu_time": 68.22182897307339, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/3/1", "iterations": 36620838, "real_time": 19.74295678082599, "cpu_time": 19.73851608747893, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/3/2", "iterations": 22380520, "real_time": 30.584310238259096, "cpu_time": 30.58146102056966, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/3/3", "iterations": 15505215, "real_time": 43.84281037251455, "cpu_time": 43.827125260759594, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/3/4", "iterations": 13073116, "real_time": 54.02779719875876, "cpu_time": 54.026599320315746, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/3/6", "iterations": 9122780, "real_time": 80.54020638453646, "cpu_time": 80.53093464930693, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/3/8", "iterations": 6801796, "real_time": 101.12268436776978, "cpu_time": 101.12270347419975, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/4/1", "iterations": 28346967, "real_time": 24.199674908345415, "cpu_time": 24.198109095763538, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/4/2", "iterations": 17579106, "real_time": 39.392355676616084, "cpu_time": 39.39233314822955, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/4/3", "iterations": 12823096, "real_time": 55.22982959767776, "cpu_time": 55.228940031327966, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/4/4", "iterations": 9197335, "real_time": 71.93919663155684, "cpu_time": 71.90093652127857, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/4/6", "iterations": 6933302, "real_time": 101.01546536387012, "cpu_time": 101.0156488207229, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/4/8", "iterations": 4975301, "real_time": 137.4660933977596, "cpu_time": 137.45037737415063, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/8/1", "iterations": 16063741, "real_time": 43.75460492519308, "cpu_time": 43.75107890497049, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/8/2", "iterations": 9354662, "real_time": 76.2604481062301, "cpu_time": 76.24529886808973, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/8/3", "iterations": 6419133, "real_time": 105.08850618775932, "cpu_time": 105.08864670665862, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/8/4", "iterations": 5161975, "real_time": 136.53278172737677, "cpu_time": 136.49988618696295, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/8/6", "iterations": 3333905, "real_time": 200.03050893993625, "cpu_time": 200.02939495876288, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/8/8", "iterations": 2567385, "real_time": 268.70413744843034, "cpu_time": 268.69674785822286, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/12/1", "iterations": 11365666, "real_time": 64.04714619839007, "cpu_time": 64.0306516133727, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/12/2", "iterations": 6117651, "real_time": 110.35232166828166, "cpu_time": 110.34970775546375, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/12/3", "iterations": 4512461, "real_time": 159.34719212622446, "cpu_time": 159.33079532431327, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/12/4", "iterations": 3394713, "real_time": 202.147998674616, "cpu_time": 202.0966131746791, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/12/6", "iterations": 2270648, "real_time": 312.33783523621946, "cpu_time": 312.29102881643064, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/12/8", "iterations": 1759811, "real_time": 396.04278754467276, "cpu_time": 396.0408248385751, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/15/1", "iterations": 8687343, "real_time": 76.98303923179209, "cpu_time": 76.9814199807709, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/15/2", "iterations": 4955963, "real_time": 137.00803639520927, "cpu_time": 137.00808500788966, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/15/3", "iterations": 3639010, "real_time": 192.8831446182899, "cpu_time": 192.88350402994513, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/15/4", "iterations": 2613725, "real_time": 262.84468681672615, "cpu_time": 262.7173861060542, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/15/6", "iterations": 1863660, "real_time": 377.2808522199571, "cpu_time": 377.274288228528, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/2/15/8", "iterations": 1000000, "real_time": 508.628211915493, "cpu_time": 508.4550000000263, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/1/1", "iterations": 64992944, "real_time": 10.316672268313182, "cpu_time": 10.316150627058047, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/1/2", "iterations": 42592807, "real_time": 16.668357031118255, "cpu_time": 16.66736827183135, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/1/3", "iterations": 35216936, "real_time": 19.41224350349275, "cpu_time": 19.41179664238845, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/1/4", "iterations": 29286742, "real_time": 23.951316948384065, "cpu_time": 23.944281682133134, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/1/6", "iterations": 20663231, "real_time": 32.76959977590285, "cpu_time": 32.76578575732014, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/1/8", "iterations": 17026825, "real_time": 42.38616694774829, "cpu_time": 42.37566310806569, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/2/1", "iterations": 43096283, "real_time": 15.777518098728892, "cpu_time": 15.777532368627053, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/2/2", "iterations": 28043074, "real_time": 24.808773925286083, "cpu_time": 24.808763832383935, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/2/3", "iterations": 20537676, "real_time": 32.38373859658771, "cpu_time": 32.38316740414065, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/2/4", "iterations": 16883825, "real_time": 41.009372165414604, "cpu_time": 41.008065411719905, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/2/6", "iterations": 11024664, "real_time": 58.886719720938295, "cpu_time": 58.87635215004641, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/2/8", "iterations": 9310368, "real_time": 75.76762615643703, "cpu_time": 75.7617744003262, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/3/1", "iterations": 33339525, "real_time": 22.024360966450818, "cpu_time": 22.020649664324235, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/3/2", "iterations": 20745351, "real_time": 33.64068899021832, "cpu_time": 33.64074196671703, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/3/3", "iterations": 15184579, "real_time": 47.36930599244886, "cpu_time": 47.351921972943934, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/3/4", "iterations": 11774006, "real_time": 58.98410745660374, "cpu_time": 58.98145457034952, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/3/6", "iterations": 8160791, "real_time": 84.32856925094322, "cpu_time": 84.31731678950891, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/3/8", "iterations": 6131476, "real_time": 109.72331784711314, "cpu_time": 109.71143000478807, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/4/1", "iterations": 27312916, "real_time": 25.933041459098458, "cpu_time": 25.931614185758573, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/4/2", "iterations": 15306067, "real_time": 43.585445437467484, "cpu_time": 43.584808559901866, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/4/3", "iterations": 11960496, "real_time": 59.21349883815531, "cpu_time": 59.19913354763981, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/4/4", "iterations": 9180809, "real_time": 76.25423708233008, "cpu_time": 76.23925081111857, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/4/6", "iterations": 6431459, "real_time": 109.85451155096177, "cpu_time": 109.85190141149032, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/4/8", "iterations": 4887415, "real_time": 144.1938257697649, "cpu_time": 144.1768296737662, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/8/1", "iterations": 13676416, "real_time": 47.89057111414578, "cpu_time": 47.883305099816475, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/8/2", "iterations": 8807801, "real_time": 80.38217733484684, "cpu_time": 80.37783778266306, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/8/3", "iterations": 5998183, "real_time": 116.87517603400644, "cpu_time": 116.86455715005215, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/8/4", "iterations": 4804755, "real_time": 147.30310453699278, "cpu_time": 147.2997062285063, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/8/6", "iterations": 3168984, "real_time": 212.50873814887458, "cpu_time": 212.50880408357332, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/8/8", "iterations": 2316745, "real_time": 290.31885426550616, "cpu_time": 290.2904721926657, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/12/1", "iterations": 10610884, "real_time": 67.0917015077069, "cpu_time": 67.09139408177647, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/12/2", "iterations": 6071698, "real_time": 119.87622769820872, "cpu_time": 119.85032852425132, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/12/3", "iterations": 4199286, "real_time": 170.22283193257698, "cpu_time": 170.22036603364435, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/12/4", "iterations": 3223341, "real_time": 220.8422075067307, "cpu_time": 220.83049854173322, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/12/6", "iterations": 2110258, "real_time": 324.8683743945497, "cpu_time": 324.86785975931156, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/12/8", "iterations": 1658882, "real_time": 432.48637153984356, "cpu_time": 432.3743340394063, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/15/1", "iterations": 8549096, "real_time": 81.75523224532061, "cpu_time": 81.75531073694907, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/15/2", "iterations": 4906771, "real_time": 142.190381206794, "cpu_time": 142.19045478177148, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/15/3", "iterations": 3242032, "real_time": 212.43927882894664, "cpu_time": 212.3686626165199, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/15/4", "iterations": 2557479, "real_time": 281.9544031385574, "cpu_time": 281.7743567004942, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/15/6", "iterations": 1664534, "real_time": 405.40242255935544, "cpu_time": 405.3212490703382, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/3/15/8", "iterations": 1341844, "real_time": 524.4409029810303, "cpu_time": 524.3769022330669, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/1/1", "iterations": 62403609, "real_time": 11.623122679202432, "cpu_time": 11.620882375569181, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/1/2", "iterations": 39838824, "real_time": 17.179659544895046, "cpu_time": 17.17964867637507, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/1/3", "iterations": 32766167, "real_time": 21.61555625513144, "cpu_time": 21.606860515603756, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/1/4", "iterations": 24391349, "real_time": 27.07169016948155, "cpu_time": 27.070335470169983, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/1/6", "iterations": 18509753, "real_time": 36.61927163081414, "cpu_time": 36.61745243169827, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/1/8", "iterations": 14812088, "real_time": 47.17137435063184, "cpu_time": 47.16748914805185, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/2/1", "iterations": 36807816, "real_time": 17.98749151642269, "cpu_time": 17.98748396264541, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/2/2", "iterations": 23621436, "real_time": 28.24310626428628, "cpu_time": 28.235285949593514, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/2/3", "iterations": 18059529, "real_time": 37.942342518010385, "cpu_time": 37.9409119695184, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/2/4", "iterations": 13767061, "real_time": 48.099409165246634, "cpu_time": 48.08520860044115, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/2/6", "iterations": 10139637, "real_time": 66.95287927690715, "cpu_time": 66.95180507941508, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/2/8", "iterations": 8250728, "real_time": 88.17015553524544, "cpu_time": 88.151372824304, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/3/1", "iterations": 29278168, "real_time": 23.159023406592443, "cpu_time": 23.15899683340959, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/3/2", "iterations": 17631620, "real_time": 39.443389771288025, "cpu_time": 39.430863414704994, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/3/3", "iterations": 12529758, "real_time": 54.492783743921215, "cpu_time": 54.488362823931574, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/3/4", "iterations": 10417752, "real_time": 66.57656556574008, "cpu_time": 66.57664724597558, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/3/6", "iterations": 7166110, "real_time": 99.97644174242546, "cpu_time": 99.96469493211991, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/3/8", "iterations": 5464481, "real_time": 128.76205205738952, "cpu_time": 128.75696703859336, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/4/1", "iterations": 23519688, "real_time": 30.34557524404691, "cpu_time": 30.342069163502853, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/4/2", "iterations": 14302118, "real_time": 49.426612122235376, "cpu_time": 49.41491882530776, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/4/3", "iterations": 10335459, "real_time": 72.29435596283466, "cpu_time": 72.12287330441895, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/4/4", "iterations": 6493205, "real_time": 99.75597597682473, "cpu_time": 99.20185794226397, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/4/6", "iterations": 4564305, "real_time": 144.52828283853472, "cpu_time": 143.8643999469753, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/4/8", "iterations": 3651520, "real_time": 196.01295598578233, "cpu_time": 194.72247173779311, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/8/1", "iterations": 11624238, "real_time": 60.146263001210585, "cpu_time": 60.092627146829116, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/8/2", "iterations": 6388727, "real_time": 112.15399483707569, "cpu_time": 111.42470166592707, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/8/3", "iterations": 4873871, "real_time": 165.551714444201, "cpu_time": 163.84717609472713, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/8/4", "iterations": 3594075, "real_time": 209.1973464694693, "cpu_time": 202.88057427848855, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/8/6", "iterations": 2429594, "real_time": 285.68016052816324, "cpu_time": 281.0103251819116, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/8/8", "iterations": 1837989, "real_time": 367.25845149562946, "cpu_time": 366.11372538137834, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/12/1", "iterations": 8204695, "real_time": 81.42465527615508, "cpu_time": 81.3559797164935, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/12/2", "iterations": 4796985, "real_time": 147.69030756716896, "cpu_time": 147.44532242648097, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/12/3", "iterations": 3062104, "real_time": 217.5030390676546, "cpu_time": 216.93645937564966, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/12/4", "iterations": 2520624, "real_time": 285.51301858828515, "cpu_time": 283.71427075199244, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/12/6", "iterations": 1757372, "real_time": 398.24948044242757, "cpu_time": 398.03126486596364, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/12/8", "iterations": 1000000, "real_time": 529.0592790115625, "cpu_time": 528.8950000000341, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/15/1", "iterations": 7131796, "real_time": 97.81037917024895, "cpu_time": 97.78266231955993, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/15/2", "iterations": 4083847, "real_time": 173.54053151036834, "cpu_time": 173.44307952771672, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/15/3", "iterations": 2619378, "real_time": 274.2547123865984, "cpu_time": 274.1074407741012, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/15/4", "iterations": 1974891, "real_time": 339.9159877357707, "cpu_time": 339.65165672437047, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/15/6", "iterations": 1448622, "real_time": 474.4825800417887, "cpu_time": 474.4067120339476, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/4/15/8", "iterations": 1077006, "real_time": 606.6167458407382, "cpu_time": 606.5128699375688, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/1/1", "iterations": 51451294, "real_time": 13.556582503605545, "cpu_time": 13.554275233581883, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/1/2", "iterations": 34442208, "real_time": 20.249190380766187, "cpu_time": 20.235955836513707, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/1/3", "iterations": 27289172, "real_time": 26.55124347324344, "cpu_time": 26.54763581687324, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/1/4", "iterations": 21995218, "real_time": 33.02767028710629, "cpu_time": 33.01794962887051, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/1/6", "iterations": 15328322, "real_time": 45.239383604584916, "cpu_time": 45.235805980591, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/1/8", "iterations": 12413989, "real_time": 58.47664622695791, "cpu_time": 58.47056896860705, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/2/1", "iterations": 32691338, "real_time": 21.08927789841522, "cpu_time": 21.08114999759399, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/2/2", "iterations": 21468112, "real_time": 33.97360768999535, "cpu_time": 33.965119988195426, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/2/3", "iterations": 15165072, "real_time": 46.51600513711822, "cpu_time": 46.51128593388924, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/2/4", "iterations": 12315921, "real_time": 59.57895523889293, "cpu_time": 59.57110312740341, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/2/6", "iterations": 8621858, "real_time": 81.34120614392292, "cpu_time": 81.33919626141093, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/2/8", "iterations": 6631048, "real_time": 111.45818444051133, "cpu_time": 111.37621081917288, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/3/1", "iterations": 23903757, "real_time": 28.76352684120826, "cpu_time": 28.741716208041286, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/3/2", "iterations": 14966123, "real_time": 47.20106209768518, "cpu_time": 47.19171424690282, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/3/3", "iterations": 10801136, "real_time": 67.18594034881055, "cpu_time": 67.16997175112297, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/3/4", "iterations": 8389362, "real_time": 83.36455335170469, "cpu_time": 83.36390776794835, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/3/6", "iterations": 5463884, "real_time": 127.65818125495605, "cpu_time": 127.63740957896786, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/3/8", "iterations": 4501260, "real_time": 157.2064153032747, "cpu_time": 157.20176128461773, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/4/1", "iterations": 19483845, "real_time": 36.36607450996497, "cpu_time": 36.34328850388535, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/4/2", "iterations": 11406782, "real_time": 60.1863396672393, "cpu_time": 60.18296834286673, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/4/3", "iterations": 8338197, "real_time": 85.3516573190806, "cpu_time": 85.34063179365417, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/4/4", "iterations": 6058246, "real_time": 109.23495844860254, "cpu_time": 109.23343159058757, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/4/6", "iterations": 4363056, "real_time": 162.83051558762142, "cpu_time": 162.8299063775518, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/4/8", "iterations": 3260667, "real_time": 222.0768072596347, "cpu_time": 222.03064587705936, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/8/1", "iterations": 11241368, "real_time": 63.15152203222859, "cpu_time": 63.13528744900474, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/8/2", "iterations": 6031415, "real_time": 113.61559816589077, "cpu_time": 113.587275954306, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/8/3", "iterations": 4288112, "real_time": 160.79261434146306, "cpu_time": 160.78684511972247, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/8/4", "iterations": 3241987, "real_time": 219.23580079367372, "cpu_time": 219.19489498261038, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/8/6", "iterations": 2082937, "real_time": 322.17429718655586, "cpu_time": 322.1768109164972, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/8/8", "iterations": 1690944, "real_time": 428.5811818449001, "cpu_time": 428.5665285189987, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/12/1", "iterations": 7393091, "real_time": 92.6985839962116, "cpu_time": 92.66624744642607, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/12/2", "iterations": 4260836, "real_time": 168.70986257641803, "cpu_time": 168.6981146423042, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/12/3", "iterations": 2726122, "real_time": 247.84465480026338, "cpu_time": 247.81356080175183, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/12/4", "iterations": 2133932, "real_time": 324.7126815547824, "cpu_time": 324.67716871952445, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/12/6", "iterations": 1448394, "real_time": 478.517934361904, "cpu_time": 478.46511377426896, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/12/8", "iterations": 1137120, "real_time": 610.8055560874767, "cpu_time": 610.8053679470678, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/15/1", "iterations": 6325054, "real_time": 116.04534015976786, "cpu_time": 115.99711243572453, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/15/2", "iterations": 3266251, "real_time": 218.90909332904664, "cpu_time": 218.88091270389398, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/15/3", "iterations": 2340417, "real_time": 306.51368499006946, "cpu_time": 306.4257352429184, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/15/4", "iterations": 1737520, "real_time": 399.36560269846456, "cpu_time": 399.2863391500726, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/15/6", "iterations": 1217984, "real_time": 594.3945305820162, "cpu_time": 594.3181519625683, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/6/15/8", "iterations": 931830, "real_time": 758.7766984263296, "cpu_time": 758.7381818571582, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/1/1", "iterations": 46516573, "real_time": 14.977390101744058, "cpu_time": 14.972341148175227, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/1/2", "iterations": 29132194, "real_time": 22.45975273919755, "cpu_time": 22.45773181381423, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/1/3", "iterations": 23066226, "real_time": 30.23069755425687, "cpu_time": 30.22206580304706, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/1/4", "iterations": 18471704, "real_time": 36.37569939619166, "cpu_time": 36.37422947011325, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/1/6", "iterations": 10000000, "real_time": 50.86851139785722, "cpu_time": 50.868200000007846, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/1/8", "iterations": 10894094, "real_time": 66.18520502344889, "cpu_time": 66.17163391467179, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/2/1", "iterations": 29674848, "real_time": 24.224997445737838, "cpu_time": 24.21680474993566, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/2/2", "iterations": 18571827, "real_time": 38.72032568635423, "cpu_time": 38.71062335439324, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/2/3", "iterations": 13801806, "real_time": 51.563614210871776, "cpu_time": 51.561730399628566, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/2/4", "iterations": 10916520, "real_time": 65.20577463566383, "cpu_time": 65.19852480460447, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/2/6", "iterations": 7301783, "real_time": 95.04817563476168, "cpu_time": 95.02062715367924, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/2/8", "iterations": 5840975, "real_time": 129.15930182068388, "cpu_time": 129.0020929724979, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/3/1", "iterations": 21209486, "real_time": 37.087220640826175, "cpu_time": 36.95756700563262, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/3/2", "iterations": 9589435, "real_time": 54.34541462066108, "cpu_time": 54.34011492856, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/3/3", "iterations": 9362920, "real_time": 78.29185136082141, "cpu_time": 78.26084170322433, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/3/4", "iterations": 7038359, "real_time": 96.07080684765545, "cpu_time": 96.06543229750352, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/3/6", "iterations": 5137238, "real_time": 143.27072583245283, "cpu_time": 143.20730322402366, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/3/8", "iterations": 3697092, "real_time": 193.92137144099195, "cpu_time": 193.82585015465978, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/4/1", "iterations": 16382623, "real_time": 43.23841798527967, "cpu_time": 43.19259498311066, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/4/2", "iterations": 10265284, "real_time": 73.80550883985127, "cpu_time": 73.51360176688729, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/4/3", "iterations": 6711088, "real_time": 97.47241029977442, "cpu_time": 97.3907658489932, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/4/4", "iterations": 5544906, "real_time": 128.65057497267418, "cpu_time": 128.6108366850624, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/4/6", "iterations": 3905509, "real_time": 185.42579443530406, "cpu_time": 185.400673766212, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/4/8", "iterations": 2729811, "real_time": 260.29918041362305, "cpu_time": 260.25904357483864, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/8/1", "iterations": 8843744, "real_time": 75.6174998903689, "cpu_time": 75.58382513107823, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/8/2", "iterations": 5366041, "real_time": 131.94637276852586, "cpu_time": 131.92892115434097, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/8/3", "iterations": 3604866, "real_time": 195.50793538299345, "cpu_time": 195.49436789050722, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/8/4", "iterations": 2839284, "real_time": 251.75813584366495, "cpu_time": 251.72261739227918, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/8/6", "iterations": 1987287, "real_time": 360.7991346753268, "cpu_time": 360.75916563636036, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/8/8", "iterations": 1484979, "real_time": 483.2974001917476, "cpu_time": 483.25329853153823, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/12/1", "iterations": 6513627, "real_time": 107.58186768089902, "cpu_time": 107.52427180739905, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/12/2", "iterations": 3404090, "real_time": 199.45774643077795, "cpu_time": 199.41834675345973, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/12/3", "iterations": 2538715, "real_time": 280.334303403461, "cpu_time": 280.2898316667998, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/12/4", "iterations": 1962808, "real_time": 361.3144403381562, "cpu_time": 361.30380556836366, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/12/6", "iterations": 1330849, "real_time": 536.9671645474955, "cpu_time": 536.8001929595408, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/12/8", "iterations": 938841, "real_time": 703.5419064035432, "cpu_time": 703.5184871559471, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/15/1", "iterations": 5313496, "real_time": 129.0890645421061, "cpu_time": 129.0476176137165, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/15/2", "iterations": 2750783, "real_time": 237.88409413018474, "cpu_time": 237.87990546689804, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/15/3", "iterations": 2038748, "real_time": 339.9723334881017, "cpu_time": 339.94245487917595, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/15/4", "iterations": 1511667, "real_time": 444.31510644682237, "cpu_time": 444.31280169507704, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/15/6", "iterations": 1036361, "real_time": 652.0114612510146, "cpu_time": 651.9909568190566, "time_unit": "ns"}, {"name": "BM_MatrixTransposeMatrixMultiplyDynamic/8/15/8", "iterations": 799114, "real_time": 893.3304421894369, "cpu_time": 893.1141238921609, "time_unit": "ns"}]}