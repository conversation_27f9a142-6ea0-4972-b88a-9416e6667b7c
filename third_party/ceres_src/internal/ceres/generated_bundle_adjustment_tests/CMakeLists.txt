# Ceres Solver - A fast non-linear least squares minimizer
# Copyright 2022 Google Inc. All rights reserved.
# http://ceres-solver.org/
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#
# * Redistributions of source code must retain the above copyright notice,
#   this list of conditions and the following disclaimer.
# * Redistributions in binary form must reproduce the above copyright notice,
#   this list of conditions and the following disclaimer in the documentation
#   and/or other materials provided with the distribution.
# * Neither the name of Google Inc. nor the names of its contributors may be
#   used to endorse or promote products derived from this software without
#   specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
# AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
# IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
# ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE
# LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
# CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
# SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
# INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
# CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
# ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
# POSSIBILITY OF SUCH DAMAGE.
#
# ========================================
# THIS FILE IS AUTOGENERATED. DO NOT EDIT.
# THIS FILE IS AUTOGENERATED. DO NOT EDIT.
# THIS FILE IS AUTOGENERATED. DO NOT EDIT.
# THIS FILE IS AUTOGENERATED. DO NOT EDIT.
# ========================================
#
# This file is generated using generate_bundle_adjustment_tests.py.

ceres_test(ba_denseschur_eigen_auto)
ceres_test(ba_denseschur_lapack_auto)
ceres_test(ba_denseschur_cuda_auto)
ceres_test(ba_sparsecholesky_suitesparse_auto)
ceres_test(ba_sparsecholesky_eigensparse_auto)
ceres_test(ba_sparsecholesky_cxsparse_auto)
ceres_test(ba_sparsecholesky_acceleratesparse_auto)
ceres_test(ba_sparseschur_suitesparse_auto)
ceres_test(ba_sparseschur_eigensparse_auto)
ceres_test(ba_sparseschur_cxsparse_auto)
ceres_test(ba_sparseschur_acceleratesparse_auto)
ceres_test(ba_iterschur_jacobi_auto)
ceres_test(ba_iterschur_schurjacobi_auto)
ceres_test(ba_iterschur_suitesparse_clustjacobi_auto)
ceres_test(ba_iterschur_eigensparse_clustjacobi_auto)
ceres_test(ba_iterschur_cxsparse_clustjacobi_auto)
ceres_test(ba_iterschur_acceleratesparse_clustjacobi_auto)
ceres_test(ba_iterschur_suitesparse_clusttri_auto)
ceres_test(ba_iterschur_eigensparse_clusttri_auto)
ceres_test(ba_iterschur_cxsparse_clusttri_auto)
ceres_test(ba_iterschur_acceleratesparse_clusttri_auto)
ceres_test(ba_denseschur_eigen_auto_threads)
ceres_test(ba_denseschur_lapack_auto_threads)
ceres_test(ba_denseschur_cuda_auto_threads)
ceres_test(ba_sparsecholesky_suitesparse_auto_threads)
ceres_test(ba_sparsecholesky_eigensparse_auto_threads)
ceres_test(ba_sparsecholesky_cxsparse_auto_threads)
ceres_test(ba_sparsecholesky_acceleratesparse_auto_threads)
ceres_test(ba_sparseschur_suitesparse_auto_threads)
ceres_test(ba_sparseschur_eigensparse_auto_threads)
ceres_test(ba_sparseschur_cxsparse_auto_threads)
ceres_test(ba_sparseschur_acceleratesparse_auto_threads)
ceres_test(ba_iterschur_jacobi_auto_threads)
ceres_test(ba_iterschur_schurjacobi_auto_threads)
ceres_test(ba_iterschur_suitesparse_clustjacobi_auto_threads)
ceres_test(ba_iterschur_eigensparse_clustjacobi_auto_threads)
ceres_test(ba_iterschur_cxsparse_clustjacobi_auto_threads)
ceres_test(ba_iterschur_acceleratesparse_clustjacobi_auto_threads)
ceres_test(ba_iterschur_suitesparse_clusttri_auto_threads)
ceres_test(ba_iterschur_eigensparse_clusttri_auto_threads)
ceres_test(ba_iterschur_cxsparse_clusttri_auto_threads)
ceres_test(ba_iterschur_acceleratesparse_clusttri_auto_threads)
ceres_test(ba_denseschur_eigen_user)
ceres_test(ba_denseschur_lapack_user)
ceres_test(ba_denseschur_cuda_user)
ceres_test(ba_sparsecholesky_suitesparse_user)
ceres_test(ba_sparsecholesky_eigensparse_user)
ceres_test(ba_sparsecholesky_cxsparse_user)
ceres_test(ba_sparsecholesky_acceleratesparse_user)
ceres_test(ba_sparseschur_suitesparse_user)
ceres_test(ba_sparseschur_eigensparse_user)
ceres_test(ba_sparseschur_cxsparse_user)
ceres_test(ba_sparseschur_acceleratesparse_user)
ceres_test(ba_iterschur_jacobi_user)
ceres_test(ba_iterschur_schurjacobi_user)
ceres_test(ba_iterschur_suitesparse_clustjacobi_user)
ceres_test(ba_iterschur_eigensparse_clustjacobi_user)
ceres_test(ba_iterschur_cxsparse_clustjacobi_user)
ceres_test(ba_iterschur_acceleratesparse_clustjacobi_user)
ceres_test(ba_iterschur_suitesparse_clusttri_user)
ceres_test(ba_iterschur_eigensparse_clusttri_user)
ceres_test(ba_iterschur_cxsparse_clusttri_user)
ceres_test(ba_iterschur_acceleratesparse_clusttri_user)
ceres_test(ba_denseschur_eigen_user_threads)
ceres_test(ba_denseschur_lapack_user_threads)
ceres_test(ba_denseschur_cuda_user_threads)
ceres_test(ba_sparsecholesky_suitesparse_user_threads)
ceres_test(ba_sparsecholesky_eigensparse_user_threads)
ceres_test(ba_sparsecholesky_cxsparse_user_threads)
ceres_test(ba_sparsecholesky_acceleratesparse_user_threads)
ceres_test(ba_sparseschur_suitesparse_user_threads)
ceres_test(ba_sparseschur_eigensparse_user_threads)
ceres_test(ba_sparseschur_cxsparse_user_threads)
ceres_test(ba_sparseschur_acceleratesparse_user_threads)
ceres_test(ba_iterschur_jacobi_user_threads)
ceres_test(ba_iterschur_schurjacobi_user_threads)
ceres_test(ba_iterschur_suitesparse_clustjacobi_user_threads)
ceres_test(ba_iterschur_eigensparse_clustjacobi_user_threads)
ceres_test(ba_iterschur_cxsparse_clustjacobi_user_threads)
ceres_test(ba_iterschur_acceleratesparse_clustjacobi_user_threads)
ceres_test(ba_iterschur_suitesparse_clusttri_user_threads)
ceres_test(ba_iterschur_eigensparse_clusttri_user_threads)
ceres_test(ba_iterschur_cxsparse_clusttri_user_threads)
ceres_test(ba_iterschur_acceleratesparse_clusttri_user_threads)
