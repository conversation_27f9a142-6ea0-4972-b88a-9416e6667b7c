// Ceres Solver - A fast non-linear least squares minimizer
// Copyright 2015 Google Inc. All rights reserved.
// http://ceres-solver.org/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//
// * Redistributions of source code must retain the above copyright notice,
//   this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright notice,
//   this list of conditions and the following disclaimer in the documentation
//   and/or other materials provided with the distribution.
// * Neither the name of Google Inc. nor the names of its contributors may be
//   used to endorse or promote products derived from this software without
//   specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
// AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE
// LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.
//
// Author: <EMAIL> (Sameer Agarwal)

#ifndef CERES_INTERNAL_COVARIANCE_IMPL_H_
#define CERES_INTERNAL_COVARIANCE_IMPL_H_

#include <map>
#include <memory>
#include <set>
#include <utility>
#include <vector>

#include "ceres/covariance.h"
#include "ceres/internal/disable_warnings.h"
#include "ceres/internal/export.h"
#include "ceres/problem_impl.h"
#include "ceres/suitesparse.h"

namespace ceres {
namespace internal {

class CompressedRowSparseMatrix;

class CERES_NO_EXPORT CovarianceImpl {
 public:
  explicit CovarianceImpl(const Covariance::Options& options);
  ~CovarianceImpl();

  bool Compute(const std::vector<std::pair<const double*, const double*>>&
                   covariance_blocks,
               ProblemImpl* problem);

  bool Compute(const std::vector<const double*>& parameter_blocks,
               ProblemImpl* problem);

  bool GetCovarianceBlockInTangentOrAmbientSpace(
      const double* parameter_block1,
      const double* parameter_block2,
      bool lift_covariance_to_ambient_space,
      double* covariance_block) const;

  bool GetCovarianceMatrixInTangentOrAmbientSpace(
      const std::vector<const double*>& parameters,
      bool lift_covariance_to_ambient_space,
      double* covariance_matrix) const;

  bool ComputeCovarianceSparsity(
      const std::vector<std::pair<const double*, const double*>>&
          covariance_blocks,
      ProblemImpl* problem);

  bool ComputeCovarianceValues();
  bool ComputeCovarianceValuesUsingDenseSVD();
  bool ComputeCovarianceValuesUsingSuiteSparseQR();
  bool ComputeCovarianceValuesUsingEigenSparseQR();

  const CompressedRowSparseMatrix* covariance_matrix() const {
    return covariance_matrix_.get();
  }

 private:
  ProblemImpl* problem_;
  Covariance::Options options_;
  Problem::EvaluateOptions evaluate_options_;
  bool is_computed_;
  bool is_valid_;
  std::map<const double*, int> parameter_block_to_row_index_;
  std::set<const double*> constant_parameter_blocks_;
  std::unique_ptr<CompressedRowSparseMatrix> covariance_matrix_;
};

}  // namespace internal
}  // namespace ceres

#include "ceres/internal/reenable_warnings.h"

#endif  // CERES_INTERNAL_COVARIANCE_IMPL_H_
