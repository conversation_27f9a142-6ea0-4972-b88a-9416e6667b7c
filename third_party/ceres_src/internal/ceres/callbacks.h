// Ceres Solver - A fast non-linear least squares minimizer
// Copyright 2015 Google Inc. All rights reserved.
// http://ceres-solver.org/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//
// * Redistributions of source code must retain the above copyright notice,
//   this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright notice,
//   this list of conditions and the following disclaimer in the documentation
//   and/or other materials provided with the distribution.
// * Neither the name of Google Inc. nor the names of its contributors may be
//   used to endorse or promote products derived from this software without
//   specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
// AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE
// LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.
//
// Author: <EMAIL> (Sameer Agarwal)

#ifndef CERES_INTERNAL_CALLBACKS_H_
#define CERES_INTERNAL_CALLBACKS_H_

#include <string>

#include "ceres/internal/export.h"
#include "ceres/iteration_callback.h"

namespace ceres {
namespace internal {

class Program;

// Callback for updating the externally visible state of parameter
// blocks.
class CERES_NO_EXPORT StateUpdatingCallback final : public IterationCallback {
 public:
  StateUpdatingCallback(Program* program, double* parameters);
  ~StateUpdatingCallback() override;
  CallbackReturnType operator()(const IterationSummary& summary) final;

 private:
  Program* program_;
  double* parameters_;
};

// Callback for updating the externally visible state of the
// parameters vector for GradientProblemSolver.
class CERES_NO_EXPORT GradientProblemSolverStateUpdatingCallback final
    : public IterationCallback {
 public:
  GradientProblemSolverStateUpdatingCallback(int num_parameters,
                                             const double* internal_parameters,
                                             double* user_parameters);
  ~GradientProblemSolverStateUpdatingCallback() override;
  CallbackReturnType operator()(const IterationSummary& summary) final;

 private:
  int num_parameters_;
  const double* internal_parameters_;
  double* user_parameters_;
};

// Callback for logging the state of the minimizer to STDERR or
// STDOUT depending on the user's preferences and logging level.
class CERES_NO_EXPORT LoggingCallback final : public IterationCallback {
 public:
  LoggingCallback(MinimizerType minimizer_type, bool log_to_stdout);
  ~LoggingCallback() override;
  CallbackReturnType operator()(const IterationSummary& summary) final;

 private:
  const MinimizerType minimizer_type;
  const bool log_to_stdout_;
};

}  // namespace internal
}  // namespace ceres

#endif  // CERES_INTERNAL_CALLBACKS_H_
