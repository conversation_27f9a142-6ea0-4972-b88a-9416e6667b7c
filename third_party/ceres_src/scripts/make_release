#!/bin/bash
#
# Ceres Solver - A fast non-linear least squares minimizer
# Copyright 2015 Google Inc. All rights reserved.
# http://ceres-solver.org/
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#
# * Redistributions of source code must retain the above copyright notice,
#   this list of conditions and the following disclaimer.
# * Redistributions in binary form must reproduce the above copyright notice,
#   this list of conditions and the following disclaimer in the documentation
#   and/or other materials provided with the distribution.
# * Neither the name of Google Inc. nor the names of its contributors may be
#   used to endorse or promote products derived from this software without
#   specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
# AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
# IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
# ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE
# LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
# CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
# SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
# INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
# CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
# ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
# POSSIBILITY OF SUCH DAMAGE.
#
# Author: <EMAIL> (Keir Mierle)
#
# Note: You will need Sphinx and Pygments installed for this to work.

if [ -z $1 ] ; then
  echo 'usage: scripts/make_release <version>'
  echo '       must be run from toplevel Ceres source directory'
  exit 1
fi

TMP="/tmp/ceres-solver-$1"
DOCS_TMP="/tmp/ceres-solver-docs-$1"
VERSION=$(grep '#define CERES_VERSION_' include/ceres/version.h | \
          sed -e 's/#define CERES_VERSION_STRING.*$//' | \
          sed -e 's/\(^#define CERES_VERSION_[MR^S].[A-Z]*\) \([0-9]\)/\2/' | \
          tr '\n' '.' | \
          sed -e 's/..$//')
GIT_COMMIT=$(git log -1 HEAD |grep commit)

if [[ $1 != $VERSION ]] ; then
  echo "ERROR: Version from the command line $1 does not match CERES_VERSION"
  echo "       in include/ceres/version.h, which is $VERSION. You may not be in the "
  echo "       toplevel source dir."
  exit 1
fi

# Export repository.
git checkout-index -f -a --prefix=$TMP/

# Build the VERSION file.
VERSIONFILE=$TMP/VERSION
echo "version $VERSION" >> $VERSIONFILE
echo "$GIT_COMMIT" >> $VERSIONFILE

# Build the documentation.
python $TMP/scripts/make_docs.py $TMP $DOCS_TMP
cp -pr $DOCS_TMP/html $TMP/docs

# Build the tarball.
cd /tmp
tar -cvzf "ceres-solver-$1.tar.gz" "ceres-solver-$1"

# Don't leave a mess behind.
rm -rf $TMP
rm -rf $DOCS_TMP

# Reminder to upload.
cat <<EOF

TODO:
  - Upload /tmp/ceres-solver-$1.tar.gz
EOF
