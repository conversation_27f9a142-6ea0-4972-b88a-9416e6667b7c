# Ceres Solver - A fast non-linear least squares minimizer
# Copyright 2022 Google Inc. All rights reserved.
# http://ceres-solver.org/
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#
# * Redistributions of source code must retain the above copyright notice,
#   this list of conditions and the following disclaimer.
# * Redistributions in binary form must reproduce the above copyright notice,
#   this list of conditions and the following disclaimer in the documentation
#   and/or other materials provided with the distribution.
# * Neither the name of Google Inc. nor the names of its contributors may be
#   used to endorse or promote products derived from this software without
#   specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
# AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
# IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
# ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE
# LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
# CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
# SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
# INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
# CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
# ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
# POSSIBILITY OF SUCH DAMAGE.
#
# Authors: <AUTHORS>
#          <EMAIL> (Alex Stewart)

cmake_minimum_required(VERSION 3.10)

if (POLICY CMP0074) # Added in CMake 3.12
  # FindTBB.cmake uses TBB_ROOT in a way that is historical, but also compliant
  # with CMP0074 so suppress the legacy compatibility warning and allow its use.
  cmake_policy(SET CMP0074 NEW)
endif()

# On macOS, add the Homebrew prefix (with appropriate suffixes) to the
# respective HINTS directories (after any user-specified locations).  This
# handles Homebrew installations into non-standard locations (not /usr/local).
# We do not use CMAKE_PREFIX_PATH for this as given the search ordering of
# find_xxx(), doing so would override any user-specified HINTS locations with
# the Homebrew version if it exists.
if (CMAKE_SYSTEM_NAME MATCHES "Darwin")
  find_program(HOMEBREW_EXECUTABLE brew)
  mark_as_advanced(FORCE HOMEBREW_EXECUTABLE)
  if (HOMEBREW_EXECUTABLE)
    # Detected a Homebrew install, query for its install prefix.
    execute_process(COMMAND ${HOMEBREW_EXECUTABLE} --prefix
      OUTPUT_VARIABLE HOMEBREW_INSTALL_PREFIX
      OUTPUT_STRIP_TRAILING_WHITESPACE)
    message(STATUS "Detected Homebrew with install prefix: "
      "${HOMEBREW_INSTALL_PREFIX}, adding to CMake search paths.")
    list(APPEND CMAKE_PREFIX_PATH "${HOMEBREW_INSTALL_PREFIX}")
  endif()
endif()

project(Ceres C CXX)

# NOTE: The following CMake variables must be applied consistently to all
# targets in project to avoid visibility warnings by placing the variables at
# the project top.

# Always build position-independent code (PIC), even when building Ceres as a
# static library so that shared libraries can link against it, not just
# executables (PIC does not apply on Windows). Global variable can be overridden
# by the user whereas target properties can be not.
set(CMAKE_POSITION_INDEPENDENT_CODE ON)
# Set the default symbol visibility to hidden to unify the behavior among
# the various compilers and to get smaller binaries
set(CMAKE_C_VISIBILITY_PRESET hidden)
set(CMAKE_CXX_VISIBILITY_PRESET hidden)
set(CMAKE_VISIBILITY_INLINES_HIDDEN ON)

# NOTE: The 'generic' CMake variables CMAKE_[SOURCE/BINARY]_DIR should not be
#       used.  Always use the project-specific variants (generated by CMake):
#       <PROJECT_NAME_MATCHING_CASE>_[SOURCE/BINARY]_DIR, e.g.
#       Ceres_SOURCE_DIR (note, *not* CERES_SOURCE_DIR) instead, as these will
#       always point to the correct directories for the Ceres project, even if
#       it is nested inside another source tree, whereas the 'generic'
#       CMake variables refer to the *first* project() declaration, i.e. the
#       top-level project, not Ceres, if Ceres is nested.

# Make CMake aware of the cmake folder for local FindXXX scripts,
# append rather than set in case the user has passed their own
# additional paths via -D.
list(APPEND CMAKE_MODULE_PATH "${Ceres_SOURCE_DIR}/cmake")
include(AddCompileFlagsIfSupported)
include(CheckCXXCompilerFlag)
include(CheckLibraryExists)
include(GNUInstallDirs)
include(UpdateCacheVariable)

check_cxx_compiler_flag(/bigobj HAVE_BIGOBJ)
check_library_exists(m pow "" HAVE_LIBM)

# Xcode 11.0-1 with macOS 10.15 (Catalina) broke alignment.
include(DetectBrokenStackCheckMacOSXcodePairing)
detect_broken_stack_check_macos_xcode_pairing()

# Set up the git hook to make Gerrit Change-Id: lines in commit messages.
include(AddGerritCommitHook)
add_gerrit_commit_hook(${Ceres_SOURCE_DIR} ${Ceres_BINARY_DIR})

set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${Ceres_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${Ceres_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${Ceres_BINARY_DIR}/lib)
# Set postfixes for generated libraries based on buildtype.
set(CMAKE_RELEASE_POSTFIX "")
set(CMAKE_DEBUG_POSTFIX "-debug")

# Read the Ceres version from the source, such that we only ever have a single
# definition of the Ceres version.
include(ReadCeresVersionFromSource)
read_ceres_version_from_source(${Ceres_SOURCE_DIR})

#enable_testing()

include(CeresThreadingModels)
include(PrettyPrintCMakeList)
find_available_ceres_threading_models(CERES_THREADING_MODELS_AVAILABLE)
pretty_print_cmake_list(PRETTY_CERES_THREADING_MODELS_AVAILABLE
  ${CERES_THREADING_MODELS_AVAILABLE})
message("-- Detected available Ceres threading models: "
  "${PRETTY_CERES_THREADING_MODELS_AVAILABLE}")
set(CERES_THREADING_MODEL "${CERES_THREADING_MODEL}" CACHE STRING
  "Ceres threading back-end" FORCE)
if (NOT CERES_THREADING_MODEL)
  list(GET CERES_THREADING_MODELS_AVAILABLE 0 DEFAULT_THREADING_MODEL)
  update_cache_variable(CERES_THREADING_MODEL ${DEFAULT_THREADING_MODEL})
endif()
set_property(CACHE CERES_THREADING_MODEL PROPERTY STRINGS
  ${CERES_THREADING_MODELS_AVAILABLE})

option(MINIGLOG "Use a stripped down version of glog." ON)
option(GFLAGS "Enable Google Flags." OFF)
option(SUITESPARSE "Enable SuiteSparse." ON)
option(CXSPARSE "Enable CXSparse." ON)
if (APPLE)
  option(ACCELERATESPARSE
    "Enable use of sparse solvers in Apple's Accelerate framework." ON)
  option(ENABLE_BITCODE
    "Enable bitcode for iOS builds (disables inline optimizations for Eigen)." OFF)
endif()
option(CUDA "Enable use of CUDA linear algebra solvers." OFF)
option(LAPACK "Enable use of LAPACK directly within Ceres." ON)
# Template specializations for the Schur complement based solvers. If
# compile time, binary size or compiler performance is an issue, you
# may consider disabling this.
option(SCHUR_SPECIALIZATIONS "Enable fixed-size schur specializations." ON)
option(CUSTOM_BLAS
       "Use handcoded BLAS routines (usually faster) instead of Eigen."
       ON)
# Enable the use of Eigen as a sparse linear algebra library for
# solving the nonlinear least squares problems.
option(EIGENSPARSE "Enable Eigen as a sparse linear algebra library." ON)
option(EXPORT_BUILD_DIR
  "Export build directory using CMake (enables external use without install)." OFF)
option(BUILD_TESTING "Enable tests" OFF)
option(BUILD_DOCUMENTATION "Build User's Guide (html)" OFF)
option(BUILD_EXAMPLES "Build examples" OFF)
option(BUILD_BENCHMARKS "Build Ceres benchmarking suite" OFF)
option(BUILD_SHARED_LIBS "Build Ceres as a shared library." OFF)
option(PROVIDE_UNINSTALL_TARGET "Add a custom target to ease removal of installed targets" ON)
set(SANITIZERS "" CACHE STRING "Semicolon-separated list of sanitizers to use (e.g address, memory, thread)")
include(EnableSanitizer)
enable_sanitizer(${SANITIZERS})
if (ANDROID)
  option(ANDROID_STRIP_DEBUG_SYMBOLS "Strip debug symbols from Android builds (reduces file sizes)" ON)
endif()
if (MSVC)
  # TODO Not needed starting CMake 3.15 which provides CMAKE_MSVC_RUNTIME_LIBRARY
  option(MSVC_USE_STATIC_CRT
    "MS Visual Studio: Use static C-Run Time Library in place of shared." OFF)
endif()

# IOS is defined iff using the iOS.cmake CMake toolchain to build a static
# library for iOS.
if (IOS)
  message(STATUS "Building Ceres for iOS platform: ${IOS_PLATFORM}")

  # Ceres requires at least iOS 7.0+.
  if (IOS_DEPLOYMENT_TARGET VERSION_LESS 7.0)
    message(FATAL_ERROR "Unsupported iOS version: ${IOS_DEPLOYMENT_TARGET}, Ceres "
      "requires at least iOS version 7.0")
  endif()

  update_cache_variable(MINIGLOG ON)
  message(STATUS "Building for iOS: Forcing use of miniglog instead of glog.")

  # Apple claims that the BLAS call dsyrk_ is a private API, and will not allow
  # you to submit to the Apple Store if the symbol is present.
  update_cache_variable(LAPACK OFF)
  message(STATUS "Building for iOS: SuiteSparse, CXSparse, LAPACK, gflags, "
    "and OpenMP are not available.")

  update_cache_variable(BUILD_EXAMPLES OFF)
  message(STATUS "Building for iOS: Will not build examples.")
endif (IOS)

unset(CERES_COMPILE_OPTIONS)

# Eigen.
# Eigen delivers Eigen3Config.cmake since v3.3.3
#find_package(Eigen3 3.3 REQUIRED)
if (Eigen3_FOUND)
  message("-- Found Eigen version ${Eigen3_VERSION}: ${Eigen3_DIR}")
  if (CMAKE_SYSTEM_PROCESSOR MATCHES "^(aarch64.*|AARCH64.*)" AND
      Eigen3_VERSION VERSION_LESS 3.3.4)
    # As per issue #289: https://github.com/ceres-solver/ceres-solver/issues/289
    # the bundle_adjustment_test will fail for Eigen < 3.3.4 on aarch64.
    message(FATAL_ERROR "-- Ceres requires Eigen version >= 3.3.4 on aarch64. "
      "Detected version of Eigen is: ${Eigen3_VERSION}.")
  endif()

  if (EIGENSPARSE)
    message("-- Enabling use of Eigen as a sparse linear algebra library.")
    list(APPEND CERES_COMPILE_OPTIONS CERES_USE_EIGEN_SPARSE)
  else (EIGENSPARSE)
    message("-- Disabling use of Eigen as a sparse linear algebra library.")
    message("   This does not affect the covariance estimation algorithm ")
    message("   which can still use the EIGEN_SPARSE_QR algorithm.")
    add_definitions(-DEIGEN_MPL2_ONLY)
  endif (EIGENSPARSE)
endif (Eigen3_FOUND)

if (CUDA)
  find_package(CUDA QUIET)
  if (CUDA_FOUND)
    message("-- Found CUDA version ${CUDA_VERSION}: "
        "${CUDA_LIBRARIES};"
        "${CUDA_cusolver_LIBRARY};"
        "${CUDA_cusparse_LIBRARY}")
  else (CUDA_FOUND)
    message("-- Did not find CUDA library, disabling CUDA support.")
    update_cache_variable(CUDA OFF)
    list(APPEND CERES_COMPILE_OPTIONS CERES_NO_CUDA)
  endif (CUDA_FOUND)
else (CUDA)
  message("-- Building without CUDA.")
  list(APPEND CERES_COMPILE_OPTIONS CERES_NO_CUDA)
endif (CUDA)

if (LAPACK)
  find_package(LAPACK QUIET)
  if (LAPACK_FOUND)
    message("-- Found LAPACK library: ${LAPACK_LIBRARIES}")
  else (LAPACK_FOUND)
    message("-- Did not find LAPACK library, disabling LAPACK support.")
    update_cache_variable(LAPACK OFF)
    list(APPEND CERES_COMPILE_OPTIONS CERES_NO_LAPACK)
  endif (LAPACK_FOUND)
else (LAPACK)
  message("-- Building without LAPACK.")
  list(APPEND CERES_COMPILE_OPTIONS CERES_NO_LAPACK)
endif (LAPACK)

# Set the install path for the installed CeresConfig.cmake configuration file
# relative to CMAKE_INSTALL_PREFIX.
set(RELATIVE_CMAKECONFIG_INSTALL_DIR ${CMAKE_INSTALL_LIBDIR}/cmake/Ceres)

if (SUITESPARSE)
  # By default, if SuiteSparse and all dependencies are found, Ceres is
  # built with SuiteSparse support.

  # Check for SuiteSparse and dependencies.
  find_package(SuiteSparse 4.0 COMPONENTS CHOLMOD SPQR)
  if (SuiteSparse_FOUND)
    set(SuiteSparse_DEPENDENCY "find_dependency(SuiteSparse ${SuiteSparse_VERSION})")
    # By default, if all of SuiteSparse's dependencies are found, Ceres is
    # built with SuiteSparse support.
    message("-- Found SuiteSparse ${SuiteSparse_VERSION}, "
            "building with SuiteSparse.")

    if (SuiteSparse_NO_CMAKE OR NOT SuiteSparse_DIR)
      install(FILES ${Ceres_SOURCE_DIR}/cmake/FindSuiteSparse.cmake
                    ${Ceres_SOURCE_DIR}/cmake/FindMETIS.cmake
              DESTINATION ${RELATIVE_CMAKECONFIG_INSTALL_DIR})
    endif (SuiteSparse_NO_CMAKE OR NOT SuiteSparse_DIR)
  else (SuiteSparse_FOUND)
    # Disable use of SuiteSparse if it cannot be found and continue.
    message("-- Did not find all SuiteSparse dependencies, disabling "
      "SuiteSparse support.")
    update_cache_variable(SUITESPARSE OFF)
    list(APPEND CERES_COMPILE_OPTIONS CERES_NO_SUITESPARSE)
  endif (SuiteSparse_FOUND)
else (SUITESPARSE)
  message("-- Building without SuiteSparse.")
  list(APPEND CERES_COMPILE_OPTIONS CERES_NO_SUITESPARSE)
endif (SUITESPARSE)

# CXSparse.
if (CXSPARSE)
  # Don't search with REQUIRED as we can continue without CXSparse.
  find_package(CXSparse)
  if (CXSparse_FOUND)
    set(CXSparse_DEPENDENCY "find_dependency(CXSparse ${CXSparse_VERSION})")
    # By default, if CXSparse and all dependencies are found, Ceres is
    # built with CXSparse support.
    message("-- Found CXSparse version: ${CXSparse_VERSION}, "
      "building with CXSparse.")

    if (CXSparse_NO_CMAKE OR NOT CXSparse_DIR)
      install(FILES ${Ceres_SOURCE_DIR}/cmake/FindCXSparse.cmake
              DESTINATION ${RELATIVE_CMAKECONFIG_INSTALL_DIR})
    endif (CXSparse_NO_CMAKE OR NOT CXSparse_DIR)
  else (CXSparse_FOUND)
    # Disable use of CXSparse if it cannot be found and continue.
    message("-- Did not find CXSparse, Building without CXSparse.")
    update_cache_variable(CXSPARSE OFF)
    list(APPEND CERES_COMPILE_OPTIONS CERES_NO_CXSPARSE)
  endif (CXSparse_FOUND)
else (CXSPARSE)
  message("-- Building without CXSparse.")
  list(APPEND CERES_COMPILE_OPTIONS CERES_NO_CXSPARSE)
endif (CXSPARSE)

if (ACCELERATESPARSE)
  find_package(AccelerateSparse)
  if (AccelerateSparse_FOUND)
    message("-- Found Apple's Accelerate framework with sparse solvers, "
      "building with Accelerate sparse support.")
  else()
    message("-- Failed to find Apple's Accelerate framework with sparse solvers, "
      "building without Accelerate sparse support.")
    update_cache_variable(ACCELERATESPARSE OFF)
    list(APPEND CERES_COMPILE_OPTIONS CERES_NO_ACCELERATE_SPARSE)
  endif()
else()
  message("-- Building without Apple's Accelerate sparse support.")
  list(APPEND CERES_COMPILE_OPTIONS CERES_NO_ACCELERATE_SPARSE)
  mark_as_advanced(FORCE AccelerateSparse_INCLUDE_DIR
                         AccelerateSparse_LIBRARY)
endif()

# Ensure that the user understands they have disabled all sparse libraries.
if (NOT SUITESPARSE AND NOT CXSPARSE AND NOT EIGENSPARSE AND NOT ACCELERATESPARSE)
  message("   ===============================================================")
  message("   Compiling without any sparse library: SuiteSparse, CXSparse ")
  message("   EigenSparse & Apple's Accelerate are all disabled or unavailable.  ")
  message("   No sparse linear solvers (SPARSE_NORMAL_CHOLESKY & SPARSE_SCHUR)")
  message("   will be available when Ceres is used.")
  message("   ===============================================================")
endif()

# ANDROID define is set by the Android CMake toolchain file.
if (ANDROID)
  message("  ================================================================")
  if (ANDROID_STRIP_DEBUG_SYMBOLS)
    # Strip debug information unconditionally to avoid +200MB library file sizes.
    set( CMAKE_EXE_LINKER_FLAGS  "${CMAKE_EXE_LINKER_FLAGS} -s" )
    set( CMAKE_SHARED_LINKER_FLAGS  "${CMAKE_SHARED_LINKER_FLAGS} -s" )
    message("  Stripping debug information from Android build of Ceres library ")
    message("  to avoid +200MB library files.")
  else()
    message("  Warning: not stripping debug information from Android build of ")
    message("  Ceres library.  This will result in a large (+200MB) library.")
  endif()
  message("")
  message("  You can control whether debug information is stripped via the ")
  message("  ANDROID_STRIP_DEBUG_SYMBOLS CMake option when configuring Ceres.")
  message("  ================================================================")
endif()

# GFlags.
if (GFLAGS)
  # Don't search with REQUIRED as we can continue without gflags.
  find_package(gflags 2.2.0)
  if (gflags_FOUND)
    if (TARGET gflags)
      message("-- Found Google Flags (gflags) version ${gflags_VERSION}: ${gflags_DIR}")
    else()
      message("-- Detected version of gflags: ${gflags_VERSION} does not define "
        "expected gflags CMake target which should be exported by gflags 2.2+. "
        "Building without gflags.")
      update_cache_variable(GFLAGS OFF)
    endif()
  else (gflags_FOUND)
    message("-- Did not find Google Flags (gflags), Building without gflags.")
    update_cache_variable(GFLAGS OFF)
  endif (gflags_FOUND)
endif()
if (NOT GFLAGS)
  message("-- Use of gflags disabled - no tests or tools will be built!")
endif()

# MiniGLog.
if (MINIGLOG)
  message("-- Compiling minimal glog substitute into Ceres.")
  set(GLOG_INCLUDE_DIRS internal/ceres/miniglog)
  set(MINIGLOG_MAX_LOG_LEVEL 2 CACHE STRING "The maximum message severity level to be logged")
  add_definitions("-DMAX_LOG_LEVEL=${MINIGLOG_MAX_LOG_LEVEL}")
  message("-- Using minimal glog substitute (include): ${GLOG_INCLUDE_DIRS}")
  message("-- Max log level for minimal glog substitute: ${MINIGLOG_MAX_LOG_LEVEL}")

  # Mark as advanced (remove from default GUI view) the glog search
  # variables in case user disables MINIGLOG, FindGlog did not find it, so
  # made search variables visible in GUI for user to set, but then user enables
  # MINIGLOG instead of setting them.
  mark_as_advanced(FORCE GLOG_INCLUDE_DIR
                         GLOG_LIBRARY)
else (MINIGLOG)
  unset(MINIGLOG_MAX_LOG_LEVEL CACHE)
  # Don't search with REQUIRED so that configuration continues if not found and
  # we can output an error messages explaining MINIGLOG option.
  find_package(Glog)
  if (NOT GLOG_FOUND)
    message(FATAL_ERROR "Can't find Google Log (glog). Please set either: "
      "glog_DIR (newer CMake built versions of glog) or GLOG_INCLUDE_DIR & "
      "GLOG_LIBRARY or enable MINIGLOG option to use minimal glog "
      "implementation.")
  endif(NOT GLOG_FOUND)
  # By default, assume gflags was found, updating the message if it was not.
  set(GLOG_GFLAGS_DEPENDENCY_MESSAGE
    " Assuming glog was built with gflags support as gflags was found. "
    "This will make gflags a public dependency of Ceres.")
  if (NOT gflags_FOUND)
    set(GLOG_GFLAGS_DEPENDENCY_MESSAGE
      " Assuming glog was NOT built with gflags support as gflags was "
      "not found.  If glog was built with gflags, please set the "
      "gflags search locations such that it can be found by Ceres.  "
      "Otherwise, Ceres may fail to link due to missing gflags symbols.")
  endif(NOT gflags_FOUND)
  message("-- Found Google Log (glog)." ${GLOG_GFLAGS_DEPENDENCY_MESSAGE})
endif (MINIGLOG)

if (NOT SCHUR_SPECIALIZATIONS)
  list(APPEND CERES_COMPILE_OPTIONS CERES_RESTRICT_SCHUR_SPECIALIZATION)
  message("-- Disabling Schur specializations (faster compiles)")
endif (NOT SCHUR_SPECIALIZATIONS)

if (NOT CUSTOM_BLAS)
  list(APPEND CERES_COMPILE_OPTIONS CERES_NO_CUSTOM_BLAS)
  message("-- Disabling custom blas")
endif (NOT CUSTOM_BLAS)

set_ceres_threading_model("${CERES_THREADING_MODEL}")

if (BUILD_BENCHMARKS)
  # Version 1.3 was first to provide import targets
  find_package(benchmark 1.3 QUIET)
  if (benchmark_FOUND)
     message("-- Found Google benchmark library. Building Ceres benchmarks.")
  else()
     message("-- Failed to find Google benchmark library, disabling build of benchmarks.")
     update_cache_variable(BUILD_BENCHMARKS OFF)
  endif()
  mark_as_advanced(benchmark_DIR)
endif()

# TODO Report features using the FeatureSummary CMake module
if (BUILD_SHARED_LIBS)
  message("-- Building Ceres as a shared library.")
else (BUILD_SHARED_LIBS)
  message("-- Building Ceres as a static library.")
endif (BUILD_SHARED_LIBS)

# Change the default build type from Debug to Release, while still
# supporting overriding the build type.
#
# The CACHE STRING logic here and elsewhere is needed to force CMake
# to pay attention to the value of these variables.
if (NOT CMAKE_BUILD_TYPE)
  message("-- No build type specified; defaulting to CMAKE_BUILD_TYPE=Release.")
  set(CMAKE_BUILD_TYPE Release CACHE STRING
    "Choose the type of build, options are: None Debug Release RelWithDebInfo MinSizeRel."
    FORCE)
else (NOT CMAKE_BUILD_TYPE)
  if (CMAKE_BUILD_TYPE STREQUAL "Debug")
    message("\n=================================================================================")
    message("\n-- Build type: Debug. Performance will be terrible!")
    message("-- Add -DCMAKE_BUILD_TYPE=Release to the CMake command line to get an optimized build.")
    message("\n=================================================================================")
  endif (CMAKE_BUILD_TYPE STREQUAL "Debug")
endif (NOT CMAKE_BUILD_TYPE)

if (MINGW)
  # MinGW produces code that segfaults when performing matrix multiplications
  # in Eigen when compiled with -O3 (see [1]), as such force the use of -O2
  # which works.
  #
  # [1] http://eigen.tuxfamily.org/bz/show_bug.cgi?id=556
  message("-- MinGW detected, forcing -O2 instead of -O3 in Release for Eigen due "
          "to a MinGW bug: http://eigen.tuxfamily.org/bz/show_bug.cgi?id=556")
  string(REPLACE "-O3" "-O2" CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE}")
  update_cache_variable(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE}")
endif (MINGW)

# After the tweaks for the compile settings, disable some warnings on MSVC.
if (MSVC)
  # On MSVC, math constants are not included in <cmath> or <math.h> unless
  # _USE_MATH_DEFINES is defined [1].  As we use M_PI in the examples, ensure
  # that _USE_MATH_DEFINES is defined before the first inclusion of <cmath>.
  #
  # [1] https://msdn.microsoft.com/en-us/library/4hwaceh6.aspx
  add_definitions("-D_USE_MATH_DEFINES")
  # Disable signed/unsigned int conversion warnings.
  add_compile_options("/wd4018" "/wd4267")
  # Disable warning about using struct/class for the same symobl.
  add_compile_options("/wd4099")
  # Disable warning about the insecurity of using "std::copy".
  add_compile_options("/wd4996")
  # Disable performance warning about int-to-bool conversion.
  add_compile_options("/wd4800")
  # Disable performance warning about fopen insecurity.
  add_compile_options("/wd4996")
  # Disable warning about int64 to int32 conversion. Disabling
  # this warning may not be correct; needs investigation.
  # TODO(keir): Investigate these warnings in more detail.
  add_compile_options("/wd4244")
  # It's not possible to use STL types in DLL interfaces in a portable and
  # reliable way. However, that's what happens with Google Log and Google Flags
  # on Windows. MSVC gets upset about this and throws warnings that we can't do
  # much about. The real solution is to link static versions of Google Log and
  # Google Test, but that seems tricky on Windows. So, disable the warning.
  add_compile_options("/wd4251")

  # Add bigobj flag otherwise the build would fail due to large object files
  # probably resulting from generated headers (like the fixed-size schur
  # specializations).
  add_compile_options("/bigobj")

  # Google Flags doesn't have their DLL import/export stuff set up correctly,
  # which results in linker warnings. This is irrelevant for Ceres, so ignore
  # the warnings.
  set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} /ignore:4049")

  # Update the C/CXX flags for MSVC to use either the static or shared
  # C-Run Time (CRT) library based on the user option: MSVC_USE_STATIC_CRT.
  list(APPEND C_CXX_FLAGS
    CMAKE_CXX_FLAGS
    CMAKE_CXX_FLAGS_DEBUG
    CMAKE_CXX_FLAGS_RELEASE
    CMAKE_CXX_FLAGS_MINSIZEREL
    CMAKE_CXX_FLAGS_RELWITHDEBINFO)

  foreach(FLAG_VAR ${C_CXX_FLAGS})
    if (MSVC_USE_STATIC_CRT)
      # Use static CRT.
      if (${FLAG_VAR} MATCHES "/MD")
        string(REGEX REPLACE "/MD" "/MT" ${FLAG_VAR} "${${FLAG_VAR}}")
      endif (${FLAG_VAR} MATCHES "/MD")
    else (MSVC_USE_STATIC_CRT)
      # Use shared, not static, CRT.
      if (${FLAG_VAR} MATCHES "/MT")
        string(REGEX REPLACE "/MT" "/MD" ${FLAG_VAR} "${${FLAG_VAR}}")
      endif (${FLAG_VAR} MATCHES "/MT")
    endif (MSVC_USE_STATIC_CRT)
  endforeach()

  # Tuple sizes of 10 are used by Gtest.
  add_definitions("-D_VARIADIC_MAX=10")

  include(CheckIfUnderscorePrefixedBesselFunctionsExist)
  check_if_underscore_prefixed_bessel_functions_exist(
    HAVE_UNDERSCORE_PREFIXED_BESSEL_FUNCTIONS)
  if (HAVE_UNDERSCORE_PREFIXED_BESSEL_FUNCTIONS)
    list(APPEND CERES_COMPILE_OPTIONS
      CERES_MSVC_USE_UNDERSCORE_PREFIXED_BESSEL_FUNCTIONS)
  endif()
endif (MSVC)

if (UNIX)
  # Flags which we add to GCC to make it more picky about stuff
  # we do care about,
  add_cxx_compiler_flag_if_supported(CERES_STRICT_CXX_FLAGS
                                     -Wmissing-declarations)
  # Flags which we add to GCC to silence lots of annoying false-positives.
  add_cxx_compiler_flag_if_supported(CERES_STRICT_CXX_FLAGS
                                     -Wno-unknown-pragmas)
  add_cxx_compiler_flag_if_supported(CERES_STRICT_CXX_FLAGS
                                     -Wno-sign-compare)
  add_cxx_compiler_flag_if_supported(CERES_STRICT_CXX_FLAGS
                                     -Wno-unused-parameter)
  add_cxx_compiler_flag_if_supported(CERES_STRICT_CXX_FLAGS
                                     -Wno-missing-field-initializers)
  set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${CERES_STRICT_CXX_FLAGS}")
endif (UNIX)

if (CMAKE_CXX_COMPILER_ID MATCHES "Clang") # Matches Clang & AppleClang.
  # Optimize for Eigen OR enable bitcode; you cannot do both since bitcode is an
  # intermediate representation.
  if (ENABLE_BITCODE)
    set(CMAKE_CXX_FLAGS
      "${CMAKE_CXX_FLAGS} -fembed-bitcode")
  else ()
    # Use a larger inlining threshold for Clang, since it hobbles Eigen,
    # resulting in an unreasonably slow version of the blas routines. The
    # -Qunused-arguments is needed because CMake passes the inline
    # threshold to the linker and clang complains about it and dies.
    set(CMAKE_CXX_FLAGS
      "${CMAKE_CXX_FLAGS} -Qunused-arguments -mllvm -inline-threshold=600")
  endif ()
  # Older versions of Clang (<= 2.9) do not support the 'return-type-c-linkage'
  # option, so check for its presence before adding it to the default flags set.
  check_cxx_compiler_flag("-Wno-return-type-c-linkage"
                          HAVE_RETURN_TYPE_C_LINKAGE)
  if (HAVE_RETURN_TYPE_C_LINKAGE)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wno-return-type-c-linkage")
  endif(HAVE_RETURN_TYPE_C_LINKAGE)
endif ()

# Disable warnings about deprecated interfaces while we are
# transitioning from LocalParameterization to Manifolds.
add_compile_options(
  $<$<OR:$<CXX_COMPILER_ID:Clang>,$<CXX_COMPILER_ID:AppleClang>,$<CXX_COMPILER_ID:GNU>>:-Wno-deprecated-declarations>
  $<$<CXX_COMPILER_ID:MSVC>:/wd4996>)

if (CMAKE_VERSION VERSION_LESS 3.12)
  # Disable the definiton of min/max macros within the project
  if (WIN32)
    add_definitions (-DNOMINMAX)
  endif (WIN32)
else (CMAKE_VERSION VERSION_LESS 3.12)
  add_compile_definitions($<$<BOOL:${WIN32}>:NOMINMAX>)
endif (CMAKE_VERSION VERSION_LESS 3.12)

# Configure the Ceres config.h compile options header using the current
# compile options and put the configured header into the Ceres build
# directory.  Note that the ceres/internal subdir in <build>/config where
# the configured config.h is placed is important, because Ceres will be
# built against this configured header, it needs to have the same relative
# include path as it would if it were in the source tree (or installed).
list(REMOVE_DUPLICATES CERES_COMPILE_OPTIONS)
include(CreateCeresConfig)
create_ceres_config("${CERES_COMPILE_OPTIONS}"
  ${Ceres_BINARY_DIR}/${CMAKE_INSTALL_INCLUDEDIR}/ceres/internal)

add_subdirectory(internal/ceres)

if (BUILD_DOCUMENTATION)
  find_package(Sphinx)
  if (NOT SPHINX_FOUND)
    message("-- Failed to find Sphinx and/or its dependencies, disabling build of documentation.")
    update_cache_variable(BUILD_DOCUMENTATION OFF)
  else()
    # Generate the User's Guide (html).
    # The corresponding target is ceres_docs, but is included in ALL.
    message("-- Build the HTML documentation.")
    add_subdirectory(docs)
  endif()
endif (BUILD_DOCUMENTATION)

if (BUILD_EXAMPLES)
  message("-- Build the examples.")
  add_subdirectory(examples)
else (BUILD_EXAMPLES)
  message("-- Do not build any example.")
endif (BUILD_EXAMPLES)

# Setup installation of Ceres public headers.
file(GLOB CERES_HDRS ${Ceres_SOURCE_DIR}/include/ceres/*.h)
install(FILES ${CERES_HDRS} DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/ceres)

file(GLOB CERES_PUBLIC_INTERNAL_HDRS ${Ceres_SOURCE_DIR}/include/ceres/internal/*.h)
install(FILES ${CERES_PUBLIC_INTERNAL_HDRS} DESTINATION
  ${CMAKE_INSTALL_INCLUDEDIR}/ceres/internal)

# Also setup installation of Ceres config.h configured with the current
# build options and export.h into the installed headers directory.
install(DIRECTORY ${Ceres_BINARY_DIR}/${CMAKE_INSTALL_INCLUDEDIR}/
        DESTINATION ${CMAKE_INSTALL_INCLUDEDIR})

if (MINIGLOG)
  # Install miniglog header if being used as logging #includes appear in
  # installed public Ceres headers.
  install(FILES ${Ceres_SOURCE_DIR}/internal/ceres/miniglog/glog/logging.h
          DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/ceres/internal/miniglog/glog)
endif (MINIGLOG)

# Ceres supports two mechanisms by which it can be detected & imported into
# client code which uses CMake via find_package(Ceres):
#
#   1) Installation (e.g. to /usr/local), using CMake's install() function.
#
#   2) (Optional) Export of the current build directory into the local CMake
#      package registry, using CMake's export() function.  This allows use of
#      Ceres from other projects without requiring installation.
#
# In both cases, we need to generate a configured CeresConfig.cmake which
# includes additional autogenerated files which in concert create an imported
# target for Ceres in a client project when find_package(Ceres) is invoked.
# The key distinctions are where this file is located, and whether client code
# references installed copies of the compiled Ceres headers/libraries,
# (option #1: installation), or the originals in the source/build directories
# (option #2: export of build directory).
#
# NOTE: If Ceres is both exported and installed, provided that the installation
#       path is present in CMAKE_MODULE_PATH when find_package(Ceres) is called,
#       the installed version is preferred.

# Build the list of Ceres components for CeresConfig.cmake from the current set
# of compile options.
include(CeresCompileOptionsToComponents)
ceres_compile_options_to_components("${CERES_COMPILE_OPTIONS}"
  CERES_COMPILED_COMPONENTS)

include(CMakePackageConfigHelpers)

# Create a CeresConfigVersion.cmake file containing the version information,
# used by both export() & install().
write_basic_package_version_file("${Ceres_BINARY_DIR}/CeresConfigVersion.cmake"
  VERSION ${CERES_VERSION}
  COMPATIBILITY SameMajorVersion)

# Install method #1: Put Ceres in CMAKE_INSTALL_PREFIX: /usr/local or equivalent.

# This "exports" for installation all targets which have been put into the
# export set "CeresExport". This generates a CeresTargets.cmake file which,
# when read in by a client project as part of find_package(Ceres) creates
# imported library targets for Ceres (with dependency relations) which can be
# used in target_link_libraries() calls in the client project to use Ceres.
install(EXPORT CeresExport
        NAMESPACE Ceres::
        DESTINATION ${RELATIVE_CMAKECONFIG_INSTALL_DIR} FILE CeresTargets.cmake)

# Save the relative path from the installed CeresConfig.cmake file to the
# install prefix.  We do not save an absolute path in case the installed package
# is subsequently relocated after installation (on Windows).
file(RELATIVE_PATH INSTALL_ROOT_REL_CONFIG_INSTALL_DIR
     ${CMAKE_INSTALL_PREFIX}/${RELATIVE_CMAKECONFIG_INSTALL_DIR}
     ${CMAKE_INSTALL_PREFIX})

# Configure a CeresConfig.cmake file for an installed version of Ceres from the
# template, reflecting the current build options.
#
# NOTE: The -install suffix is necessary to distinguish the install version from
#       the exported version, which must be named CeresConfig.cmake in
#       Ceres_BINARY_DIR to be detected.  The suffix is removed when
#       it is installed.
set(SETUP_CERES_CONFIG_FOR_INSTALLATION TRUE)
configure_file("${Ceres_SOURCE_DIR}/cmake/CeresConfig.cmake.in"
               "${Ceres_BINARY_DIR}/CeresConfig-install.cmake" @ONLY)

# Install the configuration files into the same directory as the autogenerated
# CeresTargets.cmake file.  We include the find_package() scripts for libraries
# whose headers are included in the public API of Ceres and should thus be
# present in CERES_INCLUDE_DIRS.
install(FILES "${Ceres_BINARY_DIR}/CeresConfig-install.cmake"
        RENAME CeresConfig.cmake
        DESTINATION ${RELATIVE_CMAKECONFIG_INSTALL_DIR})
install(FILES "${Ceres_BINARY_DIR}/CeresConfigVersion.cmake"
        DESTINATION ${RELATIVE_CMAKECONFIG_INSTALL_DIR})
if (GLOG_FOUND AND NOT FOUND_INSTALLED_GLOG_CMAKE_CONFIGURATION)
  # Version of glog detected was not built with CMake, install our glog module
  # file to enable detection in CeresConfig.
  install(FILES "${Ceres_SOURCE_DIR}/cmake/FindGlog.cmake"
          DESTINATION ${RELATIVE_CMAKECONFIG_INSTALL_DIR})
endif()

#if (PROVIDE_UNINSTALL_TARGET)
#  # Create an uninstall target to remove all installed files.
#  configure_file("${Ceres_SOURCE_DIR}/cmake/uninstall.cmake.in"
#                 "${Ceres_BINARY_DIR}/cmake/uninstall.cmake"
#                 @ONLY)
#  add_custom_target(uninstall
#                    COMMAND ${CMAKE_COMMAND} -P ${Ceres_BINARY_DIR}/cmake/uninstall.cmake)
#endif()

# Install method #2: Put Ceres build into local CMake registry.
#
# Optionally export the Ceres build directory into the local CMake package
# registry (~/.cmake/packages on *nix & OS X).  This allows the detection &
# use of Ceres without requiring that it be installed.
if (EXPORT_BUILD_DIR)
  message("-- Export Ceres build directory to local CMake package registry.")

  # Save the relative path from the build directory to the source directory.
  file(RELATIVE_PATH INSTALL_ROOT_REL_CONFIG_INSTALL_DIR
    ${Ceres_BINARY_DIR}
    ${Ceres_SOURCE_DIR})

  # Analogously to install(EXPORT ...), export the Ceres target from the build
  # directory as a package called Ceres into the local CMake package registry.
  export(TARGETS ceres
         NAMESPACE Ceres::
         FILE ${Ceres_BINARY_DIR}/CeresTargets.cmake)
  export(PACKAGE ${CMAKE_PROJECT_NAME})

  # Configure a CeresConfig.cmake file for the export of the Ceres build
  # directory from the template, reflecting the current build options.
  set(SETUP_CERES_CONFIG_FOR_INSTALLATION FALSE)
  configure_file("${Ceres_SOURCE_DIR}/cmake/CeresConfig.cmake.in"
    "${Ceres_BINARY_DIR}/CeresConfig.cmake" @ONLY)

endif (EXPORT_BUILD_DIR)
