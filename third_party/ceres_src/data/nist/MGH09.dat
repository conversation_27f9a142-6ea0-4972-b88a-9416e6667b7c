NIST/ITL StRD
Dataset Name:  MGH09             (MGH09.dat)

File Format:   ASCII
               Starting Values   (lines 41 to 44)
               Certified Values  (lines 41 to 49)
               Data              (lines 61 to 71)

Procedure:     Nonlinear Least Squares Regression

Description:   This problem was found to be difficult for some very 
               good algorithms.  There is a local minimum at (+inf,
               -14.07..., -inf, -inf) with final sum of squares 
               0.00102734....

               <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, B. S., and <PERSON>, K. E<PERSON> 
               (1981).  Testing unconstrained optimization software.
               ACM Transactions on Mathematical Software. 7(1): 
               pp. 17-41.

Reference:     <PERSON><PERSON><PERSON>, J<PERSON>, and <PERSON><PERSON> <PERSON><PERSON>, (1978).  
               Methods for Unconstrained Optimization Problems.  
               New York, NY:  Elsevier North-Holland.

Data:          1 Response  (y)
               1 Predictor (x)
               11 Observations
               Higher Level of Difficulty
               Generated Data
 
Model:         Rational Class (linear/quadratic)
               4 Parameters (b1 to b4)
 
               y = b1*(x**2+x*b2) / (x**2+x*b3+b4)  +  e
 

 
          Starting values                  Certified Values

        Start 1     Start 2           Parameter     Standard Deviation
  b1 =   25          0.25          1.9280693458E-01  1.1435312227E-02
  b2 =   39          0.39          1.9128232873E-01  1.9633220911E-01
  b3 =   41.5        0.415         1.2305650693E-01  8.0842031232E-02
  b4 =   39          0.39          1.3606233068E-01  9.0025542308E-02

Residual Sum of Squares:                    3.0750560385E-04
Residual Standard Deviation:                6.6279236551E-03
Degrees of Freedom:                                7
Number of Observations:                           11
 
 





 
 
 
Data:  y               x
       1.957000E-01    4.000000E+00
       1.947000E-01    2.000000E+00
       1.735000E-01    1.000000E+00
       1.600000E-01    5.000000E-01
       8.440000E-02    2.500000E-01
       6.270000E-02    1.670000E-01
       4.560000E-02    1.250000E-01
       3.420000E-02    1.000000E-01
       3.230000E-02    8.330000E-02
       2.350000E-02    7.140000E-02
       2.460000E-02    6.250000E-02
