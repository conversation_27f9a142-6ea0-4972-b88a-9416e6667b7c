#!/bin/bash

function start_front_end() {
    mkdir -p /acviewer/websocket_write/
    mkdir -p /acviewer/websocket_ota_write/
    if [ -f /acviewer/build/application/front_end_socket ]; then
        /acviewer/build/application/front_end_socket
    else
        echo "front_end_socket not exists, please compile first!"
        exit 1
    fi
}

function stop_front_end() {
    pkill -f "front_end_socket"
}


function run_start_and_stop_cmd() {
    local param=$1
    case $param in
        -ka)
            stop_front_end
            ;;
        *)
            start_front_end
            ;;
    esac      
}

run_start_and_stop_cmd "$@"
