#!/bin/bash

clear

SDK_DIR="/home/<USER>/Gitlab/super_sensor_sdk_v1"
APP_DIR="/home/<USER>/Gitlab/AcViewer2/AcViewer_Linux_x86_64_v2.0.0_2025-08-05-15-41"
APP_PLUGIN_DIR="$APP_DIR/AcViewer_Data/Plugins"
EXCLUDE_FILE="libStandaloneFileBrowser.so"

print_step() {
    echo "----------------------------------------------------------------------------------------"
    echo "[$(date '+%F %T')] $1"
    echo "----------------------------------------------------------------------------------------"
}

print_step "Activate ROS2 environment ..."
. ~/ros2_humble/install/local_setup.bash
printenv | grep -i ROS

print_step "Start compiling Super-Sensor-SDK ..."
cd "$SDK_DIR"
[ -d build ] || mkdir build
cd build
#rm -rf ./*
#cmake ..
#make -j2

print_step "Prepare AppImage ..."
mkdir -p application/lib
cp -v cyber/libcyber.so application/lib/
cp -v modules/interface/libinterface.so application/lib/

print_step "Generate AppImage using linuxdeployqt ..."
cd application
export LD_LIBRARY_PATH="$SDK_DIR/third_party/librsros/lib/linux/x86":$LD_LIBRARY_PATH
export LD_LIBRARY_PATH="$SDK_DIR/third_party/libros2/rscamera_msg/lib":$LD_LIBRARY_PATH
export LD_LIBRARY_PATH="$SDK_DIR/third_party/libros2/hyper_vision_msgs/lib":$LD_LIBRARY_PATH
linuxdeployqt front_end_app -appimage -bundle-non-qt-libs

print_step "Clean up the AcViewer plugin directory and copy new library ..."
cd "$APP_PLUGIN_DIR"
find . -mindepth 1 -maxdepth 1 -type f -not -name "$EXCLUDE_FILE" -exec rm -f {} \;
cd "$SDK_DIR/build/application/lib"
cp ./* "$APP_PLUGIN_DIR"
[ -d "$APP_PLUGIN_DIR/third_party" ] || mkdir -p "$APP_PLUGIN_DIR/third_party"
cp -r "$SDK_DIR/third_party/libros2" "$APP_PLUGIN_DIR/third_party/"

