/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef HYPER_VISION_INTERFACE_INTERFACE_IMPL_H_
#define HYPER_VISION_INTERFACE_INTERFACE_IMPL_H_

#include "hyper_vision/driver/driver.h"
#include "hyper_vision/front_end/front_end.h"
////////////////////////////////
#pragma push_marco("END_MACRO")
#ifdef END
#undef END
#endif
////////////////////////////////
#include "hyper_vision/dataiomanager/datareadermanager.h"
#include "hyper_vision/dataiomanager/datawritermanager.h"
#include "hyper_vision/dataiomanager/slamdataexportermanager.h"
#include "hyper_vision/dataiomanager/slamdatawritermanager.h"

namespace robosense {

class InterfaceImpl : public std::enable_shared_from_this<InterfaceImpl> {
public:
  using Ptr = std::shared_ptr<InterfaceImpl>;

public:
  enum class RS_MESSAGE_BUFFER_ACTION_TYPE : int {
    RS_MESSAGE_BUFFER_ACTION_BUFFER = 0,
    RS_MESSAGE_BUFFER_ACTION_BUFFER_MAX,
    RS_MESSAGE_BUFFER_ACTION_LAST_CLEAR,
  };

  // 输出消息缓冲区管理
  template <typename MESSAGE_PTR> class MessageBuffer {
  public:
    using Ptr = std::shared_ptr<MessageBuffer>;
    using ConstPtr = std::shared_ptr<const MessageBuffer>;

  public:
    MessageBuffer() = default;
    ~MessageBuffer() { finish(); };

  public:
    int init(const std::function<void(const MESSAGE_PTR &)> &callback,
             const RS_MESSAGE_BUFFER_ACTION_TYPE actionType,
             const uint32_t max_buffer = 10) {
      if (callback == nullptr) {
        return -1;
      }
      call_back_ = callback;
      is_running_ = false;
      work_thread_ = nullptr;
      max_buffer_cnt_ = max_buffer;
      return 0;
    }

    int start() {
      try {
        is_running_ = true;
        work_thread_.reset(
            new std::thread(&MessageBuffer<MESSAGE_PTR>::workThread, this));
      } catch (...) {
        is_running_ = false;
        return -1;
      }
      return 0;
    }

    int finish() {
      if (is_running_ == false) {
        return -1;
      }

      {
        std::lock_guard<std::mutex> lg(mtx_);
        is_running_ = false;
        cond_.notify_all();
      }

      if (work_thread_ && work_thread_->joinable()) {
        work_thread_->join();
        work_thread_.reset();
      }

      return 0;
    }

    int addMessage(const MESSAGE_PTR &msg_ptr) {
      if (msg_ptr) {
        std::lock_guard<std::mutex> lg(mtx_);
        switch (action_type_) {
        case RS_MESSAGE_BUFFER_ACTION_TYPE::RS_MESSAGE_BUFFER_ACTION_BUFFER: {
          queue_.push(msg_ptr);
          break;
        }
        case RS_MESSAGE_BUFFER_ACTION_TYPE::
            RS_MESSAGE_BUFFER_ACTION_BUFFER_MAX: {
          queue_.push(msg_ptr);
          while (queue_.size() > max_buffer_cnt_) {
            queue_.pop();
          }
          break;
        }
        case RS_MESSAGE_BUFFER_ACTION_TYPE::
            RS_MESSAGE_BUFFER_ACTION_LAST_CLEAR: {
          if (!queue_.empty()) {
            queue_ = std::queue<MESSAGE_PTR>();
          }
          queue_.push(msg_ptr);
          break;
        }
        }
        cond_.notify_one();
      }
      return 0;
    }

  private:
    void workThread() {
      while (is_running_) {
        MESSAGE_PTR msg_ptr = nullptr;
        {
          std::unique_lock<std::mutex> lg(mtx_);
          cond_.wait(lg, [this] { return !queue_.empty() || !is_running_; });
          if (!is_running_) {
            break;
          }
          msg_ptr = queue_.front();
          queue_.pop();
        }

        if (msg_ptr && call_back_) {
          call_back_(msg_ptr);
        }
      }
    }

  private:
    RS_MESSAGE_BUFFER_ACTION_TYPE action_type_;
    uint32_t max_buffer_cnt_;
    std::function<void(const MESSAGE_PTR &)> call_back_;

  private:
    bool is_running_;
    std::mutex mtx_;
    std::queue<MESSAGE_PTR> queue_;
    std::condition_variable cond_;
    std::shared_ptr<std::thread> work_thread_;
  };

public:
  InterfaceImpl();

  ~InterfaceImpl() { Stop(); }

public:
  // 初始化后端
  int Init(const std::string &config_file,
           const robosense::common::PlayMode play_mode);

  // 开始后端
  int Start();

  // 停止后端
  int Stop();

  // 设置点云投影&着色回调函数
  void
  SetCallback(const front_end::FrontEnd::FRONT_END_POSTPROCESS_OUTPUT_CALLBACK
                  &callback);

  // 设置SLAM回调函数
  void SetCallback(
      const front_end::FrontEnd::FRONT_END_SLAM_OUTPUT_CALLBACK &callback);

  // 设置图像深度图回调函数
  void
  SetCallback(const front_end::FrontEnd::FRONT_END_IMAGE_DEPTH_OUTPUT_CALLBACK
                  &callback);

public:
  // 获取设备序列号信息
  const std::set<std::string> GetDevices();

  // 设备开/关
  int OperatorDevice(
      const robosense::common::DeviceOperator_t &device_operator);

  // 设备OTA
  int OperatorDeviceOta(
      const robosense::common::DeviceOtaOperator_t &device_ota_operator,
      bool &ota_result_value);

  // 查询设备固件信息
  int QueryDeviceInfo(const std::string &device_uuid, std::string &device_info);

  // 开始写数据
  int StartDataWrite(const robosense::io::DataWriterConfig &dataWriterConfig);

  // 停止写数据
  int StopDataWrite();

  // 检查是否正在写数据
  int CheckDataWriteIsStart();

  // 获取读/写数据支持的数据格式
  std::set<std::string> GetDataSupportFormat() {
    return robosense::io::DataFormatSupportUtil::getSupportDataFormats();
  }

  // 获取ROS数据文件的话题-类型信息
  int GetDataTopicInfoRos(const std::string &rosbagFilePath,
                          std::map<std::string, std::string> &topic_types,
                          std::pair<uint64_t, uint64_t> &rosbagTimepoints);

  // 获取ROS2数据文件的话题-类型信息
  int GetDataTopicInfoRos2(const std::string &db3DirFilePath,
                           std::map<std::string, std::string> &topic_types,
                           std::pair<uint64_t, uint64_t> &rosbagTimepoints);

  // 获取支持的全部话题信息
  int GetSupportTopicNameInfos(
      std::map<std::string, io::DataTopicNameInfoItem> &infos);

  // 开始数据读取
  int StartDataRead(const robosense::io::DataReaderConfig &dataReaderConfig);

  // 获取打开的文件的初始化进度: 100时表示完成, -1表示失败
  uint32_t GetInitProgressDataRead();

  // 获取主数据帧的帧数
  uint32_t GetTotalFrameCount();

  // 获取数据总的时长
  uint64_t GetTotalDuration();

  // 获取开始时间戳
  uint64_t GetBeginTimestamp();

  // 获取结束时间戳
  uint64_t GetEndTimestamp();

  // 获取当前主帧的时间戳
  uint64_t GetCurrentTimestamp();

  // 获取当前主帧的帧序号: 从0开始编号
  int32_t GetCurrentFrameIndex();

  // 检查是否为连续播放模式
  bool CheckDataReadIsPlaying();

  // 跳帧播放: 例如下一帧: DataReadSkipFrame(1); 上一帧: DataReadSkipFrame(-1);
  int DataReadSkipFrame(int skip);

  // 设置为连续播放
  int DataReadPlay();

  // 暂停连续播放
  int DataReadPause();

  // 停止播放
  int DataReadStop();

  // 是否结束
  bool CheckPlayFinish();

  YAML::Node GetSocketIoConfig() const;

  // 获取AC 数据的帧率
  int GetACDataFrequence(const std::string &uuid, float &image_freq,
                         float &depth_freq, float &imu_freq);

  // 保存最近的AC数据
  int SaveACDataToFile(const std::string &save_dir_path,
                       const int save_operator_type);

  // 重置Slam
  int ResetSlam();

  // 开始保存Slam数据
  int StartSlamDataWriter(const std::string &slamDataSaveDirPath);

  // 停止保存Slam数据
  int StopSlamDataWriter();

  // 导出Slam地图
  int StartSlamDataExporter(
      const robosense::io::SlamDataExporterConfig &config);

  // 获取导出Slam地图进度
  int GetSlamDataExporterProgress(int32_t &progress);

  // 获取导出Slam地图的结果状态
  int CheckSlamDataExporterStatus(bool &status);

  // 停止导出Slam地图
  int StopSlamDataExporter();

  // 获取标定文件的序列号
  int GetCalibDeviceID(std::string &device_id);

  // 拷贝标定文件到配置文件路径
  int CopyCalibFileToConfigDirectory(const std::string &calib_file_path_src);

  // 设置Relocalization状态
  int SetEnableRelocalization(const bool is_enable);

  // 从硬件导出文件
  int ExportCalibFileFromHardware(const std::string &device_uuid,
                                  const std::string &export_calib_dir_path);

  // 写入到硬件
  int WriteCalibFileToHardware(const std::string &device_uuid);

private:
  std::string name() { return "InterfaceImpl"; }

  // 初始化
  int initInterface();

  // 打印版本信息
  void printVersion();

  // 初始化配置信息
  int initConfigNode();

  // 更新SocketIo的配置信息
  int updateSocketIoConfigNode();

  // 更新FrontEnd的配置信息
  int updateSlamConfigNode();

  // 初始化ROS2环境变量
  int initROS2Environment();

  // 初始化SuperSensor驱动
  int initSuperSensorDriver();

  // 初始化离线文件写管理器
  int initDataIoWriter();

  // 初始化SLAM离线文件写管理器
  int initSlamDataWriter();

  // 初始化FrontEnd
  int initFrontEnd();

  // 初始化消息缓冲区
  int initMessageBuffer();

private:
  // 在线驱动消息回调函数
  void runOnlineLocalDepthFrameCb(
      const std::shared_ptr<robosense::common::DepthFrame> &msgPtr);

  void runOnlineLocalMotionFrameCb(
      const std::shared_ptr<robosense::common::MotionFrame> &msgPtr);

  void runOnlineLocalImageFrameCb(
      const std::shared_ptr<robosense::common::ImageFrame> &msgPtr);

  void runOnlineLocalImageLeftFrameCb(
      const std::shared_ptr<robosense::common::ImageFrame> &msgPtr);

  void runOnlineLocalImageRightFrameCb(
      const std::shared_ptr<robosense::common::ImageFrame> &msgPtr);

private:
  // 离线文件消息回调函数
  void runLocalDepthFrameCb(
      const std::string &topicName,
      const std::shared_ptr<robosense::common::DepthFrame> &msgPtr);
  void runLocalMotionFrameCb(
      const std::string &topicName,
      const std::shared_ptr<robosense::common::MotionFrame> &msgPtr);
  void runLocalImageFrameCb(
      const std::string &topicName,
      const std::shared_ptr<robosense::common::ImageFrame> &msgPtr);
  void runLocalPointCloudXYZRGBIRTCb(
      const std::string &topicName,
      const pcl::PointCloud<RsPointXYZRGBIRT>::Ptr &msgPtr);
  void
  runLocalPointCloudXYZIRTCb(const std::string &topicName,
                             const pcl::PointCloud<RsPointXYZIRT>::Ptr &msgPtr);

private:
  // frontend 处理回调函数
  void runLocalFrontEndPostProcessCb(
      const postprocess::PostprocessOutputMsg::Ptr &msgPtr);
  void runLocalFrontEndSlamCb(const slam::SlamOutputMsg::Ptr &msgPtr);
  void runLocalFrontEndImageDepthCb(
      const imagedepth::ImageDepthOutputMsg::Ptr &msgPtr);

private:
  // interface 注册回调函数
  front_end::FrontEnd::FRONT_END_POSTPROCESS_OUTPUT_CALLBACK
      postprocess_output_cb_;
  front_end::FrontEnd::FRONT_END_SLAM_OUTPUT_CALLBACK slam_output_cb_;
  front_end::FrontEnd::FRONT_END_IMAGE_DEPTH_OUTPUT_CALLBACK
      imagedepth_output_cb_;
  // output消息缓冲区
  MessageBuffer<postprocess::PostprocessOutputMsg::Ptr>::Ptr
      postprocess_output_buffer_ptr_;
  MessageBuffer<slam::SlamOutputMsg::Ptr>::Ptr slam_output_buffer_ptr_;
  MessageBuffer<imagedepth::ImageDepthOutputMsg::Ptr>::Ptr
      imagedepth_output_buffer_ptr_;
  // 驱动
  std::mutex driver_ptr_mtx_;
  driver::SuperSensor::Ptr driver_ptr_;
  // 后端处理
  std::mutex front_end_mtx_;
  front_end::FrontEnd::Ptr front_end_ptr_;
  // 写离线文件管理器
  std::mutex writer_manager_mtx_;
  io::DataWriterManager::Ptr writer_manager_ptr_;
  // 读离线文件管理器
  std::mutex reader_manager_mtx_;
  io::DataReaderManager::Ptr reader_manager_ptr_;
  // SLAM写管理器
  std::mutex slam_writer_manager_mtx_;
  io::SlamDataWriterManager::Ptr slam_writer_manager_ptr_;
  // SLAM导出管理器
  std::mutex slam_exporter_manager_mtx_;
  io::SlamDataExporterManager::Ptr slam_exporter_manager_ptr_;
  // 在线&离线模式: 默认在线模式
  common::PlayMode play_mode_ = common::PlayMode::ONLINE;
  // 配置Node
  YAML::Node cfg_node_;
  // 配置文件路径
  std::string config_path_;

private:
  const std::string RS_CALIBARATION_SUFFIX = ".yaml";
};

} // namespace robosense

#pragma pop_marco("END_MACRO")

#endif // HYPER_VISION_INTERFACE_INTERFACE_IMPL_H_
