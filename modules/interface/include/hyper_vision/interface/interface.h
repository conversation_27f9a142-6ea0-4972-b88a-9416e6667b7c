/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef HYPER_VISION_INTERFACE_INTERFACE_H_
#define HYPER_VISION_INTERFACE_INTERFACE_H_

#if defined(_WIN32) // Windows 平台 (32位和64位)
#define EXPORT_API __declspec(dllexport)
#elif defined(__linux__) // Linux 平台
#define EXPORT_API __attribute__((visibility("default")))
#elif defined(__APPLE__) // macOS 平台
#define EXPORT_API __attribute__((visibility("default")))
#else // 其他平台，暂未定义
#define EXPORT_API
#endif

//....................获取版本信息....................
/// <summary>
/// 获取版本信息
/// </summary>
/// <param name="handler">callback args: Memory head address and length
/// TagTVersion</param>
extern "C" EXPORT_API void GetVersionInfo(void(handler)(void *));

//....................设置播放模式模式....................
/// <summary>
/// 设置参数
/// </summary>
/// <param
/// name="play_mode">表示数据运行模式，比如1表示在线算法模式，2表示离线播包模式</param>
extern "C" EXPORT_API void SetPlayMode(int play_mode = 1);

// 设置是否启动SLAM Relocalization
extern "C" EXPORT_API void
SetEnableRelocalization(bool enable_relocalization = false);

//....................设置显示内容....................
/// <summary>
/// 设置显示配置
/// </summary>
/// <param name="tag">参数</param>
/// <returns>true 表示显示对应项，false表示不显示对应项</returns>
extern "C" EXPORT_API void SetShowConfig(bool show_pos, bool show_pc,
                                         bool show_slam, bool show_tri,
                                         bool show_rgb, bool show_depth);

//....................获取设备信息....................
/// <summary>
/// 获取设备信息
/// </summary>
/// <param name="tag">参数</param>
/// <returns></returns>
extern "C" EXPORT_API void GetDeviceInfo(void(handler)(void *, unsigned int));

//....................操作设备....................
/// <summary>
/// 开关传感器
/// </summary>
/// <param name="tag">参数</param>
/// <returns></returns>
extern "C" EXPORT_API int OperatorDevice(const char *uuid,
                                         unsigned char operator_type);

//....................操作设备....................
/// <summary>
/// 设备升级: 如果需要升级的设备(uuid)为启动状态，则需要先关闭
/// </summary>
/// <param name="tag">参数</param>
/// <returns></returns>
extern "C" EXPORT_API int OperatorOtaDevice(const char *uuid,
                                            unsigned char operator_type,
                                            const char *ota_bin_file_path,
                                            bool *ota_result_value);

//....................操作设备....................
/// <summary>
/// 查询设备固件信息: 如果需要查询的设备(uuid)为启动状态，则需要先关闭
/// </summary>
/// <param name="tag">参数</param>
/// <returns>第一个返回值为: Tag_CSring类型， 第二个返回值为:
/// TagCString2类型</returns>
extern "C" EXPORT_API int QueryDeviceInfo(const char *uuid,
                                          void(handler)(void *, void *));

// 未使用到
// //....................发送数据接口....................
// /// <summary>
// /// 设置参数
// /// </summary>
// /// <param name="tag">参数</param>
// /// <returns>0,表示设置成功，不为0时返回值表示第几个参数传递不合法</returns>
// extern "C" EXPORT_API  int SetRbgImageEnable(int tag);

/// <summary>
/// 设置接收数据回调  unsigned char*, unsigned int 为数据首地址和数据长度
/// </summary>
extern "C" EXPORT_API void
SetPostProcessMsgHandler(void (*handler)(void *, void *, void *));

extern "C" EXPORT_API void SetSlamMsgHandler(void (*handler)(void *, void *,
                                                             void *));

extern "C" EXPORT_API void
SetImageDepthMsgHandler(void (*handler)(void *, void *, void *));

/// <summary>
/// Get Support Write Topic Set
/// </summary>
/// <param name="handler">callback args: Memory head address and length
/// TConfig[]</param> <returns></returns>
extern "C" EXPORT_API int GetTopicConfig(void (*handler)(void *, unsigned int));

/// <summary>
/// Get Support File Format
/// </summary>
/// <param name="handler">callback args: Memory head address and length
/// CString[] ��0 ros.bag, 1 ros2��.db3����</param> <returns></returns>
extern "C" EXPORT_API int GetFileFormat(void (*handler)(void *, unsigned int));

/// <summary>
/// Get Support File Format
/// </summary>
/// <param name="appRootPath"> App Run Root Directory Path </param>
/// <returns></returns>
extern "C" EXPORT_API void SetAppRootPath(const char *appRootPath);

/// <summary>
/// Start Write .bag/.db3
/// </summary>
/// <param name="dataDirPath">�������ݵ��ļ���Ŀ¼</param>
/// <param name="fileFormat">�ļ���ʽ</param>
/// <param
/// name="tSetting">��Ҫ¼�ƵĻ������õ��׵�ַ</param>
/// <param name="tSettingCount">��Ҫ¼�ƵĻ������õ�����</param>
/// <returns></returns>
extern "C" EXPORT_API int StartWriter(const char *dataDirPath,
                                      const char *dataFormat,
                                      const void *tSetting,
                                      unsigned int tSettingCount);

/// <summary>
/// Stop Write .bag/.db3
/// </summary>
/// <returns></returns>
extern "C" EXPORT_API int StopWriter();

/// <summary>
/// Get .bag File Topic Info.
/// </summary>
/// /// <param name="sourceDataFilePath">�ļ�·��</param>
/// <param name="handler">callback args: Memory head address and length unsigned
/// int[] ��topic enum��</param> <returns></returns>
extern "C" EXPORT_API int
GetFileTopicsRos1(const char *sourceDataFilePath,
                  void (*handler)(void *, unsigned int, unsigned long long int,
                                  unsigned long long int));

/// <summary>
/// Get .db3 File Topic Info.
/// </summary>
/// /// <param name="sourceDataFilePath">�ļ�·��</param>
/// <param name="handler">callback args: Memory head address and length unsigned
/// int[] ��topic enum��</param> <returns></returns>
extern "C" EXPORT_API int
GetFileTopicsRos2(const char *sourceDataFilePath,
                  void (*handler)(void *, unsigned int, unsigned long long int,
                                  unsigned long long int));

/// <summary>
/// Start Open Read .bag/.db3
/// </summary>
/// <param name="sourceDataFilePath">�ļ�·��</param>
/// <param name="mainSyncTopic">topic enum</param>
/// <param name="preLoadMainSyncCnt">��ʼ��֡��</param>
/// <returns></returns>
extern "C" EXPORT_API int
StartReader(const char *sourceDataFilePath, const char *mainSyncTopicName,
            unsigned int preLoadMainSyncCnt, const void *tCStrings,
            unsigned int tCStringsCount, long long int startTimestampNs,
            long long int endTimestampNs);

/// <summary>
/// Get Total Main Sync Frame Count
/// </summary>
/// <returns></returns>
extern "C" EXPORT_API unsigned int GetTotalFrameCount();

/// <summary>
/// Get Current Read Main Sync Frame Timestamp
/// </summary>
/// <returns></returns>
extern "C" EXPORT_API unsigned long long GetCurrentTimestamp();

/// <summary>
/// Get Current Read Main Sync Frame Index, Start For 0
/// </summary>
/// <returns></returns>
extern "C" EXPORT_API int GetCurrentFrameIndex();

/// <summary>
/// Check Is Continous Play Status
/// </summary>
/// <returns></returns>
extern "C" EXPORT_API unsigned int GetInitiProgress();

/// <summary>
/// Check Is Continous Play Status
/// </summary>
/// <returns></returns>
extern "C" EXPORT_API bool CheckIsPlaying();

/// <summary>
/// Skip Read .bag/.bd3
/// </summary>
/// <param name="skip">the play skip frame count</param>
/// <returns></returns>
extern "C" EXPORT_API int SkipFrame(int skip);

/// <summary>
/// Start Read .bag/.db3
/// </summary>
/// <returns></returns>
extern "C" EXPORT_API int Play();

/// <summary>
/// Pause Read .bag/.db3
/// </summary>
/// <returns></returns>
extern "C" EXPORT_API int Pause();

/// <summary>
/// Stop Read .bag/.db3
/// </summary>
/// <returns></returns>
extern "C" EXPORT_API int StopReader();

/// <summary>
/// Get AC View Frequence Info.
/// </summary>
/// <returns></returns>
extern "C" EXPORT_API int GetACDataFrequence(const char *uuid,
                                             float *image_freq,
                                             float *depth_freq,
                                             float *imu_freq);

/// <summary>
/// Get AC Slam Status
/// save_dir_path: 表示保存的根目录
/// save_oprerator_type: 见SaveACDataOperatorType类型说明，
/// 即如果低位第一位为1表示保存图像
///   如果低位第二位为1表示保存点云
///   如果低位第三位为1表示保存IMU
/// 例如save_oprerator_type = 7，则表示三种类型数据都保存
/// </summary>
/// <returns>返回值为Tag_CString类型</returns>
extern "C" EXPORT_API int SaveACDataToFile(const char *save_dir_path,
                                           const int save_oprerator_type);

/// <summary>
/// Reset Slam
/// </summary>
/// <returns>返回值0 表示调用成功，否则调用失败</returns>
extern "C" EXPORT_API int ResetSlam();

/// <summary>
/// 开始保存Slam数据到指定路径
/// </summary>
/// <returns>返回值为0表示调用成功，否则调用失败</returns>
extern "C" EXPORT_API int StartSlamDataWriter(const char *slam_data_save_path);

/// <summary>
/// 结束保存Slam数据
/// </summary>
/// <returns>返回值为0表示调用成功，否则调用失败</returns>
extern "C" EXPORT_API int StopSlamDataWriter();

/// <summary>
/// 开始将保存的Slam数据合并为地图
/// </summary>
/// <returns>返回值为0表示调用成功，否则调用失败</returns>
extern "C" EXPORT_API int
StartSlamDataExporter(const char *slam_data_read_path,
                      const char *slam_data_export_path,
                      const bool is_enable_segment,
                      const unsigned long long int segment_size_bytes_th);

/// <summary>
/// 获取Slam数据合并为地图的进度
/// </summary>
/// <returns>返回值为0表示调用成功，否则调用失败,
/// progress表示0-100的值</returns>
extern "C" EXPORT_API int GetSlamDataExporterProgress(int *progress);

/// <summary>
/// 获取Slam数据合并为地图的结果状态
/// </summary>
/// <returns>返回值为0表示调用成功，否则调用失败, status =
/// true表示正确完成，否则表示失败</returns>
extern "C" EXPORT_API int CheckSlamDataExporterStatus(bool *status);

/// <summary>
/// 停止将保存的Slam数据合并为地图
/// </summary>
/// <returns>返回值为0表示调用成功，否则调用失败</returns>
extern "C" EXPORT_API int StopSlamDataExporter();

/// <summary>
/// 获取标定文件的序列号
/// </summary>
/// <returns>返回值为Tag_CString类型</returns>
extern "C" EXPORT_API int GetCalibrationUUID(void (*handler)(void *));

/// <summary>
/// 拷贝标定文件到工具标定文件: 更新后软件进行重启
/// </summary>
/// <returns>返回值为0表示调用成功，否则调用失败</returns>
extern "C" EXPORT_API int
CopyCalibFileToConfigDirectory(const char *calibaration_file_path);

/// <summary>
/// 导出AC硬件中的标定文件信息到YAML文件: 如果设备已经打开，则需要关闭
/// </summary>
/// <returns>返回值为0表示调用成功，否则调用失败</returns>
extern "C" EXPORT_API int
ExportCalibFileFromHardware(const char *uuid,
                            const char *export_calib_dir_path);

/// <summary>
/// 将标定文件写入到AC硬件中: 如果设备已经打开，则需要关闭
/// </summary>
/// <returns>返回值为0表示调用成功，否则调用失败</returns>
extern "C" EXPORT_API int WriteCalibFileToHardware(const char *uuid);

/// <summary>
/// 程序启动时调用
/// </summary>
extern "C" EXPORT_API int OnInitialize(const char *config_file,
                                       const bool isSocketMode = false);
/// <summary>
/// 重置后端
/// </summary>
extern "C" EXPORT_API int Reset();

extern "C" EXPORT_API int OnStart();

/// <summary>
/// 程序退出时
/// </summary>
extern "C" EXPORT_API int OnDestroy();

#endif // HYPER_VISION_INTERFACE_INTERFACE_H_
