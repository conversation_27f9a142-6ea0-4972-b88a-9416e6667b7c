#ifndef SLAMDATACLIPWRITERMANAGER_H
#define SLAMDATACLIPWRITERMANAGER_H

#include "hyper_vision/dataiomanager/dataiocommon.h"

namespace robosense {
namespace io {

class SlamDataClipWriterManager {
public:
  using Ptr = std::shared_ptr<SlamDataClipWriterManager>;
  using ConstPtr = std::shared_ptr<const SlamDataClipWriterManager>;

public:
  SlamDataClipWriterManager();
  ~SlamDataClipWriterManager();

public:
  int init(const std::string &basic_write_dir_path);
  int start();
  int stop();
  int addData(const robosense::slam::SlamOutputMsg::Ptr &msgPtr);

private:
  int init();
  void writeWorkThread();

private:
  bool is_init_ = false;
  bool is_running_ = false;
  std::mutex buffer_mtx_;
  std::queue<robosense::slam::SlamOutputMsg::Ptr> buffer_;
  std::condition_variable buffer_cond_;
  std::shared_ptr<std::thread> buffer_thread_;
  std::string basic_write_dir_path_;
  std::filesystem::path basic_write_clip_dir_path_;
  uint32_t sequence_ = 0;
};

} // namespace io
} // namespace robosense

#endif // SLAMDATACLIPWRITERMANAGER_H