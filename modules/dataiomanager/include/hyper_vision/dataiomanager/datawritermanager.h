#ifndef DATAWRITERMANAGER_H
#define DATAWRITERMANAGER_H

#include "hyper_vision/dataiomanager/datawriterros.h"
#include "hyper_vision/dataiomanager/datawriterros2.h"

namespace robosense {
namespace io {

class DataWriterFactory {
public:
  DataWriterFactory() = delete;
  DataWriterFactory(const DataWriterFactory &) = delete;
  DataWriterFactory &operator=(const DataWriterFactory &) = delete;
  ~DataWriterFactory() = default;

public:
  static DataWriterInterface::Ptr
  createDataIoManager(const RS_DATA_FORMAT_TYPE &dataIoSourceType) {
    switch (dataIoSourceType) {
    case RS_DATA_FORMAT_TYPE::RS_DATA_FORMAT_ROS: {
      DataWriterRos::Ptr rosIoManagerPtr(new DataWriterRos());
      return rosIoManagerPtr;
    }
    case RS_DATA_FORMAT_TYPE::RS_DATA_FORMAT_ROS2: {
      DataWriterRos2::Ptr ros2IoManagerPtr(new DataWriterRos2);
      return ros2IoManagerPtr;
    }
    default: {
      return nullptr;
    }
    }
  }
};

class DataWriterManager {
public:
  using Ptr = std::shared_ptr<DataWriterManager>;
  using ConstPtr = std::shared_ptr<DataWriterManager>;

public:
  DataWriterManager();
  ~DataWriterManager();

public:
  int init();
  int start(const DataWriterConfig &dataWriterConfig);
  int stop();
  bool checkIsStart() {
    std::lock_guard<std::mutex> lg(data_io_interface_ptr_mtx_);
    return data_io_interface_ptr_ != nullptr;
  }

  template <typename MessageT>
  int addMessage(const std::string &topicName,
                 const std::shared_ptr<MessageT> &msgPtr) {
    std::lock_guard<std::mutex> lg(data_io_interface_ptr_mtx_);
    if (data_io_interface_ptr_ != nullptr) {
      return data_io_interface_ptr_->addMessage<MessageT>(topicName, msgPtr);
    }
    return 0;
  }

  template <typename PointT>
  int addMessage(const std::string &topicName,
                 const typename pcl::PointCloud<PointT>::Ptr &msgPtr) {
    std::lock_guard<std::mutex> lg(data_io_interface_ptr_mtx_);
    if (data_io_interface_ptr_ != nullptr) {
      return data_io_interface_ptr_->addMessage<PointT>(topicName, msgPtr);
    }
    return 0;
  }

private:
  int initDataIo();

  std::string name() const { return "DataWriterManager"; }

private:
  DataWriterConfig data_io_config_;
  std::mutex data_io_interface_ptr_mtx_;
  DataWriterInterface::Ptr data_io_interface_ptr_;
};

} // namespace io
} // namespace robosense

#endif // DATAWRITERMANAGER_H