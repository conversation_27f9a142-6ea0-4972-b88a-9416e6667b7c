#ifndef SLAMDATAWRITERMANAGER_H
#define SLAMDATAWRITERMANAGER_H

#include "hyper_vision/dataiomanager/slamdataclipwritermanager.h"

namespace robosense {
namespace io {

class SlamDataWriterManager {
public:
  using Ptr = std::shared_ptr<SlamDataWriterManager>;
  using ConstPtr = std::shared_ptr<const SlamDataWriterManager>;

public:
  SlamDataWriterManager();
  ~SlamDataWriterManager();

public:
  int init();
  int start();
  int stop();

public:
  int startWrite(const std::string &basic_save_dir_path);
  int stopWrite();
  int addData(const robosense::slam::SlamOutputMsg::Ptr &msgPtr);

public:
  SlamDataClipWriterManager::Ptr createClipWriter();
  int eraseClipWriter();
  bool
  checkClipSegment(const robosense::slam::SlamOutputMsg::Ptr &msgPtr);
  int startBufferThread();
  int stopBufferThread();
  int startEraseThread();
  int stopEraseThread();
  void bufferWorkThread();
  void eraseWorkThread();

private:
  bool is_init_ = false;
  bool is_writer_ = false;
  std::string basic_save_dir_path_;
  std::mutex current_clip_writer_mtx_;
  SlamDataClipWriterManager::Ptr current_clip_wirter_ptr_;

private:
  // 缓冲线程
  bool is_running_ = false;
  std::mutex buffer_mtx_;
  std::condition_variable buffer_cond_;
  std::queue<robosense::slam::SlamOutputMsg::Ptr> buffer_;
  std::shared_ptr<std::thread> buffer_thread_ptr_;

private:
  // 删除线程
  bool is_erase_running_ = false;
  std::mutex erase_buffer_mtx_;
  std::condition_variable erase_buffer_cond_;
  std::queue<SlamDataClipWriterManager::Ptr> erase_buffer_;
  std::shared_ptr<std::thread> erase_thread_ptr_;
  std::atomic<int32_t> cur_map_id_ = -1; 
};

} // namespace io
} // namespace robosense

#endif // SLAMDATAWRITERMANAGER_H