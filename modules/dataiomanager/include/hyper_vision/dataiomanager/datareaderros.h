#ifndef DATAREADERROS_H
#define DATAREADERROS_H

#include "hyper_vision/dataiomanager/datareaderinterface.h"
#include <map>

namespace robosense {
namespace io {

class DataReaderRos : public DataReaderInterface {
public:
  using Ptr = std::shared_ptr<DataReaderRos>;
  using ConstPtr = std::shared_ptr<DataReaderRos>;

public:
  DataReaderRos();
  virtual ~DataReaderRos();

public:
  int init(const DataReaderConfig &dataIoConfig) override;
  int skipFrame(int skip) override;
  int play() override;
  int pause() override;
  int stop() override;
  uint64_t getCurrentTimestamp() override;
  int32_t getCurrentFrameIndex() override;

protected:
  std::string name() const override { return "DataReaderRos"; }
  RS_ROSBAG_FRAME_READ_TYPE
  checkRosbagFrameReadType(const int32_t readMainFrameIndex) override;

private:
  int init();
  int prepareFrameMessage(
      const std::map<uint64_t, DataReaderSingleItem::Ptr> &messages,
      std::map<uint64_t, DataReaderSingleItem::Ptr> &frameMessages);
  void consumeWorkThread();
  int readSyncFrame(const int index,
                    std::map<uint64_t, DataReaderSingleItem::Ptr> &messages);
  int readSyncFrameAttachH265(
      const int index, std::map<uint64_t, DataReaderSingleItem::Ptr> &messages);

  int consumeFrame(const int next_index,
                   const RS_ROSBAG_FRAME_CONSUME_TYPE consume_type,
                   const bool empty_consume_queue);
  // 根据消息类型判断是否需要进一步处理
  RS_DATA_MESSAGE_PROCESS_TYPE
  fromDataTypeToProcessType(const std::string &data_type);
  bool fromDataTypeToIsUseOriMessage(const std::string &data_type);
  int getMainSyncRangeIndex(const int index,
                            std::pair<int32_t, int32_t> &rangeIndex);

  int readFrame(const int next_index, const RS_ROSBAG_FRAME_READ_TYPE read_type,
                std::map<uint64_t, DataReaderSingleItem::Ptr> &frameMessages);
  int playFrame(const std::map<uint64_t, DataReaderSingleItem::Ptr> &messages);
  int eraseFrame(const bool isAddOp);

private:
  std::vector<rosbag::View::iterator> message_iterators_;

private:
  std::shared_ptr<rosbag::Bag> bag_ptr_;
  std::shared_ptr<rosbag::View> view_ptr_;

private:
  const std::string ROS_MOTIONFRAME_DATATYPE = "hyper_vision_msgs/MotionFrame";
  const std::string ROS_MOTIONFRAME_DATATYPE2 = "sensor_msgs/Imu";
  const std::string ROS_DEPTHFRAME_DATATYPE = "hyper_vision_msgs/DepthFrame";
  const std::string ROS_IMAGEFRAME_DATATYPE = "hyper_vision_msgs/ImageFrame";
  const std::string ROS_IMAGEFRAME_DATATYPE2 = "sensor_msgs/Image";
  const std::string ROS_COMPRESSEDIMAGE_DATATYPE =
      "sensor_msgs/CompressedImage";
  const std::string ROS_POINTCLOUD2_DATATYPE = "sensor_msgs/PointCloud2";
  const std::string ROS_RSCOMPRESSEDIMAGE_DATATYPE =
      "rscamera_msg/RsCompressedImage";
};

} // namespace io
} // namespace robosense

#endif // DATAREADERROS_H