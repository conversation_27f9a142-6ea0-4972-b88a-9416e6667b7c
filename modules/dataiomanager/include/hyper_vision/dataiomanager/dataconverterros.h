#ifndef DATACONVERTERROS_H
#define DATACONVERTERROS_H

#include "hyper_vision/dataiomanager/dataiocommon.h"

namespace robosense {
namespace io {

class DataConverterRos {
public:
  using Ptr = std::shared_ptr<DataConverterRos>;
  using ConstPtr = std::shared_ptr<const DataConverterRos>;

public:
  DataConverterRos() = default;
  ~DataConverterRos() = default;

public:
  static int fromMsgToRosMsg(const robosense::common::DepthFrame &depthFrame,
                             hyper_vision_msgs::DepthFrame &depthFrameMsg);

  static int fromRosMsgToMsg(const hyper_vision_msgs::DepthFrame &depthFrameMsg,
                             robosense::common::DepthFrame &depthFrame);

  static int fromMsgToRosMsg(const robosense::common::ImageFrame &imageFrame,
                             hyper_vision_msgs::ImageFrame &imageFrameMsg);

  static int fromRosMsgToMsg(const hyper_vision_msgs::ImageFrame &imageFrameMsg,
                             robosense::common::ImageFrame &imageFrame);

  static int fromMsgToRosMsg(const robosense::common::MotionFrame &motionFrame,
                             hyper_vision_msgs::MotionFrame &motionFrameMsg);
  static int
  fromRosMsgToMsg(const hyper_vision_msgs::MotionFrame &motionFrameMsg,
                  robosense::common::MotionFrame &motionFrame);

public:
  static int fromMsgToRosMsg(const robosense::common::DepthFrame &depthFrame,
                             sensor_msgs::PointCloud2 &depthFrameMsg);

  static int fromRosMsgToMsg(const sensor_msgs::PointCloud2 &depthFrameMsg,
                             robosense::common::DepthFrame &depthFrame);

  static int fromMsgToRosMsg(const robosense::common::ImageFrame &imageFrame,
                             sensor_msgs::Image &imageFrameMsg);

  static int fromRosMsgToMsg(const sensor_msgs::Image &imageFrameMsg,
                             robosense::common::ImageFrame &imageFrame);

  static int fromMsgToRosMsg(const robosense::common::MotionFrame &motionFrame,
                             sensor_msgs::Imu &motionFrameMsg);

  static int fromRosMsgToMsg(const sensor_msgs::Imu &motionFrameMsg,
                             robosense::common::MotionFrame &motionFrame);

public:
  template <typename PointT>
  static int fromMsgToRosMsg(const pcl::PointCloud<PointT> &pclPointCloud,
                             sensor_msgs::PointCloud2 &pointCloud) {
    pcl::toROSMsg(pclPointCloud, pointCloud);
    return 0;
  }

  template <typename PointT>
  static int fromRosMsgToMsg(const sensor_msgs::PointCloud2 &pointCloud,
                             pcl::PointCloud<PointT> &pclPointCloud) {
    pcl::fromROSMsg(pointCloud, pclPointCloud);
    return 0;
  }

private:
  static int fromMsgToRosMsg(const robosense::common::Xyz &xyz,
                             hyper_vision_msgs::MotionFrameXyz &xyzMsg);

  static int fromRosMsgToMsg(const hyper_vision_msgs::MotionFrameXyz &xyzMsg,
                             robosense::common::Xyz &xyz);

  static int fromMsgToRosMsg(
      const std::shared_ptr<robosense::common::CloudPointXYZIRT> &point,
      const uint32_t pointLen,
      std::vector<hyper_vision_msgs::DepthFramePoint> &pointMsg);

  static int fromRosMsgToMsg(
      const std::vector<hyper_vision_msgs::DepthFramePoint> &pointMsg,
      std::shared_ptr<robosense::common::CloudPointXYZIRT> &point);

  static int fromMsgToRosMsg(const std::shared_ptr<uint8_t> &data,
                             const uint32_t dataLen,
                             std::vector<uint8_t> &dataMsg);

  static int fromRosMsgToMsg(const std::vector<uint8_t> &dataMsg,
                             std::shared_ptr<uint8_t> &data);

  static int fromMsgToRosMsg(const struct timeval &tv,
                             hyper_vision_msgs::FrameTimeval &tvMsg);

  static int fromRosMsgToMsg(const hyper_vision_msgs::FrameTimeval &tvMsg,
                             struct timeval &tv);
};

} // namespace io
} // namespace robosense

#endif // DATACONVERTERROS_H