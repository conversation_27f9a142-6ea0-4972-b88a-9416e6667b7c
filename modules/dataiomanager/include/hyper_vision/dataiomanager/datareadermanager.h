#ifndef DATAREADERMANAGER_H
#define DATAREADERMANAGER_H

#include "hyper_vision/dataiomanager/datareaderros.h"
#include "hyper_vision/dataiomanager/datareaderros2.h"

namespace robosense {
namespace io {

class DataReaderManagerFactory {
public:
  DataReaderManagerFactory() = delete;
  DataReaderManagerFactory(const DataReaderManagerFactory &) = delete;
  DataReaderManagerFactory &
  operator=(const DataReaderManagerFactory &) = delete;
  ~DataReaderManagerFactory() = default;

public:
  static DataReaderInterface::Ptr
  createDataIoManager(const RS_DATA_FORMAT_TYPE &dataIoSourceType) {
    switch (dataIoSourceType) {
    case RS_DATA_FORMAT_TYPE::RS_DATA_FORMAT_ROS: {
      DataReaderRos::Ptr rosIoManagerPtr(new DataReaderRos());
      return rosIoManagerPtr;
    }
    case RS_DATA_FORMAT_TYPE::RS_DATA_FORMAT_ROS2: {
      DataReaderRos2::Ptr ros2IoManagerPtr(new DataReaderRos2);
      return ros2IoManagerPtr;
    }
    default: {
      return nullptr;
    }
    }
  }
};

class DataReaderManager {
public:
  using Ptr = std::shared_ptr<DataReaderManager>;
  using ConstPtr = std::shared_ptr<const DataReaderManager>;

public:
  DataReaderManager();
  ~DataReaderManager();

public:
  int init(const DataReaderConfig &dataIoConfig);
  int registerCallback(const DATA_DEPTH_FRAME_CALLBACK &callback);
  int registerCallback(const DATA_MOTION_FRAME_CALLBACK &callback);
  int registerCallback(const DATA_IMAGE_FRAME_CALLBACK &callback);
  int registerCallback(const DATA_POINTCLOUDXYZRGBIRT_CALLBACK &callback);
  int registerCallback(const DATA_POINTCLOUDXYZIRT_CALLBACK &callback);
  // 获取总帧数
  uint32_t getTotalFrameCount();
  // 获取播放数据的时间信息
  uint64_t getTotalDuration();
  uint64_t getBeginTimestamp();
  uint64_t getEndTimestamp();
  uint64_t getCurrentTimestamp();
  int32_t getCurrentFrameIndex();
  // 是否为连续播放状态
  bool checkIsPlaying();
  // 检查是否播放到最后一帧 
  bool checkIsPlayFinish(); 
  // 获取初始化进度
  uint32_t getInitProgress();
  // 跳帧: 例如下一帧: skipFrame(1); 上一帧: skipFrame(-1);
  int skipFrame(int skip);
  int play();
  int pause();
  int stop();

private:
  int init();
  std::string name() const { return "DataReaderManager"; }
  void runLocalDepthFrameCb(
      const std::string &topicName,
      const std::shared_ptr<robosense::common::DepthFrame> &msgPtr);
  void runLocalMotionFrameCb(
      const std::string &topicName,
      const std::shared_ptr<robosense::common::MotionFrame> &msgPtr);
  void runLocalImageFrameCb(
      const std::string &topicName,
      const std::shared_ptr<robosense::common::ImageFrame> &msgPtr);
  void runLocalPointCloudXYZRGBIRTCb(
      const std::string &topicName,
      const pcl::PointCloud<RsPointXYZRGBIRT>::Ptr &msgPtr);
  void
  runLocalPointCloudXYZIRTCb(const std::string &topicName,
                             const pcl::PointCloud<RsPointXYZIRT>::Ptr &msgPtr);

private:
  DataReaderConfig data_io_config_;
  DataReaderInterface::Ptr data_io_interface_ptr_;

private:
  std::mutex depth_frame_cbs_mtx_;
  std::vector<DATA_DEPTH_FRAME_CALLBACK> depth_frame_cbs_;
  std::mutex motion_frame_cbs_mtx_;
  std::vector<DATA_MOTION_FRAME_CALLBACK> motion_frame_cbs_;
  std::mutex image_frame_cbs_mtx_;
  std::vector<DATA_IMAGE_FRAME_CALLBACK> image_frame_cbs_;
  std::mutex pointcloudxyzrgbirt_cbs_mtx_;
  std::vector<DATA_POINTCLOUDXYZRGBIRT_CALLBACK> pointcloudxyzrgbirt_cbs_;
  std::mutex pointcloudxyzirt_cbs_mtx_;
  std::vector<DATA_POINTCLOUDXYZIRT_CALLBACK> pointcloudxyzirt_cbs_;
};

} // namespace io
} // namespace robosense

#endif // DATAREADERMANAGER_H