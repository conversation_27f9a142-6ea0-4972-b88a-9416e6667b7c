#ifndef DATACONVERTERROS2_H
#define DATACONVERTERROS2_H

#include "hyper_vision/dataiomanager/dataiocommon.h"

namespace robosense {
namespace io {

#if defined(ROS2_FOUND)
class DataConverterRos2 {
public:
  using Ptr = std::shared_ptr<DataConverterRos2>;
  using ConstPtr = std::shared_ptr<const DataConverterRos2>;

public:
  DataConverterRos2() = default;
  ~DataConverterRos2() = default;

public:
  static int fromMsgToRosMsg(const robosense::common::DepthFrame &depthFrame,
                             hyper_vision_msgs::msg::DepthFrame &depthFrameMsg);

  static int
  fromRosMsgToMsg(const hyper_vision_msgs::msg::DepthFrame &depthFrameMsg,
                  robosense::common::DepthFrame &depthFrame);

  static int fromMsgToRosMsg(const robosense::common::ImageFrame &imageFrame,
                             hyper_vision_msgs::msg::ImageFrame &imageFrameMsg);

  static int
  fromRosMsgToMsg(const hyper_vision_msgs::msg::ImageFrame &imageFrameMsg,
                  robosense::common::ImageFrame &imageFrame);

  static int
  fromMsgToRosMsg(const robosense::common::MotionFrame &motionFrame,
                  hyper_vision_msgs::msg::MotionFrame &motionFrameMsg);
  static int
  fromRosMsgToMsg(const hyper_vision_msgs::msg::MotionFrame &motionFrameMsg,
                  robosense::common::MotionFrame &motionFrame);

public:
  static int fromMsgToRosMsg(const robosense::common::DepthFrame &depthFrame,
                             sensor_msgs::msg::PointCloud2 &depthFrameMsg);

  static int fromRosMsgToMsg(const sensor_msgs::msg::PointCloud2 &depthFrameMsg,
                             robosense::common::DepthFrame &depthFrame);

  static int fromMsgToRosMsg(const robosense::common::ImageFrame &imageFrame,
                             sensor_msgs::msg::Image &imageFrameMsg);

  static int fromRosMsgToMsg(const sensor_msgs::msg::Image &imageFrameMsg,
                             robosense::common::ImageFrame &imageFrame);

  static int fromMsgToRosMsg(const robosense::common::MotionFrame &motionFrame,
                             sensor_msgs::msg::Imu &motionFrameMsg);

  static int fromRosMsgToMsg(const sensor_msgs::msg::Imu &motionFrameMsg,
                             robosense::common::MotionFrame &motionFrame);

public:
  template <typename PointT>
  static int fromMsgToRosMsg(const pcl::PointCloud<PointT> &pclPointCloud,
                             sensor_msgs::msg::PointCloud2 &pointCloud) {
    pcl::toROSMsg(pclPointCloud, pointCloud);
    return 0;
  }

  template <typename PointT>
  static int fromRosMsgToMsg(const sensor_msgs::msg::PointCloud2 &pointCloud,
                             pcl::PointCloud<PointT> &pclPointCloud) {
    pcl::fromROSMsg(pointCloud, pclPointCloud);
    return 0;
  }

private:
  static int fromMsgToRosMsg(const robosense::common::Xyz &xyz,
                             hyper_vision_msgs::msg::MotionFrameXyz &xyzMsg);

  static int
  fromRosMsgToMsg(const hyper_vision_msgs::msg::MotionFrameXyz &xyzMsg,
                  robosense::common::Xyz &xyz);

  static int fromMsgToRosMsg(
      const std::shared_ptr<robosense::common::CloudPointXYZIRT> &point,
      const uint32_t pointLen,
      std::vector<hyper_vision_msgs::msg::DepthFramePoint> &pointMsg);

  static int fromRosMsgToMsg(
      const std::vector<hyper_vision_msgs::msg::DepthFramePoint> &pointMsg,
      std::shared_ptr<robosense::common::CloudPointXYZIRT> &point);

  static int fromMsgToRosMsg(const std::shared_ptr<uint8_t> &data,
                             const uint32_t dataLen,
                             std::vector<uint8_t> &dataMsg);

  static int fromRosMsgToMsg(const std::vector<uint8_t> &dataMsg,
                             std::shared_ptr<uint8_t> &data);

  static int fromMsgToRosMsg(const struct timeval &tv,
                             hyper_vision_msgs::msg::FrameTimeval &tvMsg);

  static int fromRosMsgToMsg(const hyper_vision_msgs::msg::FrameTimeval &tvMsg,
                             struct timeval &tv);
};

#endif // defined(ROS2_FOUND)

} // namespace io
} // namespace robosense

#endif // DATACONVERTERROS_H