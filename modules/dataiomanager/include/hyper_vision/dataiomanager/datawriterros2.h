#ifndef DATAWRITERROS2_H
#define DATAWRITERROS2_H

#include "hyper_vision/dataiomanager/datawriterinterface.h"

namespace robosense {
namespace io {

class DataWriterRos2 : public DataWriterInterface {
public:
  using Ptr = std::shared_ptr<DataWriterRos2>;
  using ConstPtr = std::shared_ptr<const DataWriterRos2>;

public:
  DataWriterRos2();
  virtual ~DataWriterRos2();

public:
  int init(const DataWriterConfig &dataIoConfig) override;
  int start() override;
  int stop() override;

private:
  int init();
#if defined(ROS2_FOUND)
  void bufferWorkThread();
#endif // defined(ROS2_FOUND)
  void writeWorkThread();

protected:
  std::string name() const override { return "DataWriterRos2"; }

private:
  std::shared_ptr<std::thread> buffer_thread_ptr_;
  std::shared_ptr<std::thread> write_thread_ptr_;

private:
  const std::string compression_mode_ = "message";
  const std::string compression_format_ = "zstd";
  const int32_t compression_queue_size_ = 200;
  const int32_t compression_thread_cnt_ = 4;
  const bool enable_record_compression_ = false;
};

} // namespace io
} // namespace robosense

#endif // DATAWRITERROS2_H