#ifndef SLAMDATAEXPORTERMANAGER_H
#define SLAMDATAEXPORTERMANAGER_H

#include "hyper_vision/dataiomanager/dataiocommon.h"

namespace robosense {
namespace io {

class SlamDataExporterConfig {
public:
  using Ptr = std::shared_ptr<SlamDataExporterConfig>;
  using ConstPtr = std::shared_ptr<const SlamDataExporterConfig>;

public:
  SlamDataExporterConfig() = default;
  ~SlamDataExporterConfig() = default;

public:
  std::string basic_save_dir_path;
  std::string export_save_dir_path;
  bool is_segment = false;
  uint64_t max_segment_size_bytes = 8UL * 1024UL * 1024UL * 1024UL;
};

class SlamDataExporterManager {
public:
  using Ptr = std::shared_ptr<SlamDataExporterManager>;
  using ConstPtr = std::shared_ptr<const SlamDataExporterManager>;

public:
  SlamDataExporterManager();
  ~SlamDataExporterManager();

public:
  int init(const SlamDataExporterConfig &config);
  int start();
  int stop();
  bool checkIsSuccess() const;
  int getProgress() const;

private:
  int init();
  void processWorkThread();
  int writePcdToFile(const pcl::PointCloud<RsPointXYZRGBI8>::Ptr &cloudPtr,
                     const std::string &pcd_save_path);

private:
  bool is_success_ = false;
  int progress_ = 0;
  bool is_running_ = false;
  std::vector<std::string> search_pcd_file_paths_;
  std::shared_ptr<std::thread> process_thread_ptr_;
  SlamDataExporterConfig config_;

private:
  const std::string RS_EXPORT_MAP_NAME = "AC_SLAM_MAP";
  const std::string RS_EXPORT_MAP_PART_NAME = "AC_SLAM_MAP_Part_";
  const std::string RS_PCD_EXTENSION = ".pcd";
};

} // namespace io
} // namespace robosense

#endif // SLAMDATAEXPORTERMANAGER_H