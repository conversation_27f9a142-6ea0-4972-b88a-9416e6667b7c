#ifndef DATAWRITERROS_H
#define DATAWRITERROS_H

#include "hyper_vision/dataiomanager/datawriterinterface.h"

namespace robosense {
namespace io {

class DataWriterRos : public DataWriterInterface {
public:
  using Ptr = std::shared_ptr<DataWriterRos>;
  using ConstPtr = std::shared_ptr<DataWriterRos>;

public:
  DataWriterRos();
  virtual ~DataWriterRos();

public:
  int init(const DataWriterConfig &dataIoConfig) override;
  int start() override;
  int stop() override;

protected:
  std::string name() const override { return "DataWriterRos"; }

private:
  int init();
  void bufferWorkThread();
  void writeWorkThread();

private:
  std::shared_ptr<std::thread> buffer_thread_ptr_;
  std::shared_ptr<std::thread> write_thread_ptr_;
  std::string write_bag_file_path_;
};

} // namespace io
} // namespace robosense

#endif // DATAWRITERROS_H