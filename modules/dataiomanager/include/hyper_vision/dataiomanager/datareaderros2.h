#ifndef DATAREADERROS2_H
#define DATAREADERROS2_H

#include "hyper_vision/dataiomanager/datareaderinterface.h"

namespace robosense {
namespace io {

class DataReaderRos2 : public DataReaderInterface {
public:
  using Ptr = std::shared_ptr<DataReaderRos2>;
  using ConstPtr = std::shared_ptr<DataReaderRos2>;

public:
  DataReaderRos2();
  virtual ~DataReaderRos2();

public:
  int init(const DataReaderConfig &dataIoConfig) override;
#if defined(ROS2_FOUND)
  int skipFrame(int skip) override;
  uint64_t getCurrentTimestamp() override;
  int32_t getCurrentFrameIndex() override;
  int play() override;
  int pause() override;
  int stop() override;
#else
  int skipFrame(int skip) override {
    (void)(skip);
    return 0;
  }
  uint64_t getCurrentTimestamp() override { return 0; }
  int32_t getCurrentFrameIndex() override { return 0; }
  int play() override { return 0; }
  int pause() override { return 0; }
  int stop() override { return 0; }
#endif // defined(ROS2_FOUND)
protected:
  std::string name() const override { return "DataReaderRos2"; }
  RS_ROSBAG_FRAME_READ_TYPE
  checkRosbagFrameReadType(const int32_t readMainFrameIndex) override {
    // 对于ROS2数据，只要不是连续读取都跳帧
    if (cur_consume_sync_index_ == -1 ||
        readMainFrameIndex - cur_consume_sync_index_ != 1) {
      return RS_ROSBAG_FRAME_READ_TYPE::RS_ROSBAG_FRAME_READ_SKIP;
    } else {
      return RS_ROSBAG_FRAME_READ_TYPE::RS_ROSBAG_FRAME_READ_ORDER;
    }
  }

#if defined(ROS2_FOUND)
private:
  int init();
  int prepareFrameMessage(
      const std::map<uint64_t, DataReaderSingleItem::Ptr> &messages,
      std::map<uint64_t, DataReaderSingleItem::Ptr> &frameMessages);
  void consumeWorkThread();
  int readSyncFrame(const int index,
                    std::map<uint64_t, DataReaderSingleItem::Ptr> &messages);
  int readSyncFrameAttachH265(
      const int index, std::map<uint64_t, DataReaderSingleItem::Ptr> &messages);

  int consumeFrame(const int next_index,
                   const RS_ROSBAG_FRAME_CONSUME_TYPE consume_type,
                   const bool empty_consume_queue);
  // 根据消息类型判断是否需要进一步处理
  RS_DATA_MESSAGE_PROCESS_TYPE
  fromDataTypeToProcessType(const std::string &data_type);
  bool fromDataTypeToIsUseOriMessage(const std::string &data_type);
  int getMainSyncRangeIndex(const int index,
                            std::pair<int32_t, int32_t> &rangeIndex);

  int readFrame(const int next_index, const RS_ROSBAG_FRAME_READ_TYPE read_type,
                std::map<uint64_t, DataReaderSingleItem::Ptr> &frameMessages);
  int playFrame(const std::map<uint64_t, DataReaderSingleItem::Ptr> &messages);
  int eraseFrame(const bool isAddOp);

  template <typename MessageT>
  int convertToMessageT(
      const std::shared_ptr<rosbag2_storage::SerializedBagMessage> bag_message,
      MessageT &msg) {
    if (bag_message == nullptr) {
      return -1;
    }

    rclcpp::SerializedMessage extracted_serialized_msg(
        *bag_message->serialized_data);
    static rclcpp::Serialization<MessageT> serialization;
    serialization.deserialize_message(&extracted_serialized_msg, &msg);

    return 0;
  }

private:
  std::vector<RosbagIndexInfo> message_iterators_;

private:
  std::shared_ptr<rosbag2_cpp::Reader> ros2_reader_ptr_;
#endif // #if defined(ROS2_FOUND)

private:
  const std::string ROS_MOTIONFRAME_DATATYPE =
      "hyper_vision_msgs/msg/MotionFrame";
  const std::string ROS_MOTIONFRAME_DATATYPE2 = "sensor_msgs/msg/Imu";
  const std::string ROS_DEPTHFRAME_DATATYPE =
      "hyper_vision_msgs/msg/DepthFrame";
  const std::string ROS_IMAGEFRAME_DATATYPE =
      "hyper_vision_msgs/msg/ImageFrame";
  const std::string ROS_IMAGEFRAME_DATATYPE2 = "sensor_msgs/msg/Image";
  const std::string ROS_COMPRESSEDIMAGE_DATATYPE =
      "sensor_msgs/msg/CompressedImage";
  const std::string ROS_POINTCLOUD2_DATATYPE = "sensor_msgs/msg/PointCloud2";
  const std::string ROS_RSCOMPRESSEDIMAGE_DATATYPE =
      "rscamera_msg/msg/RsCompressedImage";
};

} // namespace io
} // namespace robosense

#endif // DATAREADERROS2_H