#include "hyper_vision/dataiomanager/datawritermanager.h"

namespace robosense {
namespace io {

DataWriterManager::DataWriterManager() {}

DataWriterManager::~DataWriterManager() { stop(); }

int DataWriterManager::init() {
  data_io_interface_ptr_ = nullptr;
  return 0;
}

int DataWriterManager::start(const DataWriterConfig &dataWriterConfig) {
  int ret = stop();
  if (ret != 0) {
    RERROR << name() << ": stop failed: ret = " << ret;
    return -1;
  }

  data_io_config_ = dataWriterConfig;
  ret = initDataIo();
  if (ret != 0) {
    RERROR << name() << ": initial failed: ret = " << ret;
    return -1;
  }

  std::lock_guard<std::mutex> lg(data_io_interface_ptr_mtx_);
  if (data_io_interface_ptr_ != nullptr) {
    int ret = data_io_interface_ptr_->start();
    if (ret != 0) {
      RERROR << name() << ": start failed: ret = " << ret;
      return -2;
    }
  }

  return 0;
}

int DataWriterManager::stop() {
  std::lock_guard<std::mutex> lg(data_io_interface_ptr_mtx_);
  if (data_io_interface_ptr_ != nullptr) {
    int ret = data_io_interface_ptr_->stop();
    if (ret != 0) {
      RERROR << name() << ": stop failed: ret = " << ret;
      return -1;
    }
  }
  data_io_interface_ptr_.reset();

  return 0;
}

int DataWriterManager::initDataIo() {
  int ret = 0;
  // 设置ROS2环境变量
  #if defined(ROS2_FOUND)
  if (data_io_config_.dataIoSourceType ==
          RS_DATA_FORMAT_TYPE::RS_DATA_FORMAT_ROS2 &&
      Ros2EnvironmentUtil::getRos2EnvironmentFlag() == false) {
    ret = Ros2EnvironmentUtil::setRos2Environment();
    if (ret != 0) {
      RERROR << name() << ": Set Ros2 Env Failed: ret = " << ret;
      return -1;
    } else {
      RINFO << name() << ": Set Ros2 Env Successed !";
    }
  }
  #endif

  DataWriterInterface::Ptr dataIoInterfacePtr =
      DataWriterFactory::createDataIoManager(data_io_config_.dataIoSourceType);
  if (dataIoInterfacePtr == nullptr) {
    RERROR << name() << ": create data writer interface failed !";
    return -2;
  }

  ret = dataIoInterfacePtr->init(data_io_config_);
  if (ret != 0) {
    RERROR << name() << ": data writer initial failed: ret = " << ret;
    return -3;
  }

  {
    std::lock_guard<std::mutex> lg(data_io_interface_ptr_mtx_);
    data_io_interface_ptr_ = dataIoInterfacePtr;
  }

  return 0;
}

} // namespace io
} // namespace robosense