#include "hyper_vision/dataiomanager/datawriterros.h"

namespace robosense {
namespace io {

DataWriterRos::DataWriterRos() : DataWriterInterface() {
  is_buffer_running_ = false;
  buffer_thread_ptr_ = nullptr;

  is_write_running_ = false;
  write_thread_ptr_ = nullptr;

  write_bag_file_path_.clear();
}

DataWriterRos::~DataWriterRos() { stop(); }

int DataWriterRos::init(const DataWriterConfig &dataIoConfig) {
  data_io_config_ = dataIoConfig;

  int ret = init();
  if (ret != 0) {
    RERROR << name() << ": Initial Failed: ret = " << ret;
    return -1;
  }

  RINFO << name() << ": Initial !";

  return 0;
}

int DataWriterRos::start() {
  try {
    bag_ptr_.reset(
        new rosbag::Bag(write_bag_file_path_, rosbag::bagmode::Write));
  } catch (...) {
    RERROR << name() << ": Open Bag File = " << write_bag_file_path_
           << " To Write Failed !";
    return -1;
  }

  try {
    is_write_running_ = true;
    write_thread_ptr_.reset(
        new std::thread(&DataWriterRos::writeWorkThread, this));
  } catch (...) {
    is_write_running_ = false;
    RERROR << name() << ": Create Wrte Work Thread Failed !";
    return -2;
  }

  try {
    is_buffer_running_ = true;
    buffer_thread_ptr_.reset(
        new std::thread(&DataWriterRos::bufferWorkThread, this));
  } catch (...) {
    is_buffer_running_ = false;
    RERROR << name() << ": Create Buffer Work Thread Failed !";
    return -2;
  }

  RINFO << name() << ": Start !";

  return 0;
}

int DataWriterRos::stop() {
  if (is_buffer_running_ == false && is_write_running_ == false) {
    return 0;
  }

  // 退出缓冲处理线程
  if (is_buffer_running_) {
    {
      std::lock_guard<std::mutex> lg(buffer_mtx_);
      is_buffer_running_ = false;
      buffer_cond_.notify_all();
    }

    if (buffer_thread_ptr_ != nullptr) {
      if (buffer_thread_ptr_->joinable()) {
        buffer_thread_ptr_->join();
      }
    }
    buffer_thread_ptr_.reset();
  }

  // 如果采用异步编码时: 推出所有H265编码器的剩余数据
  for (auto iterMap = h265_coder_map_.begin(); iterMap != h265_coder_map_.end();
       ++iterMap) {
    const auto &h265CoderPtr = iterMap->second;
    if (h265CoderPtr != nullptr &&
        h265CoderPtr->dataWriteH265CodeMode() ==
            DataWriterH265Encoder::RS_DATA_WRITE_H265_CODE_MODE::
                RS_DATA_WRITE_H265_CODE_ASYNC) {
      int ret = h265CoderPtr->asyncFlushMessage(true);
      if (ret != 0) {
        RERROR << name() << ": Stop Flush H265 Message Failed: ret = " << ret;
        return -1;
      }
    }
  }

  std::this_thread::sleep_for(std::chrono::seconds(2));

  // 释放写线程
  if (is_write_running_) {
    {
      std::lock_guard<std::mutex> lg(write_buffer_mtx_);
      is_write_running_ = false;
      write_buffer_cond_.notify_all();
    }

    if (write_thread_ptr_ != nullptr) {
      if (write_thread_ptr_->joinable()) {
        write_thread_ptr_->join();
      }
    }
    write_thread_ptr_.reset();
  }
  h265_coder_map_.clear();

  // 关闭文件
  if (bag_ptr_) {
    bag_ptr_->close();
    bag_ptr_.reset();
  }

  is_buffer_running_ = false;

  RINFO << name() << ": Stop !";

  return 0;
}

int DataWriterRos::init() {
  write_bag_file_path_ = data_io_config_.dataDirPath + "/super_sensor_" +
                         data_io_time_util_.currentTimeString() + ".bag";

  // 根据配置生成编码器
  const auto &topicProcessTypes = data_io_config_.topicProcessTypes;
  for (auto iterMap = topicProcessTypes.begin();
       iterMap != topicProcessTypes.end(); ++iterMap) {
    const std::string &topicName = iterMap->first;
    const RS_DATA_MESSAGE_PROCESS_TYPE processType = iterMap->second;
    int ret = 0;
    switch (processType) {
    case RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_JPEG: {
      ret = initAddJpegCoder(topicName);
      break;
    }
    case RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_H265: {
      ret = initAddH265Coder(topicName);
      break;
    }
    default: {
      break;
    }
    }

    if (ret != 0) {
      RERROR << name()
             << ": Add Jpeg/H265 Coder Failed: TopicName = " << topicName
             << ", ret = " << ret;
      return -1;
    }
  }

  return 0;
}

void DataWriterRos::bufferWorkThread() {
  while (is_buffer_running_ || !buffer_.empty()) {
    DataWriterSingleItem::Ptr msgPtr;
    {
      std::unique_lock<std::mutex> lg(buffer_mtx_);
      buffer_cond_.wait(
          lg, [this] { return !buffer_.empty() || !is_buffer_running_; });
      if (!is_buffer_running_ && buffer_.empty()) {
        break;
      }
      msgPtr = buffer_.front();
      buffer_.pop();
    }
    if (msgPtr == nullptr) {
      continue;
    }

    // 进行后处理
    const std::string &topicName = msgPtr->topicName;

    RINFO << name() << ": topicName = " << topicName;
    auto iterMap = data_io_config_.topicProcessTypes.find(topicName);
    if (iterMap != data_io_config_.topicProcessTypes.end()) {
      // 需要进行JPEG/H265编码
      const RS_DATA_MESSAGE_PROCESS_TYPE processType = iterMap->second;
      if (processType ==
          RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_JPEG) {
        // 进行JPEG编码
        std::shared_ptr<robosense::common::ImageFrame> *ptr =
            msgPtr->message
                ->AnyCast<std::shared_ptr<robosense::common::ImageFrame>>();
        if (ptr == nullptr) {
          continue;
        } else if (*ptr == nullptr) {
          continue;
        }

        const auto &rsMsgPtr = *ptr;
        auto iter = jpeg_coder_map_.find(topicName);
        if (iter != jpeg_coder_map_.end()) {
          const robosense::jpeg::JpegCoder::Ptr &jpegCoderPtr = iter->second;
          if (jpegCoderPtr) {
            std::vector<unsigned char> jpegBuffer(
                rsMsgPtr->width * rsMsgPtr->height * 4.5, '\0');

            size_t jpegBufferLen = jpegBuffer.size();
            int ret =
                jpegCoderPtr->encode(rsMsgPtr->data.get(), rsMsgPtr->data_bytes,
                                     jpegBuffer.data(), jpegBufferLen);
            if (ret != 0) {
              RERROR << name()
                     << ": JPEG Encode Failed: topicName = " << topicName
                     << ", ret = " << ret;
              continue;
            }

            RINFO << name()
                  << ": JPEG Encode Successed: topicName = " << topicName;

            sensor_msgs::CompressedImage::Ptr rosMsgPtr(
                new sensor_msgs::CompressedImage());
            rosMsgPtr->data.resize(jpegBufferLen);
            memcpy(rosMsgPtr->data.data(), jpegBuffer.data(), jpegBufferLen);
            rosMsgPtr->format = "jpeg rgb8";
            rosMsgPtr->header.seq = rsMsgPtr->sequence;
            rosMsgPtr->header.stamp.fromSec(rsMsgPtr->capture_time.tv_sec +
                                            rsMsgPtr->capture_time.tv_usec *
                                                1e-6);
            msgPtr->message.reset(new rally::Any(rosMsgPtr));

            {
              std::lock_guard<std::mutex> lg(write_buffer_mtx_);
              msgPtr->isReady = true;
              write_buffer_.push(msgPtr);
              write_buffer_cond_.notify_one();
            }
          }
        }
      } else if (processType ==
                 RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_H265) {
        // 进行H265编码
        auto iter = h265_coder_map_.find(topicName);
        if (iter != h265_coder_map_.end()) {
          const DataWriterH265Encoder::Ptr &h265CoderPtr = iter->second;
          if (h265CoderPtr) {
            const auto codeMode = h265CoderPtr->dataWriteH265CodeMode();
            if (codeMode ==
                DataWriterH265Encoder::RS_DATA_WRITE_H265_CODE_MODE::
                    RS_DATA_WRITE_H265_CODE_ASYNC) {
              // H265 异步编码
              // 先将未编码的消息加入write的队列
              {
                std::lock_guard<std::mutex> lg(write_buffer_mtx_);
                msgPtr->isReady = false;
                write_buffer_.push(msgPtr);
                // write_buffer_cond_.notify_one();
              }

              // 由于未编码的消息加入write的队列会阻塞写线程，则消息的顺序得到了保障，那么这里则可以进行异步编码处理
              int ret = h265CoderPtr->asyncAddMessage(msgPtr, true);
              if (ret != 0) {
                RERROR << name()
                       << ": H265 Message Add Message Failed: ret = " << ret;
                continue;
              }
            } else {
              // H265 同步编码
              int ret = h265CoderPtr->syncAddMessage(msgPtr, true);
              if (ret != 0) {
                continue;
              }

              {
                std::lock_guard<std::mutex> lg(write_buffer_mtx_);
                msgPtr->isReady = true;
                write_buffer_.push(msgPtr);
                write_buffer_cond_.notify_one();
              }
            }
          }
        }
      } else {
        // 不需要额外处理
        std::lock_guard<std::mutex> lg(write_buffer_mtx_);
        msgPtr->isReady = true;
        write_buffer_.push(msgPtr);
        write_buffer_cond_.notify_one();
      }
    } else {
      // 不需要额外处理
      std::lock_guard<std::mutex> lg(write_buffer_mtx_);
      msgPtr->isReady = true;
      write_buffer_.push(msgPtr);
      write_buffer_cond_.notify_one();
    }
  }
}

void DataWriterRos::writeWorkThread() {
  while (is_write_running_ || !write_buffer_.empty()) {
    DataWriterSingleItem::Ptr singleItemPtr;
    {
      std::unique_lock<std::mutex> lg(write_buffer_mtx_);
      write_buffer_cond_.wait(
          lg, [this] { return !write_buffer_.empty() || !is_write_running_; });
      if (!is_write_running_ && write_buffer_.empty()) {
        break;
      }
      singleItemPtr = write_buffer_.front();
      if (singleItemPtr->isReady) {
        write_buffer_.pop();
      }
    }

    // 如果消息准备完毕，则保存
    if (singleItemPtr->isReady) {
      int ret = writeRegisterMessage(singleItemPtr);
      if (ret != 0) {
        RERROR << name() << ": Write Topic: " << singleItemPtr->topicName
               << " To Rosbag File Failed: ret = " << ret;
      }
    } else {
      // 等待2ms再次尝试
      std::this_thread::sleep_for(std::chrono::milliseconds(2));
      continue;
    }
  }
}

} // namespace io
} // namespace robosense