#include "hyper_vision/dataiomanager/dataconverterros2.h"
#include "hyper_vision/dataiomanager/datareaderros2.h"

namespace robosense {
namespace io {

DataReaderRos2::DataReaderRos2() : DataReaderInterface() {}

DataReaderRos2::~DataReaderRos2() { stop(); }

int DataReaderRos2::init(const DataReaderConfig &dataIoConfig) {
#if defined(ROS2_FOUND)
  data_io_config_ = dataIoConfig;
  int ret = init();
  if (ret != 0) {
    RERROR << name() << ": initial failed: ret = " << ret;
    return -1;
  }

  RINFO << name() << ": initial !";

  return 0;
#else
  RERROR << name() << ": ROS2 Reader Not Support !";
  return -1;
#endif
}

#if defined(ROS2_FOUND)
int DataReaderRos2::skipFrame(int skip) {
  int32_t next_consume_sync_index = 0;
  RS_ROSBAG_FRAME_READ_TYPE readType =
      RS_ROSBAG_FRAME_READ_TYPE::RS_ROSBAG_FRAME_READ_ORDER;
  {
    std::lock_guard<std::mutex> lg(processed_message_map_mtx_);
    if (cur_consume_sync_index_ == -1) {
      // 未播放过 或 播放完成重新开始播放
      next_consume_sync_index = cur_consume_index_ + skip;
    } else {
      next_consume_sync_index = cur_consume_sync_index_ + skip;
    }
    if (next_consume_sync_index < 0 ||
        next_consume_sync_index >=
            static_cast<int32_t>(main_sync_index_.size())) {
      AERROR << name() << ": SkipFrame Out of Range: next_consume_sync_index = "
             << next_consume_sync_index
             << ", cur_consume_sync_index_ = " << cur_consume_sync_index_
             << ", cur_consume_index_ = " << cur_consume_index_
             << ", total_frame_cnt_ = " << total_frame_cnt_;
      return 1;
    }
    // 重新开始计数
    if (cur_consume_sync_index_ == -1) {
      cur_consume_index_ = -1;
    }
    // 判断是否为顺序读取
    readType = checkRosbagFrameReadType(next_consume_sync_index);
  }

  std::map<uint64_t, DataReaderSingleItem::Ptr> frameMessages;
  {
    std::lock_guard<std::mutex> lg(processed_message_map_mtx_);
    if (processed_message_map_.find(next_consume_sync_index) !=
        processed_message_map_.end()) {
      frameMessages = processed_message_map_[next_consume_sync_index];
    }
  }

  int ret = 0;
  if (frameMessages.empty()) {
    ret = readFrame(next_consume_sync_index, readType, frameMessages);
    if (ret != 0) {
      return -1;
    }

    {
      std::lock_guard<std::mutex> lg(processed_message_map_mtx_);
      processed_message_map_.insert({next_consume_sync_index, frameMessages});
    }
  }

  {
    std::lock_guard<std::mutex> lg(processed_message_map_mtx_);
    ret = playFrame(frameMessages);
    if (ret != 0) {
      RERROR << name() << ": play frame Failed: ret = " << ret;
      return -4;
    }
    cur_consume_sync_index_ = next_consume_sync_index;
    cur_consume_index_ = cur_consume_sync_index_;
  }

  // 删除帧
  eraseFrame((skip > 0 ? true : false));

  RINFO << "Skip Frame Mode: cur_consume_sync_index_ = "
        << cur_consume_sync_index_
        << ", cur_consume_index_ = " << cur_consume_index_
        << ", total_frame_cnt_ = " << total_frame_cnt_;

  return 0;
}

int DataReaderRos2::play() {
  {
    std::lock_guard<std::mutex> lg(play_status_mtx_);
    if (play_status_ == RS_DATA_READ_PLAY_STATUS::RS_DATA_READ_PLAY_PLAYING) {
      return 0;
    }
  }
  int32_t next_consume_sync_index = 0;
  {
    std::lock_guard<std::mutex> lg(processed_message_map_mtx_);
    if (cur_consume_sync_index_ == -1 ||
        cur_consume_sync_index_ >= total_frame_cnt_ - 1) {
      // 未播放过 或 播放完成重新开始播放
      cur_consume_sync_index_ = -1;
      cur_consume_index_ = -1;
      next_consume_sync_index = 0;
    } else {
      next_consume_sync_index = cur_consume_sync_index_ + 1;
    }
  }
  consumeFrame(next_consume_sync_index,
               RS_ROSBAG_FRAME_CONSUME_TYPE::RS_ROSBAG_FRAME_CONSUME_PLAY,
               false);

  while (play_status_ != RS_DATA_READ_PLAY_STATUS::RS_DATA_READ_PLAY_PLAYING) {
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
  }

  return 0;
}

int DataReaderRos2::pause() {
  {
    std::lock_guard<std::mutex> lg(play_status_mtx_);
    if (play_status_ == RS_DATA_READ_PLAY_STATUS::RS_DATA_READ_PLAY_PAUSE) {
      return 0;
    }
  }

  consumeFrame(-1, RS_ROSBAG_FRAME_CONSUME_TYPE::RS_ROSBAG_FRAME_CONSUME_PAUSE,
               true);

  while (play_status_ != RS_DATA_READ_PLAY_STATUS::RS_DATA_READ_PLAY_PAUSE) {
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
  }
  return 0;
}

int DataReaderRos2::stop() {
  if (is_consume_running_) {
    {
      std::lock_guard<std::mutex> lg(processed_message_map_mtx_);
      is_consume_running_ = false;
      processed_message_map_cond_.notify_all();
    }

    if (consume_work_thread_ptr_ != nullptr) {
      if (consume_work_thread_ptr_->joinable()) {
        consume_work_thread_ptr_->join();
      }
      consume_work_thread_ptr_.reset();
    }
  }

  if (ros2_reader_ptr_ != nullptr) {
    ros2_reader_ptr_->close();
    ros2_reader_ptr_.reset();
  }

  RINFO << name() << ": finished !";

  return 0;
}

uint64_t DataReaderRos2::getCurrentTimestamp() {
  std::lock_guard<std::mutex> lg(processed_message_map_mtx_);
  if (cur_consume_index_ < 0 || cur_consume_index_ >= total_frame_cnt_) {
    return 0;
  } else {
    return main_sync_index_[cur_consume_index_].timestamp;
  }
}

int32_t DataReaderRos2::getCurrentFrameIndex() {
  std::lock_guard<std::mutex> lg(processed_message_map_mtx_);
  return cur_consume_index_;
}

int DataReaderRos2::init() {
  init_progress_ = 0;
  stop();

  preload_main_frame_cnt_ = data_io_config_.preLoadMainSyncCnt;
  if (ros2_reader_ptr_ == nullptr) {
    try {
      ros2_reader_ptr_.reset(new rosbag2_cpp::Reader());
    } catch (...) {
      RERROR << name() << ": Malloc rosbag2_cpp::Reader Failed !";
      return -1;
    }

    try {
      ros2_reader_ptr_->open(data_io_config_.sourceDataFilePath);
    } catch (...) {
      RERROR << name() << ": Open Ros2 File To Read Failed: "
             << data_io_config_.sourceDataFilePath;
      return -2;
    }
  }

  // 获取配置的可读消息话题
  auto readTopicNames = data_io_config_.readTopicNames;
  topicname_filters_.clear();

  const rosbag2_storage::BagMetadata &bagMetadata =
      ros2_reader_ptr_->get_metadata();

  topicname_id_mapper_.clear();
  id_topicname_mapper_.clear();
  topicname_datatype_mapper_.clear();
  int32_t id = 0;
  bool isMainSyncTopicMatch = false;
  std::map<std::string, int32_t> lastAttachH265Info;
  std::set<std::string> allReadTopicNames;
  for (size_t i = 0; i < bagMetadata.topics_with_message_count.size(); ++i) {
    const std::string &topicName =
        bagMetadata.topics_with_message_count[i].topic_metadata.name;
    const std::string &dataType =
        bagMetadata.topics_with_message_count[i].topic_metadata.type;
    const uint32_t messageCnt =
        bagMetadata.topics_with_message_count[i].message_count;
    RINFO << name() << ": dataType = " << dataType;
    topicname_id_mapper_[topicName] = id;
    topicname_datatype_mapper_[topicName] = dataType;
    id_topicname_mapper_[id] = topicName;
    ++id;

    if (readTopicNames.empty()) {
      allReadTopicNames.insert(topicName);
    }

    // 需要过滤的话题
    if (readTopicNames.find(topicName) == readTopicNames.end() &&
        !readTopicNames.empty()) {
      topicname_filters_.insert(topicName);
      RINFO << name() << ": TopicName = " << topicName << " Filter !";
      continue;
    }
    if (messageCnt == 0) {
      topicname_filters_.insert(topicName);
      RINFO << name() << ": TopicName = " << topicName << " Filter(0) !";
      continue;
    }

    if (isMainSyncTopicMatch == false) {
      isMainSyncTopicMatch = (data_io_config_.mainSyncTopicName == topicName);
    }

    // 需要H265处理的消息
    RS_DATA_MESSAGE_PROCESS_TYPE processType =
        fromDataTypeToProcessType(dataType);
    if (processType ==
            RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_H265 &&
        lastAttachH265Info.find(topicName) == lastAttachH265Info.end()) {
      lastAttachH265Info.insert({topicName, -1});
    }
  }
  // 获取需要读取的话题名称
  if (!allReadTopicNames.empty() && readTopicNames.empty()) {
    readTopicNames = allReadTopicNames;
  }

  if (!isMainSyncTopicMatch || readTopicNames.empty()) {
    RERROR << name() << ": No Matched Main Sync TopicName = "
           << data_io_config_.mainSyncTopicName;
    return -3;
  }

  {
    std::lock_guard<std::mutex> lg(play_status_mtx_);
    play_status_ = RS_DATA_READ_PLAY_STATUS::RS_DATA_READ_PLAY_PAUSE;
  }

  begin_timestamp_ns_ = std::chrono::time_point_cast<std::chrono::nanoseconds>(
                            bagMetadata.starting_time)
                            .time_since_epoch()
                            .count();
  total_duration_ns_ =
      std::chrono::duration_cast<std::chrono::nanoseconds>(bagMetadata.duration)
          .count();
  end_timestamp_ns_ = begin_timestamp_ns_ + total_duration_ns_;

  RINFO << name() << ": Before begin_timestamp_ns_ = "
        << std::to_string(begin_timestamp_ns_)
        << ", end_timestamp_ns_ = " << std::to_string(end_timestamp_ns_);

  // 进行两遍预处理
  std::set<std::string> processedTopics;
  std::map<std::string, uint64_t> lastH265IFrameTimestampInfo;
  while (ros2_reader_ptr_->has_next()) {
    const auto &msgPtr = ros2_reader_ptr_->read_next();
    if (msgPtr == nullptr) {
      RERROR << name() << ": Ros2 Reader Message Failed !";
      return -4;
    }

    const uint64_t timestampNs = msgPtr->time_stamp;
    const std::string &topicName = msgPtr->topic_name;
    const std::string &dataType = topicname_datatype_mapper_[topicName];

    // 获取所有I帧最接近开始时间的时间
    if (data_io_config_.readTopicTimeRange.first != -1 &&
        lastAttachH265Info.find(topicName) != lastAttachH265Info.end()) {
      rscamera_msg::msg::RsCompressedImage rosMsg;
      rclcpp::SerializedMessage extracted_serialized_msg(
          *msgPtr->serialized_data);
      static rclcpp::Serialization<rscamera_msg::msg::RsCompressedImage>
          serialization;
      serialization.deserialize_message(&extracted_serialized_msg, &rosMsg);

      if (rosMsg.type == 1) {
        lastH265IFrameTimestampInfo[topicName] = timestampNs;
      }
    }

    // 在过滤范围的话题不分析数据
    if (topicname_filters_.find(topicName) != topicname_filters_.end()) {
      continue;
    }

    // 更新图像分辨率信息
    if (processedTopics.find(topicName) == processedTopics.end()) {
      if (dataType == ROS_IMAGEFRAME_DATATYPE2) {
        // 普通图像数据
        sensor_msgs::msg::Image rosMsg;
        int ret = convertToMessageT<sensor_msgs::msg::Image>(msgPtr, rosMsg);
        if (ret != 0) {
          RERROR
              << name() << ": TopicName = " << topicName
              << ", MessageTopicName = " << msgPtr->topic_name
              << " Convert To hyper_vision_msgs::msg::ImageFrame Failed: ret = "
              << ret << ", Filter !";
          topicname_filters_.insert(topicName);
        } else {
          processedTopics.insert(topicName);
          const std::pair<uint32_t, uint32_t> imageSize{rosMsg.width,
                                                        rosMsg.height};
          std::pair<uint32_t, uint32_t> imageSize2;
          int ret = DataTopicNameSupportUtil::fromTopicNameToImageSize(
              topicName, imageSize2);
          if (ret == 0 && imageSize2 != imageSize) {
            topicname_filters_.insert(topicName);
            RWARN << name() << ": TopicName = " << topicName
                  << " Filter: Image ImageSize Not Matched !";
          } else {
            DataTopicNameSupportUtil::addTopicNameImageSize(
                topicName, imageSize.first, imageSize.second);
            RINFO << name() << ": TopicName = " << topicName
                  << ", MessageTopicName = " << msgPtr->topic_name
                  << ", Update Image Size: wxh = " << imageSize.first << "x"
                  << imageSize.second;
          }
        }
      } else if (dataType == ROS_COMPRESSEDIMAGE_DATATYPE) {
        // JPEG压缩图像数据
        sensor_msgs::msg::CompressedImage::SharedPtr rosMsgPtr(
            new sensor_msgs::msg::CompressedImage());
        int ret = convertToMessageT<sensor_msgs::msg::CompressedImage>(
            msgPtr, *rosMsgPtr);
        if (ret != 0) {
          RERROR
              << name() << ": TopicName = " << topicName
              << ", MessageTopicName = " << msgPtr->topic_name
              << " Convert To sensor_msgs::msg::CompressedImage Failed: ret = "
              << ret << ", Filter !";
          topicname_filters_.insert(topicName);
        } else {
          processedTopics.insert(topicName);
          cv::Mat bgr_mat = cv::imdecode(rosMsgPtr->data, cv::IMREAD_COLOR);
          if (!bgr_mat.empty()) {
            const std::pair<uint32_t, uint32_t> imageSize{bgr_mat.cols,
                                                          bgr_mat.rows};

            std::pair<uint32_t, uint32_t> imageSize2;
            int ret = DataTopicNameSupportUtil::fromTopicNameToImageSize(
                topicName, imageSize2);
            if (ret == 0 && imageSize2 != imageSize) {
              topicname_filters_.insert(topicName);
              RWARN << name() << ": TopicName = " << topicName
                    << " Filter: CompresssedImage ImageSize Not Matched !";
            } else {
              DataTopicNameSupportUtil::addTopicNameImageSize(
                  topicName, imageSize.first, imageSize.second);
              RINFO << name() << ": TopicName = " << topicName
                    << ", MessageTopicName = " << msgPtr->topic_name
                    << ", Update Image Size: wxh = " << imageSize.first << "x"
                    << imageSize.second;
            }
          } else {
            topicname_filters_.insert(topicName);
            RWARN << name() << ": TopicName = " << topicName
                  << " Filter: CompresssedImage Image Parse ImageSize Failed !";
          }
        }
      } else if (dataType == ROS_IMAGEFRAME_DATATYPE) {
        // imageFrame数据
        hyper_vision_msgs::msg::ImageFrame::SharedPtr rosMsgPtr(
            new hyper_vision_msgs::msg::ImageFrame());
        int ret = convertToMessageT<hyper_vision_msgs::msg::ImageFrame>(
            msgPtr, *rosMsgPtr);
        if (ret != 0) {
          RERROR
              << name() << ": TopicName = " << topicName
              << ", MessageTopicName = " << msgPtr->topic_name
              << " Convert To hyper_vision_msgs::msg::ImageFrame Failed: ret = "
              << ret << ", Filter !";
          topicname_filters_.insert(topicName);
        } else {
          processedTopics.insert(topicName);
          const std::pair<uint32_t, uint32_t> imageSize{rosMsgPtr->width,
                                                        rosMsgPtr->height};

          std::pair<uint32_t, uint32_t> imageSize2;
          int ret = DataTopicNameSupportUtil::fromTopicNameToImageSize(
              topicName, imageSize2);
          if (ret == 0 && imageSize2 != imageSize) {
            topicname_filters_.insert(topicName);
            RWARN << name() << ": TopicName = " << topicName
                  << " Filter: hyper_vision_msgs::msg::ImageFrame ImageSize "
                     "Not Matched !";
          } else {
            DataTopicNameSupportUtil::addTopicNameImageSize(
                topicName, imageSize.first, imageSize.second);
            RINFO << name() << ": TopicName = " << topicName
                  << ", MessageTopicName = " << msgPtr->topic_name
                  << ", Update Image Size: wxh = " << imageSize.first << "x"
                  << imageSize.second;
          }
        }
      } else if (dataType == ROS_POINTCLOUD2_DATATYPE) {
        // 点云数据: 如果不是支持的话题名称，则强制为DEPTH数据
        RS_SUPPORT_DATA_TYPE pointType;
        int ret = DataTopicNameSupportUtil::fromTopicNameToDataType(topicName,
                                                                    pointType);
        if (ret != 0) {
          DataTopicNameSupportUtil::addTopicNameDataType(
              topicName, RS_SUPPORT_DATA_TYPE::RS_SUPERSENSOR_DEPTH_FRAME);
        }
      } else {
        processedTopics.insert(topicName);
      }
    }

    // 处理完成
    if (processedTopics == readTopicNames &&
        (data_io_config_.readTopicTimeRange.first == -1 ||
         timestampNs > data_io_config_.readTopicTimeRange.first)) {
      RINFO << name() << ": Topic Process Stage 1 Finished !";
      break;
    }
  }

  // 删除被过滤的话题
  for (auto iterSet = topicname_filters_.begin();
       iterSet != topicname_filters_.end(); ++iterSet) {
    auto iterSet2 = readTopicNames.find(*iterSet);
    if (iterSet2 != readTopicNames.end()) {
      readTopicNames.erase(iterSet2);
    }
  }

  ros2_reader_ptr_->close();
  ros2_reader_ptr_.reset();

  // 更新时间过滤的开始时间
  if (data_io_config_.readTopicTimeRange.first != -1) {
    uint64_t lastBeginTimestampNs = data_io_config_.readTopicTimeRange.first;
    for (auto iterMap = lastH265IFrameTimestampInfo.begin();
         iterMap != lastH265IFrameTimestampInfo.end(); ++iterMap) {
      if (lastBeginTimestampNs > iterMap->second) {
        lastBeginTimestampNs = iterMap->second;
      }
    }
    RINFO << name() << ": Before BeginTimestampNs = "
          << std::to_string(data_io_config_.readTopicTimeRange.first)
          << ", After BeginTimestampNs = "
          << std::to_string(lastBeginTimestampNs - 10);
    data_io_config_.readTopicTimeRange.first = lastBeginTimestampNs - 10;
  }

  // 重新打开
  if (ros2_reader_ptr_ == nullptr) {
    try {
      ros2_reader_ptr_.reset(new rosbag2_cpp::Reader());
    } catch (...) {
      RERROR << name() << ": Malloc rosbag2_cpp::Reader Failed !";
      return -7;
    }

    try {
      ros2_reader_ptr_->open(data_io_config_.sourceDataFilePath);
    } catch (...) {
      RERROR << name() << ": Open Ros2 File To Read Failed: "
             << data_io_config_.sourceDataFilePath;
      return -8;
    }

    // 设置读取话题的白名单
    try {
      rosbag2_storage::StorageFilter filter;
      for (auto iterSet = readTopicNames.begin();
           iterSet != readTopicNames.end(); ++iterSet) {
        filter.topics.push_back(*iterSet);

        RERROR << "RUN HERE: topicName = " << (*iterSet);
      }
      ros2_reader_ptr_->set_filter(filter);
    } catch (...) {
      RERROR << name() << ": Set Ros2 Filter Failed: "
             << data_io_config_.sourceDataFilePath;
      return -9;
    }
  }

  bool isFindMainFrame = false;
  while (ros2_reader_ptr_->has_next()) {
    const auto &msgPtr = ros2_reader_ptr_->read_next();
    if (msgPtr == nullptr) {
      RERROR << name() << ": Ros2 Reader Message Failed !";
      return -10;
    }

    const uint64_t timestampNs = msgPtr->time_stamp;
    const std::string &topicName = msgPtr->topic_name;

    // RERROR << name() << ": After Filter: topicName = " << topicName;

    // 进行时间过滤
    if (data_io_config_.readTopicTimeRange.first != -1 &&
        timestampNs < data_io_config_.readTopicTimeRange.first) {
      RINFO << name()
            << ": Read Topic Start Timestamp Filter: topicName = " << topicName
            << ", timestampNs = " << std::to_string(timestampNs)
            << ", BeginTimestampNs = "
            << std::to_string(data_io_config_.readTopicTimeRange.first);
      continue;
    } else if (data_io_config_.readTopicTimeRange.second != -1 &&
               timestampNs > data_io_config_.readTopicTimeRange.second) {
      RINFO << name()
            << ": Read Topic End Timestamp Filter: topicName = " << topicName
            << ", timestampNs = " << std::to_string(timestampNs)
            << ", EndTimestampNs = "
            << std::to_string(data_io_config_.readTopicTimeRange.second);
      continue;
    }

    // 构造索引
    RosbagIndexInfo indexInfo;
    indexInfo.timestamp = timestampNs;
    indexInfo.topicname_id = topicname_id_mapper_[topicName];
    message_iterators_.push_back(indexInfo);

    // 对于H265
    if (lastAttachH265Info.find(topicName) != lastAttachH265Info.end()) {
      rscamera_msg::msg::RsCompressedImage rosMsg;
      rclcpp::SerializedMessage extracted_serialized_msg(
          *msgPtr->serialized_data);
      static rclcpp::Serialization<rscamera_msg::msg::RsCompressedImage>
          serialization;
      serialization.deserialize_message(&extracted_serialized_msg, &rosMsg);

      if (rosMsg.type == 1) {
        lastAttachH265Info[topicName] = message_iterators_.size() - 1;
      }
    }

    if (isFindMainFrame && main_sync_index_.size() > 0) {
      main_sync_index_[main_sync_index_.size() - 1].next_timestamp =
          timestampNs;
      isFindMainFrame = false;
    }

    // 处理主同步消息
    if (topicName == data_io_config_.mainSyncTopicName) {
      // 消息同步信息
      RosbagMainIndexInfo indexInfo;
      indexInfo.timestamp = timestampNs;
      indexInfo.mainSyncIndex = message_iterators_.size() - 1;
      indexInfo.attachH265IFrameIndexs = lastAttachH265Info;
      main_sync_index_.push_back(indexInfo);

      isFindMainFrame = true;
    }

    const std::string &dataType = topicname_datatype_mapper_[topicName];
    RS_DATA_MESSAGE_PROCESS_TYPE processType =
        fromDataTypeToProcessType(dataType);

    int ret = 0;
    switch (processType) {
    case RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_JPEG: {
      ret = initAddJpegCoder(topicName);
      break;
    }
    case RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_H265: {
      ret = initAddH265Coder(topicName);
      break;
    }
    default: {
      break;
    }
    }

    if (ret != 0) {
      RERROR << name()
             << ": Add Jpeg/H265 Coder Failed: topicName = " << topicName
             << ", dataType = " << dataType << ", ret = " << ret;
      return -11;
    }

    // 更新进度
    init_progress_ =
        std::min(static_cast<uint32_t>((timestampNs - begin_timestamp_ns_) *
                                       100.0 / total_duration_ns_),
                 99u);
  }
  RINFO << name()
        << ": Topic Process Stage 2 Finished: main_sync_index_ SIZE = "
        << main_sync_index_.size();

  // 更新主帧信息
  if (main_sync_index_.size() &&
      (data_io_config_.readTopicTimeRange.first != -1 ||
       data_io_config_.readTopicTimeRange.second != -1)) {
    begin_timestamp_ns_ = main_sync_index_[0].timestamp;
    end_timestamp_ns_ = main_sync_index_[main_sync_index_.size() - 1].timestamp;
    total_duration_ns_ = end_timestamp_ns_ - begin_timestamp_ns_;
  }

  RINFO << name() << ": After begin_timestamp_ns_ = "
        << std::to_string(begin_timestamp_ns_)
        << ", end_timestamp_ns_ = " << std::to_string(end_timestamp_ns_);

  total_frame_cnt_ = main_sync_index_.size();
  cur_consume_sync_index_ = -1;
  cur_consume_index_ = -1;

  ros2_reader_ptr_->close();
  ros2_reader_ptr_.reset();

  // 重新打开
  if (ros2_reader_ptr_ == nullptr) {
    try {
      ros2_reader_ptr_.reset(new rosbag2_cpp::Reader());
    } catch (...) {
      RERROR << name() << ": Malloc rosbag2_cpp::Reader Failed !";
      return -13;
    }

    try {
      ros2_reader_ptr_->open(data_io_config_.sourceDataFilePath);
    } catch (...) {
      RERROR << name() << ": Open Ros2 File To Read Failed: "
             << data_io_config_.sourceDataFilePath;
      return -14;
    }

    // 设置读取话题的白名单
    try {
      rosbag2_storage::StorageFilter filter;
      for (auto iterSet = readTopicNames.begin();
           iterSet != readTopicNames.end(); ++iterSet) {
        filter.topics.push_back(*iterSet);
      }
      ros2_reader_ptr_->set_filter(filter);
    } catch (...) {
      RERROR << name() << ": Set Ros2 Filter Failed: "
             << data_io_config_.sourceDataFilePath;
      return -15;
    }
  }

  try {
    is_consume_running_ = true;
    consume_work_thread_ptr_.reset(
        new std::thread(&DataReaderRos2::consumeWorkThread, this));
  } catch (...) {
    is_consume_running_ = false;
    RERROR << name() << ": Create Use Work Thread Failed !";
    return -8;
  }
  init_progress_ = 100;

  return 0;
}

int DataReaderRos2::prepareFrameMessage(
    const std::map<uint64_t, DataReaderSingleItem::Ptr> &messages,
    std::map<uint64_t, DataReaderSingleItem::Ptr> &singleFrameMessageMapper) {
  for (auto iterMap = messages.begin(); iterMap != messages.end(); ++iterMap) {
    auto singleMsgInfoPtr = iterMap->second;
    if (singleMsgInfoPtr->is_use_org_message_) {
      // 不需要处理的消息, 直接加入
      singleMsgInfoPtr->is_ready_ = true;
      if (singleMsgInfoPtr->is_belong_) {
        singleFrameMessageMapper.insert(
            {singleMsgInfoPtr->timestamp_ns_, singleMsgInfoPtr});
      }
    } else if (singleMsgInfoPtr->process_type_ ==
               RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_JPEG) {
      // JPEG 解码
      const std::string &topicName =
          id_topicname_mapper_[singleMsgInfoPtr->topicname_id_];

      sensor_msgs::msg::CompressedImage::SharedPtr *ptr =
          singleMsgInfoPtr->org_message_
              ->AnyCast<sensor_msgs::msg::CompressedImage::SharedPtr>();
      if (ptr == nullptr) {
        RWARN << name()
              << ": AnyCast To sensor_msgs::msg::CompressedImage::SharedPtr "
                 "Failed !";
        continue;
      } else if (*ptr == nullptr) {
        RWARN << name()
              << ": AnyCast To sensor_msgs::msg::CompressedImage::SharedPtr is "
                 "Nullptr !";
        continue;
      }

      const auto &rosMsgPtr = *ptr;
      if (jpeg_coder_map_.find(topicName) != jpeg_coder_map_.end()) {
        if (jpeg_coder_map_[topicName]) {
          // 获取图像的尺寸
          std::pair<uint32_t, uint32_t> image_size;
          int ret = DataTopicNameSupportUtil::fromTopicNameToImageSize(
              topicName, image_size);
          if (ret != 0) {
            RERROR << name() << ": From TopicName = " << topicName
                   << " To Image Size Failed !";
            continue;
          }

          std::shared_ptr<robosense::common::ImageFrame> imageFramePtr(
              new robosense::common::ImageFrame());
          imageFramePtr->capture_time.tv_sec = rosMsgPtr->header.stamp.sec;
          imageFramePtr->capture_time.tv_usec =
              rosMsgPtr->header.stamp.nanosec / 1000;
          size_t imageRgbSize = image_size.first * image_size.second * 3;
          imageFramePtr->data_bytes = imageRgbSize;
          imageFramePtr->width = image_size.first;
          imageFramePtr->height = image_size.second;
          imageFramePtr->step = image_size.first * 3;
          imageFramePtr->data =
              std::shared_ptr<uint8_t>(new uint8_t[imageFramePtr->data_bytes],
                                       std::default_delete<uint8_t[]>());
          imageFramePtr->frame_format =
              robosense::common::ImageFrameFormat::FRAME_FORMAT_RGB24;
          ret = jpeg_coder_map_[topicName]->decode(
              rosMsgPtr->data.data(), rosMsgPtr->data.size(),
              imageFramePtr->data.get(), imageRgbSize);
          if (ret != 0) {
            RERROR << name()
                   << ": JPEG Decode Failed: topicName = " << topicName
                   << ", ret = " << ret;
            continue;
          }

          // 解码好的相机RGB数据
          singleMsgInfoPtr->message_.reset(new rally::Any(imageFramePtr));

          // 保存JPEG解码的数据
          if (singleMsgInfoPtr->is_belong_) {
            singleMsgInfoPtr->is_ready_ = true;
            singleFrameMessageMapper.insert(
                {singleMsgInfoPtr->timestamp_ns_, singleMsgInfoPtr});
          }
        }
      }
    } else if (singleMsgInfoPtr->process_type_ ==
               RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_H265) {
      // H265 解码
      const std::string &topicName =
          id_topicname_mapper_[singleMsgInfoPtr->topicname_id_];

      rscamera_msg::msg::RsCompressedImage::SharedPtr *ptr =
          singleMsgInfoPtr->org_message_
              ->AnyCast<rscamera_msg::msg::RsCompressedImage::SharedPtr>();
      if (ptr == nullptr) {
        RWARN << name()
              << ": AnyCast To rscamera_msg::msg::RsCompressedImage::SharedPtr "
                 "Failed !";
        continue;
      } else if (*ptr == nullptr) {
        RWARN << name()
              << ": AnyCast To rscamera_msg::msg::RsCompressedImage::SharedPtr "
                 "is "
                 "Nullptr !";
        continue;
      }

      const auto &rosMsgPtr = *ptr;
      if (h265_coder_map_.find(topicName) != h265_coder_map_.end()) {
        if (h265_coder_map_[topicName]) {
          // RDEBUG << name() << ": singleMsgInfoPtr->is_belong_ = "
          //        << singleMsgInfoPtr->is_belong_
          //        << ", topicName = " << topicName
          //        << ", type = " << (int)(*ptr)->type;
          int ret = h265_coder_map_[topicName]->syncAddMessage(singleMsgInfoPtr,
                                                               false);
          if (ret != 0) {
            RERROR << name()
                   << ": H265 SYNC Decode Failed: topicName = " << topicName
                   << ", ret = " << ret;
            continue;
          }
          // RDEBUG << name() << ": singleMsgInfoPtr->is_belong_ = "
          //        << singleMsgInfoPtr->is_belong_;
          if (singleMsgInfoPtr->is_belong_) {
            singleMsgInfoPtr->is_ready_ = true;
            singleFrameMessageMapper.insert(
                {singleMsgInfoPtr->timestamp_ns_, singleMsgInfoPtr});
          }
        }
      }
    }
  }
  return 0;
}

void DataReaderRos2::consumeWorkThread() {
  int64_t nextFrameDiffTimestampNs = 0; // 前一帧主帧到下一帧第一帧的时间间隔
  uint64_t preFrameFinishTimestampNs = 0;
  while (is_consume_running_) {
    RosbagFrameConsumeOp consumeOp;
    std::map<uint64_t, DataReaderSingleItem::Ptr> singleFrameMessageMapper;
    {
      std::unique_lock<std::mutex> lg(processed_message_map_mtx_);
      processed_message_map_cond_.wait(lg, [this] {
        return !consume_queue_.empty() || !is_consume_running_;
      });
      if (!is_consume_running_) {
        break;
      }

      consumeOp = consume_queue_.front();
      consume_queue_.pop();
    }

    {
      bool isPause = false;
      {
        std::lock_guard<std::mutex> lg(play_status_mtx_);
        if (consumeOp.consume_type_ ==
            RS_ROSBAG_FRAME_CONSUME_TYPE::RS_ROSBAG_FRAME_CONSUME_PAUSE) {
          play_status_ = RS_DATA_READ_PLAY_STATUS::RS_DATA_READ_PLAY_PAUSE;
          RINFO << "DataReaderRos2::consumeWorkThread play_status_ = Pause";
          isPause = true;
        } else if (consumeOp.consume_type_ ==
                   RS_ROSBAG_FRAME_CONSUME_TYPE::RS_ROSBAG_FRAME_CONSUME_PLAY) {
          play_status_ = RS_DATA_READ_PLAY_STATUS::RS_DATA_READ_PLAY_PLAYING;
          RINFO << "DataReaderRos2::consumeWorkThread play_status_ = Playing";
        }
      }

      if (isPause) {
        std::unique_lock<std::mutex> lg(processed_message_map_mtx_);
        RINFO
            << "DataReaderRos2::consumeWorkThread: Pause Remove Consume Queue";
        while (!consume_queue_.empty()) {
          consume_queue_.pop();
        }
        nextFrameDiffTimestampNs = 0;
        preFrameFinishTimestampNs = 0;
        continue;
      }
    }

    {
      std::lock_guard<std::mutex> lg(processed_message_map_mtx_);
      if (processed_message_map_.find(consumeOp.consume_main_index_) !=
          processed_message_map_.end()) {
        singleFrameMessageMapper = processed_message_map_.begin()->second;
      }
    }

    if (singleFrameMessageMapper.empty()) {
      const RS_ROSBAG_FRAME_READ_TYPE readType =
          checkRosbagFrameReadType(consumeOp.consume_main_index_);
      int ret = readFrame(consumeOp.consume_main_index_, readType,
                          singleFrameMessageMapper);
      if (ret != 0) {
        continue;
      } else {
        std::lock_guard<std::mutex> lg(processed_message_map_mtx_);
        processed_message_map_.insert(
            {consumeOp.consume_main_index_, singleFrameMessageMapper});
      }
    }

    // 单帧范围内实现消息间隔控制
    {
      if (nextFrameDiffTimestampNs > 0) {
        const uint64_t currentTimestampNs = TIMESTAMP_NS;
        if (preFrameFinishTimestampNs != 0) {
          int64_t diffTimestampNs =
              currentTimestampNs - preFrameFinishTimestampNs;
          if (nextFrameDiffTimestampNs > diffTimestampNs) {
            std::this_thread::sleep_for(std::chrono::nanoseconds(
                nextFrameDiffTimestampNs - diffTimestampNs));
            RINFO << "Between Frame Sleep: "
                  << (nextFrameDiffTimestampNs - diffTimestampNs);
          }
        }
      }

      std::lock_guard<std::mutex> lg(processed_message_map_mtx_);
      playFrame(singleFrameMessageMapper);
      preFrameFinishTimestampNs = TIMESTAMP_NS;
      const auto &mainFrameIndex =
          main_sync_index_[consumeOp.consume_main_index_];
      if (mainFrameIndex.next_timestamp != 0) {
        nextFrameDiffTimestampNs =
            mainFrameIndex.next_timestamp - mainFrameIndex.timestamp;
        // RINFO << "nextFrameDiffTimestampNs = " << nextFrameDiffTimestampNs;
      } else {
        nextFrameDiffTimestampNs = 0;
      }
      cur_consume_sync_index_ = consumeOp.consume_main_index_;
      cur_consume_index_ = cur_consume_sync_index_;
    }

    // 更新播放状态
    {
      std::lock_guard<std::mutex> lg(play_status_mtx_);
      if (play_status_ == RS_DATA_READ_PLAY_STATUS::RS_DATA_READ_PLAY_PLAYING) {
        if (cur_consume_sync_index_ >= total_frame_cnt_ - 1) {
          cur_consume_sync_index_ = -1;
          nextFrameDiffTimestampNs = 0;
          preFrameFinishTimestampNs = 0;
          play_status_ = RS_DATA_READ_PLAY_STATUS::RS_DATA_READ_PLAY_PAUSE;
        } else {
          consumeFrame(
              cur_consume_sync_index_ + 1,
              RS_ROSBAG_FRAME_CONSUME_TYPE::RS_ROSBAG_FRAME_CONSUME_PLAY,
              false);
        }
      }
    }

    // 删除过多的缓冲数据
    eraseFrame(true);

    // 打印日志
    RINFO << "Continous Frame Mode: cur_consume_sync_index_ = "
          << cur_consume_sync_index_
          << ", cur_consume_index_ = " << cur_consume_index_
          << ", total_frame_cnt_ = " << total_frame_cnt_
          << ", processed_message_map_ size = "
          << processed_message_map_.size();
  }
}

int DataReaderRos2::readSyncFrame(
    const int index, std::map<uint64_t, DataReaderSingleItem::Ptr> &messages) {
  // 获取数据范围
  std::pair<int32_t, int32_t> rangeIndex;
  int flag = getMainSyncRangeIndex(index, rangeIndex);
  if (flag < 0) {
    RERROR << name() << ": read Sync Index = " << index << " Out Of Range !";
    return -1;
  }
  messages.clear();

  int32_t begin_index = rangeIndex.first;
  int32_t end_index = rangeIndex.second;
  RDEBUG << name() << ": read Sync index = " << index
         << ", begin_index = " << begin_index << ", end_index = " << end_index;
  for (int32_t i = begin_index; i <= end_index; ++i) {
    const auto &index_Info = message_iterators_[i];
    if (!ros2_reader_ptr_->has_next()) {
      RERROR << name() << ": ros2 has_next() is false !";
      return -2;
    }

    std::shared_ptr<rosbag2_storage::SerializedBagMessage> bagMsgPtr =
        ros2_reader_ptr_->read_next();
    if (bagMsgPtr == nullptr) {
      RERROR << name() << ": ros2 read_next() is nullptr !";
      return -3;
    }

    const std::string &topicName =
        id_topicname_mapper_[index_Info.topicname_id];
    const std::string &dataType = topicname_datatype_mapper_[topicName];
    const uint64_t timestampNs = index_Info.timestamp;
    // std::cout << "dataType = " << dataType << ", topicName = " << topicName
    //           << ", timestamp = " << std::to_string(timestampNs)
    //           << ", messageTopic = " << bagMsgPtr->topic_name
    //           << ", messageTimestamp = "
    //           << std::to_string(bagMsgPtr->time_stamp) << ", i = " << i
    //           << std::endl;
    DataReaderSingleItem::Ptr msgPtr(new DataReaderSingleItem());
    msgPtr->timestamp_ns_ = timestampNs;
    msgPtr->topicname_id_ = topicname_id_mapper_[topicName];
    msgPtr->is_belong_ = true;
    msgPtr->is_ready_ = false;
    msgPtr->is_use_org_message_ = fromDataTypeToIsUseOriMessage(dataType);
    msgPtr->process_type_ = fromDataTypeToProcessType(dataType);
    if (dataType == ROS_MOTIONFRAME_DATATYPE) {
      // 在正常范围内
      hyper_vision_msgs::msg::MotionFrame rosMsg;
      int ret = convertToMessageT<hyper_vision_msgs::msg::MotionFrame>(
          bagMsgPtr, rosMsg);
      if (ret != 0) {
        RERROR << name() << ": TopicName = " << topicName
               << ", MessageTopicName = " << bagMsgPtr->topic_name
               << " Convert To hyper_vision_msgs::msg::MotionFrame Failed: "
                  "ret = "
               << ret;
        return -4;
      }

      std::shared_ptr<robosense::common::MotionFrame> rsMsgPtr(
          new robosense::common::MotionFrame());
      ret = DataConverterRos2::fromRosMsgToMsg(rosMsg, *rsMsgPtr);
      if (ret != 0) {
        RERROR << name() << ": Read TopicName = " << topicName
               << ", DataType = " << dataType
               << ", Ros Message To Msg Failed: ret = " << ret;
        return -5;
      }
      msgPtr->org_message_.reset(new rally::Any(rsMsgPtr));
    } else if (dataType == ROS_DEPTHFRAME_DATATYPE) {
      hyper_vision_msgs::msg::DepthFrame rosMsg;
      int ret = convertToMessageT<hyper_vision_msgs::msg::DepthFrame>(bagMsgPtr,
                                                                      rosMsg);
      if (ret != 0) {
        RERROR
            << name() << ": TopicName = " << topicName
            << ", MessageTopicName = " << bagMsgPtr->topic_name
            << " Convert To hyper_vision_msgs::msg::DepthFrame Failed: ret = "
            << ret;
        return -6;
      }

      std::shared_ptr<robosense::common::DepthFrame> rsMsgPtr(
          new robosense::common::DepthFrame());
      ret = DataConverterRos2::fromRosMsgToMsg(rosMsg, *rsMsgPtr);
      if (ret != 0) {
        RERROR << name() << ": Read TopicName = " << topicName
               << ", DataType = " << dataType
               << ", Ros Message To Msg Failed: ret = " << ret;
        return -7;
      }
      msgPtr->org_message_.reset(new rally::Any(rsMsgPtr));
    } else if (dataType == ROS_IMAGEFRAME_DATATYPE) {
      hyper_vision_msgs::msg::ImageFrame rosMsg;
      int ret = convertToMessageT<hyper_vision_msgs::msg::ImageFrame>(bagMsgPtr,
                                                                      rosMsg);
      if (ret != 0) {
        RERROR
            << name() << ": TopicName = " << topicName
            << ", MessageTopicName = " << bagMsgPtr->topic_name
            << " Convert To hyper_vision_msgs::msg::ImageFrame Failed: ret = "
            << ret;
        return -8;
      }

      std::shared_ptr<robosense::common::ImageFrame> rsMsgPtr(
          new robosense::common::ImageFrame());
      ret = DataConverterRos2::fromRosMsgToMsg(rosMsg, *rsMsgPtr);
      if (ret != 0) {
        RERROR << name() << ": Read TopicName = " << topicName
               << ", DataType = " << dataType
               << ", Ros Message To Msg Failed: ret = " << ret;
        return -9;
      }
      msgPtr->org_message_.reset(new rally::Any(rsMsgPtr));
    } else if (dataType == ROS_MOTIONFRAME_DATATYPE2) {
      // 在正常范围内
      sensor_msgs::msg::Imu rosMsg;
      int ret = convertToMessageT<sensor_msgs::msg::Imu>(bagMsgPtr, rosMsg);
      if (ret != 0) {
        RERROR << name() << ": TopicName = " << topicName
               << ", MessageTopicName = " << bagMsgPtr->topic_name
               << " Convert To hyper_vision_msgs::msg::MotionFrame Failed: "
                  "ret = "
               << ret;
        return -10;
      }

      std::shared_ptr<robosense::common::MotionFrame> rsMsgPtr(
          new robosense::common::MotionFrame());
      ret = DataConverterRos2::fromRosMsgToMsg(rosMsg, *rsMsgPtr);
      if (ret != 0) {
        RERROR << name() << ": Read TopicName = " << topicName
               << ", DataType = " << dataType
               << ", Ros Message To Msg Failed: ret = " << ret;
        return -11;
      }
      msgPtr->org_message_.reset(new rally::Any(rsMsgPtr));
    } else if (dataType == ROS_IMAGEFRAME_DATATYPE2) {
      sensor_msgs::msg::Image rosMsg;
      int ret = convertToMessageT<sensor_msgs::msg::Image>(bagMsgPtr, rosMsg);
      if (ret != 0) {
        RERROR
            << name() << ": TopicName = " << topicName
            << ", MessageTopicName = " << bagMsgPtr->topic_name
            << " Convert To hyper_vision_msgs::msg::ImageFrame Failed: ret = "
            << ret;
        return -12;
      }

      std::shared_ptr<robosense::common::ImageFrame> rsMsgPtr(
          new robosense::common::ImageFrame());
      ret = DataConverterRos2::fromRosMsgToMsg(rosMsg, *rsMsgPtr);
      if (ret != 0) {
        RERROR << name() << ": Read TopicName = " << topicName
               << ", DataType = " << dataType
               << ", Ros Message To Msg Failed: ret = " << ret;
        return -13;
      }
      msgPtr->org_message_.reset(new rally::Any(rsMsgPtr));
    } else if (dataType == ROS_COMPRESSEDIMAGE_DATATYPE) {
      sensor_msgs::msg::CompressedImage::SharedPtr rosMsgPtr(
          new sensor_msgs::msg::CompressedImage());
      int ret = convertToMessageT<sensor_msgs::msg::CompressedImage>(
          bagMsgPtr, *rosMsgPtr);
      if (ret != 0) {
        RERROR << name() << ": TopicName = " << topicName
               << ", MessageTopicName = " << bagMsgPtr->topic_name
               << " Convert To sensor_msgs::msg::CompressedImage Failed: ret = "
               << ret;
        return -14;
      }

      msgPtr->org_message_.reset(new rally::Any(rosMsgPtr));
    } else if (dataType == ROS_POINTCLOUD2_DATATYPE) {
      sensor_msgs::msg::PointCloud2 rosMsg;
      int ret =
          convertToMessageT<sensor_msgs::msg::PointCloud2>(bagMsgPtr, rosMsg);
      if (ret != 0) {
        RERROR << name() << ": TopicName = " << topicName
               << ", MessageTopicName = " << bagMsgPtr->topic_name
               << " Convert To sensor_msgs::msg::PointCloud2 Failed: ret = "
               << ret;
        return -15;
      }

      RS_SUPPORT_DATA_TYPE pointType;
      ret = DataTopicNameSupportUtil::fromTopicNameToDataType(topicName,
                                                              pointType);
      if (ret != 0) {
        RERROR << name() << ": TopicName = " << topicName
               << ", MessageTopicName = " << bagMsgPtr->topic_name
               << " Get DataType Failed: ret = " << ret;
        return -16;
      }
      if (pointType ==
          RS_SUPPORT_DATA_TYPE::RS_PCL_CLOUD_POINT_RSPOINTXYZIRGBIRT) {
        pcl::PointCloud<RsPointXYZRGBIRT>::Ptr cloudPtr(
            new pcl::PointCloud<RsPointXYZRGBIRT>());

        ret = DataConverterRos2::fromRosMsgToMsg<RsPointXYZRGBIRT>(rosMsg,
                                                                   *cloudPtr);
        if (ret != 0) {
          RERROR << name() << ": Read TopicName = " << topicName
                 << ", DataType = " << dataType
                 << "(XYZRGBIRT), Ros Message To Msg Failed: ret = " << ret;
          return -17;
        }
        msgPtr->org_message_.reset(new rally::Any(cloudPtr));

        // RINFO << name() << ": topicName = " << topicName << "(XYZRGBIRT)";
      } else if (pointType ==
                 RS_SUPPORT_DATA_TYPE::RS_PCL_CLOUD_POINT_RSPOINTXYZIRT) {
        pcl::PointCloud<RsPointXYZIRT>::Ptr cloudPtr(
            new pcl::PointCloud<RsPointXYZIRT>());

        ret = DataConverterRos2::fromRosMsgToMsg<RsPointXYZIRT>(rosMsg,
                                                                *cloudPtr);
        if (ret != 0) {
          RERROR << name() << ": Read TopicName = " << topicName
                 << ", DataType = " << dataType
                 << "(XYZIRT), Ros Message To Msg Failed: ret = " << ret;
          return -18;
        }
        msgPtr->org_message_.reset(new rally::Any(cloudPtr));
      } else if (pointType ==
                 RS_SUPPORT_DATA_TYPE::RS_SUPERSENSOR_DEPTH_FRAME) {
        std::shared_ptr<robosense::common::DepthFrame> rsMsgPtr(
            new robosense::common::DepthFrame());
        ret = DataConverterRos2::fromRosMsgToMsg(rosMsg, *rsMsgPtr);
        if (ret != 0) {
          RERROR << name() << ": Read TopicName = " << topicName
                 << ", DataType = " << dataType
                 << ", Ros Message To Msg Failed: ret = " << ret;
          return -19;
        }
        msgPtr->org_message_.reset(new rally::Any(rsMsgPtr));
      }
      // RINFO << name() << ": topicName = " << topicName <<
      // "(RsPointXYZIRT)";
    } else if (dataType == ROS_RSCOMPRESSEDIMAGE_DATATYPE) {
      rscamera_msg::msg::RsCompressedImage::SharedPtr rosMsgPtr(
          new rscamera_msg::msg::RsCompressedImage());
      int ret = convertToMessageT<rscamera_msg::msg::RsCompressedImage>(
          bagMsgPtr, *rosMsgPtr);
      if (ret != 0) {
        RERROR << name() << ": TopicName = " << topicName
               << ", MessageTopicName = " << bagMsgPtr->topic_name
               << " Convert To rscamera_msg::msg::RsCompressedImage Failed: "
                  "ret = "
               << ret;
        return -20;
      }
      msgPtr->org_message_.reset(new rally::Any(rosMsgPtr));
    } else {
      RERROR << name() << ": not support now: topicName = " << topicName
             << ", DataType = " << dataType;
      return -21;
    }

    messages.insert({timestampNs, msgPtr});
  }

  return flag;
}

int DataReaderRos2::readSyncFrameAttachH265(
    const int index, std::map<uint64_t, DataReaderSingleItem::Ptr> &messages) {
  // 获取数据范围
  std::pair<int32_t, int32_t> rangeIndex;
  int flag = getMainSyncRangeIndex(index, rangeIndex);
  if (flag < 0) {
    RERROR << name() << ": read Sync Index = " << index << " Out Of Range !";
    return -1;
  }

  int32_t begin_index = rangeIndex.first;
  int32_t end_index = rangeIndex.second;

  // 当前关键帧关联的H265帧信息
  RosbagMainIndexInfo indexInfo;
  if (index == 0) {
    indexInfo = main_sync_index_[index];
  } else {
    indexInfo = main_sync_index_[index - 1];
  }

  // 判断关联的H265帧是否超出范围
  int32_t min_h265_index = total_frame_cnt_;
  for (auto iterMap = indexInfo.attachH265IFrameIndexs.begin();
       iterMap != indexInfo.attachH265IFrameIndexs.end(); ++iterMap) {
    // RINFO << "iterMap->second = " << iterMap->second;
    if (iterMap->second < min_h265_index && iterMap->second >= 0) {
      min_h265_index = iterMap->second;
    }
  }
  RINFO << "min_h265_index = " << min_h265_index
        << ", main sync index = " << index
        << ", total_frame_cnt_ = " << total_frame_cnt_;
  if (min_h265_index < 0 || min_h265_index == total_frame_cnt_) {
    min_h265_index = 0;
  }

  RDEBUG << name() << ": read Sync index = " << index
         << ", begin_index = " << begin_index << ", end_index = " << end_index
         << ", min_h265_index = " << min_h265_index;
  int real_begin_index = std::min(min_h265_index, begin_index);
  for (int i = real_begin_index; i <= end_index; ++i) {
    const auto &index_Info = message_iterators_[i];
    if (i == real_begin_index) {
      ros2_reader_ptr_->seek(index_Info.timestamp);
    }
    if (!ros2_reader_ptr_->has_next()) {
      RERROR << name() << ": ros2 has_next() is false !";
      return -2;
    }

    std::shared_ptr<rosbag2_storage::SerializedBagMessage> bagMsgPtr =
        ros2_reader_ptr_->read_next();
    if (bagMsgPtr == nullptr) {
      RERROR << name() << ": ros2 read_next() is nullptr !";
      return -3;
    }

    const std::string &topicName =
        id_topicname_mapper_[index_Info.topicname_id];
    const std::string &dataType = topicname_datatype_mapper_[topicName];
    const uint64_t timestampNs = index_Info.timestamp;
    // std::cout << "dataType = " << dataType << ", topicName = " << topicName
    //           << ", timestamp = " << std::to_string(timestampNs)
    //           << ", messageTopic = " << bagMsgPtr->topic_name
    //           << ", messageTimestamp = "
    //           << std::to_string(bagMsgPtr->time_stamp) << ", i = " << i
    //           << std::endl;
    DataReaderSingleItem::Ptr msgPtr(new DataReaderSingleItem());
    msgPtr->timestamp_ns_ = timestampNs;
    msgPtr->topicname_id_ = topicname_id_mapper_[topicName];
    msgPtr->is_belong_ = true;
    msgPtr->is_ready_ = false;
    msgPtr->is_use_org_message_ = fromDataTypeToIsUseOriMessage(dataType);
    msgPtr->process_type_ = fromDataTypeToProcessType(dataType);
    if (begin_index <= i && i <= end_index) {
      if (dataType == ROS_MOTIONFRAME_DATATYPE) {
        // 在正常范围内
        hyper_vision_msgs::msg::MotionFrame rosMsg;
        int ret = convertToMessageT<hyper_vision_msgs::msg::MotionFrame>(
            bagMsgPtr, rosMsg);
        if (ret != 0) {
          RERROR << name() << ": TopicName = " << topicName
                 << ", MessageTopicName = " << bagMsgPtr->topic_name
                 << " Convert To hyper_vision_msgs::msg::MotionFrame Failed: "
                    "ret = "
                 << ret;
          return -4;
        }

        std::shared_ptr<robosense::common::MotionFrame> rsMsgPtr(
            new robosense::common::MotionFrame());
        ret = DataConverterRos2::fromRosMsgToMsg(rosMsg, *rsMsgPtr);
        if (ret != 0) {
          RERROR << name() << ": Read TopicName = " << topicName
                 << ", DataType = " << dataType
                 << ", Ros Message To Msg Failed: ret = " << ret;
          return -5;
        }
        msgPtr->org_message_.reset(new rally::Any(rsMsgPtr));
      } else if (dataType == ROS_DEPTHFRAME_DATATYPE) {
        hyper_vision_msgs::msg::DepthFrame rosMsg;
        int ret = convertToMessageT<hyper_vision_msgs::msg::DepthFrame>(
            bagMsgPtr, rosMsg);
        if (ret != 0) {
          RERROR
              << name() << ": TopicName = " << topicName
              << ", MessageTopicName = " << bagMsgPtr->topic_name
              << " Convert To hyper_vision_msgs::msg::DepthFrame Failed: ret = "
              << ret;
          return -6;
        }

        std::shared_ptr<robosense::common::DepthFrame> rsMsgPtr(
            new robosense::common::DepthFrame());
        ret = DataConverterRos2::fromRosMsgToMsg(rosMsg, *rsMsgPtr);
        if (ret != 0) {
          RERROR << name() << ": Read TopicName = " << topicName
                 << ", DataType = " << dataType
                 << ", Ros Message To Msg Failed: ret = " << ret;
          return -7;
        }
        msgPtr->org_message_.reset(new rally::Any(rsMsgPtr));
      } else if (dataType == ROS_IMAGEFRAME_DATATYPE) {
        hyper_vision_msgs::msg::ImageFrame rosMsg;
        int ret = convertToMessageT<hyper_vision_msgs::msg::ImageFrame>(
            bagMsgPtr, rosMsg);
        if (ret != 0) {
          RERROR
              << name() << ": TopicName = " << topicName
              << ", MessageTopicName = " << bagMsgPtr->topic_name
              << " Convert To hyper_vision_msgs::msg::ImageFrame Failed: ret = "
              << ret;
          return -8;
        }

        std::shared_ptr<robosense::common::ImageFrame> rsMsgPtr(
            new robosense::common::ImageFrame());
        ret = DataConverterRos2::fromRosMsgToMsg(rosMsg, *rsMsgPtr);
        if (ret != 0) {
          RERROR << name() << ": Read TopicName = " << topicName
                 << ", DataType = " << dataType
                 << ", Ros Message To Msg Failed: ret = " << ret;
          return -9;
        }
        msgPtr->org_message_.reset(new rally::Any(rsMsgPtr));
      } else if (dataType == ROS_MOTIONFRAME_DATATYPE2) {
        // 在正常范围内
        sensor_msgs::msg::Imu rosMsg;
        int ret = convertToMessageT<sensor_msgs::msg::Imu>(bagMsgPtr, rosMsg);
        if (ret != 0) {
          RERROR << name() << ": TopicName = " << topicName
                 << ", MessageTopicName = " << bagMsgPtr->topic_name
                 << " Convert To hyper_vision_msgs::msg::MotionFrame Failed: "
                    "ret = "
                 << ret;
          return -10;
        }

        std::shared_ptr<robosense::common::MotionFrame> rsMsgPtr(
            new robosense::common::MotionFrame());
        ret = DataConverterRos2::fromRosMsgToMsg(rosMsg, *rsMsgPtr);
        if (ret != 0) {
          RERROR << name() << ": Read TopicName = " << topicName
                 << ", DataType = " << dataType
                 << ", Ros Message To Msg Failed: ret = " << ret;
          return -11;
        }
        msgPtr->org_message_.reset(new rally::Any(rsMsgPtr));
      } else if (dataType == ROS_IMAGEFRAME_DATATYPE2) {
        sensor_msgs::msg::Image rosMsg;
        int ret = convertToMessageT<sensor_msgs::msg::Image>(bagMsgPtr, rosMsg);
        if (ret != 0) {
          RERROR
              << name() << ": TopicName = " << topicName
              << ", MessageTopicName = " << bagMsgPtr->topic_name
              << " Convert To hyper_vision_msgs::msg::ImageFrame Failed: ret = "
              << ret;
          return -12;
        }

        std::shared_ptr<robosense::common::ImageFrame> rsMsgPtr(
            new robosense::common::ImageFrame());
        ret = DataConverterRos2::fromRosMsgToMsg(rosMsg, *rsMsgPtr);
        if (ret != 0) {
          RERROR << name() << ": Read TopicName = " << topicName
                 << ", DataType = " << dataType
                 << ", Ros Message To Msg Failed: ret = " << ret;
          return -13;
        }
        msgPtr->org_message_.reset(new rally::Any(rsMsgPtr));
      } else if (dataType == ROS_COMPRESSEDIMAGE_DATATYPE) {
        sensor_msgs::msg::CompressedImage::SharedPtr rosMsgPtr(
            new sensor_msgs::msg::CompressedImage());
        int ret = convertToMessageT<sensor_msgs::msg::CompressedImage>(
            bagMsgPtr, *rosMsgPtr);
        if (ret != 0) {
          RERROR
              << name() << ": TopicName = " << topicName
              << ", MessageTopicName = " << bagMsgPtr->topic_name
              << " Convert To sensor_msgs::msg::CompressedImage Failed: ret = "
              << ret;
          return -10;
        }

        msgPtr->org_message_.reset(new rally::Any(rosMsgPtr));
      } else if (dataType == ROS_POINTCLOUD2_DATATYPE) {
        sensor_msgs::msg::PointCloud2 rosMsg;
        int ret =
            convertToMessageT<sensor_msgs::msg::PointCloud2>(bagMsgPtr, rosMsg);
        if (ret != 0) {
          RERROR << name() << ": TopicName = " << topicName
                 << ", MessageTopicName = " << bagMsgPtr->topic_name
                 << " Convert To sensor_msgs::msg::PointCloud2 Failed: ret = "
                 << ret;
          return -11;
        }

        RS_SUPPORT_DATA_TYPE pointType;
        ret = DataTopicNameSupportUtil::fromTopicNameToDataType(topicName,
                                                                pointType);
        if (ret != 0) {
          RERROR << name() << ": TopicName = " << topicName
                 << ", MessageTopicName = " << bagMsgPtr->topic_name
                 << " Get DataType Failed: ret = " << ret;
          return -12;
        }

        if (pointType ==
            RS_SUPPORT_DATA_TYPE::RS_PCL_CLOUD_POINT_RSPOINTXYZIRGBIRT) {
          pcl::PointCloud<RsPointXYZRGBIRT>::Ptr cloudPtr(
              new pcl::PointCloud<RsPointXYZRGBIRT>());

          ret = DataConverterRos2::fromRosMsgToMsg<RsPointXYZRGBIRT>(rosMsg,
                                                                     *cloudPtr);
          if (ret != 0) {
            RERROR << name() << ": Read TopicName = " << topicName
                   << ", DataType = " << dataType
                   << "(XYZRGBIRT), Ros Message To Msg Failed: ret = " << ret;
            return -13;
          }
          msgPtr->org_message_.reset(new rally::Any(cloudPtr));

          // RINFO << name() << ": topicName = " << topicName << "(XYZRGBIRT)";
        } else if (pointType ==
                   RS_SUPPORT_DATA_TYPE::RS_PCL_CLOUD_POINT_RSPOINTXYZIRT) {
          pcl::PointCloud<RsPointXYZIRT>::Ptr cloudPtr(
              new pcl::PointCloud<RsPointXYZIRT>());

          ret = DataConverterRos2::fromRosMsgToMsg<RsPointXYZIRT>(rosMsg,
                                                                  *cloudPtr);
          if (ret != 0) {
            RERROR << name() << ": Read TopicName = " << topicName
                   << ", DataType = " << dataType
                   << "(XYZIRT), Ros Message To Msg Failed: ret = " << ret;
            return -14;
          }
          msgPtr->org_message_.reset(new rally::Any(cloudPtr));
        } else if (pointType ==
                   RS_SUPPORT_DATA_TYPE::RS_SUPERSENSOR_DEPTH_FRAME) {
          std::shared_ptr<robosense::common::DepthFrame> rsMsgPtr(
              new robosense::common::DepthFrame());
          ret = DataConverterRos2::fromRosMsgToMsg(rosMsg, *rsMsgPtr);
          if (ret != 0) {
            RERROR << name() << ": Read TopicName = " << topicName
                   << ", DataType = " << dataType
                   << ", Ros Message To Msg Failed: ret = " << ret;
            return -19;
          }
          msgPtr->org_message_.reset(new rally::Any(rsMsgPtr));
        }
        // RINFO << name() << ": topicName = " << topicName <<
        // "(RsPointXYZIRT)";
      } else if (dataType == ROS_RSCOMPRESSEDIMAGE_DATATYPE) {
        rscamera_msg::msg::RsCompressedImage::SharedPtr rosMsgPtr(
            new rscamera_msg::msg::RsCompressedImage());
        int ret = convertToMessageT<rscamera_msg::msg::RsCompressedImage>(
            bagMsgPtr, *rosMsgPtr);
        if (ret != 0) {
          RERROR << name() << ": TopicName = " << topicName
                 << ", MessageTopicName = " << bagMsgPtr->topic_name
                 << " Convert To rscamera_msg::msg::RsCompressedImage Failed: "
                    "ret = "
                 << ret;
          return -15;
        }
        msgPtr->org_message_.reset(new rally::Any(rosMsgPtr));
      } else {
        RERROR << name() << ": not support now: topicName = " << topicName
               << ", DataType = " << dataType;
        return -16;
      }
    } else if (indexInfo.attachH265IFrameIndexs.find(topicName) !=
                   indexInfo.attachH265IFrameIndexs.end() &&
               indexInfo.attachH265IFrameIndexs[topicName] <= i) {
      // Attach H265 消息: 从I帧开始处理
      rscamera_msg::msg::RsCompressedImage::SharedPtr rosMsgPtr(
          new rscamera_msg::msg::RsCompressedImage());
      int ret = convertToMessageT<rscamera_msg::msg::RsCompressedImage>(
          bagMsgPtr, *rosMsgPtr);
      if (ret != 0) {
        RERROR << name() << ": TopicName = " << topicName
               << ", MessageTopicName = " << bagMsgPtr->topic_name
               << " Convert To rscamera_msg::msg::RsCompressedImage Failed: "
                  "ret = "
               << ret;
        return -17;
      }

      msgPtr->is_belong_ = false;
      msgPtr->org_message_.reset(new rally::Any(rosMsgPtr));
    }

    // 加入缓冲区
    if (msgPtr->org_message_ == nullptr) {
      // RINFO << name() << ": Not Need Process Message: topicName = " <<
      // topicName
      //       << ", index = " << i
      //       << ", In Range = " << (i >= begin_index && i <= end_index);
      continue;
    }
    messages.insert({timestampNs, msgPtr});
  }

  return 0;
} // namespace io

int DataReaderRos2::consumeFrame(
    const int next_index, const RS_ROSBAG_FRAME_CONSUME_TYPE consume_type,
    const bool empty_consume_queue) {
  std::lock_guard<std::mutex> lg(processed_message_map_mtx_);
  if (empty_consume_queue) {
    consume_queue_ = std::queue<RosbagFrameConsumeOp>();
  }
  RosbagFrameConsumeOp consumeOp;
  consumeOp.consume_type_ = consume_type;
  consumeOp.consume_main_index_ = next_index;
  consume_queue_.push(consumeOp);
  processed_message_map_cond_.notify_one();

  return 0;
}

RS_DATA_MESSAGE_PROCESS_TYPE
DataReaderRos2::fromDataTypeToProcessType(const std::string &data_type) {
  RS_DATA_MESSAGE_PROCESS_TYPE processType =
      RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_NOTHING;
  if (data_type == ROS_COMPRESSEDIMAGE_DATATYPE) {
    return RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_JPEG;
  } else if (data_type == ROS_RSCOMPRESSEDIMAGE_DATATYPE) {
    return RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_H265;
  }

  return processType;
}

bool DataReaderRos2::fromDataTypeToIsUseOriMessage(
    const std::string &data_type) {
  bool isUseOrgMessage = true;
  if (data_type == ROS_COMPRESSEDIMAGE_DATATYPE ||
      data_type == ROS_RSCOMPRESSEDIMAGE_DATATYPE) {
    isUseOrgMessage = false;
  }
  return isUseOrgMessage;
}

int DataReaderRos2::getMainSyncRangeIndex(
    const int index, std::pair<int32_t, int32_t> &rangeIndex) {
  if (index < 0 || index >= total_frame_cnt_) {
    return -1;
  }

  bool isFirstFrame = (index == 0);
  bool isLastFrame = (index == (total_frame_cnt_ - 1));

  int32_t begin_index =
      (isFirstFrame ? 0 : main_sync_index_[index - 1].mainSyncIndex + 1);
  int32_t end_index = (isLastFrame ? message_iterators_.size() - 1
                                   : main_sync_index_[index].mainSyncIndex);

  rangeIndex = std::pair<int32_t, int32_t>{begin_index, end_index};

  if (isLastFrame) {
    return 1;
  }

  return 0;
}

int DataReaderRos2::readFrame(
    const int next_index, const RS_ROSBAG_FRAME_READ_TYPE read_type,
    std::map<uint64_t, DataReaderSingleItem::Ptr> &frameMessages) {
  int flag = 0;
  std::map<uint64_t, DataReaderSingleItem::Ptr> messages;
  switch (read_type) {
  case RS_ROSBAG_FRAME_READ_TYPE::RS_ROSBAG_FRAME_READ_ORDER: {
    flag = readSyncFrame(next_index, messages);
    RINFO << name() << ": readFrame => next_index = " << next_index
          << ", readType = ORDER, flag = " << flag;
    break;
  }
  case RS_ROSBAG_FRAME_READ_TYPE::RS_ROSBAG_FRAME_READ_SKIP: {
    flag = readSyncFrameAttachH265(next_index, messages);
    RINFO << name() << ": readFrame => next_index = " << next_index
          << ", readType = SKIP, flag = " << flag;
    break;
  }
  }

  if (flag < 0) {
    RERROR << name() << ": Read rosbag Failed !";
    return -1;
  }

  {
    std::lock_guard<std::mutex> lg(h265_coder_map_mtx_);
    if (read_type == RS_ROSBAG_FRAME_READ_TYPE::RS_ROSBAG_FRAME_READ_SKIP) {
      int ret = reInitAddH265Coder();
      if (ret != 0) {
        RERROR << name() << ": Re-Initial H265 Coder Failed: ret = " << ret;
        return -2;
      }
    }

    int ret = prepareFrameMessage(messages, frameMessages);
    if (ret != 0) {
      RERROR << name() << ": prepare frame Failed: ret = " << ret;
      return -3;
    }
  }

  return 0;
}

int DataReaderRos2::playFrame(
    const std::map<uint64_t, DataReaderSingleItem::Ptr>
        &singleFrameMessageMapper) {
  int64_t emptyTimestampNs = 0;
  uint64_t preTimestampNs = 0;
  uint64_t startTimestampNs = TIMESTAMP_NS;
  uint64_t beforeTimestampNs = TIMESTAMP_NS;
  for (auto iterMap = singleFrameMessageMapper.begin();
       iterMap != singleFrameMessageMapper.end(); ++iterMap) {
    const auto &singleMessageInfoPtr = iterMap->second;
    if (singleMessageInfoPtr->is_ready_) {
      // Send
      int ret = sendRegisterMessage(singleMessageInfoPtr);
      if (ret != 0) {
        RERROR << name() << ": Send TopicName = "
               << id_topicname_mapper_[singleMessageInfoPtr->topicname_id_]
               << " Failed !";
      }
    }
    uint64_t endTimestampNs = TIMESTAMP_NS;
    int64_t diffMsgTimestampNs = (iterMap->first - preTimestampNs);
    int64_t diffPlayTimestampNs = (endTimestampNs - beforeTimestampNs);
    // RINFO << "diffMsgTimestampNs - diffPlayTimestampNs = "
    //       << (diffMsgTimestampNs - diffPlayTimestampNs)
    //       << ", diffMsgTimestampNs  = " << diffMsgTimestampNs
    //       << ", diffPlayTimestampNs = " << diffPlayTimestampNs
    //       << ", currentTimestampNs = " << iterMap->first;

    if (preTimestampNs != 0) {
      emptyTimestampNs += diffMsgTimestampNs - diffPlayTimestampNs;
      preTimestampNs = iterMap->first;
    } else {
      preTimestampNs = iterMap->first;
    }

    // RINFO << "emptyTimestampNs = " << emptyTimestampNs
    //       << ", TIMESTAMP_NS = " << TIMESTAMP_NS;
    if (emptyTimestampNs > 1000000) {
      std::this_thread::sleep_for(std::chrono::nanoseconds(emptyTimestampNs));
      uint64_t sleepTimestampNs = TIMESTAMP_NS;
      int64_t diffSleepTimestampNs = sleepTimestampNs - endTimestampNs;
      emptyTimestampNs = emptyTimestampNs - diffSleepTimestampNs;
    }

    beforeTimestampNs = TIMESTAMP_NS;
    // RINFO << "emptyTimestampNs 2 = " << emptyTimestampNs
    //       << ", TIMESTAMP_NS = " << TIMESTAMP_NS;
  }
  if (emptyTimestampNs > 0) {
    std::this_thread::sleep_for(std::chrono::nanoseconds(emptyTimestampNs));
  }
  uint64_t finishTimestampNs = TIMESTAMP_NS;

  RINFO << "finishTimestampNs - startTimestampNs = "
        << (finishTimestampNs - startTimestampNs) * 1e-6
        << ", total Timestamp Diff = "
        << (singleFrameMessageMapper.rbegin()->first -
            singleFrameMessageMapper.begin()->first) *
               1e-6;
  return 0;
}

int DataReaderRos2::eraseFrame(const bool isAddOp) {
  {
    std::lock_guard<std::mutex> lg(processed_message_map_mtx_);
    if (processed_message_map_.size() >
        static_cast<size_t>(preload_main_frame_cnt_)) {
      const auto &rangeIndex =
          getIndexRange(cur_consume_sync_index_, total_frame_cnt_);

      if (isAddOp) {
        for (auto iterMap = processed_message_map_.begin();
             iterMap != processed_message_map_.end() &&
             processed_message_map_.size() >
                 static_cast<size_t>(preload_main_frame_cnt_);) {
          const int index = iterMap->first;
          if (index < rangeIndex.first || index > rangeIndex.second) {
            RINFO << name() << ": erase index = " << index;
            iterMap = processed_message_map_.erase(iterMap);
          } else {
            ++iterMap;
          }
        }
      } else {
        for (auto iterMap = processed_message_map_.rbegin();
             iterMap != processed_message_map_.rend() &&
             processed_message_map_.size() >
                 static_cast<size_t>(preload_main_frame_cnt_);) {
          const int index = iterMap->first;
          if (index < rangeIndex.first || index > rangeIndex.second) {
            RINFO << name() << ": reverse erase index = " << index;
            auto next_iterMap = iterMap++;
            processed_message_map_.erase(iterMap.base());
            iterMap = next_iterMap;
          } else {
            ++iterMap;
          }
        }
      }
    }
  }
  return 0;
}

#endif // defined(ROS2_FOUND)

} // namespace io
} // namespace robosense