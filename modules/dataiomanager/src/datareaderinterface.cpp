#include "hyper_vision/dataiomanager/datareaderinterface.h"

namespace robosense {
namespace io {

DataReaderInterface::DataReaderInterface() {
  play_status_ = RS_DATA_READ_PLAY_STATUS::RS_DATA_READ_PLAY_PAUSE;
  total_duration_ns_ = 0;
  begin_timestamp_ns_ = 0;
  end_timestamp_ns_ = 0;
  total_frame_cnt_ = 0;
  init_progress_ = 0;
  data_io_time_util_.setTimeFormat("%Y_%m_%d_%H_%M_%S");
}

DataReaderInterface::~DataReaderInterface() {}

int DataReaderInterface::registerCallback(
    const DATA_DEPTH_FRAME_CALLBACK &callback) {
  if (callback == nullptr) {
    RERROR << name() << ": register depth frame callback is nullptr !";
    return -1;
  }
  depth_frame_cb_ = callback;
  return 0;
}

int DataReaderInterface::registerCallback(
    const DATA_MOTION_FRAME_CALLBACK &callback) {
  if (callback == nullptr) {
    RERROR << name() << ": register motion frame callback is nullptr !";
    return -1;
  }
  motion_frame_cb_ = callback;
  return 0;
}

int DataReaderInterface::registerCallback(
    const DATA_IMAGE_FRAME_CALLBACK &callback) {
  if (callback == nullptr) {
    RERROR << name() << ": register image frame callback is nullptr !";
    return -1;
  }
  image_frame_cb_ = callback;
  return 0;
}

int DataReaderInterface::registerCallback(
    const DATA_POINTCLOUDXYZRGBIRT_CALLBACK &callback) {
  if (callback == nullptr) {
    RERROR << name() << ": register pointcloud xyzrgbirt callback is nullptr !";
    return -1;
  }
  pointcloudxyzrgbirt_cb_ = callback;
  return 0;
}

int DataReaderInterface::registerCallback(
    const DATA_POINTCLOUDXYZIRT_CALLBACK &callback) {
  if (callback == nullptr) {
    RERROR << name() << ": register pointcloud xyzirt callback is nullptr !";
    return -1;
  }
  pointcloudxyzirt_cb_ = callback;
  return 0;
}

uint64_t DataReaderInterface::getTotalDuration() { return total_duration_ns_; }

uint64_t DataReaderInterface::getBeginTimestamp() {
  return begin_timestamp_ns_;
}

uint64_t DataReaderInterface::getEndTimestamp() { return end_timestamp_ns_; }

uint32_t DataReaderInterface::getTotalFrameCount() { return total_frame_cnt_; }

bool DataReaderInterface::checkIsPlaying() {
  std::lock_guard<std::mutex> lg(play_status_mtx_);
  return (play_status_ == RS_DATA_READ_PLAY_STATUS::RS_DATA_READ_PLAY_PLAYING);
}

bool DataReaderInterface::checkIsPlayFinish() {
  std::lock_guard<std::mutex> lg(processed_message_map_mtx_);
  return (cur_consume_index_ >= total_frame_cnt_ - 1);
}

uint32_t DataReaderInterface::getInitProgress() { return init_progress_; }

int DataReaderInterface::initAddJpegCoder(const std::string &topicName) {
  if (jpeg_coder_map_.find(topicName) == jpeg_coder_map_.end()) {
    robosense::jpeg::JpegCoder::Ptr jpegCoderPtr;
    try {
      jpegCoderPtr.reset(new robosense::jpeg::JpegCoder());
    } catch (...) {
      RERROR << name() << ": Malloc Jpeg Coder Failed !";
      return -1;
    }

    std::pair<uint32_t, uint32_t> image_size;
    int ret = DataTopicNameSupportUtil::fromTopicNameToImageSize(topicName,
                                                                 image_size);
    if (ret != 0) {
      RERROR << name() << ": From TopicName = " << topicName
             << " To Image Size Failed !";
      return -2;
    }

    robosense::jpeg::JpegCodesConfig jpegConfig;
    jpegConfig.coderType =
        robosense::jpeg::JPEG_CODER_TYPE::RS_JPEG_CODER_DECODE;
    jpegConfig.imageFrameFormat =
        robosense::common::ImageFrameFormat::FRAME_FORMAT_RGB24;
    jpegConfig.imageWidth = image_size.first;
    jpegConfig.imageHeight = image_size.second;
    jpegConfig.jpegQuality = 70;
    jpegConfig.sampleFactor = 1;
    jpegConfig.gpuDeviceId = 0;

    ret = jpegCoderPtr->init(jpegConfig);
    if (ret != 0) {
      RERROR << name()
             << ": Jpeg Coder Initial Failed: topicName = " << topicName
             << ", ret = " << ret;
      return -3;
    }

    jpeg_coder_map_.insert({topicName, jpegCoderPtr});
    RINFO << name() << ": Add Jpeg Coder Successed: topicName = " << topicName;
  }

  return 0;
}

int DataReaderInterface::initAddH265Coder(const std::string &topicName) {
  if (h265_coder_map_.find(topicName) == h265_coder_map_.end()) {
    DataReaderH265Decoder::Ptr h265CoderPtr;
    try {
      h265CoderPtr.reset(new DataReaderH265Decoder());
    } catch (...) {
      RERROR << name() << ": Malloc H265 Coder Failed !";
      return -1;
    }

    std::pair<uint32_t, uint32_t> image_size;
    int ret = DataTopicNameSupportUtil::fromTopicNameToImageSize(topicName,
                                                                 image_size);
    if (ret != 0) {
      RERROR << name() << ": From TopicName = " << topicName
             << " To Image Size Failed !";
      return -2;
    }

    robosense::h265::H265CodesConfig h265Config;
    h265Config.codeType =
        robosense::h265::H265_CODER_TYPE::RS_H265_CODER_DECODE;
    h265Config.imageFrameFormat =
        robosense::common::ImageFrameFormat::FRAME_FORMAT_RGB24;
    h265Config.imgWidth = image_size.first;
    h265Config.imgHeight = image_size.second;
    h265Config.imgFreq = 30;
    h265Config.codesRateType =
        robosense::h265::H265_CODES_RATE_TYPE::RS_H265_CODES_CUSTOM;
    h265Config.imgCodeRate = 5 * 1024 * 1024;

    // H265 解码同步模式
    ret = h265CoderPtr->init(topicName, h265Config, nullptr);
    if (ret != 0) {
      RERROR << name()
             << ": H265 Coder Initial Failed: topicName = " << topicName
             << ", ret = " << ret;
      return -3;
    }

    h265_coder_map_.insert({topicName, h265CoderPtr});

    RINFO << name() << ": Add H265 Coder Successed: topicName = " << topicName;
  }

  return 0;
}

int DataReaderInterface::reInitAddH265Coder() {
  for (auto iterMap = h265_coder_map_.begin(); iterMap != h265_coder_map_.end();
       ++iterMap) {
    if (iterMap->second != nullptr) {
      int ret = iterMap->second->resetInit();
      if (ret != 0) {
        RERROR << name() << ": Re-Initial H265 Coder Failed: topicName = "
               << iterMap->first << ", ret = " << ret;
        return -1;
      }
    }
  }
  return 0;
}

int DataReaderInterface::sendRegisterMessage(
    const DataReaderSingleItem::Ptr &msgPtr) {
  if (msgPtr == nullptr ||
      (msgPtr->is_use_org_message_ && msgPtr->org_message_ == nullptr) ||
      (!msgPtr->is_use_org_message_ && msgPtr->message_ == nullptr)) {
    return 0;
  }

  int ret = 0;
  const std::string &topicName = id_topicname_mapper_[msgPtr->topicname_id_];
  if (msgPtr->is_use_org_message_) {
    ret = sendRegisterMessage(topicName, msgPtr->org_message_);
  } else {
    ret = sendRegisterMessage(topicName, msgPtr->message_);
  }
  if (ret != 0) {
    RERROR << name()
           << ": send register message failed: topicName = " << topicName
           << ", ret = " << ret;
    return -1;
  }

  return 0;
}

int DataReaderInterface::sendRegisterMessage(
    const std::string &topicName, const rally::Any::Ptr &messagePtr) {
  const static std::type_info &imuMessageTypeInfo =
      typeid(std::shared_ptr<robosense::common::MotionFrame>);
  const static std::type_info &imageMessageTypeInfo =
      typeid(std::shared_ptr<robosense::common::ImageFrame>);
  const static std::type_info &depthMessageTypeInfo =
      typeid(std::shared_ptr<robosense::common::DepthFrame>);
  const static std::type_info &pclPointCloudTypeInfo =
      typeid(typename pcl::PointCloud<RsPointXYZRGBIRT>::Ptr);
  const static std::type_info &pclPointCloudTypeInfo2 =
      typeid(typename pcl::PointCloud<RsPointXYZIRT>::Ptr);

  const std::type_info &typeInfo = messagePtr->type_info();
  if (typeInfo == imuMessageTypeInfo) {
    std::shared_ptr<robosense::common::MotionFrame> *ptr =
        messagePtr->AnyCast<std::shared_ptr<robosense::common::MotionFrame>>();
    if (ptr == nullptr) {
      RERROR << name() << ": topicName = " << topicName << ", AnyCast Failed !";
      return -1;
    } else if (*ptr == nullptr) {
      return 0;
    }
    if (motion_frame_cb_ != nullptr) {
      motion_frame_cb_(topicName, *ptr);
    }
  } else if (typeInfo == imageMessageTypeInfo) {
    std::shared_ptr<robosense::common::ImageFrame> *ptr =
        messagePtr->AnyCast<std::shared_ptr<robosense::common::ImageFrame>>();
    if (ptr == nullptr) {
      RERROR << name() << ": topicName = " << topicName << ", AnyCast Failed !";
      return -2;
    } else if (*ptr == nullptr) {
      return 0;
    }
    if (image_frame_cb_ != nullptr) {
      image_frame_cb_(topicName, *ptr);
    }
  } else if (typeInfo == depthMessageTypeInfo) {
    std::shared_ptr<robosense::common::DepthFrame> *ptr =
        messagePtr->AnyCast<std::shared_ptr<robosense::common::DepthFrame>>();
    if (ptr == nullptr) {
      RERROR << name() << ": topicName = " << topicName << ", AnyCast Failed !";
      return -3;
    } else if (*ptr == nullptr) {
      return 0;
    }
    if (depth_frame_cb_ != nullptr) {
      depth_frame_cb_(topicName, *ptr);
    }
  } else if (typeInfo == pclPointCloudTypeInfo) {
    typename pcl::PointCloud<RsPointXYZRGBIRT>::Ptr *ptr =
        messagePtr->AnyCast<typename pcl::PointCloud<RsPointXYZRGBIRT>::Ptr>();
    if (ptr == nullptr) {
      RERROR << name() << ": topicName = " << topicName << ", AnyCast Failed !";
      return -4;
    } else if (*ptr == nullptr) {
      return 0;
    }
    if (pointcloudxyzrgbirt_cb_ != nullptr) {
      pointcloudxyzrgbirt_cb_(topicName, *ptr);
    }
  } else if (typeInfo == pclPointCloudTypeInfo2) {
    typename pcl::PointCloud<RsPointXYZIRT>::Ptr *ptr =
        messagePtr->AnyCast<typename pcl::PointCloud<RsPointXYZIRT>::Ptr>();
    if (ptr == nullptr) {
      RERROR << name() << ": topicName = " << topicName << ", AnyCast Failed !";
      return -5;
    } else if (*ptr == nullptr) {
      return 0;
    }
    if (pointcloudxyzirt_cb_ != nullptr) {
      pointcloudxyzirt_cb_(topicName, *ptr);
    }
  } else {
    RWARN << name() << ": topicName = " << topicName
          << " Is Not Support To Send !";
  }

  return 0;
}

} // namespace io
} // namespace robosense