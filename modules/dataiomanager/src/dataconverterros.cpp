#include "hyper_vision/dataiomanager/dataconverterros.h"
#include "rally/utils/utils.h"

namespace robosense {
namespace io {

int DataConverterRos::fromMsgToRosMsg(
    const robosense::common::DepthFrame &depthFrame,
    hyper_vision_msgs::DepthFrame &depthFrameMsg) {
  // points
  int ret = fromMsgToRosMsg(depthFrame.points, depthFrame.point_nums,
                            depthFrameMsg.points);
  if (ret != 0) {
    RERROR
        << "DataConverterRos:fromMsgToRosMsg->DepthFrame.points failed: ret = "
        << ret;
    return -1;
  }

  // points_nums
  depthFrameMsg.points_nums = depthFrame.point_nums;

  // capture_time
  ret = fromMsgToRosMsg(depthFrame.capture_time, depthFrameMsg.capture_time);
  if (ret != 0) {
    RERROR << "DataConverterRos:fromMsgToRosMsg->DepthFrame.capture_time "
              "failed: ret = "
           << ret;
    return -2;
  }

  return 0;
}

int DataConverterRos::fromRosMsgToMsg(
    const hyper_vision_msgs::DepthFrame &depthFrameMsg,
    robosense::common::DepthFrame &depthFrame) {
  // points
  int ret = fromRosMsgToMsg(depthFrameMsg.points, depthFrame.points);
  if (ret != 0) {
    RERROR
        << "DataConverterRos:fromRosMsgToMsg->DepthFrame.points failed: ret = "
        << ret;
    return -1;
  }

  // points_nums
  depthFrame.point_nums = depthFrameMsg.points_nums;

  // capture_time
  ret = fromRosMsgToMsg(depthFrameMsg.capture_time, depthFrame.capture_time);
  if (ret != 0) {
    RERROR << "DataConverterRos:fromRosMsgToMsg->DepthFrame.capture_time "
              "failed: ret = "
           << ret;
    return -2;
  }

  return 0;
}

int DataConverterRos::fromMsgToRosMsg(
    const robosense::common::ImageFrame &imageFrame,
    hyper_vision_msgs::ImageFrame &imageFrameMsg) {
  // data
  int ret = fromMsgToRosMsg(imageFrame.data, imageFrame.data_bytes,
                            imageFrameMsg.data);
  if (ret != 0) {
    RERROR << "DataConverterRos:fromMsgToRosMsg->ImageFrame.data failed: ret = "
           << ret;
    return -1;
  }

  // data_bytes & width & height & frame_format & step & sequence
  imageFrameMsg.data_bytes = imageFrame.data_bytes;
  imageFrameMsg.width = imageFrame.width;
  imageFrameMsg.height = imageFrame.height;
  imageFrameMsg.frame_format = static_cast<uint8_t>(imageFrame.frame_format);
  imageFrameMsg.step = imageFrame.step;
  imageFrameMsg.sequence = imageFrame.sequence;

  // capture_time
  ret = fromMsgToRosMsg(imageFrame.capture_time, imageFrameMsg.capture_time);
  if (ret != 0) {
    RERROR << "DataConverterRos:fromMsgToRosMsg->ImageFrame.capture_time "
              "failed: ret = "
           << ret;
    return -2;
  }

  return 0;
}

int DataConverterRos::fromRosMsgToMsg(
    const hyper_vision_msgs::ImageFrame &imageFrameMsg,
    robosense::common::ImageFrame &imageFrame) {
  // data
  int ret = fromRosMsgToMsg(imageFrameMsg.data, imageFrame.data);
  if (ret != 0) {
    RERROR << "DataConverterRos:fromRosMsgToMsg->ImageFrame.data failed: ret = "
           << ret;
    return -1;
  }

  // data_bytes & width & height & frame_format & step & sequence
  imageFrame.data_bytes = imageFrameMsg.data_bytes;
  imageFrame.width = imageFrameMsg.width;
  imageFrame.height = imageFrameMsg.height;
  imageFrame.frame_format = static_cast<robosense::common::ImageFrameFormat>(
      imageFrameMsg.frame_format);
  imageFrame.step = imageFrameMsg.step;
  imageFrame.sequence = imageFrameMsg.sequence;

  // capture_time
  ret = fromRosMsgToMsg(imageFrameMsg.capture_time, imageFrame.capture_time);
  if (ret != 0) {
    RERROR << "DataConverterRos:fromRosMsgToMsg->ImageFrame.capture_time "
              "failed: ret = "
           << ret;
    return -2;
  }

  return 0;
}

int DataConverterRos::fromMsgToRosMsg(
    const robosense::common::MotionFrame &motionFrame,
    hyper_vision_msgs::MotionFrame &motionFrameMsg) {
  // accel
  int ret = fromMsgToRosMsg(motionFrame.accel, motionFrameMsg.accel);
  if (ret != 0) {
    RERROR
        << "DataConverterRos:fromMsgToRosMsg->MotionFrame.accel failed: ret = "
        << ret;
    return -1;
  }

  // gyro
  ret = fromMsgToRosMsg(motionFrame.gyro, motionFrameMsg.gyro);
  if (ret != 0) {
    RERROR
        << "DataConverterRos:fromMsgToRosMsg->MotionFrame.gyro failed: ret = "
        << ret;
    return -2;
  }

  // temperature
  motionFrameMsg.temperature = motionFrame.temperature;

  // capture_time
  ret = fromMsgToRosMsg(motionFrame.capture_time, motionFrameMsg.capture_time);
  if (ret != 0) {
    RERROR << "DataConverterRos:fromMsgToRosMsg->MotionFrame.capture_time "
              "failed: ret = "
           << ret;
    return -3;
  }

  return 0;
}

int DataConverterRos::fromRosMsgToMsg(
    const hyper_vision_msgs::MotionFrame &motionFrameMsg,
    robosense::common::MotionFrame &motionFrame) {
  // accel
  int ret = fromRosMsgToMsg(motionFrameMsg.accel, motionFrame.accel);
  if (ret != 0) {
    RERROR
        << "DataConverterRos:fromRosMsgToMsg->MotionFrame.accel failed: ret = "
        << ret;
    return -1;
  }

  // gyro
  ret = fromRosMsgToMsg(motionFrameMsg.gyro, motionFrame.gyro);
  if (ret != 0) {
    RERROR
        << "DataConverterRos:fromRosMsgToMsg->MotionFrame.gyro failed: ret = "
        << ret;
    return -2;
  }

  // temperature
  motionFrame.temperature = motionFrameMsg.temperature;

  // capture_time
  ret = fromRosMsgToMsg(motionFrameMsg.capture_time, motionFrame.capture_time);
  if (ret != 0) {
    RERROR << "DataConverterRos:fromRosMsgToMsg->MotionFrame.capture_time "
              "failed: ret = "
           << ret;
    return -3;
  }

  return 0;
}

int DataConverterRos::fromMsgToRosMsg(
    const robosense::common::DepthFrame &depthFrame,
    sensor_msgs::PointCloud2 &depthFrameMsg) {
  // Step1: 转为pcl::PointCloud
  pcl::PointCloud<robosense::common::CloudPointXYZIRT> pointCloud;
  pointCloud.resize(depthFrame.point_nums);
  memcpy(pointCloud.points.data(), depthFrame.points.get(),
         sizeof(robosense::common::CloudPointXYZIRT) * depthFrame.point_nums);
  pointCloud.width = 1;
  pointCloud.height = depthFrame.point_nums;

  pointCloud.header.frame_id = "/base_link";
  pointCloud.header.seq = 0;
  pointCloud.header.stamp =
      static_cast<uint64_t>(depthFrame.capture_time.tv_sec) * 1000000 +
      depthFrame.capture_time.tv_usec;

  // Step2: 转为ROS2消息
  int ret = fromMsgToRosMsg<robosense::common::CloudPointXYZIRT>(pointCloud,
                                                                 depthFrameMsg);
  if (ret != 0) {
    RERROR << "DataConverterRos:fromMsgToRosMsg->DepthFrame failed: ret = "
           << ret;
    return -1;
  }

  return 0;
}

int DataConverterRos::fromRosMsgToMsg(
    const sensor_msgs::PointCloud2 &depthFrameMsg,
    robosense::common::DepthFrame &depthFrame) {
  // Step1: 转为pcl::PointCloud消息
  pcl::PointCloud<robosense::common::CloudPointXYZIRT> pointCloud;
  int ret = fromRosMsgToMsg<robosense::common::CloudPointXYZIRT>(depthFrameMsg,
                                                                 pointCloud);
  if (ret != 0) {
    RERROR << "DataConverterRos: fromRosMsgToMsg->DepthFrame failed: ret = "
           << ret;
    return -1;
  }

  // Step2: 转为DepthFrame消息
  const uint32_t pointCnt = pointCloud.size();
  depthFrame.point_nums = pointCnt;
  depthFrame.points = std::shared_ptr<robosense::common::CloudPointXYZIRT>(
      new robosense::common::CloudPointXYZIRT[pointCnt],
      std::default_delete<robosense::common::CloudPointXYZIRT[]>());
  memcpy(depthFrame.points.get(), pointCloud.points.data(),
         sizeof(robosense::common::CloudPointXYZIRT) * pointCnt);
  depthFrame.capture_time.tv_sec = depthFrameMsg.header.stamp.sec;
  depthFrame.capture_time.tv_usec = depthFrameMsg.header.stamp.nsec / 1000;

  return 0;
}

int DataConverterRos::fromMsgToRosMsg(
    const robosense::common::ImageFrame &imageFrame,
    sensor_msgs::Image &imageFrameMsg) {
  imageFrameMsg.width = imageFrame.width;
  imageFrameMsg.height = imageFrame.height;
  imageFrameMsg.is_bigendian = false;
  imageFrameMsg.step = imageFrame.step;
  if (imageFrame.frame_format ==
      robosense::common::ImageFrameFormat::FRAME_FORMAT_RGB24) {
    imageFrameMsg.encoding = "rgb8";
  } else if (imageFrame.frame_format ==
             robosense::common::ImageFrameFormat::FRAME_FORMAT_NV12) {
    imageFrameMsg.encoding = "nv12";
  }
  imageFrameMsg.data.resize(imageFrame.data_bytes);
  memcpy(imageFrameMsg.data.data(), imageFrame.data.get(),
         imageFrame.data_bytes);

  imageFrameMsg.header.frame_id = "/base_link";
  imageFrameMsg.header.seq = imageFrame.sequence;
  imageFrameMsg.header.stamp = ros::Time().fromSec(
      imageFrame.capture_time.tv_sec + imageFrame.capture_time.tv_usec * 1e-6);

  return 0;
}

int DataConverterRos::fromRosMsgToMsg(
    const sensor_msgs::Image &imageFrameMsg,
    robosense::common::ImageFrame &imageFrame) {

  const uint32_t data_bytes = imageFrameMsg.data.size();
  imageFrame.data_bytes = data_bytes;
  imageFrame.data = std::shared_ptr<uint8_t>(new uint8_t[data_bytes],
                                             std::default_delete<uint8_t[]>());
  memcpy(imageFrame.data.get(), imageFrameMsg.data.data(), data_bytes);

  imageFrame.state = true;
  imageFrame.width = imageFrameMsg.width;
  imageFrame.height = imageFrameMsg.height;
  imageFrame.step = imageFrameMsg.step;
  if (imageFrameMsg.encoding == "rgb8") {
    imageFrame.frame_format =
        robosense::common::ImageFrameFormat::FRAME_FORMAT_RGB24;
  } else if (imageFrameMsg.encoding == "nv12") {
    imageFrame.frame_format =
        robosense::common::ImageFrameFormat::FRAME_FORMAT_NV12;
  }
  imageFrame.capture_time.tv_sec = imageFrameMsg.header.stamp.sec;
  imageFrame.capture_time.tv_usec = imageFrameMsg.header.stamp.nsec / 1000;
  imageFrame.sequence = imageFrameMsg.header.seq;
  imageFrame.state = true;

  return 0;
}

int DataConverterRos::fromMsgToRosMsg(
    const robosense::common::MotionFrame &motionFrame,
    sensor_msgs::Imu &motionFrameMsg) {
  motionFrameMsg.orientation.x = motionFrame.orientation.x;
  motionFrameMsg.orientation.y = motionFrame.orientation.y;
  motionFrameMsg.orientation.z = motionFrame.orientation.z;
  motionFrameMsg.orientation.w = motionFrame.orientation.w;
  motionFrameMsg.orientation_covariance = {1, 0, 0, 0, 1, 0, 0, 0, 1};

  motionFrameMsg.linear_acceleration.x = motionFrame.accel.x;
  motionFrameMsg.linear_acceleration.y = motionFrame.accel.y;
  motionFrameMsg.linear_acceleration.z = motionFrame.accel.z;
  motionFrameMsg.linear_acceleration_covariance = {1, 0, 0, 0, 1, 0, 0, 0, 1};

  motionFrameMsg.angular_velocity.x = motionFrame.gyro.x;
  motionFrameMsg.angular_velocity.y = motionFrame.gyro.y;
  motionFrameMsg.angular_velocity.z = motionFrame.gyro.z;
  motionFrameMsg.angular_velocity_covariance = {1, 0, 0, 0, 1, 0, 0, 0, 1};

  motionFrameMsg.header.frame_id = "/base_link";
  motionFrameMsg.header.seq = 0;
  motionFrameMsg.header.stamp =
      ros::Time().fromSec(motionFrame.capture_time.tv_sec +
                          motionFrame.capture_time.tv_usec * 1e-6);

  return 0;
}

int DataConverterRos::fromRosMsgToMsg(
    const sensor_msgs::Imu &motionFrameMsg,
    robosense::common::MotionFrame &motionFrame) {

  motionFrame.state = true;
  motionFrame.temperature = 0;
  motionFrame.accel.x = motionFrameMsg.linear_acceleration.x;
  motionFrame.accel.y = motionFrameMsg.linear_acceleration.y;
  motionFrame.accel.z = motionFrameMsg.linear_acceleration.z;

  motionFrame.gyro.x = motionFrameMsg.angular_velocity.x;
  motionFrame.gyro.y = motionFrameMsg.angular_velocity.y;
  motionFrame.gyro.z = motionFrameMsg.angular_velocity.z;

  motionFrame.orientation.x = motionFrameMsg.orientation.x;
  motionFrame.orientation.y = motionFrameMsg.orientation.y;
  motionFrame.orientation.z = motionFrameMsg.orientation.z;
  motionFrame.orientation.w = motionFrameMsg.orientation.w;

  motionFrame.capture_time.tv_sec = motionFrameMsg.header.stamp.sec;
  motionFrame.capture_time.tv_usec = motionFrameMsg.header.stamp.nsec / 1000;

  return 0;
}

int DataConverterRos::fromMsgToRosMsg(
    const robosense::common::Xyz &xyz,
    hyper_vision_msgs::MotionFrameXyz &xyzMsg) {
  xyzMsg.x = xyz.x;
  xyzMsg.y = xyz.y;
  xyzMsg.z = xyz.z;

  return 0;
}

int DataConverterRos::fromRosMsgToMsg(
    const hyper_vision_msgs::MotionFrameXyz &xyzMsg,
    robosense::common::Xyz &xyz) {
  xyz.x = xyzMsg.x;
  xyz.y = xyzMsg.y;
  xyz.z = xyzMsg.z;

  return 0;
}

int DataConverterRos::fromMsgToRosMsg(
    const std::shared_ptr<robosense::common::CloudPointXYZIRT> &point,
    const uint32_t pointLen,
    std::vector<hyper_vision_msgs::DepthFramePoint> &pointMsg) {
  if (point == nullptr && pointLen > 0) {
    RERROR
        << "DataConverterRos:fromMsgToRosMsg->DepthFramePoint.point is Nullptr";
    return -1;
  }

  pointMsg.resize(pointLen);
  robosense::common::CloudPointXYZIRT *pointer = point.get();
  for (uint32_t i = 0; i < pointLen; ++i) {
    const robosense::common::CloudPointXYZIRT &p = pointer[i];
    hyper_vision_msgs::DepthFramePoint &pMsg = pointMsg[i];
    pMsg.x = p.x;
    pMsg.y = p.y;
    pMsg.z = p.z;
    pMsg.intensity = p.intensity;
    pMsg.ring = p.ring;
    pMsg.timestamp = p.timestamp;
  }

  return 0;
}

int DataConverterRos::fromRosMsgToMsg(
    const std::vector<hyper_vision_msgs::DepthFramePoint> &pointMsg,
    std::shared_ptr<robosense::common::CloudPointXYZIRT> &point) {
  const uint32_t pointLen = pointMsg.size();
  point.reset();
  if (pointLen > 0) {
    try {
      point = std::shared_ptr<robosense::common::CloudPointXYZIRT>(
          new robosense::common::CloudPointXYZIRT[pointLen],
          std::default_delete<robosense::common::CloudPointXYZIRT[]>());
    } catch (...) {
      RERROR << "DataConverterRos:fromRosMsgToMsg->DepthFramePoint.point "
                "malloc failed !";
      return -1;
    }
  }

  robosense::common::CloudPointXYZIRT *pointer = point.get();
  for (uint32_t i = 0; i < pointLen; ++i) {
    robosense::common::CloudPointXYZIRT &p = pointer[i];
    const hyper_vision_msgs::DepthFramePoint &pMsg = pointMsg[i];
    p.x = pMsg.x;
    p.y = pMsg.y;
    p.z = pMsg.z;
    p.intensity = pMsg.intensity;
    p.ring = pMsg.ring;
    p.timestamp = pMsg.timestamp;
  }

  return 0;
}

int DataConverterRos::fromMsgToRosMsg(const std::shared_ptr<uint8_t> &data,
                                      const uint32_t dataLen,
                                      std::vector<uint8_t> &dataMsg) {
  if (data == nullptr && dataLen > 0) {
    RERROR << "DataConverterRos:fromMsgToRosMsg->data is Nullptr";
    return -1;
  }
  dataMsg.resize(dataLen);
  memcpy(dataMsg.data(), data.get(), dataLen);

  return 0;
}

int DataConverterRos::fromRosMsgToMsg(const std::vector<uint8_t> &dataMsg,
                                      std::shared_ptr<uint8_t> &data) {
  const uint32_t dataLen = dataMsg.size();
  data.reset();
  if (dataLen > 0) {
    try {
      data = std::shared_ptr<uint8_t>(new uint8_t[dataLen],
                                      std::default_delete<uint8_t[]>());
    } catch (...) {
      RERROR << "DataConverterRos:fromRosMsgToMsg->data malloc failed !";
      return -1;
    }
  }
  memcpy(data.get(), dataMsg.data(), dataLen);

  return 0;
}

int DataConverterRos::fromMsgToRosMsg(const struct timeval &tv,
                                      hyper_vision_msgs::FrameTimeval &tvMsg) {
  tvMsg.tv_sec = tv.tv_sec;
  tvMsg.tv_usec = tv.tv_usec;
  return 0;
}

int DataConverterRos::fromRosMsgToMsg(
    const hyper_vision_msgs::FrameTimeval &tvMsg, struct timeval &tv) {
  tv.tv_sec = tvMsg.tv_sec;
  tv.tv_usec = tvMsg.tv_usec;
  return 0;
}

} // namespace io
} // namespace robosense