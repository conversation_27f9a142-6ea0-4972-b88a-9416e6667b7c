#include "hyper_vision/dataiomanager/dataiocommon.h"

namespace robosense {
namespace io {

//
bool Ros2EnvironmentUtil::RS_IS_ROS2_ENV_SETTING = false;
std::string Ros2EnvironmentUtil::RS_APP_SETTING_RUNTIME_DIR_PATH = "";
std::string Ros2EnvironmentUtil::RS_ROS2_AMENT_DIR_PATH = "";

std::map<RS_SUPPORT_TOPICNAME_INDEX, std::string>
    DataTopicNameSupportUtil::RS_INDEX_TOPICNAME_MAPPER = {
        {RS_SUPPORT_TOPICNAME_INDEX::RS_DATA_TOPICNAME_MOTION_FRAME, "/rs_imu"},
        {RS_SUPPORT_TOPICNAME_INDEX::RS_DATA_TOPICNAME_IMAGE_FRAME,
         "/rs_camera/rgb"},
        {RS_SUPPORT_TOPICNAME_INDEX::RS_DATA_TOPICNAME_DEPTH_FRAME,
         "/rs_lidar/points"},
        {RS_SUPPORT_TOPICNAME_INDEX::RS_DATA_TOPICNAME_POINTCLOUD_XYZIRT,
         "/cloud_xyzirt"},
        {RS_SUPPORT_TOPICNAME_INDEX::RS_DATA_TOPICNAME_POINTCLOUD_XYZRGBIRT,
         "/cloud_xyzrgbirt"},
        {RS_SUPPORT_TOPICNAME_INDEX::RS_DATA_TOPICNAME_LEFT_IMAGE_FRAME,
         "/rs_camera/left/rgb"},
        {RS_SUPPORT_TOPICNAME_INDEX::RS_DATA_TOPICNAME_RIGHT_IMAGE_FRAME,
         "/rs_camera/right/rgb"},
};

std::map<std::string, std::pair<uint32_t, uint32_t>>
    DataTopicNameSupportUtil::RS_TOPICNAME_IMAGESIZE_MAPPER = {
        // Image Frame
        {DataTopicNameSupportUtil::RS_INDEX_TOPICNAME_MAPPER
             [RS_SUPPORT_TOPICNAME_INDEX::RS_DATA_TOPICNAME_IMAGE_FRAME],
         {1920, 1080}},
};

std::map<std::string, RS_SUPPORT_DATA_TYPE>
    DataTopicNameSupportUtil::RS_TOPICNAME_DATATYPE_MAPPER = {
        // Motion Frame
        {DataTopicNameSupportUtil::RS_INDEX_TOPICNAME_MAPPER
             [RS_SUPPORT_TOPICNAME_INDEX::RS_DATA_TOPICNAME_MOTION_FRAME],
         RS_SUPPORT_DATA_TYPE::RS_SUPERSENSOR_MOTION_FRAME},
        // Image Frame
        {DataTopicNameSupportUtil::RS_INDEX_TOPICNAME_MAPPER
             [RS_SUPPORT_TOPICNAME_INDEX::RS_DATA_TOPICNAME_IMAGE_FRAME],
         RS_SUPPORT_DATA_TYPE::RS_SUPERSENSOR_IMAGE_FRAME},
        // Depth Frame
        {DataTopicNameSupportUtil::RS_INDEX_TOPICNAME_MAPPER
             [RS_SUPPORT_TOPICNAME_INDEX::RS_DATA_TOPICNAME_DEPTH_FRAME],
         RS_SUPPORT_DATA_TYPE::RS_SUPERSENSOR_DEPTH_FRAME},
        // Cloud XYZIRT
        {DataTopicNameSupportUtil::RS_INDEX_TOPICNAME_MAPPER
             [RS_SUPPORT_TOPICNAME_INDEX::RS_DATA_TOPICNAME_POINTCLOUD_XYZIRT],
         RS_SUPPORT_DATA_TYPE::RS_PCL_CLOUD_POINT_RSPOINTXYZIRT},
        // Cloud XYZRGBIRT
        {DataTopicNameSupportUtil::RS_INDEX_TOPICNAME_MAPPER
             [RS_SUPPORT_TOPICNAME_INDEX::
                  RS_DATA_TOPICNAME_POINTCLOUD_XYZRGBIRT],
         RS_SUPPORT_DATA_TYPE::RS_PCL_CLOUD_POINT_RSPOINTXYZIRGBIRT},
        // Left Image Frame
        {DataTopicNameSupportUtil::RS_INDEX_TOPICNAME_MAPPER
             [RS_SUPPORT_TOPICNAME_INDEX::RS_DATA_TOPICNAME_LEFT_IMAGE_FRAME],
         RS_SUPPORT_DATA_TYPE::RS_SUPERSENSOR_IMAGE_FRAME},
        // Right Image Frame
        {DataTopicNameSupportUtil::RS_INDEX_TOPICNAME_MAPPER
             [RS_SUPPORT_TOPICNAME_INDEX::RS_DATA_TOPICNAME_RIGHT_IMAGE_FRAME],
         RS_SUPPORT_DATA_TYPE::RS_SUPERSENSOR_IMAGE_FRAME},
};

std::map<std::string, std::vector<RS_DATA_MESSAGE_PROCESS_TYPE>>
    DataTopicNameSupportUtil::RS_TOPICNAME_PROCESSTYPE_MAPPER = {
        // Motion Frame
        {DataTopicNameSupportUtil::RS_INDEX_TOPICNAME_MAPPER
             [RS_SUPPORT_TOPICNAME_INDEX::RS_DATA_TOPICNAME_MOTION_FRAME],
         {RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_NOTHING}},
        // Image Frame
        {DataTopicNameSupportUtil::RS_INDEX_TOPICNAME_MAPPER
             [RS_SUPPORT_TOPICNAME_INDEX::RS_DATA_TOPICNAME_IMAGE_FRAME],
         {RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_NOTHING,
          RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_JPEG,
          RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_H265}},
        // Depth Frame
        {DataTopicNameSupportUtil::RS_INDEX_TOPICNAME_MAPPER
             [RS_SUPPORT_TOPICNAME_INDEX::RS_DATA_TOPICNAME_DEPTH_FRAME],
         {RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_NOTHING}},
        // Cloud XYZIRT
        {DataTopicNameSupportUtil::RS_INDEX_TOPICNAME_MAPPER
             [RS_SUPPORT_TOPICNAME_INDEX::RS_DATA_TOPICNAME_POINTCLOUD_XYZIRT],
         {RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_NOTHING}},
        // Cloud XYZRGBIRT
        {DataTopicNameSupportUtil::RS_INDEX_TOPICNAME_MAPPER
             [RS_SUPPORT_TOPICNAME_INDEX::
                  RS_DATA_TOPICNAME_POINTCLOUD_XYZRGBIRT],
         {RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_NOTHING}},
        // Image Frame
        {DataTopicNameSupportUtil::RS_INDEX_TOPICNAME_MAPPER
             [RS_SUPPORT_TOPICNAME_INDEX::RS_DATA_TOPICNAME_LEFT_IMAGE_FRAME],
         {RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_NOTHING,
          RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_JPEG,
          RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_H265}},
        // Image Frame
        {DataTopicNameSupportUtil::RS_INDEX_TOPICNAME_MAPPER
             [RS_SUPPORT_TOPICNAME_INDEX::RS_DATA_TOPICNAME_RIGHT_IMAGE_FRAME],
         {RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_NOTHING,
          RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_JPEG,
          RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_H265}},
};

int DataReaderConfig::getDataFormatType(RS_DATA_FORMAT_TYPE &dataIoSourceType) {
  return DataFormatSupportUtil::getDataFormatTypeFromPath(sourceDataFilePath,
                                                          dataIoSourceType);
}

int Ros2EnvironmentUtil::setRos2Environment() {
  if (Ros2EnvironmentUtil::RS_IS_ROS2_ENV_SETTING) {
    RINFO << "Ros2 Environment Already Setting !";
    return 0;
  }

  std::filesystem::path curExeFilePath;
  if (Ros2EnvironmentUtil::checkAppSettingRuntimeDirPathEmpty()) {
    // 使用filesystem::path进行路径操作
    curExeFilePath = std::filesystem::current_path();
    RINFO << "App Setting Runtime Directory Path Is Empty !";
  } else {
    curExeFilePath = std::filesystem::path(
        Ros2EnvironmentUtil::RS_APP_SETTING_RUNTIME_DIR_PATH);
    RINFO << "App Setting Runtime Directory Path Is Not Empty !";
  }
  std::filesystem::path fullFilePath =
      curExeFilePath / Ros2EnvironmentUtil::RS_ROS2_AMENT_DIR_PATH;
  RINFO << "curExeFilePath = " << curExeFilePath.string()
        << ", fullFilePath = " << fullFilePath.string();

  std::set<std::string> ros2LibPaths;
  try {
    for (const auto &entry :
         std::filesystem::directory_iterator(fullFilePath)) {
      if (entry.is_directory()) { // 确保是目录
        std::string ros2LibFilePath = entry.path().string();
        ros2LibPaths.insert(ros2LibFilePath);
        RINFO << "ros2LibFilePath = " << ros2LibFilePath;
      }
    }
  } catch (std::filesystem::filesystem_error &e) {
    RERROR << "Search Ros2 Directory Failed: " << e.what();
    return -1;
  }

  // 根据平台选择分隔符
#ifdef _WIN32
  // const char delimiter = ';';
  // const char* oldPath = std::getenv("PATH");
  // if (oldPath == nullptr) {
  //     RERROR << "Failed to get PATH environment variable.";
  //     return -1;
  // }
  std::string pathToAdd = fullFilePath.string();
  std::string newPath =
      pathToAdd; // pathToAdd + delimiter + std::string(oldPath);
  if (_putenv(("AMENT_PREFIX_PATH=" + newPath).c_str()) != 0) {
    RERROR << "Failed to set PATH environment variable." << newPath;
  } else {
    RINFO << "Successfully added to PATH: " << pathToAdd;
  }
#else
  const char delimiter = ':';
  std::string AMENT_PREFIX_PATH;
  for (auto iter = ros2LibPaths.begin(); iter != ros2LibPaths.end(); ++iter) {
    if (iter != ros2LibPaths.begin())
      AMENT_PREFIX_PATH += delimiter;
    AMENT_PREFIX_PATH += *iter;
  }
  int ret = setenv("AMENT_PREFIX_PATH", AMENT_PREFIX_PATH.c_str(), 1);
  if (ret != 0) {
    RERROR << "SetEnv: AMENT_PREFIX_PATH = " << AMENT_PREFIX_PATH
           << " Failed: ret = " << ret;
    return -2;
  } else {
    RINFO << "SetEnv: AMENT_PREFIX_PATH = " << AMENT_PREFIX_PATH
          << " Successed !";
  }
#endif

  // 验证环境变量
#ifdef _WIN32
  char buffer[32768];
  size_t requiredSize;
  getenv_s(&requiredSize, buffer, sizeof(buffer), "AMENT_PREFIX_PATH");
  char *pPATH = (requiredSize > 0) ? buffer : nullptr;
  if (pPATH == nullptr) {
    RERROR << "GetEnv: AMENT_PREFIX_PATH = " << pathToAdd << " Failed !";
    return -3;
  } else {
    RINFO << "GetEnv: AMENT_PREFIX_PATH = " << pPATH << " success";
  }
#else
  char *pAMENT_PREFIX_PATH = getenv("AMENT_PREFIX_PATH");
  if (pAMENT_PREFIX_PATH == nullptr) {
    RERROR << "GetEnv: AMENT_PREFIX_PATH = " << AMENT_PREFIX_PATH
           << " Failed !";
    return -3;
  } else {
    RINFO << "111GetEnv: AMENT_PREFIX_PATH = " << pAMENT_PREFIX_PATH
          << " success";
  }
#endif
  Ros2EnvironmentUtil::RS_IS_ROS2_ENV_SETTING = true;
  return 0;
}

} // namespace io
} // namespace robosense
