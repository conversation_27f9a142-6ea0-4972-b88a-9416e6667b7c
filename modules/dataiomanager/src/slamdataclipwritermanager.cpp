#include "hyper_vision/dataiomanager/slamdataclipwritermanager.h"
#include <pcl/io/pcd_io.h>

namespace robosense {
namespace io {

SlamDataClipWriterManager::SlamDataClipWriterManager() {}

SlamDataClipWriterManager::~SlamDataClipWriterManager() { stop(); }

int SlamDataClipWriterManager::init(const std::string &basic_write_dir_path) {

  basic_write_dir_path_ = basic_write_dir_path;

  int ret = init();
  if (ret != 0) {
    RERROR << "SlamDataClipWriterManager: init Failed: ret = " << ret;
    return -2;
  }

  return 0;
}

int SlamDataClipWriterManager::start() {
  if (!is_init_) {
    return -1;
  }

  try {
    is_running_ = true;
    buffer_thread_.reset(
        new std::thread(&SlamDataClipWriterManager::writeWorkThread, this));
  } catch (...) {
    is_running_ = false;
    return -2;
  }

  return 0;
}

int SlamDataClipWriterManager::stop() {
  if (!is_init_) {
    return 0;
  }

  {
    std::lock_guard<std::mutex> lg(buffer_mtx_);
    is_running_ = false;
    buffer_cond_.notify_all();
  }

  if (buffer_thread_) {
    if (buffer_thread_->joinable()) {
      buffer_thread_->join();
    }
  }

  return 0;
}

int SlamDataClipWriterManager::addData(
    const robosense::slam::SlamOutputMsg::Ptr &msgPtr) {
  if (msgPtr) {
    std::lock_guard<std::mutex> lg(buffer_mtx_);
    buffer_.push(msgPtr);
    buffer_cond_.notify_one();
  }

  return 0;
}

int SlamDataClipWriterManager::init() {
  sequence_ = 0;
  // 检查基础文件夹是否存在
  std::filesystem::path basic_write_dir(basic_write_dir_path_);
  if (!std::filesystem::exists(basic_write_dir)) {
    try {
      bool isSuccess = std::filesystem::create_directories(basic_write_dir);
      if (isSuccess) {
        RINFO << "SlamDataClipWriterManager: Create Directory: "
              << basic_write_dir.string() << " Successed !";
      } else {
        RERROR << "SlamDataClipWriterManager(A): Create Directory: "
               << basic_write_dir.string() << " Failed !";
        return -1;
      }
    } catch (...) {
      RERROR << "SlamDataClipWriterManager(B): Create Directory: "
             << basic_write_dir.string() << " Failed !";
      return -1;
    }
  }

  DataIoTimeFormatUtil timeFormatUtil("%Y_%m_%d_%H_%M_%S");
  const std::string &current_timestamp = timeFormatUtil.currentTimeString();

  // 用时间戳构造分段Clip路径
  std::filesystem::path currentPath = basic_write_dir / current_timestamp;
  // 如果存在旧的文件夹，则删除
  if (std::filesystem::exists(currentPath)) {
    try {
      bool isSuccess = std::filesystem::remove_all(currentPath);
      if (isSuccess) {
        RINFO << "SlamDataClipWriterManager: Remove Exist Directory: "
              << currentPath.string() << " Successed !";
      } else {
        RERROR << "SlamDataClipWriterManager(A): Remove Exist Directory: "
               << currentPath.string() << " Failed !";
        return -2;
      }
    } catch (...) {
      RERROR << "SlamDataClipWriterManager(B): Remove Exist Directory: "
             << currentPath.string() << " Failed !";
      return -2;
    }
  }

  // 创建文件夹
  try {
    bool isSuccess = std::filesystem::create_directory(currentPath);
    if (isSuccess) {
      RINFO << "SlamDataClipWriterManager: Create Directory: "
            << currentPath.string() << " Successed !";
    } else {
      RERROR << "SlamDataClipWriterManager(A): Create Directory: "
             << currentPath.string() << " Failed !";
      return -3;
    }
  } catch (...) {
    RERROR << "SlamDataClipWriterManager(B): Create Directory: "
           << currentPath.string() << " Failed !";
    return -3;
  }
  basic_write_clip_dir_path_ = currentPath;

  is_init_ = true;
  return 0;
}

void SlamDataClipWriterManager::writeWorkThread() {
  pcl::PointCloud<RsPointXYZRGBI8>::Ptr cloudPtr(
      new pcl::PointCloud<RsPointXYZRGBI8>());

  char buffer[10] = {0};
  while (is_running_) {
    robosense::slam::SlamOutputMsg::Ptr msgPtr;
    {
      std::unique_lock<std::mutex> lg(buffer_mtx_);
      buffer_cond_.wait(lg,
                        [this] { return !buffer_.empty() || !is_running_; });
      if (!is_running_ && buffer_.empty()) {
        break;
      }
      msgPtr = buffer_.front();
      buffer_.pop();
    }

    if (msgPtr) {
      // 构造填充字符串
      memset(buffer, 0, sizeof(buffer));
      snprintf(buffer, 9, "%08d", sequence_);

      std::filesystem::path curPointCloudWritePath =
          basic_write_clip_dir_path_ / (std::string(buffer, 8) + ".pcd");

      const std::string &pcd_file_path = curPointCloudWritePath.string();

      RINFO << "pcd_file_path = " << pcd_file_path;

      // 进行类型转换
      const robosense::common::PointCloud &cloud = msgPtr->slam_point_cloud;
      const size_t total_cnt = cloud.points_vec.size();
      cloudPtr->points.resize(total_cnt);
      cloudPtr->width = 1;
      cloudPtr->height = total_cnt;

      for (size_t i = 0; i < total_cnt; ++i) {
        const auto &point = cloud.points_vec[i];
        auto &pcl_point = cloudPtr->points[i];
        pcl_point.x = point.x;
        pcl_point.y = point.y;
        pcl_point.z = point.z;
        pcl_point.intensity = point.intensity;
        pcl_point.r = point.r;
        pcl_point.g = point.g;
        pcl_point.b = point.b;
      }

      int ret = pcl::io::savePCDFileBinary(pcd_file_path, *cloudPtr);
      if (ret != 0) {
        RWARN << "SlamDataClipWriterManager: Write PCD Data To File: "
              << pcd_file_path << " Failed: ret = " << ret;
      }
      ++sequence_;
    }
  }
}

} // namespace io
} // namespace robosense