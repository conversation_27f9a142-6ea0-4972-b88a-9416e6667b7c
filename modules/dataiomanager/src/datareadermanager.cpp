#include "hyper_vision/dataiomanager/datareadermanager.h"

namespace robosense {
namespace io {

DataReaderManager::DataReaderManager() {}

DataReaderManager::~DataReaderManager() { stop(); }

int DataReaderManager::init(const DataReaderConfig &dataIoConfig) {
  data_io_config_ = dataIoConfig;
  int ret = init();
  if (ret != 0) {
    RERROR << name() << ": initial failed: ret = " << ret;
    return -1;
  }
  return 0;
}

int DataReaderManager::registerCallback(
    const DATA_DEPTH_FRAME_CALLBACK &callback) {
  if (callback != nullptr) {
    std::lock_guard<std::mutex> lg(depth_frame_cbs_mtx_);
    depth_frame_cbs_.push_back(callback);
  }
  return 0;
}

int DataReaderManager::registerCallback(
    const DATA_MOTION_FRAME_CALLBACK &callback) {
  if (callback != nullptr) {
    std::lock_guard<std::mutex> lg(motion_frame_cbs_mtx_);
    motion_frame_cbs_.push_back(callback);
  }
  return 0;
}

int DataReaderManager::registerCallback(
    const DATA_IMAGE_FRAME_CALLBACK &callback) {
  if (callback != nullptr) {
    std::lock_guard<std::mutex> lg(image_frame_cbs_mtx_);
    image_frame_cbs_.push_back(callback);
  }
  return 0;
}

int DataReaderManager::registerCallback(
    const DATA_POINTCLOUDXYZRGBIRT_CALLBACK &callback) {
  if (callback != nullptr) {
    std::lock_guard<std::mutex> lg(pointcloudxyzrgbirt_cbs_mtx_);
    pointcloudxyzrgbirt_cbs_.push_back(callback);
  }

  return 0;
}

int DataReaderManager::registerCallback(
    const DATA_POINTCLOUDXYZIRT_CALLBACK &callback) {
  if (callback != nullptr) {
    std::lock_guard<std::mutex> lg(pointcloudxyzirt_cbs_mtx_);
    pointcloudxyzirt_cbs_.push_back(callback);
  }
  return 0;
}

uint32_t DataReaderManager::getTotalFrameCount() {
  if (data_io_interface_ptr_ != nullptr) {
    return data_io_interface_ptr_->getTotalFrameCount();
  }
  return 0;
}

uint64_t DataReaderManager::getTotalDuration() {
  if (data_io_interface_ptr_ != nullptr) {
    return data_io_interface_ptr_->getTotalDuration();
  }
  return 0;
}

uint64_t DataReaderManager::getBeginTimestamp() {
  if (data_io_interface_ptr_ != nullptr) {
    return data_io_interface_ptr_->getBeginTimestamp();
  }
  return 0;
}

uint64_t DataReaderManager::getEndTimestamp() {
  if (data_io_interface_ptr_ != nullptr) {
    return data_io_interface_ptr_->getEndTimestamp();
  }
  return 0;
}

uint64_t DataReaderManager::getCurrentTimestamp() {
  if (data_io_interface_ptr_ != nullptr) {
    return data_io_interface_ptr_->getCurrentTimestamp();
  }
  return 0;
}

int32_t DataReaderManager::getCurrentFrameIndex() {
  if (data_io_interface_ptr_ != nullptr) {
    return data_io_interface_ptr_->getCurrentFrameIndex();
  }
  return -1;
}

bool DataReaderManager::checkIsPlaying() {
  if (data_io_interface_ptr_ != nullptr) {
    return data_io_interface_ptr_->checkIsPlaying();
  } else {
    return false;
  }
}

bool DataReaderManager::checkIsPlayFinish() {
  if (data_io_interface_ptr_ != nullptr) {
    return data_io_interface_ptr_->checkIsPlayFinish();
  } else {
    return false;
  }
}

uint32_t DataReaderManager::getInitProgress() {
  if (data_io_interface_ptr_ != nullptr) {
    return data_io_interface_ptr_->getInitProgress();
  } else {
    return 0;
  }
}

int DataReaderManager::skipFrame(int skip) {
  if (data_io_interface_ptr_ != nullptr) {
    return data_io_interface_ptr_->skipFrame(skip);
  } else {
    return -1;
  }
}

int DataReaderManager::play() {
  if (data_io_interface_ptr_ != nullptr) {
    return data_io_interface_ptr_->play();
  } else {
    return -1;
  }
}

int DataReaderManager::pause() {
  if (data_io_interface_ptr_ != nullptr) {
    return data_io_interface_ptr_->pause();
  } else {
    return -1;
  }
}

int DataReaderManager::stop() {
  if (data_io_interface_ptr_ != nullptr) {
    return data_io_interface_ptr_->stop();
  } else {
    return -1;
  }
}

int DataReaderManager::init() {
  if (data_io_interface_ptr_ != nullptr) {
    data_io_interface_ptr_.reset();
  }

  RS_DATA_FORMAT_TYPE formatType;
  int ret = data_io_config_.getDataFormatType(formatType);
  if (ret != 0) {
    RERROR << name() << ": parse data format type failed: ret = " << ret;
    return -1;
  }

// 设置ROS2环境变量
#if defined(ROS2_FOUND)
  if (formatType == RS_DATA_FORMAT_TYPE::RS_DATA_FORMAT_ROS2 &&
      Ros2EnvironmentUtil::getRos2EnvironmentFlag() == false) {
    ret = Ros2EnvironmentUtil::setRos2Environment();
    if (ret != 0) {
      RERROR << name() << ": Set Ros2 Env Failed: ret = " << ret;
      return -2;
    } else {
      RINFO << name() << ": Set Ros2 Env Successed !";
    }
  }
#endif

  data_io_interface_ptr_ =
      DataReaderManagerFactory::createDataIoManager(formatType);
  if (data_io_interface_ptr_ == nullptr) {
    RERROR << name() << ": not support data format to read: formatType = "
           << static_cast<int>(formatType);
    return -2;
  }

  // 注册回调函数
  data_io_interface_ptr_->registerCallback(
      std::bind(&DataReaderManager::runLocalDepthFrameCb, this,
                std::placeholders::_1, std::placeholders::_2));
  data_io_interface_ptr_->registerCallback(
      std::bind(&DataReaderManager::runLocalImageFrameCb, this,
                std::placeholders::_1, std::placeholders::_2));
  data_io_interface_ptr_->registerCallback(
      std::bind(&DataReaderManager::runLocalMotionFrameCb, this,
                std::placeholders::_1, std::placeholders::_2));
  data_io_interface_ptr_->registerCallback(
      std::bind(&DataReaderManager::runLocalPointCloudXYZRGBIRTCb, this,
                std::placeholders::_1, std::placeholders::_2));
  data_io_interface_ptr_->registerCallback(
      std::bind(&DataReaderManager::runLocalPointCloudXYZIRTCb, this,
                std::placeholders::_1, std::placeholders::_2));

  ret = data_io_interface_ptr_->init(data_io_config_);
  if (ret != 0) {
    RERROR << "data io interface initial failed: ret = " << ret;
    return -3;
  }

  return 0;
}

void DataReaderManager::runLocalDepthFrameCb(
    const std::string &topicName,
    const std::shared_ptr<robosense::common::DepthFrame> &msgPtr) {
  if (msgPtr != nullptr) {
    std::lock_guard<std::mutex> lg(depth_frame_cbs_mtx_);
    for (const auto &cb : depth_frame_cbs_) {
      if (cb) {
        cb(topicName, msgPtr);
      }
    }
  }
}

void DataReaderManager::runLocalMotionFrameCb(
    const std::string &topicName,
    const std::shared_ptr<robosense::common::MotionFrame> &msgPtr) {
  if (msgPtr != nullptr) {
    std::lock_guard<std::mutex> lg(motion_frame_cbs_mtx_);
    for (const auto &cb : motion_frame_cbs_) {
      if (cb) {
        cb(topicName, msgPtr);
      }
    }
  }
}

void DataReaderManager::runLocalImageFrameCb(
    const std::string &topicName,
    const std::shared_ptr<robosense::common::ImageFrame> &msgPtr) {
  if (msgPtr != nullptr) {
    std::lock_guard<std::mutex> lg(image_frame_cbs_mtx_);
    for (const auto &cb : image_frame_cbs_) {
      if (cb) {
        cb(topicName, msgPtr);
      }
    }
  }
}

void DataReaderManager::runLocalPointCloudXYZRGBIRTCb(
    const std::string &topicName,
    const pcl::PointCloud<RsPointXYZRGBIRT>::Ptr &msgPtr) {
  if (msgPtr != nullptr) {
    std::lock_guard<std::mutex> lg(pointcloudxyzrgbirt_cbs_mtx_);
    for (const auto &cb : pointcloudxyzrgbirt_cbs_) {
      if (cb) {
        cb(topicName, msgPtr);
      }
    }
  }
}

void DataReaderManager::runLocalPointCloudXYZIRTCb(
    const std::string &topicName,
    const pcl::PointCloud<RsPointXYZIRT>::Ptr &msgPtr) {
  if (msgPtr != nullptr) {
    std::lock_guard<std::mutex> lg(pointcloudxyzirt_cbs_mtx_);
    for (const auto &cb : pointcloudxyzirt_cbs_) {
      if (cb) {
        cb(topicName, msgPtr);
      }
    }
  }
}

} // namespace io
} // namespace robosense