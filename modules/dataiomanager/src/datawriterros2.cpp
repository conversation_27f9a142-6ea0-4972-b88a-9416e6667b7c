#include "hyper_vision/dataiomanager/datawriterros2.h"

namespace robosense {
namespace io {

DataWriterRos2::DataWriterRos2() : DataWriterInterface() {}

DataWriterRos2::~DataWriterRos2() {}

int DataWriterRos2::init(const DataWriterConfig &dataIoConfig) {
  data_io_config_ = dataIoConfig;

  int ret = init();
  if (ret != 0) {
    RERROR << name() << ": Initial Failed: ret = " << ret;
    return -1;
  }

  return 0;
}
int DataWriterRos2::start() {
#if defined(ROS2_FOUND)
  rosbag2_transport::RecordOptions compression_opts;
  compression_opts.compression_mode = compression_mode_;
  compression_opts.compression_format = compression_format_;
  compression_opts.compression_queue_size = compression_queue_size_;
  compression_opts.compression_threads = compression_thread_cnt_;

  auto non_compression_opts = compression_opts;
  non_compression_opts.compression_format.clear();

  ros2_writer_ptr_ = rosbag2_transport::ReaderWriterFactory::make_writer(
      (enable_record_compression_ ? compression_opts : non_compression_opts));
  if (ros2_writer_ptr_ == nullptr) {
    RERROR << name() << ": Create Ros2 Write Failed !";
    return -1;
  }

  rosbag2_storage::StorageOptions storage_options;
  storage_options.uri = data_io_config_.dataDirPath + "/super_sensor_" +
                        data_io_time_util_.currentTimeString();
  storage_options.storage_id = "sqlite3";
  storage_options.max_cache_size = 16 * 1024 * 1024;
  storage_options.max_bagfile_size = 0;     // Fixed
  storage_options.max_bagfile_duration = 0; // Fixed

  try {
    RINFO << "rmw_get_serialization_format = "
          << rmw_get_serialization_format();
    ros2_writer_ptr_->open(storage_options,
                           {rmw_get_serialization_format(), "cdr"});
  } catch (...) {
    RERROR << name() << ": Open Ros2 To Write Failed !";
    return -2;
  }
#else
  RERROR << name() << ": Not Support Ros2 !";
  return -1;
#endif // defined(ROS2_FOUND)

  try {
    is_write_running_ = true;
    write_thread_ptr_.reset(
        new std::thread(&DataWriterRos2::writeWorkThread, this));
  } catch (...) {
    is_write_running_ = false;
    RERROR << name() << ": Create Wrte Work Thread Failed !";
    return -3;
  }

#if defined(ROS2_FOUND)
  try {
    is_buffer_running_ = true;
    buffer_thread_ptr_.reset(
        new std::thread(&DataWriterRos2::bufferWorkThread, this));
  } catch (...) {
    is_buffer_running_ = false;
    RERROR << name() << ": Create Buffer Work Thread Failed !";
    return -4;
  }
#endif // defined(ROS2_FOUND)

  RINFO << name() << ": Start !";

  return 0;
}
int DataWriterRos2::stop() {
  if (is_buffer_running_ == false && is_write_running_ == false) {
    return 0;
  }

  // 退出缓冲处理线程
  if (is_buffer_running_) {
    {
      std::lock_guard<std::mutex> lg(buffer_mtx_);
      is_buffer_running_ = false;
      buffer_cond_.notify_all();
    }

    if (buffer_thread_ptr_ != nullptr) {
      if (buffer_thread_ptr_->joinable()) {
        buffer_thread_ptr_->join();
      }
    }
    buffer_thread_ptr_.reset();
  }

  // 如果采用异步编码时: 推出所有H265编码器的剩余数据
  for (auto iterMap = h265_coder_map_.begin(); iterMap != h265_coder_map_.end();
       ++iterMap) {
    const auto &h265CoderPtr = iterMap->second;
    if (h265CoderPtr != nullptr &&
        h265CoderPtr->dataWriteH265CodeMode() ==
            DataWriterH265Encoder::RS_DATA_WRITE_H265_CODE_MODE::
                RS_DATA_WRITE_H265_CODE_ASYNC) {
      int ret = h265CoderPtr->asyncFlushMessage(false);
      if (ret != 0) {
        RERROR << name() << ": Stop Flush H265 Message Failed: ret = " << ret;
        return -1;
      }
    }
  }

  std::this_thread::sleep_for(std::chrono::seconds(2));

  // 释放写线程
  if (is_write_running_) {
    {
      std::lock_guard<std::mutex> lg(write_buffer_mtx_);
      is_write_running_ = false;
      write_buffer_cond_.notify_all();
    }

    if (write_thread_ptr_ != nullptr) {
      if (write_thread_ptr_->joinable()) {
        write_thread_ptr_->join();
      }
    }
    write_thread_ptr_.reset();
  }
  h265_coder_map_.clear();

#if defined(ROS2_FOUND)
  if (ros2_writer_ptr_ != nullptr) {
    ros2_writer_ptr_->close();
    ros2_writer_ptr_.reset();
  }
#endif // defined(ROS2_FOUND)

  is_buffer_running_ = false;

  RINFO << name() << ": Stop !";
  return 0;
}

#if defined(ROS2_FOUND)
void DataWriterRos2::bufferWorkThread() {
  while (is_buffer_running_ || !buffer_.empty()) {
    DataWriterSingleItem::Ptr msgPtr;
    {
      std::unique_lock<std::mutex> lg(buffer_mtx_);
      buffer_cond_.wait(
          lg, [this] { return !buffer_.empty() || !is_buffer_running_; });
      if (!is_buffer_running_ && buffer_.empty()) {
        break;
      }
      msgPtr = buffer_.front();
      buffer_.pop();
    }
    if (msgPtr == nullptr) {
      continue;
    }

    // 进行后处理
    const std::string &topicName = msgPtr->topicName;

    // RINFO << name() << ": topicName = " << topicName;
    auto iterMap = data_io_config_.topicProcessTypes.find(topicName);
    if (iterMap != data_io_config_.topicProcessTypes.end()) {
      // 需要进行JPEG/H265编码
      const RS_DATA_MESSAGE_PROCESS_TYPE processType = iterMap->second;
      if (processType ==
          RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_JPEG) {
        // 进行JPEG编码
        std::shared_ptr<robosense::common::ImageFrame> *ptr =
            msgPtr->message
                ->AnyCast<std::shared_ptr<robosense::common::ImageFrame>>();
        if (ptr == nullptr) {
          continue;
        } else if (*ptr == nullptr) {
          continue;
        }

        const auto &rsMsgPtr = *ptr;
        auto iter = jpeg_coder_map_.find(topicName);
        if (iter != jpeg_coder_map_.end()) {
          const robosense::jpeg::JpegCoder::Ptr &jpegCoderPtr = iter->second;
          if (jpegCoderPtr) {
            std::vector<unsigned char> jpegBuffer(
                rsMsgPtr->width * rsMsgPtr->height * 4.5, '\0');

            size_t jpegBufferLen = jpegBuffer.size();
            int ret =
                jpegCoderPtr->encode(rsMsgPtr->data.get(), rsMsgPtr->data_bytes,
                                     jpegBuffer.data(), jpegBufferLen);
            if (ret != 0) {
              RERROR << name()
                     << ": JPEG Encode Failed: topicName = " << topicName
                     << ", ret = " << ret;
              continue;
            }

            RINFO << name()
                  << ": JPEG Encode Successed: topicName = " << topicName;

            sensor_msgs::msg::CompressedImage::SharedPtr rosMsgPtr(
                new sensor_msgs::msg::CompressedImage());
            rosMsgPtr->data.resize(jpegBufferLen);
            memcpy(rosMsgPtr->data.data(), jpegBuffer.data(), jpegBufferLen);
            rosMsgPtr->format = "jpeg rgb8";
            // rosMsgPtr->header.seq = rsMsgPtr->sequence;
            rosMsgPtr->header.stamp =
                rclcpp::Time(rsMsgPtr->capture_time.tv_sec,
                             rsMsgPtr->capture_time.tv_usec * 1000ull);
            msgPtr->message.reset(new rally::Any(rosMsgPtr));

            {
              std::lock_guard<std::mutex> lg(write_buffer_mtx_);
              msgPtr->isReady = true;
              write_buffer_.push(msgPtr);
              write_buffer_cond_.notify_one();
            }
          }
        }
      } else if (processType ==
                 RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_H265) {
        // 进行H265编码
        auto iter = h265_coder_map_.find(topicName);
        if (iter != h265_coder_map_.end()) {
          const DataWriterH265Encoder::Ptr &h265CoderPtr = iter->second;
          if (h265CoderPtr) {
            const auto codeMode = h265CoderPtr->dataWriteH265CodeMode();
            if (codeMode ==
                DataWriterH265Encoder::RS_DATA_WRITE_H265_CODE_MODE::
                    RS_DATA_WRITE_H265_CODE_ASYNC) {
              // H265 异步编码
              // 先将未编码的消息加入write的队列
              {
                std::lock_guard<std::mutex> lg(write_buffer_mtx_);
                msgPtr->isReady = false;
                write_buffer_.push(msgPtr);
                // write_buffer_cond_.notify_one();
              }

              // 由于未编码的消息加入write的队列会阻塞写线程，则消息的顺序得到了保障，那么这里则可以进行异步编码处理
              int ret = h265CoderPtr->asyncAddMessage(msgPtr, false);
              if (ret != 0) {
                RERROR << name()
                       << ": H265 Message Add Message Failed: ret = " << ret;
                continue;
              }
            } else {
              // H265 同步编码
              int ret = h265CoderPtr->syncAddMessage(msgPtr, false);
              if (ret != 0) {
                continue;
              }

              {
                std::lock_guard<std::mutex> lg(write_buffer_mtx_);
                msgPtr->isReady = true;
                write_buffer_.push(msgPtr);
                write_buffer_cond_.notify_one();
              }
            }
          }
        }
      } else {
        // 不需要额外处理
        std::lock_guard<std::mutex> lg(write_buffer_mtx_);
        msgPtr->isReady = true;
        write_buffer_.push(msgPtr);
        write_buffer_cond_.notify_one();
      }
    } else {
      // 不需要额外处理
      std::lock_guard<std::mutex> lg(write_buffer_mtx_);
      msgPtr->isReady = true;
      write_buffer_.push(msgPtr);
      write_buffer_cond_.notify_one();
    }
  }
}
#endif // defined(ROS2_FOUND)

void DataWriterRos2::writeWorkThread() {
  while (is_write_running_ || !write_buffer_.empty()) {
    DataWriterSingleItem::Ptr singleItemPtr;
    {
      std::unique_lock<std::mutex> lg(write_buffer_mtx_);
      write_buffer_cond_.wait(
          lg, [this] { return !write_buffer_.empty() || !is_write_running_; });
      if (!is_write_running_ && write_buffer_.empty()) {
        break;
      }
      singleItemPtr = write_buffer_.front();
      if (singleItemPtr->isReady) {
        write_buffer_.pop();
      }
    }

    // 如果消息准备完毕，则保存
    if (singleItemPtr->isReady) {
      int ret = writeRegisterMessageRos2(singleItemPtr);
      if (ret != 0) {
        RERROR << name() << ": Write Topic: " << singleItemPtr->topicName
               << " To Rosbag File Failed: ret = " << ret;
      }
    } else {
      // 等待2ms再次尝试
      std::this_thread::sleep_for(std::chrono::milliseconds(2));
      continue;
    }
  }
}

int DataWriterRos2::init() {
  // 根据配置生成编码器
  const auto &topicProcessTypes = data_io_config_.topicProcessTypes;
  for (auto iterMap = topicProcessTypes.begin();
       iterMap != topicProcessTypes.end(); ++iterMap) {
    const std::string &topicName = iterMap->first;
    const RS_DATA_MESSAGE_PROCESS_TYPE processType = iterMap->second;
    int ret = 0;
    switch (processType) {
    case RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_JPEG: {
      ret = initAddJpegCoder(topicName);
      break;
    }
    case RS_DATA_MESSAGE_PROCESS_TYPE::RS_DATA_MESSAGE_PROCESS_H265: {
      ret = initAddH265Coder(topicName);
      break;
    }
    default: {
      break;
    }
    }

    if (ret != 0) {
      RERROR << name()
             << ": Add Jpeg/H265 Coder Failed: TopicName = " << topicName
             << ", ret = " << ret;
      return -1;
    }
  }

  return 0;
}

} // namespace io
} // namespace robosense