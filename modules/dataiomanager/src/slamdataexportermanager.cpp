#include "hyper_vision/dataiomanager/slamdataexportermanager.h"
#include <pcl/io/pcd_io.h>

namespace robosense {
namespace io {

SlamDataExporterManager::SlamDataExporterManager() {}

SlamDataExporterManager::~SlamDataExporterManager() { stop(); }

int SlamDataExporterManager::init(const SlamDataExporterConfig &config) {
  config_ = config;
  int ret = init();
  if (ret != 0) {
    RERROR << "SlamDataExporterManager: Initial Failed: ret = " << ret;
    return -1;
  }

  return 0;
}

int SlamDataExporterManager::start() {
  try {
    is_running_ = true;
    process_thread_ptr_.reset(
        new std::thread(&SlamDataExporterManager::processWorkThread, this));
  } catch (...) {
    RERROR << "SlamDataExporterManager: Malloc Process Thread Failed !";
    return -1;
  }

  RINFO << "SlamDataExporterManager: Start !";

  return 0;
}

int SlamDataExporterManager::stop() {
  if (is_running_) {
    is_running_ = false;
  }

  if (process_thread_ptr_) {
    if (process_thread_ptr_->joinable()) {
      process_thread_ptr_->join();
    }
    process_thread_ptr_.reset();
  }

  RINFO << "SlamDataExporterManager: Stop !";

  return 0;
}

bool SlamDataExporterManager::checkIsSuccess() const { return is_success_; }

int SlamDataExporterManager::getProgress() const { return progress_; }

int SlamDataExporterManager::init() {
  is_success_ = false;
  is_running_ = false;
  progress_ = 0;
  search_pcd_file_paths_.clear();

  // 检查输入路径
  const std::filesystem::path basicSaveDirPath(config_.basic_save_dir_path);
  if (!std::filesystem::exists(basicSaveDirPath)) {
    RERROR << "SlamDataExporterManager: basic_save_dir_path = "
           << config_.basic_save_dir_path << " Not Exist !";
    return -1;
  } else {
    RINFO << "SlamDataExporterManager: basic_save_dir_path = "
          << config_.basic_save_dir_path;
  }

  // 检查输出路径
  if (config_.export_save_dir_path.empty()) {
    config_.export_save_dir_path = config_.basic_save_dir_path;
    RINFO << "SlamDataExporterManager: Not Setting export_save_dir_path, Use "
             "basic_save_dir_path = "
          << config_.basic_save_dir_path;
  } else {
    const std::filesystem::path exportSaveDirPath(config_.export_save_dir_path);
    if (!std::filesystem::exists(exportSaveDirPath)) {
      bool isSuccess = std::filesystem::create_directory(exportSaveDirPath);
      if (!isSuccess) {
        RERROR << "SlamDataExporterManager: export_save_dir_path = "
               << config_.export_save_dir_path
               << " Not Exist And Create Directory Failed !";
        return -2;
      }
    }
    RINFO << "SlamDataExporterManager: export_save_dir_path = "
          << config_.export_save_dir_path;
  }

  // 遍历输入的PCD文件
  try {
    for (const auto &entry :
         std::filesystem::directory_iterator(basicSaveDirPath)) {
      RINFO << "entry.path() = " << entry.path().string();
      // 普通文件
      if (entry.is_regular_file()) {
        const std::filesystem::path &file_path = entry.path();
        const std::string &extension = file_path.extension().string();
        if (extension == RS_PCD_EXTENSION) {
          search_pcd_file_paths_.push_back(file_path.string());
          RINFO << "SlamDataExporterManager: Search PCD File: "
                << file_path.string();
        } else {
          RINFO << "SlamDataExporterManager: Not PCD File: "
                << file_path.string();
        }
      }
    }
  } catch (std::filesystem::filesystem_error &e) {
    RERROR << "SlamDataExporterManager: Search PCD File Path Failed: error = "
           << std::string(e.what());
    return -3;
  }

  // 进行排序
  std::sort(search_pcd_file_paths_.begin(), search_pcd_file_paths_.end());

  RINFO << "SlamDataExporterManager: search_pcd_file_paths_ SIZE = "
        << search_pcd_file_paths_.size();

  return 0;
}

void SlamDataExporterManager::processWorkThread() {
  pcl::PointCloud<RsPointXYZRGBI8>::Ptr accu_cloud_ptr(
      new pcl::PointCloud<RsPointXYZRGBI8>());

  int segment_index = 0;
  size_t i = 0;
  for (; i < search_pcd_file_paths_.size() && is_running_; ++i) {
    const std::string &pcd_file_path = search_pcd_file_paths_[i];

    RINFO << "pcd_file_path = " << pcd_file_path;
    // 载入一帧PCD
    pcl::PointCloud<RsPointXYZRGBI8>::Ptr single_cloud_ptr(
        new pcl::PointCloud<RsPointXYZRGBI8>());
    int ret = pcl::io::loadPCDFile(pcd_file_path, *single_cloud_ptr);
    if (ret != 0) {
      RERROR << "SlamDataExporterManager: Load PCD Data From File: "
             << pcd_file_path << " Failed: ret = " << ret;
      is_success_ = false;
      progress_ = 100;
      return;
    }

    RINFO << "pcd_file_path = " << pcd_file_path;

    // 累积一帧PCD
    const size_t point_cnt = single_cloud_ptr->size();
    if (point_cnt > 0) {
      const size_t accu_point_cnt = accu_cloud_ptr->size();
      const size_t total_point_cnt = accu_point_cnt + point_cnt;
      accu_cloud_ptr->resize(total_point_cnt);
      for (size_t j = 0; j < point_cnt; ++j) {
        accu_cloud_ptr->points[j + accu_point_cnt] =
            single_cloud_ptr->points[j];
      }
      accu_cloud_ptr->width = 1;
      accu_cloud_ptr->height = total_point_cnt;

      // 如果分段，则判断是否写出分段数据
      if (config_.is_segment) {
        const uint64_t total_map_size =
            total_point_cnt * sizeof(RsPointXYZRGBI8);
        if (total_map_size > config_.max_segment_size_bytes) {
          const std::filesystem::path seg_pcd_save_path =
              std::filesystem::path(config_.export_save_dir_path) /
              (RS_EXPORT_MAP_PART_NAME + std::to_string(segment_index) +
               RS_PCD_EXTENSION);
          int ret = writePcdToFile(accu_cloud_ptr, seg_pcd_save_path.string());
          if (ret != 0) {
            RERROR << "SlamDataExporterManager: Write Pcd Data To File: "
                   << seg_pcd_save_path.string() << " Failed: ret = " << ret;
            is_success_ = false;
            progress_ = 100;
            return;
          } else {
            RINFO << "SlamDataExporterManager: Write Pcd Data To File: "
                  << seg_pcd_save_path.string() << " Successed !";
            accu_cloud_ptr.reset(new pcl::PointCloud<RsPointXYZRGBI8>());
          }
          ++segment_index;
        }
      }
    }
  }

  // 保存数据
  std::filesystem::path seg_pcd_save_path;
  if (accu_cloud_ptr->size() > 0) {
    if (config_.is_segment) {
      seg_pcd_save_path = std::filesystem::path(config_.export_save_dir_path) /
                          (RS_EXPORT_MAP_PART_NAME +
                           std::to_string(segment_index) + RS_PCD_EXTENSION);

    } else {
      seg_pcd_save_path = std::filesystem::path(config_.export_save_dir_path) /
                          (RS_EXPORT_MAP_NAME + RS_PCD_EXTENSION);
    }

    int ret = writePcdToFile(accu_cloud_ptr, seg_pcd_save_path.string());
    if (ret != 0) {
      RERROR << "SlamDataExporterManager: Write Pcd Data To File: "
             << seg_pcd_save_path.string() << " Failed: ret = " << ret;
      is_success_ = false;
      progress_ = 100;
      return;
    } else {
      RINFO << "SlamDataExporterManager: Write Pcd Data To File: "
            << seg_pcd_save_path.string() << " Successed !";
    }
  }

  // 处理成功
  is_success_ = (i == search_pcd_file_paths_.size());
  progress_ = 100;
}

int SlamDataExporterManager::writePcdToFile(
    const pcl::PointCloud<RsPointXYZRGBI8>::Ptr &cloudPtr,
    const std::string &pcd_save_path) {
  if (cloudPtr == nullptr) {
    RERROR << "SlamDataExporterManager: Input cloudPtr is Nullptr !";
    return -1;
  }

  int ret = pcl::io::savePCDFileBinary(pcd_save_path, *cloudPtr);
  if (ret != 0) {
    RERROR << "SlamDataExporterManager: Save PCD Data To File: "
           << pcd_save_path << " Failed: ret = " << ret;
    return -2;
  } else {
    RINFO << "SlamDataExporterManager: Save PCD Data To File: " << pcd_save_path
          << " Successed !";
  }

  return 0;
}

} // namespace io
} // namespace robosense