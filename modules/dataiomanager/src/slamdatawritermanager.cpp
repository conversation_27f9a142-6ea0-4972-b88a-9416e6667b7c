#include "hyper_vision/dataiomanager/slamdatawritermanager.h"

namespace robosense {
namespace io {

SlamDataWriterManager::SlamDataWriterManager() {}

SlamDataWriterManager::~SlamDataWriterManager() { stop(); }

int SlamDataWriterManager::init() {
  is_writer_ = false;
  is_init_ = true;
  return 0;
}

int SlamDataWriterManager::start() {
  if (!is_init_) {
    RERROR << "SlamDataWriterManager: Not Initial Before Start !";
    return -1;
  }

  int ret = startEraseThread();
  if (ret != 0) {
    RERROR << "SlamDataWriterManager: Start Erase Thread Failed: ret = " << ret;
    return -2;
  }

  return 0;
}

int SlamDataWriterManager::stop() {
  if (!is_init_) {
    return 0;
  }

  // 关闭buffer线程
  stopBufferThread();

  // 关闭erase线程
  stopEraseThread();

  is_init_ = false;

  return 0;
}

int SlamDataWriterManager::startWrite(const std::string &basic_save_dir_path) {
  std::filesystem::path basicSaveDirPath(basic_save_dir_path);
  if (!std::filesystem::exists(basicSaveDirPath)) {
    bool isSuccess = std::filesystem::create_directory(basicSaveDirPath);
    if (!isSuccess) {
      RERROR << "SlamDataWriterManager: basic_save_dir_path = "
             << basic_save_dir_path
             << ", Not Exist, But Create This Directory Failed !";
      return -1;
    }
  }
  basic_save_dir_path_ = basic_save_dir_path;

  int ret = startBufferThread();
  if (ret != 0) {
    RERROR << "SlamDataWriterManager: Start Buffer Thread Failed: ret = "
           << ret;
    return -2;
  }

  is_writer_ = true;

  RINFO << "Start Slam Data Writer: " << basic_save_dir_path_;

  return 0;
}

int SlamDataWriterManager::stopWrite() {
  // 设置状态位
  is_writer_ = false;

  // 删除最后一个Clip Writer
  stopBufferThread();

  RINFO << "Stop Slam Data Writer: " << basic_save_dir_path_;

  return 0;
}

int SlamDataWriterManager::addData(
    const robosense::slam::SlamOutputMsg::Ptr &msgPtr) {
  if (is_writer_ && msgPtr) {
    std::lock_guard<std::mutex> lg(buffer_mtx_);
    buffer_.push(msgPtr);
    buffer_cond_.notify_one();
    RINFO << "Slam Data Writer Manager Add Data !";
  }
  return 0;
}

SlamDataClipWriterManager::Ptr SlamDataWriterManager::createClipWriter() {
  SlamDataClipWriterManager::Ptr clipWriterPtr;
  try {
    clipWriterPtr.reset(new SlamDataClipWriterManager());
  } catch (...) {
    RERROR << "SlamDataWriterManager: Malloc Clip Writer Manager Failed !";
    return nullptr;
  }

  int ret = clipWriterPtr->init(basic_save_dir_path_);
  if (ret != 0) {
    RERROR << "SlamDataClipWriterManager: Initial Clip Writer Manager Failed: "
              "ret = "
           << ret;
    return nullptr;
  }

  ret = clipWriterPtr->start();
  if (ret != 0) {
    RERROR
        << "SlamDataClipWriterManager: Start Clip Writer Manager Failed: ret = "
        << ret;
    return nullptr;
  }

  return clipWriterPtr;
}

int SlamDataWriterManager::eraseClipWriter() {
  SlamDataClipWriterManager::Ptr lastClipWriterManagerPtr;
  {
    std::lock_guard<std::mutex> lg(current_clip_writer_mtx_);
    if (current_clip_wirter_ptr_) {
      lastClipWriterManagerPtr = current_clip_wirter_ptr_;
    }
    current_clip_wirter_ptr_.reset();
  }

  if (lastClipWriterManagerPtr) {
    std::lock_guard<std::mutex> lg(erase_buffer_mtx_);
    erase_buffer_.push(lastClipWriterManagerPtr);
    erase_buffer_cond_.notify_one();
  }

  return 0;
}

bool SlamDataWriterManager::checkClipSegment(
    const robosense::slam::SlamOutputMsg::Ptr &msgPtr) {
  const int32_t cur_map_id = msgPtr->slam_status.cur_map_id;
  // Case1: 未创建任何Clip Writer
  {
    std::lock_guard<std::mutex> lg(current_clip_writer_mtx_);
    if (current_clip_wirter_ptr_ == nullptr) {
      cur_map_id_ = cur_map_id;
      return true;
    }
  }

  // Case2: 状态改变需要重新分段
  if (cur_map_id_ >= 0 && cur_map_id != cur_map_id_) {
    cur_map_id_ = cur_map_id;
    return true;
  }

  return false;
}

int SlamDataWriterManager::startBufferThread() {
  try {
    is_running_ = true;
    buffer_thread_ptr_.reset(
        new std::thread(&SlamDataWriterManager::bufferWorkThread, this));
  } catch (...) {
    RERROR << "SlamDataClipWriterManager: Malloc Buffer Thread Failed !";
    is_running_ = false;
    return -1;
  }

  return 0;
}

int SlamDataWriterManager::stopBufferThread() {
  {
    std::lock_guard<std::mutex> lg(buffer_mtx_);
    is_running_ = false;
    buffer_cond_.notify_all();
  }

  if (buffer_thread_ptr_) {
    if (buffer_thread_ptr_->joinable()) {
      buffer_thread_ptr_->join();
    }
    buffer_thread_ptr_.reset();
  }

  return 0;
}

int SlamDataWriterManager::startEraseThread() {
  try {
    is_erase_running_ = true;
    erase_thread_ptr_.reset(
        new std::thread(&SlamDataWriterManager::eraseWorkThread, this));
  } catch (...) {
    RERROR << "SlamDataClipWriterManager: Malloc Erase Thread Failed !";
    is_erase_running_ = false;
    return -1;
  }
  return 0;
}

int SlamDataWriterManager::stopEraseThread() {
  {
    std::lock_guard<std::mutex> lg(erase_buffer_mtx_);
    is_erase_running_ = false;
    erase_buffer_cond_.notify_all();
  }

  if (erase_thread_ptr_) {
    if (erase_thread_ptr_->joinable()) {
      erase_thread_ptr_->join();
    }
    erase_thread_ptr_.reset();
  }

  return 0;
}

void SlamDataWriterManager::bufferWorkThread() {
  while (is_running_) {
    bool is_empty = false;
    robosense::slam::SlamOutputMsg::Ptr msgPtr;
    {
      std::unique_lock<std::mutex> lg(buffer_mtx_);
      buffer_cond_.wait(lg,
                        [this] { return !buffer_.empty() || !is_running_; });
      if (!is_running_ && buffer_.empty()) {
        break;
      }
      msgPtr = buffer_.front();
      buffer_.pop();

      is_empty = buffer_.empty();
    }

    if (msgPtr) {
      // 定位状态不可靠: 过滤
      if (msgPtr->slam_status.cur_map_id < 0 &&
          msgPtr->slam_status.is_enable_relocalization) {
        continue;
      }

      bool isClipSegment = checkClipSegment(msgPtr);
      if (isClipSegment) {
        auto writerPtr = createClipWriter();
        if (writerPtr == nullptr) {
          RERROR << "SlamDataWriterManager: Create Clip Writer Failed !";
          break;
        }

        std::lock_guard<std::mutex> lg(current_clip_writer_mtx_);
        current_clip_wirter_ptr_ = writerPtr;
      }

      {
        std::lock_guard<std::mutex> lg(current_clip_writer_mtx_);
        if (current_clip_wirter_ptr_) {
          RINFO << "RUN HERE SLAM DATA ADD !";
          current_clip_wirter_ptr_->addData(msgPtr);
        }
      }
    }
  }

  // 将最后一个clip writer 加入erase队列
  eraseClipWriter();
}

void SlamDataWriterManager::eraseWorkThread() {
  while (is_erase_running_) {
    SlamDataClipWriterManager::Ptr clipWriterPtr;
    {
      std::unique_lock<std::mutex> lg(erase_buffer_mtx_);
      erase_buffer_cond_.wait(
          lg, [this] { return !erase_buffer_.empty() || !is_erase_running_; });

      if (!is_erase_running_ && erase_buffer_.empty()) {
        break;
      }

      clipWriterPtr = erase_buffer_.front();
      erase_buffer_.pop();
    }

    if (clipWriterPtr) {
      clipWriterPtr->stop();
    }
  }
}

} // namespace io
} // namespace robosense