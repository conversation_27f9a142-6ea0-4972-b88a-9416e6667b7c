#========================
# libs
#========================

set(CMAKE_POSITION_INDEPENDENT_CODE ON)

set(CUR_SRCS "")
set(CUR_INCLUDES "include")

set(CUR_SUB_DIR "")
LIST(APPEND CUR_SUB_DIR include)
LIST(APPEND CUR_SUB_DIR src)

foreach (dir ${CUR_SUB_DIR})
    file(GLOB_RECURSE tmp_srcs ${dir}/*.cpp ${dir}/*.h)
    list(APPEND CUR_SRCS ${tmp_srcs})
endforeach ()

add_library(${CUR_LIB} STATIC
        ${CUR_SRCS}
        )

target_include_directories(${CUR_LIB}
        PUBLIC
        ${CUR_INCLUDES}
        )
target_link_libraries(${CUR_LIB}
        PUBLIC
        rally_utils
        rsbag 
        rsros
        rsmsg
        common
        codec
        front_end
        ${ROS2_LIBS}
        )
target_compile_definitions(${CUR_LIB} PRIVATE MODULE_NAME="dataio")

set(enable_test false)
if(enable_test)
        message("enable dataio test !")
        add_executable(rosbag_write_test ./test/rosbag_write_test.cpp)
        target_link_libraries(rosbag_write_test dataio)
        
        add_executable(ros2_write_test ./test/ros2_write_test.cpp)
        target_link_libraries(ros2_write_test dataio)

        add_executable(rosbag_read_test ./test/rosbag_read_test.cpp)
        target_link_libraries(rosbag_read_test dataio)

        add_executable(ros2_read_test ./test/ros2_read_test.cpp)
        target_link_libraries(ros2_read_test dataio)

        add_executable(rosbag_read_filter_topics_test ./test/rosbag_read_filter_topics_test.cpp)
        target_link_libraries(rosbag_read_filter_topics_test dataio)

        add_executable(ros2_read_filter_topics_test ./test/ros2_read_filter_topics_test.cpp)
        target_link_libraries(ros2_read_filter_topics_test dataio)

        add_executable(rosbag_read_filter_time_test ./test/rosbag_read_filter_time_test.cpp)
        target_link_libraries(rosbag_read_filter_time_test dataio)

        add_executable(ros2_read_filter_time_test ./test/ros2_read_filter_time_test.cpp)
        target_link_libraries(ros2_read_filter_time_test dataio)
else() 
        message("disable dataio test !")
endif(enable_test)
