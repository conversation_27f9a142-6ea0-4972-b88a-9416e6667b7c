/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "hyper_vision/driver/super_sensor_dummy.h"

#define ENABLE_POINTER_IS_SHARED_PTR (0)

namespace robosense {
namespace driver {

void SuperSensorDummy::Init(const YAML::Node &cfg_node) {
  RINFO << name() << ": get cfg is " << cfg_node;
  RDEBUG << name() << ": init succeed!";
}

void SuperSensorDummy::Start() {
  run_flag_ = true;
  if (thread_ptr_ == nullptr) {
    thread_ptr_ = std::make_unique<std::thread>(&SuperSensorDummy::Core, this);
  }
}

void SuperSensorDummy::Stop() {
  if (thread_ptr_ != nullptr) {
    run_flag_ = false;
    if (thread_ptr_->joinable()) {
      thread_ptr_->join();
    }
    thread_ptr_.reset(nullptr);
  }
}

void SuperSensorDummy::SetCallback(const std::function<void(const std::shared_ptr<robosense::common::MotionFrame> &msg_ptr)> &callback) {
  std::lock_guard<std::mutex> lg(mx_cb_);
  motion_frame_cb_ = callback;
}

void SuperSensorDummy::SetCallback(const std::function<void(const std::shared_ptr<robosense::common::DepthFrame> &msg_ptr)> &callback) {
  std::lock_guard<std::mutex> lg(mx_cb_);
  depth_frame_cb_ = callback;
}

void SuperSensorDummy::SetCallback(const std::function<void(const std::shared_ptr<robosense::common::ImageFrame> &msg_ptr)> &callback) {
  std::lock_guard<std::mutex> lg(mx_cb_);
  image_frame_cb_ = callback;
}

void SuperSensorDummy::Core() {
  while (run_flag_) {
    //=============test code============
    std::shared_ptr<robosense::common::MotionFrame> motion_frame_msg_ptr(new robosense::common::MotionFrame);
    std::shared_ptr<robosense::common::DepthFrame> depth_frame_msg_ptr(new robosense::common::DepthFrame);
    std::shared_ptr<robosense::common::ImageFrame> image_frame_msg_ptr(new robosense::common::ImageFrame);
    if (motion_frame_cb_) {
      motion_frame_cb_(motion_frame_msg_ptr);
    }
    if (depth_frame_cb_) {
      depth_frame_cb_(depth_frame_msg_ptr);
    }
    if (image_frame_cb_) {
      image_frame_cb_(image_frame_msg_ptr);
    }

    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    //==================================
  }
}

} // namespace driver
} // namespace robosense
