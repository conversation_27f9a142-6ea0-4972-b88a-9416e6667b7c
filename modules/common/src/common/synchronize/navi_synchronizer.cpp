/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "hyper_vision/common/synchronize/navi_synchronizer.h"

namespace robosense {
namespace common {

NaviSynchronizer::NaviSynchronizer(const NaviSynOptions& options) {
  options_ = options;
  RENSURE(!options_.frame_info_vec.empty());
  if (options_.time_out <= 0.) {
    RWARN << name() << ": time_out is illegal and set as default value as 0.1s";
    options_.time_out = 0.1;
  }
  if (options_.min_time_interval <= 0.) {
    RWARN << name() << ": min_time_interval is less than zero " << std::to_string(options_.min_time_interval);
  }
  timeout_ = std::chrono::duration_cast<std::chrono::seconds>(std::chrono::duration<double>(options_.time_out));

  for (size_t i = 0; i < options_.frame_info_vec.size(); ++i) {
    const auto& frame_id = options_.frame_info_vec[i].frame_id;
    const auto& max_size = options_.frame_info_vec[i].queue_size;
    std::deque<rally::AnySensor::Ptr> tmp_msg_deque;
    sensor_msg_map_[frame_id] = tmp_msg_deque;
    sensor_msg_max_size_[frame_id] = max_size;
    ready_flags_map_[frame_id] = false;
  }

  running_ = true;
  sync_thread_ = std::thread(&NaviSynchronizer::Core, this);
  RINFO << name() << ": init succeed!";
}

void NaviSynchronizer::AddData(const rally::AnySensor::Ptr& any_sensor_ptr) {
  std::unique_lock<std::mutex> lock(mutex_);

  // check
  const auto& frame_id = any_sensor_ptr->frame_id;
  if (sensor_msg_map_.find(frame_id) == sensor_msg_map_.end()) {
    RWARN << name() << ": receive unknown frame_id as " << frame_id;
    return;
  }

  // set
  sensor_msg_map_.at(frame_id).emplace_back(any_sensor_ptr);
  while (sensor_msg_map_.at(frame_id).size() > sensor_msg_max_size_.at(frame_id)) {
    sensor_msg_map_.at(frame_id).pop_front();
  }
  ready_flags_map_.at(frame_id) = true;

  // trigger
  all_ready_ = true;
  for (auto itr = ready_flags_map_.begin(); itr != ready_flags_map_.end(); ++itr) {
    if (!itr->second) {
      all_ready_ = false;
      break;
    }
  }
  if (!all_ready_) {
    return;
  }
  RINFO << name() << ": all_ready! " << any_sensor_ptr->frame_id;
  condition_.notify_one();
}

void NaviSynchronizer::AddMainData(const rally::AnySensor::Ptr& any_sensor_ptr) {
  std::unique_lock<std::mutex> lock(mutex_);

  // check
  const auto& frame_id = any_sensor_ptr->frame_id;
  if (sensor_msg_map_.find(frame_id) == sensor_msg_map_.end()) {
    RWARN << name() << ": receive unknown frame_id as " << frame_id;
    return;
  }

  sensor_msg_map_.at(frame_id).push_back(any_sensor_ptr);
  main_data_ready_ = true;
  condition_.notify_one();
}

void NaviSynchronizer::regSynCallback(const std::function<void(const std::vector<rally::AnySensor::Ptr> &msg_ptr_vec)> &cb) {
  std::unique_lock<std::mutex> lock(mutex_);
  syn_cb_ = cb;
}

void NaviSynchronizer::Core() {
  while (running_) {
    std::unique_lock<std::mutex> lock(mutex_);

    // 等待三种条件：全部就绪、超时、主数据到达
    condition_.wait_for(lock, timeout_, [this] {
      return all_ready_ || main_data_ready_;
    });

    // 触发回调
    if (syn_cb_) {
      uint64_t latest_time{0};
      std::string latest_frame_id;
      for (auto itr = sensor_msg_map_.begin(); itr != sensor_msg_map_.end(); ++itr) {
        if (itr->second.empty()) {
          continue;
        }
        if (latest_time == 0) {
          latest_time = 1;
        }
        if (itr->second.back()->timestamp > latest_time) {
          latest_time = itr->second.back()->timestamp;
          latest_frame_id = itr->first;
        }
      }
      if (latest_time == 0) {
//          RWARN << name() << ": do not receive any data during " << std::to_string(options_.time_out) << " s.";
        continue;
      } else if (latest_time == 1) {
        RWARN << name() << ": all receive data is time 0!";
      }

      std::vector<rally::AnySensor::Ptr> res_vec;
      for (auto itr = sensor_msg_map_.begin(); itr != sensor_msg_map_.end(); ++itr) {
        const auto& frame_id = itr->first;
        if (itr->second.empty()) {
          continue;
        }
        const auto& sensor_ptr = itr->second.back();
        if (NanoToSec(latest_time) - NanoToSec(sensor_ptr->timestamp) < options_.min_time_interval) {
          res_vec.emplace_back(sensor_ptr);
        } else {
          RINFO << name() << ": the " << frame_id << " is to late at " << rally::Time(sensor_ptr->timestamp).toString() << ", latest " << latest_frame_id << " at " << rally::Time(latest_time).toString();
        }
        itr->second.clear();
        ready_flags_map_.at(frame_id) = false;
      }
      all_ready_ = false;
      main_data_ready_ = false;
      syn_cb_(res_vec);
    }
  }
}

}  // namespace common
}  // namespace robosense
