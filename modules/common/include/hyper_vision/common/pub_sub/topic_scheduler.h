/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef HYPER_VISION_COMMON_PUB_SUB_TOPIC_SCHEDULER_H_
#define HYPER_VISION_COMMON_PUB_SUB_TOPIC_SCHEDULER_H_

#include <iostream>
#include <string>
#include <unordered_map>
#include <vector>
#include <functional>
#include <any>
#include <mutex>
#include <queue>
#include <thread>
#include <condition_variable>
#include <atomic>
#include "rally/utils/utils.h"

namespace robosense {
namespace common {

class TopicScheduler {
 public:
  // 获取单例实例
  static TopicScheduler &instance() {
    static TopicScheduler instance;
    return instance;
  }

  // 订阅主题（类型安全的模板接口）
  template<typename T>
  void subscribe(const std::string &topic, std::function<void(const T &)> callback) {
    std::lock_guard<std::mutex> lock(mutex_);
    auto wrapper = [topic, callback](const std::any &data) {
      try {
        callback(std::any_cast<const T &>(data));
      } catch (const std::bad_any_cast &e) {
        handle_type_error(topic, typeid(T).name(), e.what());
      }
    };
    subscriptions_[topic].push_back(wrapper);
  }

  // 同步发布（阻塞式）
  template<typename T>
  void publish(const std::string &topic, const T &message) {
    std::vector<std::function<void(const std::any &)>> callbacks;
    {
      std::lock_guard<std::mutex> lock(mutex_);
      auto it = subscriptions_.find(topic);
      if (it == subscriptions_.end()) return;
      callbacks = it->second;
    }
    for (auto &cb : callbacks) {
      cb(std::any(message));
    }
  }

  // 异步发布（非阻塞）
  template<typename T>
  void publish_async(const std::string &topic, const T &message) {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = subscriptions_.find(topic);
    if (it == subscriptions_.end()) return;

    // 创建任务副本
    auto task = [=]() {
      auto data = std::any(message);
      for (auto &cb : it->second) {
        cb(data);
      }
    };

    // 提交到线程池
    {
      std::lock_guard<std::mutex> queue_lock(task_mutex_);
      task_queue_.push(task);
    }
    task_cv_.notify_one();
  }

  // 初始化线程池
  void start_workers(size_t num_threads = std::thread::hardware_concurrency()) {
    stop_flag_ = false;
    for (size_t i = 0; i < num_threads; ++i) {
      workers_.emplace_back([this] { worker_loop(); });
    }
  }

  // 关闭线程池
  void stop_workers() {
    {
      std::lock_guard<std::mutex> lock(task_mutex_);
      stop_flag_ = true;
    }
    task_cv_.notify_all();
    for (auto &worker : workers_) {
      if (worker.joinable()) worker.join();
    }
  }

 private:
  TopicScheduler() = default;
  ~TopicScheduler() {
    stop_workers();
  }
  TopicScheduler(const TopicScheduler &) = delete;
  TopicScheduler &operator=(const TopicScheduler &) = delete;

  // 工作线程循环
  void worker_loop() {
    while (true) {
      std::function<void()> task;
      {
        std::unique_lock<std::mutex> lock(task_mutex_);
        task_cv_.wait(lock, [this] {
          return !task_queue_.empty() || stop_flag_;
        });

        if (stop_flag_ && task_queue_.empty()) return;

        task = std::move(task_queue_.front());
        task_queue_.pop();
      }
      task();
    }
  }

  // 类型错误处理
  static void handle_type_error(const std::string &topic,
                                const char *expected_type,
                                const char *error_msg) {
    RERROR << "[PubSub Error] Topic '" << topic
           << "' type mismatch. Expected: " << expected_type
           << ", Error: " << error_msg;
  }

  // 成员变量
  std::unordered_map<std::string, std::vector<std::function<void(const std::any &)> > > subscriptions_;
  std::mutex mutex_;

  // 异步任务相关
  std::queue<std::function<void()>> task_queue_;
  std::vector<std::thread> workers_;
  std::mutex task_mutex_;
  std::condition_variable task_cv_;
  std::atomic<bool> stop_flag_{false};
};

}  // namespace common
}  // namespace robosense

#endif  // HYPER_VISION_COMMON_PUB_SUB_TOPIC_SCHEDULER_H_
