/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef HYPER_VISION_COMMON_BASIC_TYPE_BASIC_TYPE_H_
#define HYPER_VISION_COMMON_BASIC_TYPE_BASIC_TYPE_H_

#include "hyper_vision/common/basic_type/DataType.h"
#include "hyper_vision/common/basic_type/header.h"
#include "hyper_vision/common/basic_type/odom.h"
#include "hyper_vision/common/basic_type/triangle_facet.h"
#include "hyper_vision/common/basic_type/point_cloud.h"
#include "hyper_vision/common/basic_type/rgb_image.h"
#include "hyper_vision/common/basic_type/depth_image.h"
#include "hyper_vision/common/basic_type/imu.h"
#include "hyper_vision/common/basic_type/slam_status.h"
#include "hyper_vision/common/basic_type/sensor_data_types.h"

#endif  // HYPER_VISION_COMMON_BASIC_TYPE_BASIC_TYPE_H_
