/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef HYPER_VISION_COMMON_BASIC_TYPE_SLAM_STATUS_H_
#define HYPER_VISION_COMMON_BASIC_TYPE_SLAM_STATUS_H_

#include "hyper_vision/common/basic_type/DataType.h"
#include "hyper_vision/common/basic_type/header.h"
#include "rally/common/common.h"

namespace robosense {
namespace common {

struct SlamStatus {
  using Ptr = std::shared_ptr<SlamStatus>;

  Header header;

  bool is_valid = false;
  bool is_src_lidar = false;         // 是否为Lidar 更新
  bool is_src_image = false;         // 是否为Image 更新
  bool is_degenerate = false;        // 是否退化
  double degenerate_score = 0;       // 退化分数
  bool is_image_degenerate = false;  // 图像定位是否退化
  double image_degenerate_score = 0; // 图像定位退化分数
  bool is_enable_relocalization = false; 
  int cur_map_id = -1;               // 地图分段id

  void reset() {
    is_valid = false;
    is_src_lidar = false;
    is_src_image = false;
    is_degenerate = false;
    degenerate_score = 0;
    is_image_degenerate = false;
    image_degenerate_score = 0;
    is_enable_relocalization = false; 
    cur_map_id = -1;
  }

  TagSlamStatus ToOutSlamStatus() {
    TagSlamStatus slamStatus;
    slamStatus.is_valid = is_valid;
    slamStatus.is_src_lidar = is_src_lidar;
    slamStatus.is_src_image = is_src_image;
    slamStatus.is_degenerate = is_degenerate;
    slamStatus.degenerate_score = degenerate_score;
    slamStatus.is_image_degenerate = is_image_degenerate;
    slamStatus.image_degenerate_score = image_degenerate_score;
    slamStatus.is_enable_relocalization = is_enable_relocalization; 
    slamStatus.cur_map_id = cur_map_id;

    return slamStatus;
  }
};

} // namespace common
} // namespace robosense

#endif // HYPER_VISION_COMMON_BASIC_TYPE_ODOM_H_
