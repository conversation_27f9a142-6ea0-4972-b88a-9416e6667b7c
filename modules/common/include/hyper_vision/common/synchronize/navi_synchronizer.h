/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef HYPER_VISION_COMMON_SYNCHRONIZE_NAVI_SYNCHRONIZER_H_
#define HYPER_VISION_COMMON_SYNCHRONIZE_NAVI_SYNCHRONIZER_H_

#include <atomic>
#include "rally/utils/utils.h"

namespace robosense {
namespace common {

/*
 * 一种多路数据同步方案，三种条件下可立刻触发
 * 1. 多路数据都到齐之后，触发
 * 2. 距离上一次触发之后一段时间内，触发
 * 3. 一个主传感器，来了就触发
 */
struct NaviSynOptions {
  struct FrameInfo {
    std::string frame_id;
    uint32_t queue_size{10};
  };
  std::vector<FrameInfo> frame_info_vec;
  double time_out{0.1}; // 默认100ms触发一次
  double min_time_interval{std::numeric_limits<double>::max()}; // 同步之后的数据，最大时间差
};

class NaviSynchronizer {
 public:
  using Ptr = std::shared_ptr<NaviSynchronizer>;

  NaviSynchronizer(const NaviSynOptions& options);

  ~NaviSynchronizer() {
    running_ = false;
    if (sync_thread_.joinable()) {
      sync_thread_.join();
    }
  }

  void AddData(const rally::AnySensor::Ptr& any_sensor_ptr);

  void AddMainData(const rally::AnySensor::Ptr& any_sensor_ptr);

  void regSynCallback(const std::function<void(const std::vector<rally::AnySensor::Ptr> &msg_ptr_vec)> &cb);

 private:
  std::string name() { return "NaviSynchronizer"; }

  void Core();

  double NanoToSec(uint64_t nano_t) { return static_cast<double>(nano_t) / 1000000000UL; }

  NaviSynOptions options_;
  std::function<void(const std::vector<rally::AnySensor::Ptr> &msg_ptr_vec)> syn_cb_;

  std::map<std::string, std::deque<rally::AnySensor::Ptr> > sensor_msg_map_;
  std::map<std::string, uint32_t> sensor_msg_max_size_;
  std::map<std::string, std::atomic_bool> ready_flags_map_;
  std::mutex mutex_;
  std::condition_variable condition_;
  std::thread sync_thread_;
  std::atomic_bool running_;
  std::atomic_bool all_ready_;
  std::atomic_bool main_data_ready_;
  std::chrono::milliseconds timeout_;
};

}  // namespace common
}  // namespace robosense

#endif  // HYPER_VISION_COMMON_SYNCHRONIZE_NAVI_SYNCHRONIZER_H_
