/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef HYPER_VISION_COMMON_SYNCHRONIZE_FAST_SYNCHRONIZER_H_
#define HYPER_VISION_COMMON_SYNCHRONIZE_FAST_SYNCHRONIZER_H_

#include <atomic>
#include "rally/utils/utils.h"

namespace robosense {
namespace common {

/*
 * 一种多路数据同步方案，三种条件下可立刻触发
 * 1. 多路数据都到齐之后，触发
 * 2. 不满足第一个条件，但是距离上一次触发超过一段时间且buffer中有某一路或多路数据到了，触发
 * 3. 一个主传感器，来了就触发
 */
struct FastSynOptions {
  struct FrameInfo {
    std::string frame_id;
    uint32_t queue_size{10};
  };
  std::vector<FrameInfo> frame_info_vec;
  double time_out{0.1}; // 默认100ms触发一次
  double min_time_interval{std::numeric_limits<double>::max()}; // 同步之后的数据，最大时间差
};

class FastSynchronizer {
 public:
  using Ptr = std::shared_ptr<FastSynchronizer>;

  FastSynchronizer(const FastSynOptions& options) {
    options_ = options;
    RENSURE(!options_.frame_info_vec.empty());
    if (options_.time_out <= 0.) {
      RWARN << name() << ": time_out is illegal and set as default value as 0.1s";
      options_.time_out = 0.1;
    }
    if (options_.min_time_interval <= 0.) {
      RWARN << name() << ": min_time_interval is less than zero " << std::to_string(options_.min_time_interval);
    }

    for (size_t i = 0; i < options_.frame_info_vec.size(); ++i) {
      const auto& frame_id = options_.frame_info_vec[i].frame_id;
      const auto& max_size = options_.frame_info_vec[i].queue_size;
      std::deque<rally::AnySensor::Ptr> tmp_msg_deque;
      sensor_msg_map_[frame_id] = tmp_msg_deque;
      sensor_msg_max_size_[frame_id] = max_size;
      ready_flags_map_[frame_id] = false;
    }

    last_trigger_time_ = rally::getNowInSeconds();
    RINFO << name() << ": init succeed!";
  }

  ~FastSynchronizer() {}

  void AddData(const rally::AnySensor::Ptr& any_sensor_ptr) {
    std::unique_lock<std::mutex> lock(mutex_);

    // check
    const auto& frame_id = any_sensor_ptr->frame_id;
    if (sensor_msg_map_.find(frame_id) == sensor_msg_map_.end()) {
      RWARN << name() << ": receive unknown frame_id as " << frame_id;
      return;
    }

    // set
    sensor_msg_map_.at(frame_id).emplace_back(any_sensor_ptr);
    while (sensor_msg_map_.at(frame_id).size() > sensor_msg_max_size_.at(frame_id)) {
      sensor_msg_map_.at(frame_id).pop_front();
    }
    ready_flags_map_.at(frame_id) = true;

    // 条件 1：任意一路数据到来，并且距离上次触发已经过去了time_out
    if (rally::getNowInSeconds() - last_trigger_time_ > options_.time_out) {
      Trigger("Condition 1: Time interval reached");
      return;
    }

    // 条件 2：所有路数据都到达
    bool all_ready = true;
    for (auto itr = ready_flags_map_.begin(); itr != ready_flags_map_.end(); ++itr) {
      if (!itr->second) {
        all_ready = false;
        break;
      }
    }
    if (all_ready) {
      Trigger("Condition 2: All data received");
      for (auto& flag : ready_flags_map_) {
        flag.second = false; // 重置标志
      }
    }
  }

  void AddMainData(const rally::AnySensor::Ptr& any_sensor_ptr) {
    std::unique_lock<std::mutex> lock(mutex_);

    // check
    const auto& frame_id = any_sensor_ptr->frame_id;
    if (sensor_msg_map_.find(frame_id) == sensor_msg_map_.end()) {
      RWARN << name() << ": receive unknown frame_id as " << frame_id;
      return;
    }

    sensor_msg_map_.at(frame_id).push_back(any_sensor_ptr);

    Trigger("Condition 3: Main sensor received");
  }

  void regSynCallback(const std::function<void(const std::vector<rally::AnySensor::Ptr> &msg_ptr_vec)> &cb) {
    std::unique_lock<std::mutex> lock(mutex_);
    syn_cb_ = cb;
  }

 private:
  std::string name() { return "FastSynchronizer"; }

  void Trigger(const std::string& condition) {
    RINFO << name() << ": Triggered by " << condition;
    std::vector<rally::AnySensor::Ptr> res_vec;
    for (auto itr = sensor_msg_map_.begin(); itr != sensor_msg_map_.end(); ++itr) {
      if (itr->second.empty()) {
        continue;
      }
      res_vec.emplace_back(itr->second.back());
      itr->second.clear();
      ready_flags_map_.at(itr->first) = false;
    }

    if (syn_cb_) {
      syn_cb_(res_vec);
    }
    last_trigger_time_ = rally::getNowInSeconds();
  }

  FastSynOptions options_;
  std::function<void(const std::vector<rally::AnySensor::Ptr> &msg_ptr_vec)> syn_cb_;
  double last_trigger_time_{0.};

  std::map<std::string, std::deque<rally::AnySensor::Ptr> > sensor_msg_map_;
  std::map<std::string, uint32_t> sensor_msg_max_size_;
  std::map<std::string, std::atomic_bool> ready_flags_map_;
  std::mutex mutex_;
};

}  // namespace common
}  // namespace robosense

#endif  // HYPER_VISION_COMMON_SYNCHRONIZE_FAST_SYNCHRONIZER_H_
