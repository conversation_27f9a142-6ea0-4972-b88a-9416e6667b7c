#========================
# libs
#========================

set(CMAKE_POSITION_INDEPENDENT_CODE ON)

set(SUBMODULE_NAME "FAST-LIVO")
set(CUR_SRCS ${SUBMODULE_NAME})
set(CUR_INCLUDES "${SUBMODULE_NAME}/include")
LIST(APPEND CUR_INCLUDES interface)
LIST(APPEND CUR_INCLUDES vikit_common/include)

set(CUR_SUB_DIR "")
LIST(APPEND CUR_SUB_DIR ${SUBMODULE_NAME}/include)
LIST(APPEND CUR_SUB_DIR interface)
LIST(APPEND CUR_SUB_DIR vikit_common)
LIST(APPEND CUR_SUB_DIR ${SUBMODULE_NAME}/src)

foreach (dir ${CUR_SUB_DIR})
    file(GLOB_RECURSE tmp_srcs ${dir}/*.cpp ${dir}/*.h)
    list(APPEND CUR_SRCS ${tmp_srcs})
endforeach ()

add_library(${CUR_LIB} STATIC
        ${CUR_SRCS}
        )

if(APPLE)
    target_include_directories(${CUR_LIB}
            PUBLIC
            ${CUR_INCLUDES}
            ${EIGEN3_INCLUDE_DIR}
            ${CERES_INCLUDE_DIR}
            ${SOPHUS_INCLUDE_DIR}
            ${PCL_INCLUDE_DIRS}
            ${OMP_INCLUDE_DIR}
            # ${GTSAM_INCLUDE_DIR}
            )
    target_link_libraries(${CUR_LIB}
            PUBLIC
            common
            rally_utils
            ${SOPHUS_LIBRARY}
            ${CERES_LIBRARY}
            ${PCL_LIBRARIES}
            ${OMP_LIBRARY}
            # ${GTSAM_LIBRARIES}
            )
else()
    target_include_directories(${CUR_LIB}
            PUBLIC
            ${CUR_INCLUDES}
            ${EIGEN3_INCLUDE_DIR}
            ${CERES_INCLUDE_DIR}
            ${SOPHUS_INCLUDE_DIR}
            ${PCL_INCLUDE_DIRS}
            )
    target_link_libraries(${CUR_LIB}
            PUBLIC
            common
            rally_utils
            ${SOPHUS_LIBRARY}
            ${CERES_LIBRARY}
            ${PCL_LIBRARIES}
            )
    if(ENABLE_BACK_END)    
        message(ENABLE_BACK_END=${ENABLE_BACK_END})
        target_include_directories(${CUR_LIB} 
            PUBLIC
            ${GTSAM_INCLUDE_DIR})
        target_link_libraries(${CUR_LIB}
            PUBLIC
            ${GTSAM_LIBRARIES}
            Boost::serialization
            Boost::system
            Boost::filesystem
            Boost::thread
            Boost::date_time
            Boost::regex
            Boost::timer
            Boost::chrono
            tbb
            tbbmalloc
        )
        add_definitions(-DBACK_END=ON)
    endif()
endif()

if(WIN32)
    find_package(Threads REQUIRED)
    target_link_libraries(${CUR_LIB} PRIVATE Threads::Threads)
    target_compile_options(${CUR_LIB} PRIVATE "/bigobj")
    target_compile_definitions(${CUR_LIB} PRIVATE MODULE_NAME="slam" PCL_NO_PRECOMPILE)
else()
    target_compile_definitions(${CUR_LIB} PRIVATE MODULE_NAME="slam")
endif()

target_compile_definitions(${CUR_LIB} PRIVATE APP_MODE)
