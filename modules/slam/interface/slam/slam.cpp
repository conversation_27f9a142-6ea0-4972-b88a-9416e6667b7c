/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "slam/slam.h"
#include <filesystem>

namespace robosense {
namespace slam {

void Slam::Init(const YAML::Node& cfg_node)
{
  if (!cfg_node["slam"])
    throw std::runtime_error("SLAM section not found in YAML configuration file.");

  if (cfg_node["slam"]["config_root_path"])
  {
    config_path_ = cfg_node["slam"]["config_root_path"].as<std::string>();
    RINFO << name() << "slam config root path: " << config_path_;
  }
  else
  {
    throw std::runtime_error("SLAM config_root_path not found in YAML configuration file.");
  }

  initPreprocess();

  initSlam();

  initRelocation();

  registerRelocationCallback();
}

void Slam::AddData(
    const std::shared_ptr<robosense::common::MotionFrame> &msg_ptr) {
  uint64_t nanosecond = timevalToNanoseconds(msg_ptr->capture_time);
  slam::Imu::Ptr imu_msg_ptr(new slam::Imu);
  imu_msg_ptr->header.timestamp = nanosecond;
  imu_msg_ptr->linear_acceleration.x = msg_ptr->accel.x;
  imu_msg_ptr->linear_acceleration.y = msg_ptr->accel.y;
  imu_msg_ptr->linear_acceleration.z = msg_ptr->accel.z;
  imu_msg_ptr->angular_velocity.x = msg_ptr->gyro.x;
  imu_msg_ptr->angular_velocity.y = msg_ptr->gyro.y;
  imu_msg_ptr->angular_velocity.z = msg_ptr->gyro.z;

  std::lock_guard<std::mutex> lg(slam_ptr_mtx_);
  slam_ptr_->AddData(imu_msg_ptr);
}

void Slam::AddData(
    const std::shared_ptr<robosense::common::DepthFrame> &msg_ptr) {
  // 重新启动
  if (need_restart_.load()) {
    restartSlam();
    need_restart_.store(false);
    detected_restart_signal_.store(true);
  }

  uint64_t nanosecond = timevalToNanoseconds(msg_ptr->capture_time);
  rally::Time time(nanosecond);
  pcl::PointCloud<robosense::Point>::Ptr cloud_msg_ptr(
      new pcl::PointCloud<robosense::Point>);
  cloud_msg_ptr->height = 1;
  cloud_msg_ptr->width = msg_ptr->point_nums;
  cloud_msg_ptr->points.resize(msg_ptr->point_nums);
  for (size_t i = 0; i < cloud_msg_ptr->points.size(); ++i) {
    const auto &in_pt = msg_ptr->points.get()[i];
    auto &out_pt = cloud_msg_ptr->points[i];
    out_pt.x = in_pt.x;
    out_pt.y = in_pt.y;
    out_pt.z = in_pt.z;
    out_pt.intensity = in_pt.intensity;
    out_pt.timestamp = in_pt.timestamp;
    out_pt.ring = in_pt.ring;
  }
  preprocess_ptr_->velodyne_handler(*cloud_msg_ptr, time.toSecond());

  std::lock_guard<std::mutex> lg(slam_ptr_mtx_);
  slam_ptr_->AddData(preprocess_ptr_->pcl_out, time.toSecond());
}

void Slam::AddData(
    const std::shared_ptr<robosense::common::ImageFrame> &msg_ptr) {
  uint64_t nanosecond = timevalToNanoseconds(msg_ptr->capture_time);
  rally::Time time(nanosecond);
  auto tmp_mat = cv::Mat(cv::Size(msg_ptr->width, msg_ptr->height), CV_8UC3,
                         msg_ptr->data.get());
  std::shared_ptr<cv::Mat> img_msg_ptr(new cv::Mat);
  *img_msg_ptr = tmp_mat.clone();
  cv::cvtColor(*img_msg_ptr, *img_msg_ptr, cv::COLOR_RGB2BGR);

  std::lock_guard<std::mutex> lg(slam_ptr_mtx_);
  slam_ptr_->AddData(img_msg_ptr, time.toSecond());
}

void Slam::SetCallback(
    const std::function<void(const SlamOutputMsg::Ptr &msg_ptr)> &callback) {
  output_msg_callback_ = callback;
  registerSlamCallback();
}

int Slam::initSlam() {
  std::lock_guard<std::mutex> lg(slam_ptr_mtx_);
  slam_ptr_.reset(new RsSlam());
  slam_ptr_->Init(config_path_);
  return 0;
}

int Slam::startSlam() {
  std::lock_guard<std::mutex> lg(slam_ptr_mtx_);
  if (slam_ptr_) {
    slam_ptr_->Start();
  }
  return 0;
}

int Slam::stopSlam() {
  std::lock_guard<std::mutex> lg(slam_ptr_mtx_);
  if (slam_ptr_) {
    slam_ptr_->Stop();
  }
  return 0;
}

int Slam::initPreprocess() {
  preprocess_ptr_.reset(new Preprocess(config_path_));
  return 0;
}

int Slam::initRelocation() {
  std::filesystem::path configPath = std::filesystem::path(config_path_);
  std::filesystem::path metaFilePath = configPath / RS_META_YAML_FILE_NAME;
  YAML::Node cfg_node;
  try {
    cfg_node = YAML::LoadFile(metaFilePath.string());
  } catch (...) {
    RERROR << name()
           << ": Relocation Load Yaml Failed: " << metaFilePath.string();
    return -1;
  }

  relocalization::InExtrinsic in_extrinsic;
  Eigen::Matrix4d Tcl = slam_ptr_->T_C_L;
  Eigen::Matrix4d Til = slam_ptr_->T_I_L;
  Eigen::Matrix4d Tlc = Eigen::Matrix4d::Identity();
  Tlc.block<3, 3>(0, 0) = Tcl.block<3, 3>(0, 0).transpose();
  Tlc.block<3, 1>(0, 3) =
      Tcl.block<3, 3>(0, 0).transpose() * (-Tcl.block<3, 1>(0, 3));
  in_extrinsic.Ril_ = Til.block<3, 3>(0, 0);
  in_extrinsic.til_ = Til.block<3, 1>(0, 3);
  in_extrinsic.Ric_ = in_extrinsic.Ril_ * Tlc.block<3, 3>(0, 0);
  in_extrinsic.tic_ =
      in_extrinsic.Ril_ * Tlc.block<3, 1>(0, 3) + in_extrinsic.til_;

  double img_scaling_ratio = slam_ptr_->img_scaling_ratio;
  in_extrinsic.fx_ = img_scaling_ratio * slam_ptr_->cam_fx;
  in_extrinsic.fy_ = img_scaling_ratio * slam_ptr_->cam_fy;
  in_extrinsic.cx_ = img_scaling_ratio * slam_ptr_->cam_cx;
  in_extrinsic.cy_ = img_scaling_ratio * slam_ptr_->cam_cy;

  relocalization::ReConfig config;
  yamlRead(cfg_node["relocalization"]["auto_restart"], "lidar_en", config.lidar_en_);
  yamlRead(cfg_node["relocalization"]["auto_restart"], "vision_en", config.vision_en_);
  yamlRead(cfg_node["relocalization"]["auto_restart"], "lidar_save_en", config.lidar_save_en_);
  yamlRead(cfg_node["relocalization"]["auto_restart"], "lidar_rgb_save_en", config.lidar_rgb_save_en_);
  yamlRead(cfg_node["relocalization"]["auto_restart"], "vision_save_en", config.vision_save_en_);
  yamlRead(cfg_node["relocalization"]["auto_restart"], "max_kf_num", config.max_kf_num_);
  yamlRead(cfg_node["relocalization"]["auto_restart"], "plane_k", config.plane_k_);
  yamlRead(cfg_node["relocalization"]["auto_restart"], "inliner_dist1", config.inliner_dist1_);
  yamlRead(cfg_node["relocalization"]["auto_restart"], "inliner_dist2", config.inliner_dist2_);
  yamlRead(cfg_node["relocalization"]["auto_restart"], "inliner_epipolar", config.inliner_epipolar_);
  yamlRead(cfg_node["relocalization"]["auto_restart"], "damping_ratio", config.damping_ratio_);
  yamlRead(cfg_node["relocalization"]["auto_restart"], "kf_dist", config.kf_dist_thres_);
  yamlRead(cfg_node["relocalization"]["auto_restart"], "kf_angle", config.kf_angle_thres_);
  yamlRead(cfg_node["relocalization"]["auto_restart"], "kf_parallax", config.kf_parallax_thres_);
  yamlRead(cfg_node["relocalization"]["auto_restart"], "kf_track_ratio", config.kf_track_ratio_thres_);
  yamlRead(cfg_node["relocalization"]["auto_restart"], "img_scale", config.img_scale_);
  yamlRead(cfg_node["relocalization"]["auto_restart"], "feature_num", config.feature_num_);
  yamlRead(cfg_node["relocalization"]["auto_restart"], "pyramid_level", config.pyramid_level_);
  yamlRead(cfg_node["relocalization"]["auto_restart"], "window_size", config.window_size_);
  yamlRead(cfg_node["relocalization"]["auto_restart"], "min_dist", config.min_dist_);
  yamlRead(cfg_node["relocalization"]["auto_restart"], "max_layer", config.max_layer_);
  yamlRead(cfg_node["relocalization"]["auto_restart"], "f_ransac", config.f_ransac_);
  yamlRead(cfg_node["relocalization"], "img_enhance", config.img_enhance_);
  yamlRead(cfg_node["relocalization"], "clip_limit", config.clip_limit_);
  yamlRead(cfg_node["relocalization"], "size", config.size_);

  // 使用 /tmp 目录临时保存地图文件
  std::filesystem::path mapPath =
      std::filesystem::temp_directory_path() / std::string("map");
  if (std::filesystem::exists(mapPath)) {
    try {
      bool isSuccess = std::filesystem::remove_all(mapPath);
      if (isSuccess) {
        RINFO << name() << ": Remove Map Directory: " << mapPath.string()
              << " Successed !";

      } else {
        RERROR << name() << ": (A)Remove Map Directory: " << mapPath.string()
               << " Failed !";
        return -2;
      }
    } catch (...) {
      RERROR << name() << ": (B)Remove Map Directory: " << mapPath.string()
             << " Failed !";
      return -3;
    }
  }
  try {
    bool isSuccess = std::filesystem::create_directories(mapPath);
    if (isSuccess) {
      RINFO << name() << ": Create Map Directory: " << mapPath.string()
            << " Successed !";
    } else {
      RERROR << name() << ": (A) Create Map Directory: " << mapPath.string()
             << " Failed !";
      return -4;
    }
  } catch (...) {
    RERROR << name() << ": (B) Create Map Directory: " << mapPath.string()
           << " Failed !";
    return -5;
  }

  // 重置参数
  detected_restart_signal_.store(true);
  need_restart_.store(false);

  try {
    relocalization_ptr_.reset(new relocalization::ReLocalization(
        mapPath.string(), in_extrinsic, config));
  } catch (...) {
    RERROR << name() << ": Malloc Relocation Failed !";
    return -6;
  }

  return 0;
}

void Slam::slamLIVOResultCallback(const LIVOResult &livo_res)
{
  const auto &init_process = livo_res.imu_process_result->init_process;
  if (init_process < 100)
  {
    RINFO << name() << "Initializing " << init_process;
    return;
  }

  SlamOutputMsg::Ptr msg_ptr(new SlamOutputMsg);

  if (livo_res.source == robosense::slam::SensorType::LIDAR)
  {
    // 雷达定位
    RINFO << name() << "LIO ts: " << std::to_string(livo_res.update_ts);
    msg_ptr->header.timestamp = static_cast<uint64_t>(livo_res.update_ts * 1000000000UL);

    double timer = rally::getNowInSeconds();

    const auto &pose = livo_res.lio_result->lidar_pose;
    auto &out_pose = msg_ptr->odom;
    out_pose.header.timestamp = static_cast<uint64_t>(livo_res.update_ts * 1000000000UL);

    out_pose.odom_data.pose_x = pose.xyz.x();
    out_pose.odom_data.pose_y = pose.xyz.y();
    out_pose.odom_data.pose_z = pose.xyz.z();

    out_pose.odom_data.orientation_x = pose.q.x();
    out_pose.odom_data.orientation_y = pose.q.y();
    out_pose.odom_data.orientation_z = pose.q.z();
    out_pose.odom_data.orientation_w = pose.q.w();

    const auto &map = livo_res.lio_result->rgb_scan;
    auto &out_map = msg_ptr->slam_point_cloud;
    out_map.points_vec.resize(map->size());
    for (size_t i = 0; i < map->size(); ++i)
    {
      out_map.points_vec[i].x = map->points[i].x;
      out_map.points_vec[i].y = map->points[i].y;
      out_map.points_vec[i].z = map->points[i].z;
      out_map.points_vec[i].r = map->points[i].r;
      out_map.points_vec[i].g = map->points[i].g;
      out_map.points_vec[i].b = map->points[i].b;
    }

    const auto &scan = livo_res.lio_result->rgb_scan;
    auto &out_scan = msg_ptr->point_cloud;
    out_scan.points_vec.resize(scan->size());
    for (size_t i = 0; i < scan->size(); ++i)
    {
      out_scan.points_vec[i].x = scan->points[i].x;
      out_scan.points_vec[i].y = scan->points[i].y;
      out_scan.points_vec[i].z = scan->points[i].z;
      out_scan.points_vec[i].r = scan->points[i].r;
      out_scan.points_vec[i].g = scan->points[i].g;
      out_scan.points_vec[i].b = scan->points[i].b;
    }

    const auto &image = livo_res.vio_result->img_all_cloud;
    auto &out_image = msg_ptr->rgb_image;
    out_image.header.timestamp =
        static_cast<uint64_t>(livo_res.update_ts * 1000000000UL);
    out_image.width = image.cols;
    out_image.height = image.rows;
    out_image.data_vec.resize(image.cols * image.rows * 3);
    for (int row = 0; row < image.rows; ++row) {
      for (int col = 0; col < image.cols; ++col) {
        auto index = row * image.cols + col;
        out_image.data_vec[index * 3 + 0] = image.at<cv::Vec3b>(row, col)[2];
        out_image.data_vec[index * 3 + 1] = image.at<cv::Vec3b>(row, col)[1];
        out_image.data_vec[index * 3 + 2] = image.at<cv::Vec3b>(row, col)[0];
      }
    }

    // 更新定位状态
    {
      std::lock_guard<std::mutex> lg(slam_status_mtx_);
      slam_status_.is_valid = true;
      slam_status_.is_src_lidar = true;
      slam_status_.is_src_image = false;
      slam_status_.is_degenerate = livo_res.lio_result->is_degenerate;
      slam_status_.degenerate_score = livo_res.lio_result->degenerate_score;
      slam_status_.cur_map_id = cur_map_id_.load();
      slam_status_.is_enable_relocalization = enable_relocalization_.load();

      msg_ptr->slam_status = slam_status_;
    }

    // 更新Relocation
    if (relocalization_ptr_ && enable_relocalization_.load()) {
      const int valid_num = livo_res.lio_result->log->valid_size;
      const double residual_mean = livo_res.lio_result->log->res_mean_last;
      double timestamp = pose.timestamp;
      Eigen::Quaterniond qwi = pose.q;
      Eigen::Vector3d twi = pose.xyz;
      auto cloud_ptr = livo_res.lio_result->scan;
      int downsample_cloud_size =
          livo_res.lio_result->log->downsample_cloud_size;
      if (livo_res.is_ready && livo_res.lio_result->log->flg_EKF_inited) {
        relocalization_ptr_->addFrame(timestamp, qwi, twi, valid_num,
                                      residual_mean, downsample_cloud_size,
                                      cloud_ptr);
      }
      if (detected_restart_signal_) {
        bool tmp = relocalization_ptr_->need_reset_;
        if (tmp) {
          detected_restart_signal_ = false;
          resetCurMapId();
          RINFO << name() << ": LIDAR Source Relocation !";
        }
        need_restart_ = tmp;
      }
    }

    if (output_msg_callback_)
    {
      output_msg_callback_(msg_ptr);
      RINFO << name() << "Successfully published slam message. time consumption: " << rally::getNowInSeconds() - timer << " s";
    }
  } else if (livo_res.source ==
             robosense::slam::SensorType::CAMERA) { // 图像定位
    // 更新定位状态
    {
      std::lock_guard<std::mutex> lg(slam_status_mtx_);
      slam_status_.is_image_degenerate =
          livo_res.vio_result->log->lidar_selector_result->is_degenerate;
      slam_status_.image_degenerate_score =
          livo_res.vio_result->log->lidar_selector_result->degenerate_score;
    }

    // 更新Relocation
    if (relocalization_ptr_ && enable_relocalization_.load()) {
      Pose cam_pose = livo_res.vio_result->cam_pose;

      const int valid_num = livo_res.vio_result->log->valid_size;
      const double residual_mean = livo_res.vio_result->log->res_mean_last;
      double timestamp = cam_pose.timestamp;
      Eigen::Quaterniond qwi = cam_pose.q;
      Eigen::Vector3d twi = cam_pose.xyz;
      cv::Mat raw_img = livo_res.vio_result->img_raw;
      cv::Mat gray_img = livo_res.vio_result->img_gray;
      if (livo_res.is_ready && livo_res.vio_result->log->vio_valid) {
        relocalization_ptr_->addFrame(livo_res.lio_result->lidar_pose.timestamp,
                                      livo_res.lio_result->rgb_scan);
        relocalization_ptr_->addFrame(timestamp, qwi, twi, valid_num,
                                      residual_mean, valid_num, raw_img,
                                      gray_img);
      }

      if (detected_restart_signal_) {
        bool tmp = relocalization_ptr_->need_reset_;
        if (tmp) {
          detected_restart_signal_ = false;
          resetCurMapId();
          RINFO << name() << ": IMAGE Source Relocation !";
        }
        need_restart_ = tmp;
      }
    }
  }
}

void Slam::registerSlamCallback()
{
  const auto &callback =
      std::bind(&Slam::slamLIVOResultCallback, this, std::placeholders::_1);

  std::lock_guard<std::mutex> lg(slam_ptr_mtx_);
  slam_ptr_->RegisterGetLIVOResult(callback);
}

void Slam::relocationCallback(const relocalization::RestartInfo &restart_info) {
  int32_t cur_map_id = restart_info.cur_map_id_;
  if (cur_map_id < 0) {
    RERROR << "Invalid cur_map_id = " << cur_map_id;
  }
  cur_map_id_.store(cur_map_id);
}

void Slam::registerRelocationCallback() {
  const auto &callback =
      std::bind(&Slam::relocationCallback, this, std::placeholders::_1);
  if (relocalization_ptr_) {
    relocalization_ptr_->registerInfoCallback(callback);
  }
}

void Slam::resetSlamStatus() {
  std::lock_guard<std::mutex> lg(slam_status_mtx_);
  slam_status_.reset();
}

void Slam::resetCurMapId() { cur_map_id_ = -1; }

void Slam::restartSlam() {
  RINFO << name() << ": Restart Slam !";
  initSlam();

  registerSlamCallback();

  startSlam();

  if (relocalization_ptr_) {
    relocalization_ptr_->startNewMap();
  }
}

} // namespace slam
} // namespace robosense
