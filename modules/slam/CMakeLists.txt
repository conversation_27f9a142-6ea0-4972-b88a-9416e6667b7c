cmake_minimum_required(VERSION 3.5)

#========================
# Project
#========================

set(CUR_LIB slam)
project(${CUR_LIB})

set(CMAKE_BUILD_TYPE RelWithDebInfo)
add_compile_options("$<$<C_COMPILER_ID:MSVC>:/utf-8>")
add_compile_options("$<$<CXX_COMPILER_ID:MSVC>:/utf-8>")

add_compile_options(-fvisibility=hidden)
set(CMAKE_SHARED_LINKER_FLAGS "${CMAKE_SHARED_LINKER_FLAGS} -Wl,--version-script=version.script")

IF (WIN32)
    ADD_COMPILE_OPTIONS(/W4 /std:c++17 /EHsc )
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /std:c++14 /O2")
    # 关键配置：指定vcpkg工具链路径
    set(CMAKE_TOOLCHAIN_FILE "$ENV{VCPKG_ROOT}/scripts/buildsystems/vcpkg.cmake")
ELSE()
    ADD_COMPILE_OPTIONS(-std=c++17)
    set(CMAKE_CXX_FLAGS "-std=c++17 -O3 -fexceptions")
ENDIF()

add_definitions(-DNOMINMAX)  # 所有目标生效
add_compile_options(-std=c++17)
add_definitions(-DDEBUG_LEVEL=0)

include(cmake/srcs.cmake)

set(${CUR_LIB} PARENT_SCOPE)


