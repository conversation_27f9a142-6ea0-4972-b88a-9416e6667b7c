import os
import re
import sys

def replace_keywords_in_file(file_path, keyword_dict, whole_word, case_sensitive, extensions):
    """
    替换文件中的指定关键字
    :param file_path: 文件路径
    :param keyword_dict: 包含旧关键字和新关键字的字典
    :param whole_word: 是否全字匹配
    :param case_sensitive: 是否区分大小写
    :param extensions: 允许处理的文件扩展名列表
    """
    try:
        # 检查文件扩展名是否在允许列表中
        if not any(file_path.endswith(ext) for ext in extensions):
            return

        # 打开文件并读取内容
        with open(file_path, 'r', encoding='utf-8') as file:
            lines = file.readlines()

        # 替换关键字并记录替换位置
        replaced_lines = []
        for old_keyword, new_keyword in keyword_dict.items():
            # 构造正则表达式
            if whole_word:
                pattern = r'\b' + re.escape(old_keyword) + r'\b'
            else:
                pattern = re.escape(old_keyword)

            # 根据是否区分大小写设置正则标志
            flags = 0 if case_sensitive else re.IGNORECASE

            for i, line in enumerate(lines, start=1):
                new_line, count = re.subn(pattern, new_keyword, line, flags=flags)
                if count > 0:
                    replaced_lines.append((i, old_keyword, new_keyword))
                lines[i - 1] = new_line

        # 如果内容有变化，则写回文件
        if replaced_lines:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.writelines(lines)
            print(f"已替换文件 {file_path} 中的关键字：")
            for line_num, old_keyword, new_keyword in replaced_lines:
                print(f"  行 {line_num}: 替换 '{old_keyword}' 为 '{new_keyword}'")
    except Exception as e:
        print(f"处理文件 {file_path} 时出错：{e}")


def replace_keywords_in_project(project_path, keyword_dict, whole_word, case_sensitive, extensions):
    """
    在工程目录下递归替换指定关键字
    :param project_path: 工程目录路径
    :param keyword_dict: 包含旧关键字和新关键字的字典
    :param whole_word: 是否全字匹配
    :param case_sensitive: 是否区分大小写
    :param extensions: 允许处理的文件扩展名列表
    """
    # 遍历工程目录下的所有文件
    for root, dirs, files in os.walk(project_path):
        for file in files:
            file_path = os.path.join(root, file)
            replace_keywords_in_file(file_path, keyword_dict, whole_word, case_sensitive, extensions)

if __name__ == "__main__":
    # 不采用os.getcwd() 来获取，而是采用传入参数的方法直接使用
    if len(sys.argv) < 2:
        print("--missing param! python replace_project_key_words.py <input_path>")
        sys.exit(1)
    current_path = sys.argv[1] # 根据传参获取目录并判断是否合法
    if not os.path.isdir(current_path):
        print(f"--invalid path given:{current_path}")
    #current_path = os.getcwd()
    whole_word = True
    case_sensitive = True
    extensions = [".cpp", ".hpp", ".h", ".txt", ".launch", ".xml"]

    # 输入新旧关键字对
    keyword_dict = {}
    keyword_dict["FastLivoSlam"] = "RsSlam"
    keyword_dict["fast_lio_is_ready"] = "is_ready"
    keyword_dict["fast_livo"] = "rs_slam_node"
    keyword_dict["FAST_LIVO"] = "slam_node"
    keyword_dict["fast_livo_slam_app"] = "rs_slam_app"
    print(current_path)

    # 地址的分隔符动态拼接
    sub_paths = [
        os.path.join(current_path, "modules", "slam", "FAST-LIVO"),
        os.path.join(current_path, "modules", "slam", "interface", "slam")
    ]

    for project_path in sub_paths:
        print("--replace_project_key_words: processing", project_path)
        replace_keywords_in_project(project_path, keyword_dict, whole_word, case_sensitive, extensions)