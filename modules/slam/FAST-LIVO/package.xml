<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>rs_slam_node</name>
  <version>0.20.4</version>

  <description>Aapts slam_node to Robosense Active Camera</description>
  <maintainer email="<EMAIL>"><PERSON><PERSON></maintainer>
  <maintainer email="<EMAIL>">Silence Song</maintainer>
  <license>GPL-v2</license>

  <buildtool_depend condition="$ROS_VERSION == '1'">catkin</buildtool_depend>
  <buildtool_depend condition="$ROS_VERSION == '2'">ament_cmake</buildtool_depend>

  <build_depend condition="$ROS_VERSION == '1'">roscpp</build_depend>
  <build_depend condition="$ROS_VERSION == '2'">rclcpp</build_depend>

  <build_depend>geometry_msgs</build_depend>
  <build_depend>nav_msgs</build_depend>
  <build_depend>std_msgs</build_depend>
  <build_depend>sensor_msgs</build_depend>
  <build_depend>tf</build_depend>
  <build_depend>pcl_ros</build_depend>
  <build_depend>cv_bridge</build_depend>
  <build_depend>image_transport</build_depend>

  <exec_depend>geometry_msgs</exec_depend>
  <exec_depend>nav_msgs</exec_depend>
  <exec_depend>sensor_msgs</exec_depend>
  <exec_depend>roscpp</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>tf</exec_depend>
  <exec_depend>pcl_ros</exec_depend>
  <exec_depend>message_runtime</exec_depend>
  <exec_depend>cv_bridge</exec_depend>
  <exec_depend>image_transport</exec_depend>
  <test_depend>rostest</test_depend>
  <test_depend>rosbag</test_depend>

  <export>
    <build_type condition="$ROS_VERSION == '2'">ament_cmake</build_type>
  </export>
</package>
