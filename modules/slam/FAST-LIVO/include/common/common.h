#pragma once
#include "utility/yaml_reader.hpp"
#include "utility/math.h"

#define RSRESET "\033[0m"
#define RSBOLDRED "\033[1m\033[31m"     /* Bold Red */
#define RSBOLDGREEN "\033[1m\033[32m"   /* Bold Green */
#define RSBOLDYELLOW "\033[1m\033[33m"  /* Bold Yellow */
#define RSBOLDBLUE "\033[1m\033[34m"  /* Bold blue */
#define RSBOLDMAGENTA "\033[1m\033[35m" /* Bold Magenta */
#define RSBOLDCYAN "\033[1m\033[36m"    /* Bold Cyan */

#ifdef APP_MODE

#include "rally/utils/utils.h"
#include "cyber/glog_wrapper/glog_wrapper.h"

#define LINFO AINFO
#define LWARNING AWARN
#define LERROR AERROR
#define LDEBUG ADEBUG
#define LTITLE AINFO
#define LBLUE AINFO
#define END ""
#define REND ""
#define PRINT log_info

#else

#define LINFO (std::cout << RSBOLDGREEN)
#define LWARNING (std::cout << RSBOLDYELLOW)
#define LERROR (std::cout << RSBOLDRED)
#define LDEBUG (std::cout << RSBOLDCYAN)
#define LTITLE (std::cout << RSBOLDMAGENTA)
#define LBLUE (std::cout << RSBOLDBLUE)
#define END (std::endl)
#define REND "\033[0m" << std::endl
#define PRINT printf

#endif

#define GETBIT(X, Y) ((X) >> (Y)&1)
#define NAME(x) (#x)

using namespace Eigen;

typedef Vector3d V3D;
typedef Vector2d V2D;
typedef Vector2i V2I;
typedef Matrix3d M3D;
typedef Vector3f V3F;
typedef Matrix3f M3F;

#define MD(a,b)  Matrix<double, (a), (b)>
#define VD(a)    Matrix<double, (a), 1>
#define MF(a,b)  Matrix<float, (a), (b)>
#define VF(a)    Matrix<float, (a), 1>

#include "msg/imu_msg.h"
#include "msg/pose_msg.h"
#include "msg/lidar_msg.h"
#include "msg/cloud_msg.h"
#include "utility/cloud_process.h"

using namespace robosense;
