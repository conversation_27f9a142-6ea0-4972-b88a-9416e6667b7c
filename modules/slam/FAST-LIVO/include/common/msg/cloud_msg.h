#pragma once
#include <pcl/point_types.h>
#include <pcl/point_cloud.h>
#include "lidar_msg.h"

typedef pcl::PointXYZINormal PointType;
typedef pcl::PointCloud<PointType> PointCloudXYZI;
typedef PointCloudXYZI::Ptr CloudPtr;
typedef std::vector<PointType, Eigen::aligned_allocator<PointType>>  PointVector;

typedef pcl::PointXYZRGB PointTypeRGB;
typedef pcl::PointCloud<PointTypeRGB> PointCloudXYZRGB;
typedef PointCloudXYZRGB::Ptr RGBCloudPtr;

typedef pcl::PointXYZRGBNormal RGBNPoint;
typedef pcl::PointCloud<RGBNPoint> RGBNCloud;
typedef RGBNCloud::Ptr RGBNCloudPtr;

typedef pcl::PointCloud<robosense::Point> RSCloud;

inline void RGB2RGBN(RGBCloudPtr &in, RGBNCloudPtr &out) {
  out.reset(new RGBNCloud);
  out->resize(in->size());
  int size = in->size();
  for (int i = 0; i < size; ++i) {
    out->points[i].x = in->points[i].x;
    out->points[i].y = in->points[i].y;
    out->points[i].z = in->points[i].z;
    out->points[i].r = in->points[i].r;
    out->points[i].g = in->points[i].g;
    out->points[i].b = in->points[i].b;
  }
}
inline void IN2RGBN(CloudPtr &in, RGBNCloudPtr &out) {
  out.reset(new RGBNCloud);
  out->resize(in->size());
  int size = in->size();
  for (int i = 0; i < size; ++i) {
    out->points[i].x = in->points[i].x;
    out->points[i].y = in->points[i].y;
    out->points[i].z = in->points[i].z;
  }
}