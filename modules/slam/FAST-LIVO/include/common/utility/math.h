/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#pragma once
#include <Eigen/Dense>
#include <iostream>
#define PI 3.141592654f
#define HALF_PI 1.570796327f

inline Eigen::Quaterniond RpyToQuaternion(double roll, double pitch, double yaw) {
  // 创建绕每个轴的旋转向量
  Eigen::AngleAxisd rollAngle(roll, Eigen::Vector3d::UnitX());
  Eigen::AngleAxisd pitchAngle(pitch, Eigen::Vector3d::UnitY());
  Eigen::AngleAxisd yawAngle(yaw, Eigen::Vector3d::UnitZ());

  // 按照 ZYX 顺序组合旋转
  Eigen::Quaterniond quaternion = yawAngle * pitchAngle * rollAngle;
  return quaternion;
}

// max absolute error (degree) compared to std::atan2
// err_pitch_max:0.011628 err_yaw_max:0.011625 for Airy(360*90)
// err_pitch_max:0.004914 err_yaw_max:0.011642 for AC(130*60)
// err_pitch_max:0.010997 err_yaw_max:0.011611 for E1(130*90)

// Time cost(ns) calc by std::chrono::high_resolution_clock::now()
// Time cost is 0.35 times the std::atan2 for pitch calculation, while 0.98 for yaw. Airy
// Time cost is 0.49 times the std::atan2 for pitch calculation, while 0.99 for yaw. AC
inline float FastAtan2(float dy, float dx) {
  if (dx == 0.0f) {
    if (dy > 0.0f) {
      return HALF_PI;
    }
    if (dy < 0.0f) {
      return -HALF_PI;
    }
    if (dy == 0.0f) {
      // std::cout << "undefined\n";
      return std::numeric_limits<float>::quiet_NaN(); // undefined
    }
  }

  float ax = fabs(dx), ay = fabs(dy);
  float temp1 = std::min(ax, ay) / std::max(ax, ay);
  float temp2 = temp1 * temp1;
  float result = ((-0.0464964749 * temp2 + 0.15931422) * temp2 - 0.327622764) *
                     temp2 * temp1 +
                 temp1;

  if (ay > ax) {
    result = (HALF_PI) - result;
  }
  if (dx < 0.0f) {
    result = PI - result;
  }
  if (dy < 0.0f) {
    result = (-result);
  }
  return result;
}