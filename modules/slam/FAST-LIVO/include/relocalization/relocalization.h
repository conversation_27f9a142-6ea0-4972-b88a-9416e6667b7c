#ifndef RELOCALIZATION_H
#define RELOCALIZATION_H

#include <thread>
#include <mutex>
#include <condition_variable>


#include <pcl/filters/voxel_grid.h>

#include "common/utility/yaml_reader.hpp"
#include "relocalization/common.h"

namespace relocalization
{
class ReLocalization
{
public:
  ReLocalization(std::string _root_path, const InExtrinsic &_in_extrinsic, const ReConfig &_config);
  ~ReLocalization();
  void setInExtrinsic(const InExtrinsic &_in_extrinsic);
  void loadConfig(const ReConfig &_config);
  void addFrame(const double _time, Eigen::Quaterniond _qwi, Eigen::Vector3d _twi, int _res_num, double _res_value, int _total_num, CloudPtr _undistorted_world_cloud);
  void addFrame(const double _time, Eigen::Quaterniond _qwi, Eigen::Vector3d _twi, int _res_num, double _res_value, int _total_num, cv::Mat _raw_img, cv::Mat _gray_img);
  void addFrame(const double _time, RGBCloudPtr _rgb_world_cloud);
  void startNewMap();
  int getCurMapId() const;
  void registerInfoCallback(std::function<void(const RestartInfo&)> _restart_info_func);
private:
  std::string root_path_;
  std::queue<CloudPtr> lidar_cloud_queue_;
  std::mutex lidar_cloud_queue_mutex_;
  pcl::VoxelGrid<PointType> voxel_filter_;

  std::thread stm_thread_;
  std::thread mtm_thread_;
  std::thread ltm_thread_;

  mutable std::mutex stm_mutex_;
  std::mutex mtm_mutex_;
  std::mutex ltm_mutex_;

  std::condition_variable stm_available_;
  std::condition_variable mtm_available_;
  std::condition_variable ltm_available_;

  std::atomic_bool stm_alive_{false};
  std::atomic_bool mtm_alive_{false};
  std::atomic_bool ltm_alive_{false};

  void stmLoop();
  void mtmLoop();
  void ltmLoop();
  
  double last_check_time_{-1.};
  const double check_T_{0.3};
  
  bool mustBeBad(const LidarInfo &_info) const;
  bool mayBeBad(const LidarInfo &_info) const;
  bool checkBad(CloudPtr _cloud_ref, CloudPtr _cloud_cur, const int _k, const double _thres1, const double _thres2, const double _ratio1=0.6, const double _ratio2=0.6) const;
  std::pair<double, double> checkBad(std::shared_ptr<VisionInfo> _vision_ref, std::shared_ptr<VisionInfo> _vision_cur) const;
  void endCurMap();

  bool checkFrameBad(std::shared_ptr<MapInfo> _map_ptr, std::shared_ptr<LidarInfo> _frame_ref_ptr, std::shared_ptr<LidarInfo> _frame_cur_ptr);
  bool checkFrameBad(std::shared_ptr<MapInfo> _map_ptr, std::shared_ptr<VisionInfo> _frame_ref_ptr, std::shared_ptr<VisionInfo> _frame_cur_ptr);

  void imgPreProcess(std::shared_ptr<VisionInfo> _vision_frame_ptr, bool _check_en=true);
  bool featureExtract(std::shared_ptr<VisionInfo> _vision_frame_ptr, bool _check_en=true);

  int map_id_ = 0;
  std::atomic<double> map_start_time_{-1.};
  std::atomic<double> map_end_time_{-1.};
  const double map_dead_zone_{1.};

  std::shared_ptr<MapInfo> current_map_;
  std::queue<std::shared_ptr<MapInfo>> map_mtm_cache_;
  std::vector<std::shared_ptr<MapInfo>> map_ltm_cache_;

  InExtrinsic in_extrinsic_;
  ReConfig config_;
  RestartInfo restart_info_;
  cv::Ptr<cv::CLAHE> clahe_;
  double inliner_ratio_{1.};
  std::function<void(const RestartInfo&)> restart_info_func_;
public:
  std::atomic_bool need_reset_{false};

};
} // namespace relocalization
#endif // RELOCALIZATION_H