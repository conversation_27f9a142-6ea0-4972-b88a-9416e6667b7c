#ifndef COMMON_H
#define COMMON_H
#include <queue>
#include <deque>
#include <fstream>
#include <Eigen/Dense>
#include <opencv2/opencv.hpp>

#include "common/msg/cloud_msg.h"
#include "common/common.h"

namespace relocalization
{
std::string getCurTime();
Eigen::Matrix3d vectorToSkewSymmetric(const Eigen::Vector3d& _v);
bool inBorder(cv::Size _img_size, cv::Point2f _pt, const int _border_size=0);
std::pair<double, double> calNormalParam(std::vector<double> _meas, double _ratio);
#define R2D 57.295779513

struct RestartInfo
{
  std::atomic<int> cur_map_id_{-1};
};

struct InExtrinsic
{
  Eigen::Matrix3d Ril_;
  Eigen::Vector3d til_;
  Eigen::Matrix3d Ric_;
  Eigen::Vector3d tic_;
  double fx_;
  double fy_;
  double cx_;
  double cy_;

  void logInfo(void);
};

struct ReConfig
{
  bool lidar_en_{true};
  bool vision_en_{true};
  bool lidar_save_en_{true};
  bool lidar_rgb_save_en_{true};
  bool vision_save_en_{true};
  int max_kf_num_{33};
  int plane_k_{5};
  double inliner_dist1_{0.15};
  double inliner_dist2_{0.1};
  double inliner_epipolar_{1.};
  double damping_ratio_{0.8};
  double kf_dist_thres_{2.};
  double kf_angle_thres_{10.};
  double kf_parallax_thres_{30.};
  double kf_track_ratio_thres_{0.6};

  double img_scale_{1.};
  int feature_num_{150};
  double min_dist_{30.};
  int pyramid_level_{3};
  int window_size_{21};
  int max_layer_{-1};
  double f_ransac_{1.};

  bool img_enhance_{false};
  double clip_limit_;
  int size_;

  void logInfo(void);
};

struct LidarInfo
{
  double timestamp_{-1.};
  Eigen::Vector3d twi_;
  Eigen::Quaterniond qwi_;
  CloudPtr cloud_world_ptr_;
  RGBCloudPtr rgb_world_ptr_;
  int res_num_{-1};
  double res_value_{-1.};
  int total_num_{-1};
  LidarInfo(const double _time, Eigen::Quaterniond _qwi, Eigen::Vector3d _twi, int _res_num, double _res_value, int _total_num, CloudPtr _undistorted_world_cloud)
  : timestamp_(_time), qwi_(_qwi), twi_(_twi), cloud_world_ptr_(_undistorted_world_cloud), res_num_(_res_num), res_value_(_res_value), total_num_(_total_num)
  {
    ;
  }
};

struct VisionInfo
{
  double timestamp_{-1.};
  Eigen::Vector3d twi_;
  Eigen::Quaterniond qwi_;
  cv::Mat raw_img_;
  cv::Mat gray_img_;
  cv::Mat enhance_img_;
  std::vector<cv::Mat> pyramid_img_;
  std::vector<cv::Point2f> corners_;
  int res_num_{-1};
  double res_value_{-1.};
  int total_num_{-1};
  VisionInfo(const double _time, Eigen::Quaterniond _qwi, Eigen::Vector3d _twi, int _res_num, double _res_value, int _total_num, cv::Mat _raw_img, cv::Mat _gray_img)
  : timestamp_(_time), qwi_(_qwi), twi_(_twi), res_num_(_res_num), res_value_(_res_value), total_num_(_total_num), raw_img_(_raw_img), gray_img_(_gray_img)
  {
    ;
  }
};

class MapInfo
{
public:
  int map_id_{-1};
  double start_time_{-1.};
  double end_time_{-1.};
  double end_good_time_{-1.};
  std::atomic<double> newest_frame_time_{-1.};
  std::queue<std::shared_ptr<LidarInfo>> lidar_frame_queue_;
  std::deque<std::shared_ptr<LidarInfo>> lidar_keyframe_list_;
  std::queue<std::shared_ptr<VisionInfo>> vision_frame_queue_;
  std::deque<std::shared_ptr<VisionInfo>> vision_keyframe_list_;
  std::queue<std::pair<double, RGBCloudPtr>> time_rgb_cloud_queue_;
  std::ofstream ofs_lidar_pose_;
  std::ofstream ofs_vision_pose_;
  std::ofstream ofs_log_;
  std::string map_path_;
  std::string lidar_path_;
  std::string vision_path_;
  std::string lidar_rgb_path_;

  MapInfo(const int _map_id, const std::string &_root_path, const InExtrinsic &_in_extrinsic, const ReConfig &_config);
  ~MapInfo();
  void addFrame(std::shared_ptr<LidarInfo> _frame_ptr);
  void addKeyFrame(std::shared_ptr<LidarInfo> _frame_ptr, bool _check_en=true);
  void addFrame(std::shared_ptr<VisionInfo> _frame_ptr);
  void addKeyFrame(std::shared_ptr<VisionInfo> _frame_ptr, bool _check_en=true);
  void addFrame(const double _time, RGBCloudPtr _rgb_world_cloud);
  void updateEndTime(const double _time=-1.);
  void saveFrame(std::shared_ptr<LidarInfo> _frame);
  void saveFrame(std::shared_ptr<VisionInfo> _frame);
  void saveFrame(std::pair<double, RGBCloudPtr> _time_rgb_cloud);
private:
  InExtrinsic in_extrinsic_;
  ReConfig config_;

  static const std::string lidar_pose_file_name_;
  static const std::string vision_pose_file_name_;
  static const std::string map_info_file_name_;
  static const std::string log_file_name_;
};

void featureTrack(std::shared_ptr<VisionInfo> _frame_ref_ptr, std::shared_ptr<VisionInfo> _frame_cur_ptr, const double _window_size, const int _pyramid_level, const double _f_ransac, std::vector<cv::Point2f> &_corners_ref, std::vector<cv::Point2f> &_corners_cur);

} // namespace relocalization
#endif // COMMON_H