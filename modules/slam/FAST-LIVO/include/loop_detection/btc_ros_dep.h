#ifndef ROS_DEP_H
#define ROS_DEP_H
#include <cv_bridge/cv_bridge.h>
#include <nav_msgs/Odometry.h>
#include <nav_msgs/Path.h>
#include <pcl_conversions/pcl_conversions.h>
#include <ros/publisher.h>
#include <ros/ros.h>
#include <sensor_msgs/PointCloud2.h>
#include <visualization_msgs/Marker.h>
#include <visualization_msgs/MarkerArray.h>

#include "btc_struct.h"
#include "btc.h"
#include "common/common.h"

class BTCDebug {
public:
  BTCDebug(const ConfigSetting &_config_setting)
      : config_setting(_config_setting) {
    Init();
  };
  ~BTCDebug() { ; };

  void Init() {
    pubCurrentCloud =
        nh.advertise<sensor_msgs::PointCloud2>("/cloud_current", 100);
    pubCurrentBinary =
        nh.advertise<sensor_msgs::PointCloud2>("/cloud_key_points", 100);
    pubAllBinary =
        nh.advertise<sensor_msgs::PointCloud2>("/all_key_points", 100);
    pubCloudPlane =
        nh.advertise<sensor_msgs::PointCloud2>("/cloud_all_plane", 100);
    pubProjPlane =
        nh.advertise<sensor_msgs::PointCloud2>("/cloud_proj_plane", 100);

    pubAlignedSource =
        nh.advertise<sensor_msgs::PointCloud2>("/aligned_source", 100);
    pubMatchedCloud =
        nh.advertise<sensor_msgs::PointCloud2>("/cloud_matched", 100);
    pubMatchedBinary = nh.advertise<sensor_msgs::PointCloud2>(
        "/cloud_matched_key_points", 100);
    pubCorrectedMap =
        nh.advertise<sensor_msgs::PointCloud2>("/corrected_map", 100);

    pubLoopStatus =
        nh.advertise<visualization_msgs::MarkerArray>("/loop_status", 100);

    // BTC
    pubBTC =
        nh.advertise<visualization_msgs::MarkerArray>("/BTC_pair_line", 10);
    pubAlignedBTC =
        nh.advertise<visualization_msgs::MarkerArray>("/aligned_BTC_line", 10);
    pubBTCList = nh.advertise<visualization_msgs::MarkerArray>(
        "descriptor_line_list", 10);
    pubBTCCloud =
        nh.advertise<sensor_msgs::PointCloud2>("/BTC_pair_cloud", 100);

    // Pose
    pubCurrentPose = nh.advertise<nav_msgs::Odometry>("/current_pose", 10);
    pubCorrectedPath = nh.advertise<nav_msgs::Odometry>("/matched_pose", 10);
    pubOdomAftMapped =
        nh.advertise<nav_msgs::Odometry>("/aft_mapped_to_init", 10);
    pubLoopConstraintEdge = nh.advertise<visualization_msgs::MarkerArray>(
        "/loop_closure_constraints", 10);
    color_tp.a = 1.0;
    color_tp.r = 0.0 / 255.0;
    color_tp.g = 255.0 / 255.0;
    color_tp.b = 0.0 / 255.0;

    color_fp.a = 1.0;
    color_fp.r = 1.0;
    color_fp.g = 0.0;
    color_fp.b = 0.0;

    color_path.a = 0.8;
    color_path.r = 255.0 / 255.0;
    color_path.g = 255.0 / 255.0;
    color_path.b = 255.0 / 255.0;

    scale_tp *= config_setting.visualization_scale_;
    scale_fp *= config_setting.visualization_scale_;
    scale_path *= config_setting.visualization_scale_;
  }

  //
  void PublishLDResult(const std::vector<BTC> &btcs_vec) {
    PubCurrentCloud();
    PubAlignedCloud();

    PubPlane();
    PubProjCloud();
    PubDescriptorCloud();
    PubAllDescriptorCloud();

    //=== visulizaion
    publish_std_list(btcs_vec);
    PubAlignInfo();
  }

  //
  void CleanRviz() {
    // TODO: clean code
    //=== clear rviz
    // cloud
    sensor_msgs::PointCloud2 pub_cloud;
    pcl::PointCloud<RGBNPoint> empty_cloud;
    pcl::toROSMsg(empty_cloud, pub_cloud);
    pub_cloud.header.frame_id = "camera_init";
    pubMatchedBinary.publish(pub_cloud);
    pcl::PointCloud<pcl::PointXYZRGB> empty_rgb_cloud;
    pcl::toROSMsg(empty_rgb_cloud, pub_cloud);
    pub_cloud.header.frame_id = "camera_init";
    pubMatchedCloud.publish(pub_cloud);
    pubCorrectedMap.publish(pub_cloud);

    // btc
    visualization_msgs::MarkerArray ma_line;
    visualization_msgs::Marker m_line;
    m_line.type = visualization_msgs::Marker::LINE_LIST;
    m_line.action = visualization_msgs::Marker::ADD;
    m_line.ns = "lines";
    m_line.header.frame_id = "camera_init";
    m_line.id = 0;
    for (int j = 0; j < 100 * 6; j++) {
      m_line.color.a = 0.00;
      ma_line.markers.push_back(m_line);
      m_line.id++;
    }
    ma_line.markers.push_back(m_line);
    pubBTC.publish(ma_line);
    pubAlignedBTC.publish(ma_line);
  }

  // ================================publish current
  // info================================ publish current source cloud
  void PubCurrentCloud() {
    pcl::toROSMsg(localmap_cloud, pub_cloud);
    pub_cloud.header.frame_id = "camera_init";
    pubCurrentCloud.publish(pub_cloud);
  }

  // publish aligned source cloud
  void PubAlignedCloud() {
    if (search_result.first > 0) {
      T_target_source.block<3, 3>(0, 0) =
          loop_transform.second; // T_source_target
      T_target_source.block<3, 1>(0, 3) = loop_transform.first;
      T_target_source = T_target_source.inverse();
      pcl::transformPointCloud(localmap_cloud, aligned_localmap_cloud,
                               T_target_source);
    }
    if (!aligned_localmap_cloud.size())
      return;
    pcl::toROSMsg(aligned_localmap_cloud, pub_cloud);
    pub_cloud.header.frame_id = "camera_init";
    pubAlignedSource.publish(pub_cloud);
  }

  // publish current frame whole plane
  void PubPlane() {
    PointCloudXYZI plane_normal_cloud;
    if (btc_manager->proj_plane_ != nullptr &&
        btc_manager->plane_cloud_vec_.size()) {
      int num = 5;
      double inter = 0.1;
      int intensity = 100;
      int cnt = 0;
      // for (auto &plane_cloud : btc_manager->plane_cloud_vec_) {
      for (auto &plane_pt : btc_manager->plane_cloud_vec_.back()->points) {
        // if(++cnt > 5) break;
        const auto &pt = plane_pt;
        for (int i = 0; i < num; i++) {
          pcl::PointXYZINormal tmp;
          tmp.x = pt.x + i * inter * pt.normal_x;
          tmp.y = pt.y + i * inter * pt.normal_y;
          tmp.z = pt.z + i * inter * pt.normal_z;
          tmp.intensity = intensity + i * 10;
          plane_normal_cloud.push_back(tmp);
        }
      }
    }
    if (!plane_normal_cloud.size())
      return;
    pcl::toROSMsg(plane_normal_cloud, pub_cloud);
    pub_cloud.header.frame_id = "camera_init";
    pubCloudPlane.publish(pub_cloud);
  }

  // publish current projection planes
  void PubProjCloud() {
    PointCloudXYZI proj_plane_normal_cloud;
    if (btc_manager->valid_proj_plane_!= nullptr && btc_manager->proj_plane_->size()) {
      int num = 5;
      double inter = 0.1;
      int cnt = 0;
      bool is_valid = true;
      for (auto &proj_plane : *btc_manager->valid_proj_plane_) {
        if (++cnt > config_setting.proj_plane_num_) {
          is_valid = false;
        }
        if (!proj_plane->is_plane_)
          continue;
        num = std::ceil(proj_plane->radius_ * 10);
        num = std::max(num, 2);
        for (int i = 0; i < num; i++) {
          const auto &pt = proj_plane->center_;
          const auto &pt_normal = proj_plane->normal_;
          pcl::PointXYZINormal tmp;
          tmp.x = pt.x() + i * inter * pt_normal.x();
          tmp.y = pt.y() + i * inter * pt_normal.y();
          tmp.z = pt.z() + i * inter * pt_normal.z();
          if (i == 0) {
            tmp.intensity = 1;
          } else {
            tmp.intensity = is_valid ? 2 : 0;
          }
          tmp.curvature = proj_plane->min_eigen_value_;
          tmp.normal_x = proj_plane->points_size_;
          tmp.normal_y = proj_plane->sub_plane_num_;
          tmp.normal_z = proj_plane->radius_;
          proj_plane_normal_cloud.push_back(tmp);
        }
      }
    }
    if (!proj_plane_normal_cloud.size())
      return;
    pcl::toROSMsg(proj_plane_normal_cloud, pub_cloud);
    pub_cloud.header.frame_id = "camera_init";
    pubProjPlane.publish(pub_cloud);
  }

  // publish current BTC descriptor points
  void PubDescriptorCloud() {
    PointCloudXYZI key_points_cloud;
    if (btc_manager->history_binary_list_.empty())
      return;
    for (auto var : btc_manager->history_binary_list_.back()) {
      PointType pi;
      pi.x = var.location_[0];
      pi.y = var.location_[1];
      pi.z = var.location_[2];
      key_points_cloud.push_back(pi);
    }
    if (key_points_cloud.size()) {
      pcl::toROSMsg(key_points_cloud, pub_cloud);
      pub_cloud.header.frame_id = "camera_init";
      pubCurrentBinary.publish(pub_cloud);
    }
  }

  // ================================publish hitorical
  // info================================ publish hitorical plane

  // publish all BTC descriptor points
  void PubAllDescriptorCloud() {
    PointCloudXYZI database_key_points_cloud;
    database_key_points_cloud.reserve(btc_manager->history_binary_list_.size());
    for (auto binray : btc_manager->history_binary_list_) {
      for (auto var : binray) {
        PointType pi;
        pi.x = var.location_[0];
        pi.y = var.location_[1];
        pi.z = var.location_[2];
        pi.intensity = var.summary_;
        database_key_points_cloud.push_back(pi);
      }
    }
    if (database_key_points_cloud.size()) {
      pcl::toROSMsg(database_key_points_cloud, pub_cloud);
      pub_cloud.header.frame_id = "camera_init";
      pubAllBinary.publish(pub_cloud);
    }
  }

  // ================================publish success
  // info================================
  void PubAlignInfo() {
    visualization_msgs::MarkerArray marker_array;
    visualization_msgs::Marker marker;
    marker.header.frame_id = "camera_init";
    marker.ns = "colored_path";
    marker.id = pose_id;
    marker.type = visualization_msgs::Marker::SPHERE; // LINE_LIST
    marker.action = visualization_msgs::Marker::ADD;
    marker.pose.orientation.w = 1.0;
    if (search_result.first >= 0) {
      triggle_loop_num++;

      // publish matched key points
      PointCloudXYZI match_key_points_cloud;
      for (auto var : btc_manager->history_binary_list_[search_result.first]) {
        PointType pi;
        pi.x = var.location_[0];
        pi.y = var.location_[1];
        pi.z = var.location_[2];
        pi.intensity = var.summary_;
        match_key_points_cloud.push_back(pi);
      }
      pcl::toROSMsg(match_key_points_cloud, pub_cloud);
      pub_cloud.header.frame_id = "camera_init";
      pubMatchedBinary.publish(pub_cloud);

      RGBNCloud::Ptr matcher_cloud =
          btc_manager->key_cloud_vec_[search_result.first];
      pcl::PointCloud<pcl::PointXYZRGB> matched_cloud_rgb;
      int matched_cloud_size = matcher_cloud->size();
      matched_cloud_rgb.resize(matched_cloud_size);
      for (size_t i = 0; i < matched_cloud_size; i++) {
        auto &pi = matched_cloud_rgb.points[i];
        pi.x = matcher_cloud->points[i].x;
        pi.y = matcher_cloud->points[i].y;
        pi.z = matcher_cloud->points[i].z;
        pi.r = pi.g = pi.b = 0;
      }

      // add cloud color
      // double cloud_overlap =
      //     calc_overlap(localmap_cloud.makeShared(), matcher_cloud, 0.5);
      // double cloud_overlap =
      //     calc_overlap(localmap_cloud.makeShared(),
      //                  aligned_localmap_cloud.makeShared(), 0.5*0.5);
      // true positive
      if (cloud_overlap >= cloud_overlap_thr) {
        LINFO << "[LD][calc_overlap] cloud_overlap: " << cloud_overlap
              << " thresh: " << cloud_overlap_thr << REND;
        true_loop_num++;
        for (size_t i = 0; i < matched_cloud_size; i++) {
          auto &pi = matched_cloud_rgb.points[i];
          pi.g = 255;
        }
        marker.scale.x = scale_tp;
        marker.color = color_tp;
      } else if (cloud_overlap < 0.5 * cloud_overlap_thr) {
        LERROR << "[LD][calc_overlap] cloud_overlap: " << cloud_overlap
               << " thresh: " << cloud_overlap_thr << REND;
        for (size_t i = 0; i < matched_cloud_size; i++) {
          auto &pi = matched_cloud_rgb.points[i];
          pi.r = 255;
        }
        marker.scale.x = scale_fp;
        marker.color = color_fp;
      } else if (cloud_overlap < cloud_overlap_thr) {
        LWARNING << "[LD][calc_overlap] cloud_overlap: " << cloud_overlap
                 << " thresh: " << cloud_overlap_thr << REND;
        for (size_t i = 0; i < matched_cloud_size; i++) {
          auto &pi = matched_cloud_rgb.points[i];
          pi.r = 255;
          pi.g = 255;
          pi.b = 0;
        }
        marker.scale.x = scale_fp;
        marker.color = color_fp;
      }
      // puslish matched cloud rgb
      pcl::toROSMsg(matched_cloud_rgb, pub_cloud);
      pub_cloud.header.frame_id = "camera_init";
      pubMatchedCloud.publish(pub_cloud);

      // publish BTC pair
      // <0.5 红，0.5-1 黄，1-1.5 绿，>1.5 蓝
      Eigen::Vector3d rgb;
      double good_overlap = 1.5 * cloud_overlap_thr;
      good_overlap = std::min(good_overlap, 0.8);
      if (cloud_overlap > good_overlap) {
        rgb = Eigen::Vector3d(0.0, 255.0, 255.0);
      } else if (cloud_overlap >= cloud_overlap_thr) {
        rgb = Eigen::Vector3d(0.0, 255.0, 0.0);
      } else if (cloud_overlap >= 0.5 * cloud_overlap_thr) {
        rgb = Eigen::Vector3d(255.0, 255.0, 0.0);
      } else {
        LERROR << "rgb cloud_overlap: " << cloud_overlap << REND;
        rgb = Eigen::Vector3d(255.0, 0.0, 0.0);
      }
      Eigen::Matrix4d transform1 = Eigen::Matrix4d::Identity();
      Eigen::Matrix4d transform2 = Eigen::Matrix4d::Identity();
      publish_std_pair(loop_std_pair, transform1, transform2, pubBTC, rgb);
      // publish aligned source BTC
      publish_std(loop_std_pair, T_target_source, pubAlignedBTC, true);

      int vertex_size{0}, pair_size{0};
      pcl::PointCloud<pcl::PointXYZRGB> BTC_pari_cloud;
      auto &std_pair_vec = btc_manager->loop_res_->sol_res_->std_pair_vec_;
      for (auto &std_pair : std_pair_vec) {
        pcl::PointXYZRGB pi;
        pi.x = std_pair.first.center_.x();
        pi.y = std_pair.first.center_.y();
        pi.z = std_pair.first.center_.z();
        pi.r = pi.g = pi.b = 255;
        BTC_pari_cloud.push_back(pi);
        // LINFO << pi.x << " " << pi.y << " " << pi.z << ", ";
        pi.x = std_pair.second.center_.x();
        pi.y = std_pair.second.center_.y();
        pi.z = std_pair.second.center_.z();
        pi.r = pi.g = pi.b = 128;
        BTC_pari_cloud.push_back(pi);
        // LDEBUG << pi.x << " " << pi.y << " " << pi.z << REND;
        ++pair_size;
      }
      auto &src_vec = btc_manager->loop_res_->sol_res_->src_vec_;
      auto &ref_vec = btc_manager->loop_res_->sol_res_->ref_vec_;
      for (int i = 0; i < src_vec.size(); ++i) {
        auto &src = src_vec[i];
        pcl::PointXYZRGB pi;
        pi.x = src.x();
        pi.y = src.y();
        pi.z = src.z();
        pi.r = pi.g = pi.b = 255;
        BTC_pari_cloud.push_back(pi);
        auto &ref = ref_vec[i];
        pi.x = ref.x();
        pi.y = ref.y();
        pi.z = ref.z();
        pi.r = pi.g = 0;
        pi.b = 255;
        BTC_pari_cloud.push_back(pi);
        ++vertex_size;
      }
      // LINFO << "pair_size: " << pair_size << " vertex_size: " << vertex_size
      //       << REND;
      pcl::toROSMsg(BTC_pari_cloud, pub_cloud);
      pub_cloud.header.frame_id = "camera_init";
      pubBTCCloud.publish(pub_cloud);

      if (cloud_overlap < cloud_overlap_thr) {
        // sleep for visualization
        if (btc_manager->print_debug_info_)
          std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      }
    } else {
      //=== clear rviz
      // cloud
      RGBNCloud empty_cloud;
      pcl::toROSMsg(empty_cloud, pub_cloud);
      pub_cloud.header.frame_id = "camera_init";
      pubMatchedBinary.publish(pub_cloud);
      pcl::PointCloud<pcl::PointXYZRGB> empty_rgb_cloud;
      pcl::toROSMsg(empty_rgb_cloud, pub_cloud);
      pub_cloud.header.frame_id = "camera_init";
      pubMatchedCloud.publish(pub_cloud);
      // pubCloudPlane.publish(pub_cloud);
      // pubProjPlane.publish(pub_cloud);

      // btc
      visualization_msgs::MarkerArray ma_line;
      visualization_msgs::Marker m_line;
      m_line.type = visualization_msgs::Marker::LINE_LIST;
      m_line.action = visualization_msgs::Marker::ADD;
      m_line.ns = "lines";
      m_line.header.frame_id = "camera_init";
      m_line.id = 0;
      for (int j = 0; j < 100 * 6; j++) {
        m_line.color.a = 0.00;
        ma_line.markers.push_back(m_line);
        m_line.id++;
      }
      ma_line.markers.push_back(m_line);
      pubBTC.publish(ma_line);
      pubAlignedBTC.publish(ma_line);

      //
      if (pose_id > 0) {
        marker.scale.x = scale_path;
        marker.color = color_path;
      }
    }
    marker.scale.y = marker.scale.x;
    marker.scale.z = marker.scale.x;

    // publish path marker
    geometry_msgs::Point point1;
    point1.x = pose_list[pose_id - 1].first[0];
    point1.y = pose_list[pose_id - 1].first[1];
    point1.z = pose_list[pose_id - 1].first[2];
    geometry_msgs::Point point2;
    point2.x = pose_list[pose_id].first[0];
    point2.y = pose_list[pose_id].first[1];
    point2.z = pose_list[pose_id].first[2];

    Eigen::Quaterniond q = Eigen::Quaterniond(pose_list[pose_id].second);
    marker.pose.orientation.w = q.w();
    marker.pose.orientation.x = q.x();
    marker.pose.orientation.y = q.y();
    marker.pose.orientation.z = q.z();

    // marker.points.push_back(point1);
    marker.points.push_back(point2);
    marker_array.markers.push_back(marker);
    pubLoopStatus.publish(marker_array);
    LINFO << pose_list[pose_id - 1].first[0] << " "
          << pose_list[pose_id - 1].first[1] << " "
          << pose_list[pose_id - 1].first[2] << ", "
          << pose_list[pose_id].first[0] << " " << pose_list[pose_id].first[1]
          << " " << pose_list[pose_id].first[2] << REND;
  }

  // publish correct cloud map
  void PubCorrectedMap(const std::vector<RGBNCloudPtr> &cloud_vec,
                       const std::vector<Eigen::Matrix4d> &pose_vec) {
    RGBNCloud corrected_map;
    RGBNCloud corrected_cloud;
    for (int i = 0; i < pose_vec.size(); ++i) {
      pcl::transformPointCloud(*cloud_vec[i], corrected_cloud, pose_vec[i]);
      corrected_map += corrected_cloud;
    }
    LDEBUG << "[LD][debug] corrected_map: " << corrected_map.size() << REND;
    down_sampling_voxel(corrected_map, config_setting.localmap_voxel_size_);
    LDEBUG << "[LD][debug] downsampled corrected_map: " << corrected_map.size() << REND;
    sensor_msgs::PointCloud2 pub_cloud;
    pcl::toROSMsg(corrected_map, pub_cloud);
    pub_cloud.header.frame_id = "camera_init";
    pubCorrectedMap.publish(pub_cloud);
  }

  // publish corerct path
  void PubCorrectedPose(const std::vector<Eigen::Matrix4d> &pose_vec) {
    nav_msgs::Path correct_path;
    for (int i = 0; i < pose_vec.size(); i += 1) {
      geometry_msgs::PoseStamped msg_pose;
      msg_pose.pose.position.x = pose_vec[i](0,3);
      msg_pose.pose.position.y = pose_vec[i](1,3);
      msg_pose.pose.position.z = pose_vec[i](2,3);
      Eigen::Quaterniond pose_q(pose_vec[i].block<3, 3>(0, 0));
      msg_pose.header.frame_id = "camera_init";
      msg_pose.pose.orientation.x = pose_q.x();
      msg_pose.pose.orientation.y = pose_q.y();
      msg_pose.pose.orientation.z = pose_q.z();
      msg_pose.pose.orientation.w = pose_q.w();
      correct_path.poses.push_back(msg_pose);
    }
    correct_path.header.stamp = ros::Time::now();
    correct_path.header.frame_id = "camera_init";
    pubCorrectedPath.publish(correct_path);
  }

  void PubLoopConstraint(const std::vector<std::pair<int, int>> &loop_container,
                         const std::vector<Eigen::Matrix4d> &key_pose_vec) {
    if (loop_container.empty())
      return;

    visualization_msgs::MarkerArray markerArray;
    // 闭环顶点
    visualization_msgs::Marker markerNode;
    markerNode.header.frame_id = "camera_init"; // camera_init
    // markerNode.header.stamp = ros::Time().fromSec( keyframeTimes.back() );
    markerNode.action = visualization_msgs::Marker::ADD;
    markerNode.type = visualization_msgs::Marker::SPHERE_LIST;
    markerNode.ns = "loop_nodes";
    markerNode.id = 0;
    markerNode.pose.orientation.w = 1;
    markerNode.scale.x = 0.3 * config_setting.visualization_scale_;
    markerNode.scale.y = 0.3 * config_setting.visualization_scale_;
    markerNode.scale.z = 0.3 * config_setting.visualization_scale_;
    markerNode.color.r = 0;
    markerNode.color.g = 0.8;
    markerNode.color.b = 1;
    markerNode.color.a = 1;
    // 闭环边
    visualization_msgs::Marker markerEdge;
    markerEdge.header.frame_id = "camera_init";
    // markerEdge.header.stamp = ros::Time().fromSec( keyframeTimes.back() );
    markerEdge.action = visualization_msgs::Marker::ADD;
    markerEdge.type = visualization_msgs::Marker::LINE_LIST;
    markerEdge.ns = "loop_edges";
    markerEdge.id = 1;
    markerEdge.pose.orientation.w = 1;
    markerEdge.scale.x = 0.1 * config_setting.visualization_scale_;
    markerEdge.color.r = 0.9;
    markerEdge.color.g = 0.9;
    markerEdge.color.b = 0;
    markerEdge.color.a = 1;

    // 遍历闭环
    for (auto it = loop_container.begin(); it != loop_container.end(); ++it) {
      int key_cur = it->first;
      int key_pre = it->second;
      geometry_msgs::Point p;
      p.x = key_pose_vec[key_cur](0, 3);
      p.y = key_pose_vec[key_cur](1, 3);
      p.z = key_pose_vec[key_cur](2, 3);
      markerNode.points.push_back(p);
      markerEdge.points.push_back(p);
      p.x = key_pose_vec[key_pre](0, 3);
      p.y = key_pose_vec[key_pre](1, 3);
      p.z = key_pose_vec[key_pre](2, 3);
      markerNode.points.push_back(p);
      markerEdge.points.push_back(p);
    }

    markerArray.markers.push_back(markerNode);
    markerArray.markers.push_back(markerEdge);
    pubLoopConstraintEdge.publish(markerArray);
  }

public:
  void publish_binary(const std::vector<BinaryDescriptor> &binary_list,
                      const Eigen::Vector3d &text_color,
                      const std::string &text_ns,
                      const ros::Publisher &text_publisher) {
    visualization_msgs::MarkerArray text_array;
    visualization_msgs::Marker text;
    text.type = visualization_msgs::Marker::TEXT_VIEW_FACING;
    text.action = visualization_msgs::Marker::ADD;
    text.ns = text_ns;
    text.color.a = 0.8; // Don't forget to set the alpha!
    text.scale.z = 0.08;
    text.pose.orientation.w = 1.0;
    text.header.frame_id = "camera_init";
    for (size_t i = 0; i < binary_list.size(); i++) {
      text.pose.position.x = binary_list[i].location_[0];
      text.pose.position.y = binary_list[i].location_[1];
      text.pose.position.z = binary_list[i].location_[2];
      std::ostringstream str;
      str << std::to_string((int)(binary_list[i].summary_));
      text.text = str.str();
      text.scale.x = 0.5 * config_setting.visualization_scale_;
      text.scale.y = 0.5 * config_setting.visualization_scale_;
      text.scale.z = 0.5 * config_setting.visualization_scale_;
      text.color.r = text_color[0];
      text.color.g = text_color[1];
      text.color.b = text_color[2];
      text.color.a = 1;
      text.id++;
      text_array.markers.push_back(text);
    }
    for (int i = 1; i < 100; i++) {
      text.color.a = 0;
      text.id++;
      text_array.markers.push_back(text);
    }
    text_publisher.publish(text_array);
    return;
  }

  void publish_std_list(const std::vector<BTC> &btc_list) {
    // publish descriptor
    visualization_msgs::MarkerArray ma_line;
    visualization_msgs::Marker m_line;
    m_line.type = visualization_msgs::Marker::LINE_LIST;
    m_line.action = visualization_msgs::Marker::ADD;
    m_line.ns = "std";
    // Don't forget to set the alpha!
    m_line.scale.x = 0.5 * config_setting.visualization_scale_;
    m_line.pose.orientation.w = 1.0;
    m_line.header.frame_id = "camera_init";
    m_line.id = 0;
    m_line.points.clear();
    m_line.color.r = 0;
    m_line.color.g = 1;
    m_line.color.b = 0;
    m_line.color.a = 1;
    for (auto var : btc_list) {
      geometry_msgs::Point p;
      p.x = var.binary_A_.location_[0];
      p.y = var.binary_A_.location_[1];
      p.z = var.binary_A_.location_[2];
      m_line.points.push_back(p);
      p.x = var.binary_B_.location_[0];
      p.y = var.binary_B_.location_[1];
      p.z = var.binary_B_.location_[2];
      m_line.points.push_back(p);
      ma_line.markers.push_back(m_line);
      m_line.id++;
      m_line.points.clear();
      p.x = var.binary_C_.location_[0];
      p.y = var.binary_C_.location_[1];
      p.z = var.binary_C_.location_[2];
      m_line.points.push_back(p);
      p.x = var.binary_B_.location_[0];
      p.y = var.binary_B_.location_[1];
      p.z = var.binary_B_.location_[2];
      m_line.points.push_back(p);
      ma_line.markers.push_back(m_line);
      m_line.id++;
      m_line.points.clear();
      p.x = var.binary_C_.location_[0];
      p.y = var.binary_C_.location_[1];
      p.z = var.binary_C_.location_[2];
      m_line.points.push_back(p);
      p.x = var.binary_A_.location_[0];
      p.y = var.binary_A_.location_[1];
      p.z = var.binary_A_.location_[2];
      m_line.points.push_back(p);
      ma_line.markers.push_back(m_line);
      m_line.id++;
      m_line.points.clear();
    }
    for (int j = 0; j < 1000 * 3; j++) {
      m_line.color.a = 0.00;
      ma_line.markers.push_back(m_line);
      m_line.id++;
    }
    pubBTCList.publish(ma_line);
    m_line.id = 0;
    ma_line.markers.clear();
  }

  // 给每个三角形描述子三条边画线
  void publish_std(const std::vector<std::pair<BTC, BTC>> &match_std_list,
                   const Eigen::Matrix4d &transform1,
                   const ros::Publisher &std_publisher, bool pub_source) {
    // publish descriptor
    // bool transform_enable = true;
    visualization_msgs::MarkerArray ma_line;
    visualization_msgs::Marker m_line;
    m_line.type = visualization_msgs::Marker::LINE_LIST;
    m_line.action = visualization_msgs::Marker::ADD;
    m_line.ns = "single_BTC_lines";
    // Don't forget to set the alpha!
    m_line.scale.x = 0.25 * config_setting.visualization_scale_;
    m_line.pose.orientation.w = 1.0;
    m_line.header.frame_id = "camera_init";
    m_line.id = 0;
    const int max_pub_cnt = 100;
    int pub_cnt = 1;
    for (auto var : match_std_list) {
      if (pub_cnt > max_pub_cnt) {
        break;
      }
      pub_cnt++;
      m_line.color.a = 0.8;
      m_line.points.clear();

      // 用于求解位姿的BTC
      if (var.second.match_frame_number_ == var.first.frame_number_) {
        m_line.scale.x = 0.3 * config_setting.visualization_scale_;
        m_line.color.a = 1;
      } else {
        m_line.scale.x = 0.15 * config_setting.visualization_scale_;
        m_line.color.a = 0.5;
      }
      m_line.color.r = 0.0 / 255;
      m_line.color.g = 0.0 / 255;
      m_line.color.b = 255.0 / 255;
      geometry_msgs::Point p;
      Eigen::Vector3d t_p;
      t_p = var.first.binary_A_.location_;
      t_p = transform1.block<3, 3>(0, 0) * t_p + transform1.block<3, 1>(0, 3);
      p.x = t_p[0];
      p.y = t_p[1];
      p.z = t_p[2];
      m_line.points.push_back(p);

      t_p = var.first.binary_B_.location_;
      t_p = transform1.block<3, 3>(0, 0) * t_p + transform1.block<3, 1>(0, 3);
      p.x = t_p[0];
      p.y = t_p[1];
      p.z = t_p[2];
      m_line.points.push_back(p);
      ma_line.markers.push_back(m_line);
      m_line.id++;
      m_line.points.clear();

      t_p = var.first.binary_C_.location_;
      t_p = transform1.block<3, 3>(0, 0) * t_p + transform1.block<3, 1>(0, 3);
      p.x = t_p[0];
      p.y = t_p[1];
      p.z = t_p[2];
      m_line.points.push_back(p);
      t_p = var.first.binary_B_.location_;
      t_p = transform1.block<3, 3>(0, 0) * t_p + transform1.block<3, 1>(0, 3);
      p.x = t_p[0];
      p.y = t_p[1];
      p.z = t_p[2];
      m_line.points.push_back(p);
      ma_line.markers.push_back(m_line);
      m_line.id++;
      m_line.points.clear();

      t_p = var.first.binary_C_.location_;
      t_p = transform1.block<3, 3>(0, 0) * t_p + transform1.block<3, 1>(0, 3);
      p.x = t_p[0];
      p.y = t_p[1];
      p.z = t_p[2];
      m_line.points.push_back(p);
      t_p = var.first.binary_A_.location_;
      t_p = transform1.block<3, 3>(0, 0) * t_p + transform1.block<3, 1>(0, 3);
      p.x = t_p[0];
      p.y = t_p[1];
      p.z = t_p[2];
      m_line.points.push_back(p);
      ma_line.markers.push_back(m_line);
      m_line.id++;
      m_line.points.clear();
      // debug
      // std_publisher.publish(ma_line);
      // std::cout << "var first: " << var.first.triangle_.transpose()
      //           << " , var second: " << var.second.triangle_.transpose()
      //           << std::endl;
      // getchar();
    }
    for (int j = 0; j < max_pub_cnt * 3; j++) {
      m_line.color.a = 0.00;
      ma_line.markers.push_back(m_line);
      m_line.id++;
    }
    std_publisher.publish(ma_line);
    m_line.id = 0;
    ma_line.markers.clear();
  }

  void publish_std_pair(const std::vector<std::pair<BTC, BTC>> &match_std_list,
                        const Eigen::Matrix4d &transform1,
                        const Eigen::Matrix4d &transform2,
                        const ros::Publisher &std_publisher,
                        const Eigen::Vector3d &rgb) {
    // publish descriptor
    // bool transform_enable = true;
    visualization_msgs::MarkerArray ma_line;
    visualization_msgs::Marker m_line;
    m_line.type = visualization_msgs::Marker::LINE_LIST;
    m_line.action = visualization_msgs::Marker::ADD;
    m_line.ns = "BTC_pair_lines";
    // Don't forget to set the alpha!
    m_line.scale.x = 0.25 * config_setting.visualization_scale_;
    m_line.pose.orientation.w = 1.0;
    m_line.header.frame_id = "camera_init";
    m_line.id = 0;
    const int max_pub_cnt = 100;
    int pub_cnt = 1;
    for (auto var : match_std_list) {
      if (pub_cnt > max_pub_cnt) {
        break;
      }
      pub_cnt++;
      m_line.points.clear();
      // target triangle
      // 用于求解位姿的BTC
      if (var.second.match_frame_number_ == var.first.frame_number_) {
        m_line.scale.x = 0.3 * config_setting.visualization_scale_;
        m_line.color.a = 1;
      } else {
        m_line.scale.x = 0.15 * config_setting.visualization_scale_;
        m_line.color.a = 0.5;
      }
      m_line.color.r = rgb(0) / 255;
      m_line.color.g = rgb(1) / 255;
      m_line.color.b = rgb(2) / 255;
      geometry_msgs::Point p;
      Eigen::Vector3d t_p;
      t_p = var.second.binary_A_.location_;
      t_p = transform2.block<3, 3>(0, 0) * t_p + transform2.block<3, 1>(0, 3);
      p.x = t_p[0];
      p.y = t_p[1];
      p.z = t_p[2];
      m_line.points.push_back(p);

      t_p = var.second.binary_B_.location_;
      t_p = transform2.block<3, 3>(0, 0) * t_p + transform2.block<3, 1>(0, 3);
      p.x = t_p[0];
      p.y = t_p[1];
      p.z = t_p[2];
      m_line.points.push_back(p);
      ma_line.markers.push_back(m_line);
      m_line.id++;
      m_line.points.clear();

      t_p = var.second.binary_C_.location_;
      t_p = transform2.block<3, 3>(0, 0) * t_p + transform2.block<3, 1>(0, 3);
      p.x = t_p[0];
      p.y = t_p[1];
      p.z = t_p[2];
      m_line.points.push_back(p);

      t_p = var.second.binary_B_.location_;
      t_p = transform2.block<3, 3>(0, 0) * t_p + transform2.block<3, 1>(0, 3);
      p.x = t_p[0];
      p.y = t_p[1];
      p.z = t_p[2];
      m_line.points.push_back(p);
      ma_line.markers.push_back(m_line);
      m_line.id++;
      m_line.points.clear();

      t_p = var.second.binary_C_.location_;
      t_p = transform2.block<3, 3>(0, 0) * t_p + transform2.block<3, 1>(0, 3);
      p.x = t_p[0];
      p.y = t_p[1];
      p.z = t_p[2];
      m_line.points.push_back(p);

      t_p = var.second.binary_A_.location_;
      t_p = transform2.block<3, 3>(0, 0) * t_p + transform2.block<3, 1>(0, 3);
      p.x = t_p[0];
      p.y = t_p[1];
      p.z = t_p[2];
      m_line.points.push_back(p);
      ma_line.markers.push_back(m_line);
      m_line.id++;
      m_line.points.clear();

      // source triangle
      m_line.color.r = 1;
      m_line.color.g = 1;
      m_line.color.b = 1;
      t_p = var.first.binary_A_.location_;
      t_p = transform1.block<3, 3>(0, 0) * t_p + transform1.block<3, 1>(0, 3);
      p.x = t_p[0];
      p.y = t_p[1];
      p.z = t_p[2];
      m_line.points.push_back(p);

      t_p = var.first.binary_B_.location_;
      t_p = transform1.block<3, 3>(0, 0) * t_p + transform1.block<3, 1>(0, 3);
      p.x = t_p[0];
      p.y = t_p[1];
      p.z = t_p[2];
      m_line.points.push_back(p);
      ma_line.markers.push_back(m_line);
      m_line.id++;
      m_line.points.clear();

      t_p = var.first.binary_C_.location_;
      t_p = transform1.block<3, 3>(0, 0) * t_p + transform1.block<3, 1>(0, 3);
      p.x = t_p[0];
      p.y = t_p[1];
      p.z = t_p[2];
      m_line.points.push_back(p);
      t_p = var.first.binary_B_.location_;
      t_p = transform1.block<3, 3>(0, 0) * t_p + transform1.block<3, 1>(0, 3);
      p.x = t_p[0];
      p.y = t_p[1];
      p.z = t_p[2];
      m_line.points.push_back(p);
      ma_line.markers.push_back(m_line);
      m_line.id++;
      m_line.points.clear();

      t_p = var.first.binary_C_.location_;
      t_p = transform1.block<3, 3>(0, 0) * t_p + transform1.block<3, 1>(0, 3);
      p.x = t_p[0];
      p.y = t_p[1];
      p.z = t_p[2];
      m_line.points.push_back(p);
      t_p = var.first.binary_A_.location_;
      t_p = transform1.block<3, 3>(0, 0) * t_p + transform1.block<3, 1>(0, 3);
      p.x = t_p[0];
      p.y = t_p[1];
      p.z = t_p[2];
      m_line.points.push_back(p);
      ma_line.markers.push_back(m_line);
      m_line.id++;
      m_line.points.clear();
      // debug
      // std_publisher.publish(ma_line);
      // std::cout << "var first: " << var.first.triangle_.transpose()
      //           << " , var second: " << var.second.triangle_.transpose()
      //           << std::endl;
      // getchar();
    }
    for (int j = 0; j < max_pub_cnt * 6; j++) {
      m_line.color.a = 0.00;
      ma_line.markers.push_back(m_line);
      m_line.id++;
    }
    std_publisher.publish(ma_line);
    m_line.id = 0;
    ma_line.markers.clear();
  }

  void CalcQuation(const Eigen::Vector3d &vec, const int axis,
                   geometry_msgs::Quaternion &q) {
    Eigen::Vector3d x_body = vec;
    Eigen::Vector3d y_body(1, 1, 0);
    if (x_body(2) != 0) {
      y_body(2) = -(y_body(0) * x_body(0) + y_body(1) * x_body(1)) / x_body(2);
    } else {
      if (x_body(1) != 0) {
        y_body(1) = -(y_body(0) * x_body(0)) / x_body(1);
      } else {
        y_body(0) = 0;
      }
    }
    y_body.normalize();
    Eigen::Vector3d z_body = x_body.cross(y_body);
    Eigen::Matrix3d rot;

    rot << x_body(0), x_body(1), x_body(2), y_body(0), y_body(1), y_body(2),
        z_body(0), z_body(1), z_body(2);
    Eigen::Matrix3d rotation = rot.transpose();
    if (axis == 2) {
      Eigen::Matrix3d rot_inc;
      rot_inc << 0, 0, 1, 0, 1, 0, -1, 0, 0;
      rotation = rotation * rot_inc;
    }
    Eigen::Quaterniond eq(rotation);
    q.w = eq.w();
    q.x = eq.x();
    q.y = eq.y();
    q.z = eq.z();
  }

  void pubPlane(const ros::Publisher &plane_pub, const std::string plane_ns,
                const int plane_id, const pcl::PointXYZINormal normal_p,
                const float radius, const Eigen::Vector3d rgb) {
    visualization_msgs::Marker plane;
    plane.header.frame_id = "camera_init";
    plane.header.stamp = ros::Time();
    plane.ns = plane_ns;
    plane.id = plane_id;
    plane.type = visualization_msgs::Marker::CUBE;
    plane.action = visualization_msgs::Marker::ADD;
    plane.pose.position.x = normal_p.x;
    plane.pose.position.y = normal_p.y;
    plane.pose.position.z = normal_p.z;
    geometry_msgs::Quaternion q;
    Eigen::Vector3d normal_vec(normal_p.normal_x, normal_p.normal_y,
                               normal_p.normal_z);
    CalcQuation(normal_vec, 2, q);
    plane.pose.orientation = q;
    plane.scale.x = 3.0 * radius;
    plane.scale.y = 3.0 * radius;
    plane.scale.z = 0.1;
    plane.color.a = 0.8; // 0.8
    plane.color.r = fabs(rgb(0));
    plane.color.g = fabs(rgb(1));
    plane.color.b = fabs(rgb(2));
    plane.lifetime = ros::Duration();
    plane_pub.publish(plane);
  }

public:
  ConfigSetting config_setting;
  std::shared_ptr<BtcDescManager> btc_manager;
  int pose_id;
  RGBNCloud localmap_cloud;
  RGBNCloud aligned_localmap_cloud;

  std::pair<int, double> search_result;
  std::pair<Eigen::Vector3d, Eigen::Matrix3d> loop_transform;
  Eigen::Matrix4d T_target_source;

  int triggle_loop_num = 0;
  int true_loop_num = 0;
  int false_loop_num = 0;
  double cloud_overlap_thr = 0.5;
  double cloud_overlap = -1;

  std::vector<std::pair<Eigen::Vector3d, Eigen::Matrix3d>> pose_list;
  std::vector<std::pair<BTC, BTC>> loop_std_pair;

private:
  ros::NodeHandle nh;
  sensor_msgs::PointCloud2 pub_cloud;

  std_msgs::ColorRGBA color_tp;
  std_msgs::ColorRGBA color_fp;
  std_msgs::ColorRGBA color_path;
  double scale_tp = 1.0;
  double scale_fp = 2.0;
  double scale_path = 0.5;
  // cloud
  ros::Publisher pubCurrentCloud;
  ros::Publisher pubCurrentBinary;
  ros::Publisher pubAllBinary;
  ros::Publisher pubCloudPlane;
  ros::Publisher pubProjPlane;

  ros::Publisher pubAlignedSource;
  ros::Publisher pubMatchedCloud;
  ros::Publisher pubMatchedBinary;
  ros::Publisher pubCorrectedMap;
  ros::Publisher pubLoopStatus;

  // BTC
  ros::Publisher pubBTC;
  ros::Publisher pubAlignedBTC;
  ros::Publisher pubBTCList;
  ros::Publisher pubBTCCloud;

  // Pose
  ros::Publisher pubCurrentPose;
  ros::Publisher pubCorrectedPath;
  ros::Publisher pubOdomAftMapped;
  ros::Publisher pubLoopConstraintEdge;
};
#endif // ROS_DEP_H