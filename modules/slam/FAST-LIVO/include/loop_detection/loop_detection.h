#pragma once

#include "btc.h"
#include "common/msg/cloud_msg.h"

#if BTC_DEBUG
#include "btc_ros_dep.h"
#endif

namespace robosense {
namespace slam {
class LoopDetection {
public:
  LoopDetection(ConfigSetting config): config_(config) {
    key_localmap.reset(new RGBNCloud);
  };

  ~LoopDetection() { ; };

  void SavePose(std::ofstream &f_corr, std::ofstream &f_ori) {
    Eigen::Quaterniond q;
    Eigen::Vector3d t;
    for (int i = 0; i < fixed_pose_vec.size(); ++i) {
      q = Eigen::Quaterniond(fixed_pose_vec[i].block<3, 3>(0, 0));
      t = fixed_pose_vec[i].block<3, 1>(0, 3);
      f_corr << std::fixed << pose_ts_vec[i] << " " << t.x() << " " << t.y()
             << " " << t.z() << " " << q.x() << " " << q.y() << " " << q.z()
             << " " << q.w() << std::endl;
    }
    for (int i = 0; i < origin_pose_vec.size(); ++i) {
      q = Eigen::Quaterniond(origin_pose_vec[i].block<3, 3>(0, 0));
      t = origin_pose_vec[i].block<3, 1>(0, 3);
      f_ori << std::fixed << pose_ts_vec[i] << " " << t.x() << " " << t.y()
            << " " << t.z() << " " << q.x() << " " << q.y() << " " << q.z()
            << " " << q.w() << std::endl;
    }
  }
  ConfigSetting config_;
  std::shared_ptr<BtcDescManager> btc_manager_ = nullptr;

  // input
  size_t cloud_idx = 0;
  size_t localmap_idx = 0;
  std::vector<RGBNCloudPtr> raw_cloud_vec;
  std::vector<Eigen::Matrix4d> fixed_pose_vec;
  std::vector<Eigen::Matrix4d> origin_pose_vec;
  std::vector<double> pose_ts_vec;

  // result
  std::vector<std::pair<int, int>> loop_container;
  RGBNCloudPtr key_localmap;
  bool has_loop_flag = false;
  int loop_cnt = 0;

  // time cost
  std::vector<double> descriptor_time;
  std::vector<double> querying_time;
  std::vector<double> update_time;

private:
};

} // namespace slam
} // namespace robosense