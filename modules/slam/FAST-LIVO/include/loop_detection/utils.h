
#ifndef UTILS_H
#define UTILS_H
#include <pcl/common/io.h>
#include <pcl/kdtree/kdtree_flann.h>

#include <Eigen/Core>
#include <Eigen/Dense>
#include <fstream>
#include <string>

#include "common/common.h"

inline double time_inc(double t_end, double t_begin) {
  return (t_end - t_begin) * 1000.;
}
// inline double time_inc(std::chrono::system_clock::time_point &t_end,
//                 std::chrono::system_clock::time_point &t_begin) {
//   return std::chrono::duration_cast<std::chrono::duration<double>>(t_end -
//                                                                    t_begin)
//              .count() *
//          1000;
// }

inline double calc_overlap(const RGBNCloud::Ptr &cloud1,
                    const RGBNCloud::Ptr &cloud2,
                    double dis_threshold, int point_kip = 1) {
  if (cloud2->empty()) {
    return -1;
  }             
  double match_num = 0;
  pcl::KdTreeFLANN<RGBNPoint>::Ptr kd_tree(
      new pcl::KdTreeFLANN<RGBNPoint>);
  kd_tree->setInputCloud(cloud2);
  std::vector<int> pointIdxNKNSearch(1);
  std::vector<float> pointNKNSquaredDistance(1);
  for (size_t i = 0; i < cloud1->size(); i += point_kip) {
    RGBNPoint searchPoint = cloud1->points[i];
    if (kd_tree->nearestKSearch(searchPoint, 1, pointIdxNKNSearch,
                                pointNKNSquaredDistance) > 0) {
      if (pointNKNSquaredDistance[0] < dis_threshold * dis_threshold) {
        match_num++;
      }
    }
  }
  // std::cout << "cloud1 size:" << cloud1->size()
  //           << " cloud2 size: " << cloud2->size() << " match size:" <<
  //           match_num
  //           << std::endl;
  // FIXME: overlap/min(A,B)
  // double overlap =
  //     2 * match_num * point_kip / (cloud1->size() + cloud2->size());
  double overlap =
      match_num * point_kip / std::min(cloud1->size(), cloud2->size());

  // TODO: use multi thresh to calc weighed overlap
  return overlap;
}

inline void GetOverlapCloud(RGBNCloud::Ptr &cloud1, RGBNCloud::Ptr &cloud2,
                            const ConfigSetting &cfg, int point_kip = 1) {
  if (cloud2->empty()) {
    return;
  }
  RGBNCloud::Ptr cloud1_overlap(new RGBNCloud);
  cloud1_overlap->reserve(cloud1->size());
  RGBNCloud::Ptr cloud2_overlap(new RGBNCloud);
  cloud2_overlap->reserve(cloud2->size());

  double match_num = 0;
  pcl::KdTreeFLANN<RGBNPoint>::Ptr kd_tree(new pcl::KdTreeFLANN<RGBNPoint>);
  kd_tree->setInputCloud(cloud2);
  std::vector<int> pointIdxNKNSearch(1);
  std::vector<float> pointNKNSquaredDistance(1);
  for (size_t i = 0; i < cloud1->size(); i += point_kip) {
    RGBNPoint searchPoint = cloud1->points[i];
    if (kd_tree->nearestKSearch(searchPoint, 1, pointIdxNKNSearch,
                                pointNKNSquaredDistance) > 0) {
      if (pointNKNSquaredDistance[0] < cfg.search_dis_ * cfg.search_dis_) {
        match_num++;
        cloud1_overlap->push_back(searchPoint);
        cloud2_overlap->push_back(cloud2->points[pointIdxNKNSearch[0]]);
      }
    }
  }

  double overlap1 = match_num * point_kip / (cloud1->size());
  double overlap2 = match_num * point_kip / (cloud2->size());
  std::cout << "overlap1: " << overlap1 << " overlap2: " << overlap2
            << std::endl;

  cloud1 = cloud1_overlap;
  cloud2 = cloud2_overlap;
}
#endif