/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#pragma once

#include <iostream>
#include <gtsam/geometry/Pose3.h>
#include <gtsam/nonlinear/ISAM2.h>
#include <gtsam/nonlinear/Values.h>
#include <gtsam/slam/BetweenFactor.h>
#include <gtsam/slam/PriorFactor.h>
#include <mutex>

/* PGO with GTSAM Demo
// PGO 1.设置因子图和初始值
gtsam::Values initial;
gtsam::NonlinearFactorGraph graph;

// PGO 2.设置iSAM2优化器（iSAM2是递增式的优化器。
// 递增式优化器更符合SLAM中常见的使用方式，也就是持续加入新的位姿和约束，并持续进行优化。）
// https://github.com/TixiaoShan/LIO-SAM/blob/6665aa0a4fcb5a9bb3af7d3923ae4a035b489d47/src/mapOptmization.cpp#L1385
gtsam::ISAM2 isam;

// PGO 3.添加初始值到初始估计集合
initial.insert(cloudInd, gtsam::Pose3(pose.matrix()));

// PGO 4.添加里程计因子
graph.add(gtsam::PriorFactor<gtsam::Pose3>(0, gtsam::Pose3(pose.matrix()),
                                           odometryNoise));
// PGO 4.添加回环因子
graph.add(gtsam::BetweenFactor<gtsam::Pose3>(
    tar_frame, src_frame,
    gtsam::Pose3(tar_pose.matrix())
        .between(gtsam::Pose3(src_pose_refined.matrix())),
    robust_loop_noise));

// PGO 5.优化
isam.update(graph, initial);
isam.update();
curr_estimate = isam.calculateEstimate();
优化结果更新里程计pose

// PGO 6.清空当前因子图和初始值
// 因子图已经加入优化器，因此需要清空为下一次因子图做准备
graph.resize(0);
initial.clear();

*/
namespace robosense {
namespace slam {
class LoopCorrection {
 public:
  LoopCorrection(){
    SetLoopNoise(1e-1, 1e-1);
    SetOdomNoise(1e-6, 1e-4);
    //
    isam_params.relinearizeThreshold = 0.01;
    isam_params.relinearizeSkip = 1;
  };
  ~LoopCorrection() { ; };

  void SetLoopNoise(double loop_R_noise, double loop_t_noise) {
    gtsam::Vector noise_vec(6);
    noise_vec << loop_R_noise, loop_R_noise, loop_R_noise, loop_t_noise,
        loop_t_noise, loop_t_noise;
    robust_loop_noise = gtsam::noiseModel::Robust::Create(
        gtsam::noiseModel::mEstimator::Cauchy::Create(1),
        gtsam::noiseModel::Diagonal::Variances(noise_vec));
  }
  void SetOdomNoise(double odom_R_noise, double odom_t_noise) {
    odometry_noise = gtsam::noiseModel::Diagonal::Variances(
        (gtsam::Vector(6) << odom_R_noise, odom_R_noise, odom_R_noise,
         odom_t_noise, odom_t_noise, odom_t_noise)
            .finished());
  }

  void AddValues(int value_index, gtsam::Pose3 pose) {
    initial.insert(value_index, pose);
  };

  void AddLIOGraphFactor(){

  };

  void AddVIOGraphFactor(){

  };

  void AddLoopGraphFactor(){

  };

  void UpdatePoses(const gtsam::Values &estimates,
                    std::vector<Eigen::Matrix4d> &poses) {
    assert(estimates.size() == poses.size());
    poses.clear();
    for (int i = 0; i < estimates.size(); ++i) {
      auto est = estimates.at<gtsam::Pose3>(i);
      Eigen::Matrix4d est_affine3d(est.matrix());
      poses.push_back(est_affine3d);
    }
  }

  gtsam::Values initial;
  gtsam::NonlinearFactorGraph graph;
  gtsam::noiseModel::Diagonal::shared_ptr odometry_noise;
  gtsam::noiseModel::Base::shared_ptr robust_loop_noise;

  gtsam::ISAM2Params isam_params;
  gtsam::ISAM2 isam;
  gtsam::Values curr_estimate;

  // t iSAM
  std::vector<double> isam_update_time;
  std::vector<double> est_time;

private:
};

} // namespace slam
} // namespace robosense