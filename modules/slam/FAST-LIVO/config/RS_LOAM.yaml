lidar_odometry:
  hardware_setup:
###Meta
###AC1_1.0 yaw: 52~127 pit: -45~45
    # N_SCAN: 144 # 90/0.625
    # Horizon_SCAN: 120 # 75/0.625
    # ang_bottom: 45
    # groundScanInd: 50
    # ang_res_x: 0.625
    # ang_res_y: 0.625
###AC1_2.0 yaw: 25~155 pit: -30~30
    N_SCAN: 96 # 60/0.625
    Horizon_SCAN: 208 # 130/0.625
    ang_bottom: 30
    groundScanInd: 50
    ang_res_x: 0.625
    ang_res_y: 0.625
###m1p
    # N_SCAN: 125 # 25/0.2
    # Horizon_SCAN: 600 # 120/0.2
    # ang_bottom: 12.5
    # groundScanInd: 80
###ruby
    # N_SCAN: 200 # 40/0.2
    # Horizon_SCAN: 2700 # 600 for m1p, 2700 for ruby
    # ang_bottom: 25 # 12.5 for m1p, 25 for ruby
    # groundScanInd: 125 # 25/0.2
###E1
    # N_SCAN: 176 # 110/0.625
    # Horizon_SCAN: 208 # 130/0.625
    # ang_res_x: 0.625
    # ang_res_y: 0.625
###Airy
    # N_SCAN: 96 # 90/0.947
    # Horizon_SCAN: 900 # 360/0.4
    # # ang_bottom: 0
    # groundScanInd: 0
    # ang_res_x: 0.4
    # ang_res_y: 0.947
  image_projection:
    sensorMinimumRange: 0.0
    sensorMaxRange: 1000.
    z_thresh: -999. # 地面阈值，米
    segmentTheta: 5 # 聚类阈值，度 # 约大类越多
  noise_filter:
    # 注：通常只需要调整`curva_thresh`
    use_layer_num: 1 # 使用几圈计算深度差
    curva_thresh: 4.8 # 单边深度不连续角度(度)。减小提高噪点召回率。角分辨率越小，该值应该越小
    B_depth_jump_num : 0 # 单边深度跳变数量阈值，大于认为B类噪点。增大以保留物体边缘。
    A_depth_jump_num : 3 # 多边深度跳变数量阈值，大于认为A类噪点。增大以保留细小物体。
    drop_layer_num: 1 # 丢弃几圈。减小以保留边缘物体，0表示只滤除中心点。

    # 召回地图平面
    recall_map_plane_thresh: -0.8 # 邻域地图点平均夹角阈值，小于认为是平面，度。-1表示不召回平面

    # 召回scan平面
    radius: 2 #2 # 拟合平面用到的2D深度图邻域半径
    fit_plane_dist_thresh: 0.05 # scan平面拟合阈值，米，低于表示平面度高
    recall_scan_plane_thresh: 1 # 点云2D深度图邻域拟合平面距离阈值，度。
    pca_thresh: [0.01, 1, 0.0001] # scan平面PCA特征值阈值, 低于表示退化度高，分别对应最大值，次小值，最小值