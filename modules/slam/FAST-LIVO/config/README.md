# 介绍
## 配置
config路径包含了所有配置文件，包括RS自采数据集配置和HKU官方数据集配置

- calibration.yaml : 传感器内外参，目前只使用了 ADDTION_INFO字段
- RS_META.yaml : LIVO算法配置
- RS_LOAM.yaml : 滤除拖点算法配置

### rs_data_config
存放RS自采META数据集对应配置
- RS_META_indoor.yaml : 室内数据集参考配置
- RS_META_outdoor.yaml : 室外数据集参考配置

### hku_data_config（暂未支持）
存放官方数据集对应配置

## quick start
### 运行

```
roslaunch fast_livo mapping_meta.launch
rosbag play xxx.bag
```
### 查看结果
建图点云保存在PCD目录下，算法Log存储在Log目录下

- PCD
  - rgb_scan.pcd : 所有帧累计的RGB点云
  - intensity_map.pcd: LIO点云 （只在关闭VIO时生成）
- Log
  - sensor_buffer.txt: 传感器数据缓存情况
  - drop_msg.txt : 被丢弃的传感器数据

  - imu_prediction.txt : IMU积分结果
  - cloud_process_log.txt : 点云处理结果，包括拖点处理

  - lio_log.txt : LIO优化结果
  - vio_detect_log.txt : VIO优化结果
  - t_LIO.txt : LIO耗时
  - t_VIO.txt : VIO耗时
  - time_cost_log.txt : 各个模块耗时

  - utm_LV_opt_I_pose_W.txt: utm格式世界系LIO和VIO优化后IMU位姿
  - utm_V_opt_Cam_pose_W.txt: utm格式相机系VIO优化后Cam位姿
  - utm_V_opt_I_pose_W.txt: utm格式相机系VIO优化后IMU位姿
  - utm_L_opt_I_pose_W.txt: utm格式世界系LIO优化后IMU位姿

## 问题反馈
用户需要提供以下信息：
- 配置，包括'calibration.yaml', 'RS_META.yaml', 'RS_LOAM.yaml'
- 能复现问题的数据
- 算法Log，包括'Log'目录下所有文件
- PCD，包括'PCD'目录下所有文件