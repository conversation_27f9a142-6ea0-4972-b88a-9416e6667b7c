### ================================loop detection ================================
sub_frame_num: 10   # Number of for localmap | enlarge when FOV is small
cloud_voxel_size: 0.001 # downsample for single frame cloud
localmap_voxel_size: 0.1 # downsample for localmap
### ===get planes===
# init_voxel_map
voxel_size: 0.4      # Side length of each voxel during plane extraction | recommonded larger than localmap_voxel_size*3 | also ICP cloud resolution
voxel_init_num: 10   # Minimum number of points in the voxel for plane extraction | must smaller pow((than voxel_size/localmap_voxel_size),3)
plane_detection_thre: 0.01  # Maximum allowed value for the minimum eigenvalue of the covariance matrix to consider as a plane

# get_project_plane
plane_merge_normal_thre: 0.1 # Normal threshold for plane merging | reduce to reduce plane num
plane_merge_dis_thre: 0.3   # Distance threshold for plane merging | reduce to reduce plane num
plane_merge_center_dis_thre: 20.  # Center distance threshold for plane merging | enlarge to reduce plane num

# plane filter
plane_valid_min_num: 500 # Minimum number of points in a plane | enlarge to reduce plane num
plane_max_min_eigen_value: 0.0015 # Maximum allowed value for the minimum eigenvalue of the covariance matrix to consider as a plane | reduce to reduce plane num
valid_vertical_normal_thresh: 20 # -1 means disable, pos value means max allowed angle (deg) between normal and (x,y,0) or (0,0,1) | reduce to reduce plane num
proj_plane_num: 1    # Number of projection planes | use ground firstly | reduce to reduce plane num

### ===generate binary descriptor===
useful_corner_num: 500  # Maximum number of triangle vertices
proj_image_resolution: 0.1  # In meters | recommonded equal to 'localmap_voxel_size'
proj_image_high_inc: 0.1    # In meters | enlarge for high building, reduce for lower building
proj_dis_min: -1.0   # Minimum distance to the projection plane | enlarge to reduce binary num
proj_dis_max: 6.0  # Maximum distance to the projection plane | reduce to reduce binary num
summary_min_thre: 6  # Minimum number of points in one image grid | enlarge to reduce binary num
line_filter_enable: 0  # Enable line point filtering; turn off in environments lacking features (indoor and outdoor), otherwise enable to reduce computation
non_max_suppression_radius: 1.5      # Neighborhood range for non-maximum suppression
std_side_resolution: 0.2

### ===generate_btc (std descriptor)===
descriptor_near_num: 15   # Number of neighboring points to save for each point when generating triangles | enlarge to improve btc num
descriptor_min_len: 1     # Minimum edge length | reduce to improve btc num
descriptor_max_len: 30    # Maximum edge length | enlarge to improve btc num
triangle_resolution: 0.2  # Edge length amplification coefficient | not understood

### ===candidate search===
skip_near_num: 100   # Number of frames to skip; set to a positive value for intra-machine loops, and -1000000 for inter-machine loops
candidate_num: 50   # Maximum number of reference frames | enlarge to improve recall rate
similarity_threshold: 0.3   # BTC similarity coefficient | reduce to improve recall rate
rough_dis_threshold: 0.02   # Threshold coefficient for BTC edge length distance | enlarge to improve recall rate

### ===candidate verify===
# BTC candidate_verify
btc_verify_normal_threshold: 0.2  # Threshold for plane ICP and geometric validation: point-to-plane distance
btc_verify_dis_threshold: 0.5  # Distance threshold for point-to-plane validation
btc_overlap_threshold: 0.2

### ===node===
time_diff_thresh: 30 # (s) Valid loop when (Target time - Source time > time_diff_thresh)
enable_ICP_fix_pose: true # Enable ICP to fix pose
# plane Icp
icp_normal_threshold: 0.6  # ICP normal similarity threshold
plane_icp_dis_threshold: 5  # Distance threshold for point-to-plane validation
icp_dynamic_convege_ratio: 1. # positive means threshold decay with iteration, negetive means disable
icp_max_opti_count: 10
plane_icp_overlap_threshold: 0.15  # Threshold for plane-to-plane ICP to triggle loop. | reduce to improve recall rate
valid_delta_angle: 30 # (deg) | enlarge to improve recall rate
valid_delta_trans: 10 # (m) | enlarge to improve recall rate

# point search
verify_dis_NN_ratio: 0.5 # Calc threshold for point-to-point 1NN overlap, threshold = verify_dis_NN_ratio * voxel_size | enlarge to improve recall rate
point_icp_overlap_threshold: 0.1  # Threshold for point-to-point 1NN overlap | reduce to improve recall rate, recommended < plane_icp_overlap_threshold

# debug
visualization_scale: 0.5 # Marker scale ratio
save_pose: 1 # Save pose to txt
save_map: 1 # Save map to pcd
save_map_voxel_size: 0.03 # Voxel size of saved map
enable_ICP_test: false
test_source_path: "ac.pcd"
test_target_path: "truth.pcd"
ds_size: 0.05 # Downsample size
search_dis: 0.5 # Search radius

### ================================loop correction================================
loop_correction:
  add_LIO_factor: true # Add LIO factor, high frequence
  add_VIO_factor: true # Add VIO factor, high frequence
  add_Loop_factor: true # Add loop factor, low frequence
  add_key_factor: false # Add keyframe factor, low frequence
  add_IMU_factor: false
  update_time: 5  # Update time of ISAM

  loop_R_noise: 1e-4 # Variances, reduce when the loop is more accurate
  loop_t_noise: 1e-4 
  odom_R_noise: 1e-1
  odom_t_noise: 1e-1

  enable_dynamic_weight: false
  LIO_residual_base: 0.1 # (m) noise = odom_noise * residual/ LIO_noise_base
  VIO_residual_base: 5 # (pixel)