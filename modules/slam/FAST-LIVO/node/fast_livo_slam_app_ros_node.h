/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#pragma once

#include "ros_dep.h"
#include "laserMapping.h"
#include "preprocess.h"
#include "relocalization/relocalization.h"

namespace robosense {
namespace slam {

class FastLivoSlamApp {
 public:
  FastLivoSlamApp(const std::string cfg_path);

  ~FastLivoSlamApp() {
    LINFO << "Stopinging FastLivoSlamApp..." << REND;
    while (!init_done_) {
      std::this_thread::sleep_for(std::chrono::milliseconds(30));
    }
    Stop();
    LINFO << "FastLivoSlamApp stopped successfully..." << REND;
  }

  void Start() {
    std::lock_guard<std::mutex> lock(mutex_start_);
    init_done_ = false;
    LINFO << "Starting FastLivoSlamApp..." << REND;
    slam_ptr_->Start();
    LINFO << "FastLivoSlamApp started successfully..." << REND;
    init_done_ = true;
  }

  void Stop() {
    // LINFO<< "FastLivoSlamApp stopping"<<REND;
    // slam_ptr_->Stop(); //FIXME: 会析构两次，不必调用
    // LINFO<< "FastLivoSlamApp stopped"<<REND;
  }

  // 重启应用程序的方法
  void Restart(const std::string cfg_path) {
    std::lock_guard<std::mutex> lock(mutex_start_);
    init_done_ = false;
    LINFO << "Restarting FastLivoSlamApp..." << REND;
    resetStatics();
    
    // 创建新的SLAM实例
    slam_ptr_.reset(new slam::RsSlam());
    slam_ptr_->Init(cfg_path);
    slam_ptr_->RegisterGetLIVOResult(full_result_func_);
    slam_ptr_->SetRGBImageCallback(rgb_img_func_);
    slam_ptr_->SetNoiseImageCallback(noise_img_func_);
    slam_ptr_->SetRawImageCallback(rs_raw_img_func_);
    slam_ptr_->SetAllCloudImageCallback(rs_all_cloud_img_func_);
    slam_ptr_->SetLaserCloudFullResCallback(cloud_register_func_);
    slam_ptr_->SetSubVisualCloudCallback(visual_sub_map_func_);
    slam_ptr_->SetLaserCloudEffectCallback(cloud_effected_func_);
    slam_ptr_->SetLaserCloudMapCallback(laser_map_func_);
    slam_ptr_->SetOdomAftMappedCallback(odom_aft_mapped_func_);
    slam_ptr_->SetPathCallback(path_func_);
    slam_ptr_->RegisterGetBackendResult(be_result_func_);

    // 重新启动
    slam_ptr_->Start();
    
    LINFO << "FastLivoSlamApp restarted successfully" << REND;
    init_done_ = true;
  }

 private:
  std::string name() {
    return "FastLivoSlamApp";
  }
  std::atomic<bool> init_done_{false};
  std::mutex mutex_start_;

  // module
  YAML::Node cfg_node_;
  std::string cfg_path_;  // 存储配置路径
  shared_ptr<Preprocess> p_pre_;
  slam::RsSlam::Ptr slam_ptr_;

  // restart
  std::atomic<bool> need_restart_{false};
  std::atomic<bool> detected_restart_signal_{true};
  std::shared_ptr<relocalization::ReLocalization> relocalization_ptr_;

  // input
  void LidarCallback(const PointCloud2MsgsConstPtr msg);
  void ImuCallback(const ImuMsgsConstPtr imu_msg_ptr);
  void ImageCallback(const ImageMsgsConstPtr image_ptr);
  void CompressedImageCallback(const CompressedImageMsgsConstPtr image_ptr);
  void RestartSignalCallback(const EmptyMsgsConstPtr msg);  // 新增的重启信号回调

  // ROS to common
  void CloudRosToCommon(const PointCloud2MsgsConstPtr &msg, CloudPtr &ptr, double& cloud_abs_ts);
  slam::Imu::Ptr ImuRosToCommon(const ImuMsgsConstPtr &imu_msg_ptr);
  
  // ROS subscriber and publisher
#ifdef USE_ROS1
  ros::NodeHandle nh_;
  ros::Subscriber lidar_sub_, imu_sub_, image_sub_, compressed_image_sub_;
  ros::Subscriber restart_signal_sub_;  // 添加重启信号订阅器
  // image_transport::ImageTransport it_;
  image_transport::Publisher img_pub_, noise_img_pub_, raw_img_pub_, rs_all_cloud_img_pub_;
  ros::Publisher pubLaserCloudFullResRGB_, pubLaserCloudFullRes_,
                 pubVisualCloud_, pubSubVisualCloud_, pubLaserCloudEffect_,
                 pubLaserCloudMap_;
  ros::Publisher pubOdomAftMapped_;
  ros::Publisher pubPath_;
  tf::TransformBroadcaster br_;
#elif defined(USE_ROS2)
  rclcpp::Subscription<sensor_msgs::msg::PointCloud2>::SharedPtr lidar_sub_;
  rclcpp::Subscription<sensor_msgs::msg::Imu>::SharedPtr imu_sub_;
  rclcpp::Subscription<sensor_msgs::msg::Image>::SharedPtr image_sub_;
  rclcpp::Subscription<sensor_msgs::msg::CompressedImage>::SharedPtr compressed_image_sub_;
  rclcpp::Subscription<EmptyMsgs>::SharedPtr restart_signal_sub_;
  image_transport::Publisher img_pub_, noise_img_pub_, raw_img_pub_, rs_all_cloud_img_pub_;
  rclcpp::Publisher<PointCloud2Msgs>::SharedPtr pubLaserCloudFullResRGB_, pubLaserCloudFullRes_,
      pubVisualCloud_, pubSubVisualCloud_, pubLaserCloudEffect_,
      pubLaserCloudMap_;
  rclcpp::Publisher<nav_msgs::msg::Odometry>::SharedPtr pubOdomAftMapped_;
  rclcpp::Publisher<nav_msgs::msg::Path>::SharedPtr pubPath_;
  std::shared_ptr<tf2_ros::TransformBroadcaster> br_ptr_;
#endif
  PathMsgs path_;

  std::function<void(const relocalization::RestartInfo&)> restart_info_func_;
  std::function<void(const LIVOResult&)> full_result_func_;
  std::function<void(const cv::Mat&)> rgb_img_func_;
  std::function<void(const cv::Mat&)> noise_img_func_;
  std::function<void(const cv::Mat&)> rs_raw_img_func_;
  std::function<void(const cv::Mat&)> rs_all_cloud_img_func_;
  std::function<void(const pcl::PointCloud<pcl::PointXYZINormal>::Ptr &msg_ptr)> cloud_register_func_;
  std::function<void(const pcl::PointCloud<pcl::PointXYZINormal>::Ptr &msg_ptr)> visual_sub_map_func_;
  std::function<void(const pcl::PointCloud<pcl::PointXYZINormal>::Ptr &msg_ptr)> cloud_effected_func_;
  std::function<void(const pcl::PointCloud<pcl::PointXYZINormal>::Ptr &msg_ptr)> laser_map_func_;
  std::function<void(const Pose &msg_ptr)> odom_aft_mapped_func_;
  std::function<void(const Pose &msg_ptr)> path_func_;
  std::function<void(const BackendResult&)> be_result_func_;

  // statics in previous version
  double LidarCallback_last_sys_t_ = 0;
  double LidarCallback_last_header_t_ = 0;
  double ImuCallback_last_sys_t_ = 0;
  double ImuCallback_last_header_t_ = 0;
  bool imu_init_done_ = false;
  void resetStatics()
  {
    LidarCallback_last_sys_t_ = 0;
    LidarCallback_last_header_t_ = 0;
    ImuCallback_last_sys_t_ = 0;
    ImuCallback_last_header_t_ = 0;
    imu_init_done_ = false;
    path_ = PathMsgs();
  }

};

} // namespace slam
} // namespace robosense
