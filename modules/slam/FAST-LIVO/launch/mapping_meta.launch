<launch>
<!-- Launch file for Robosense MetaS( Active Camera ) LiDAR -->

	<arg name="rviz" default="true" />

	<node pkg="rs_slam_node" type="fastlivo_mapping" name="laserMapping" output="screen" >
	</node>
	
	<group if="$(arg rviz)">
		<node launch-prefix="nice" pkg="rviz" type="rviz" name="rviz" args="-d $(find rs_slam_node)/rviz_cfg/meta.rviz" />
	</group>
launch-prefix="gdb -ex run --args" launch-prefix="valgrind --leak-check=full --show-leak-kinds=all"

</launch> 	
