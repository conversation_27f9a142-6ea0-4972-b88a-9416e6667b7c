#include "loop_detection/btc.h"

void load_config_setting(std::string &config_file,
                         ConfigSetting &config_setting) {
  YAML::Node node = YAML::LoadFile(config_file);
  auto& cs = config_setting;

  yamlRead<int>(node, "sub_frame_num", cs.sub_frame_num_,10);
  yamlRead<float>(node, "localmap_voxel_size", cs.localmap_voxel_size_, 0.1);
  yamlRead<float>(node, "cloud_voxel_size", cs.cloud_voxel_size_, 0.05);

  // for binary descriptor
  yamlRead<int>(node, "useful_corner_num", cs.useful_corner_num_,30);
  yamlRead<float>(node, "plane_merge_normal_thre", cs.plane_merge_normal_thre_,0.1);
  yamlRead<float>(node, "plane_merge_dis_thre", cs.plane_merge_dis_thre_,0.3);
  yamlRead<float>(node, "plane_merge_center_dis_thre", cs.plane_merge_center_dis_thre_,10.);
  yamlRead<float>(node, "plane_detection_thre", cs.plane_detection_thre_,0.01);
  yamlRead<float>(node, "voxel_size", cs.voxel_size_,1.0);
  yamlRead<int>(node, "voxel_init_num", cs.voxel_init_num_,1);
  yamlRead<int>(node, "proj_plane_num", cs.proj_plane_num_,1);
  yamlRead<float>(node, "proj_image_resolution", cs.proj_image_resolution_,0.5);
  yamlRead<float>(node, "proj_image_high_inc", cs.proj_image_high_inc_,0.5);
  yamlRead<float>(node, "proj_dis_min", cs.proj_dis_min_,0);
  yamlRead<float>(node, "proj_dis_max", cs.proj_dis_max_,5);
  yamlRead<float>(node, "summary_min_thre", cs.summary_min_thre_,10);
  yamlRead<int>(node, "line_filter_enable", cs.line_filter_enable_,0);
  yamlRead<int>(node, "plane_valid_min_num", cs.plane_valid_min_num_,50);
  yamlRead<float>(node, "plane_max_min_eigen_value", cs.plane_max_min_eigen_value_,0.1);
  yamlRead<float>(node, "valid_vertical_normal_thresh", cs.valid_vertical_normal_thresh_,-1.);
  cs.valid_vertical_normal_thresh_ =
      std::min(cs.valid_vertical_normal_thresh_, 45.f);
  // std descriptor
  yamlRead<float>(node, "descriptor_near_num", cs.descriptor_near_num_, 10);
  yamlRead<float>(node, "descriptor_min_len", cs.descriptor_min_len_,1);
  yamlRead<float>(node, "descriptor_max_len", cs.descriptor_max_len_,10);
  yamlRead<float>(node, "non_max_suppression_radius", cs.non_max_suppression_radius_,3.0);
  yamlRead<float>(node, "std_side_resolution", cs.std_side_resolution_,0.2);

  // candidate search
  yamlRead<int>(node, "skip_near_num", cs.skip_near_num_,20);
  yamlRead<int>(node, "candidate_num", cs.candidate_num_,50);
  yamlRead<float>(node, "rough_dis_threshold", cs.rough_dis_threshold_,0.03);
  yamlRead<float>(node, "similarity_threshold", cs.similarity_threshold_,0.7);

  yamlRead<float>(node, "btc_overlap_threshold", cs.btc_overlap_threshold_,0.5);
  yamlRead<float>(node, "btc_verify_normal_threshold", cs.btc_verify_normal_threshold_,0.1);
  yamlRead<float>(node, "btc_verify_dis_threshold", cs.btc_verify_dis_threshold_,0.3);

  yamlRead<float>(node, "time_diff_thresh", cs.time_diff_thresh_,10.);
  yamlRead<bool>(node, "enable_ICP_fix_pose", cs.enable_ICP_fix_pose_,false);

  yamlRead<float>(node, "icp_normal_threshold", cs.icp_normal_threshold_,0.1);
  yamlRead<float>(node, "plane_icp_dis_threshold", cs.icp_dis_threshold_,0.3);
  yamlRead<float>(node, "icp_dynamic_convege_ratio", cs.icp_dynamic_convege_ratio_,0.1);
  yamlRead<int>(node, "icp_max_opti_count", cs.icp_max_opti_count_,50);
  yamlRead<float>(node, "plane_icp_overlap_threshold", cs.plane_icp_overlap_threshold_,0.1);
  yamlRead<float>(node, "valid_delta_angle", cs.valid_delta_angle_, 360.);
  yamlRead<float>(node, "valid_delta_trans", cs.valid_delta_trans_, 999.);
  
  yamlRead<std::string>(node, "test_source_path", cs.test_source_path_,"");
  yamlRead<std::string>(node, "test_target_path", cs.test_target_path_,"");
  yamlRead<bool>(node, "enable_ICP_test", cs.enable_ICP_test_,false);
  yamlRead<float>(node, "ds_size", cs.ds_size_, 999.);
  yamlRead<float>(node, "search_dis", cs.search_dis_, 999.);


  yamlRead<float>(node, "verify_dis_NN_ratio", cs.verify_dis_NN_ratio_,1.);
  yamlRead<float>(node, "point_icp_overlap_threshold", cs.point_icp_overlap_threshold_,0.5);
  
  // 
  yamlRead<int>(node, "skip_near_num", cs.skip_near_num_,20);
  yamlRead<float>(node, "visualization_scale", cs.visualization_scale_,1.0);

  // 
  yamlRead<int>(node, "save_pose", cs.save_pose_,-1);
  yamlRead<int>(node, "save_map", cs.save_map_,-1);
  yamlRead<float>(node, "save_map_voxel_size", cs.save_map_voxel_size_,0.03);

  // 
  YAML::Node LC_node = node["loop_correction"];
  yamlRead<bool>(LC_node, "add_LIO_factor", cs.add_LIO_factor_,true);
  yamlRead<bool>(LC_node, "add_VIO_factor", cs.add_VIO_factor_,true);
  yamlRead<bool>(LC_node, "add_Loop_factor", cs.add_Loop_factor_,true);
  yamlRead<int>(LC_node, "update_time", cs.update_time_,5);
  yamlRead<double>(LC_node, "loop_R_noise", cs.loop_R_noise_,1e-1);
  yamlRead<double>(LC_node, "loop_t_noise", cs.loop_t_noise_,1e-1);
  yamlRead<double>(LC_node, "odom_R_noise", cs.odom_R_noise_,1e-6);
  yamlRead<double>(LC_node, "odom_t_noise", cs.odom_t_noise_,1e-4);

  yamlRead<bool>(LC_node, "enable_dynamic_weight", cs.enable_dynamic_weight_,true);
  yamlRead<double>(LC_node, "LIO_residual_base", cs.LIO_residual_base_,1.);
  yamlRead<double>(LC_node, "VIO_residual_base", cs.VIO_residual_base_,1.);
  
  LBLUE << "Sucessfully load config file:" << config_file << REND;
  // if (print_debug_info_) {
    // LINFO << node << REND;
  // }
}

void down_sampling_voxel(RGBNCloud &pl_feat, double voxel_size) {
  if (voxel_size < 0.01) {
    LERROR << "voxel_size is too small, please check the config file" << REND;
    return;
  }
  std::unordered_map<VOXEL_LOC, M_POINT> voxel_map;
  uint plsize = pl_feat.size();

  for (uint i = 0; i < plsize; i++) {
    RGBNPoint &p_c = pl_feat[i];
    float loc_xyz[3];
    for (int j = 0; j < 3; j++) {
      loc_xyz[j] = p_c.data[j] / voxel_size;
      if (loc_xyz[j] < 0) {
        loc_xyz[j] -= 1.0;
      }
    }

    VOXEL_LOC position((int64_t)loc_xyz[0], (int64_t)loc_xyz[1],
                       (int64_t)loc_xyz[2]);
    auto iter = voxel_map.find(position);
    if (iter != voxel_map.end()) {
      iter->second.xyz[0] += p_c.x;
      iter->second.xyz[1] += p_c.y;
      iter->second.xyz[2] += p_c.z;
      // iter->second.intensity += p_c.intensity;
      iter->second.r += p_c.r;
      iter->second.g += p_c.g;
      iter->second.b += p_c.b;
      iter->second.count++;
    } else {
      M_POINT anp;
      anp.xyz[0] = p_c.x;
      anp.xyz[1] = p_c.y;
      anp.xyz[2] = p_c.z;
      // anp.intensity = p_c.intensity;
      anp.r = p_c.r;
      anp.g = p_c.g;
      anp.b = p_c.b;
      anp.count = 1;
      voxel_map[position] = anp;
    }
  }
  plsize = voxel_map.size();
  pl_feat.clear();
  pl_feat.resize(plsize);

  uint i = 0;
  for (auto iter = voxel_map.begin(); iter != voxel_map.end(); ++iter) {
    pl_feat[i].x = iter->second.xyz[0] / iter->second.count;
    pl_feat[i].y = iter->second.xyz[1] / iter->second.count;
    pl_feat[i].z = iter->second.xyz[2] / iter->second.count;
    // pl_feat[i].intensity = iter->second.intensity / iter->second.count;
    pl_feat[i].r = iter->second.r / iter->second.count;
    pl_feat[i].g = iter->second.g / iter->second.count;
    pl_feat[i].b = iter->second.b / iter->second.count;
    i++;
  }
}
double binary_similarity(const BinaryDescriptor &b1,
                         const BinaryDescriptor &b2) {
  double dis = 0;
  for (size_t i = 0; i < b1.occupy_array_.size(); i++) {
    // to be debug hanming distance
    if (b1.occupy_array_[i] == true && b2.occupy_array_[i] == true) {
      dis += 1;
    }
  }
  return 2 * dis / (b1.summary_ + b2.summary_);
}

bool binary_greater_sort(BinaryDescriptor a, BinaryDescriptor b) {
  return (a.summary_ > b.summary_);
}

bool plane_greater_sort(std::shared_ptr<Plane> plane1,
                        std::shared_ptr<Plane> plane2) {
  return plane1->points_size_ > plane2->points_size_;
}

bool plane_normal_z_greater_sort(std::shared_ptr<Plane> plane1,
                                 std::shared_ptr<Plane> plane2) {
  return abs(plane1->normal_.z()) > abs(plane2->normal_.z());
}

void OctoTree::init_octo_tree() {
  if (voxel_points_.size() > config_setting_.voxel_init_num_) {
    init_plane();
  }
}

void OctoTree::init_plane() {
  plane_ptr_->covariance_ = Eigen::Matrix3d::Zero();
  plane_ptr_->center_ = Eigen::Vector3d::Zero();
  plane_ptr_->normal_ = Eigen::Vector3d::Zero();
  plane_ptr_->points_size_ = voxel_points_.size();
  plane_ptr_->radius_ = 0;
  for (auto pi : voxel_points_) {
    plane_ptr_->covariance_ += pi * pi.transpose();
    plane_ptr_->center_ += pi;
  }
  plane_ptr_->center_ = plane_ptr_->center_ / plane_ptr_->points_size_;
  plane_ptr_->covariance_ =
      plane_ptr_->covariance_ / plane_ptr_->points_size_ -
      plane_ptr_->center_ * plane_ptr_->center_.transpose();
  Eigen::EigenSolver<Eigen::Matrix3d> es(plane_ptr_->covariance_);
  Eigen::Matrix3cd evecs = es.eigenvectors();
  Eigen::Vector3cd evals = es.eigenvalues();
  Eigen::Vector3d evalsReal;
  evalsReal = evals.real();
  Eigen::Matrix3d::Index evalsMin, evalsMax;
  evalsReal.rowwise().sum().minCoeff(&evalsMin);
  evalsReal.rowwise().sum().maxCoeff(&evalsMax);
  int evalsMid = 3 - evalsMin - evalsMax;
  if (evalsReal(evalsMin) < config_setting_.plane_detection_thre_) {
    plane_ptr_->normal_ << evecs.real()(0, evalsMin), evecs.real()(1, evalsMin),
        evecs.real()(2, evalsMin);
    plane_ptr_->min_eigen_value_ = evalsReal(evalsMin);
    plane_ptr_->radius_ = sqrt(evalsReal(evalsMax));
    plane_ptr_->is_plane_ = true;

    plane_ptr_->d_ = -(plane_ptr_->normal_(0) * plane_ptr_->center_(0) +
                       plane_ptr_->normal_(1) * plane_ptr_->center_(1) +
                       plane_ptr_->normal_(2) * plane_ptr_->center_(2));
    plane_ptr_->p_center_.x = plane_ptr_->center_(0);
    plane_ptr_->p_center_.y = plane_ptr_->center_(1);
    plane_ptr_->p_center_.z = plane_ptr_->center_(2);
    plane_ptr_->p_center_.normal_x = plane_ptr_->normal_(0);
    plane_ptr_->p_center_.normal_y = plane_ptr_->normal_(1);
    plane_ptr_->p_center_.normal_z = plane_ptr_->normal_(2);
  } else {
    plane_ptr_->is_plane_ = false;
  }
}

double calc_triangle_dis(
    const std::vector<std::pair<BTC, BTC>> &match_std_list) {
  double mean_triangle_dis = 0;
  for (auto var : match_std_list) {
    mean_triangle_dis += (var.first.triangle_ - var.second.triangle_).norm() /
                         var.first.triangle_.norm();
  }
  if (match_std_list.size() > 0) {
    mean_triangle_dis = mean_triangle_dis / match_std_list.size();
  } else {
    mean_triangle_dis = -1;
  }
  return mean_triangle_dis;
}

double calc_binary_similaity(
    const std::vector<std::pair<BTC, BTC>> &match_std_list) {
  double mean_binary_similarity = 0;
  for (auto var : match_std_list) {
    mean_binary_similarity +=
        (binary_similarity(var.first.binary_A_, var.second.binary_A_) +
         binary_similarity(var.first.binary_B_, var.second.binary_B_) +
         binary_similarity(var.first.binary_C_, var.second.binary_C_)) /
        3;
  }
  if (match_std_list.size() > 0) {
    mean_binary_similarity = mean_binary_similarity / match_std_list.size();
  } else {
    mean_binary_similarity = -1;
  }
  return mean_binary_similarity;
}

int BtcDescManager::GenerateBtcDescs(
    const RGBNCloudPtr &input_cloud, const int frame_id,
    std::vector<BTC> &btcs_vec) {
  // step1, voxelization and plane dection
  auto t_iv = omp_get_wtime();
  std::unordered_map<VOXEL_LOC, OctoTree *> voxel_map;
  init_voxel_map(input_cloud, voxel_map);
  double t_iv_cost = (omp_get_wtime() - t_iv) * 1000;

  // step2, get and push plane cloud into database
  RGBNCloudPtr plane_cloud(new RGBNCloud);
  auto t_gp = omp_get_wtime();
  get_plane(voxel_map, plane_cloud);
  double t_gp_cost = (omp_get_wtime() - t_gp) * 1000;

  plane_cloud_vec_.push_back(plane_cloud);
  if (print_debug_info_) {
    LBLUE << "[Description][get_plane] planes size:" << plane_cloud->size()
          << " init_voxel_map(ms): " << t_iv_cost
          << ", get_project_plane(ms): " << t_gp_cost << REND;
  }
  if(plane_cloud->size() < 10){
    LERROR << "[Description][get_plane] No plane! return" << REND;
    return -1;
  }

  // step3, extraction binary descriptors
  std::vector<std::shared_ptr<Plane>> proj_plane_list;
  std::vector<std::shared_ptr<Plane>> merge_plane_list;
  std::vector<std::shared_ptr<Plane>> large_size_vec;
  int min_size = std::min(config_setting_.plane_valid_min_num_,
                          int(input_cloud->size() * 0.05));
  std::vector<std::shared_ptr<Plane>> toward_xy;
  std::vector<std::shared_ptr<Plane>> toward_z;
  std::vector<std::shared_ptr<Plane>> filtered_planes;
  int z_size, xy_size;
  get_project_plane(voxel_map, proj_plane_list);

  auto t_mp = omp_get_wtime();
  if (proj_plane_list.size() == 0) {
    // TODO(rum): need better solution
    std::shared_ptr<Plane> single_plane(new Plane);
    single_plane->normal_ << 0, 0, 1;
    single_plane->center_ << input_cloud->points[0].x, input_cloud->points[0].y,
        input_cloud->points[0].z;
    merge_plane_list.push_back(single_plane);
  } else {
    // TODO(rum): not understood
    sort(proj_plane_list.begin(), proj_plane_list.end(), plane_greater_sort);
    merge_plane(proj_plane_list, merge_plane_list);
    // RS. filter planes
    // size large enough
    for (auto i : merge_plane_list) {
      if (i->points_size_ > min_size &&
          i->min_eigen_value_ < config_setting_.plane_max_min_eigen_value_) {
        large_size_vec.push_back(i);
      }
    }
    // strong vertical and horizontal
    for (auto i : large_size_vec) {
      double angle_z = acos(i->normal_.z()) * 180 / M_PI;
      if (angle_z < config_setting_.valid_vertical_normal_thresh_) {
        toward_z.push_back(i);
      } else if (angle_z >
                 (90 - config_setting_.valid_vertical_normal_thresh_)) {
        toward_xy.push_back(i);
      }
    }
    std::sort(toward_z.begin(), toward_z.end(), plane_greater_sort);
    int exp_z_size = config_setting_.proj_plane_num_ * 2;
    for (auto i : toward_z) {
      filtered_planes.push_back(i);
      if (filtered_planes.size() >= exp_z_size) {
        break;
      }
    }
    z_size = filtered_planes.size();
    int exp_xy_size;
    if (filtered_planes.size() <= config_setting_.proj_plane_num_) {
      exp_xy_size = config_setting_.proj_plane_num_ * 3;
    } else {
      exp_xy_size = config_setting_.proj_plane_num_ * 2;
    }
    std::sort(toward_xy.begin(), toward_xy.end(), plane_greater_sort);
    for (auto i : toward_xy) {
      filtered_planes.push_back(i);
      if (filtered_planes.size() >= exp_xy_size) {
        break;
      }
    }
    xy_size = filtered_planes.size() - z_size;
  }
  if (print_debug_info_) {
    LBLUE << "[Description][merge_plane] proj_plane_list: "
          << proj_plane_list.size()
          << " merge_plane_list: " << merge_plane_list.size()
          << " large_size_vec: " << large_size_vec.size()
          << "(xy=" << toward_xy.size() << ", z=" << toward_z.size()
          << ")(size limit=" << min_size
          << ") filtered_planes: " << filtered_planes.size()
          << "(xy=" << xy_size << ", z=" << z_size << ")"
          << " merge_plane(ms): " << (omp_get_wtime() - t_mp) * 1000 << REND;
  }

  auto t_be = omp_get_wtime();
  std::vector<BinaryDescriptor> binary_list;
  binary_extractor(filtered_planes, input_cloud, binary_list); 
  history_binary_list_.push_back(binary_list);
  // corner_cloud_vec_.push_back(corner_points);
  if (print_debug_info_) {
    LBLUE << "[Description][binary_extractor] binary size:"
          << binary_list.size()
          << " cost(ms): " << (omp_get_wtime() - t_be) * 1000 << REND;
  }

  // step4, generate stable triangle descriptors
  auto t_gb = omp_get_wtime();
  btcs_vec.clear();
  generate_btc(binary_list, frame_id, btcs_vec);
  if (print_debug_info_) {
    LBLUE << "[Description][generate_btc] btcs size:" << btcs_vec.size()
          << " cost(ms): " << (omp_get_wtime() - t_gb) * 1000 << REND;
  }
  // step5, clear memory
  for (auto iter = voxel_map.begin(); iter != voxel_map.end(); iter++) {
    delete (iter->second);
  }

  // debug
  proj_plane_ = std::make_shared<std::vector<std::shared_ptr<Plane>>>(merge_plane_list);
  valid_proj_plane_ = std::make_shared<std::vector<std::shared_ptr<Plane>>>(filtered_planes);
  return 1;
}

void BtcDescManager::SearchLoop(
    const std::vector<BTC> &btcs_vec, std::pair<int, double> &loop_result,
    std::pair<Eigen::Vector3d, Eigen::Matrix3d> &loop_transform,
    std::vector<std::pair<BTC, BTC>> &loop_std_pair) {
  if (btcs_vec.size() == 0) {
    LERROR << "No STDescs! return" << REND;
    loop_result = std::pair<int, double>(-1, 0);
    return;
  }
  // step1, select candidates, default number 50
  double t1 = std::chrono::time_point_cast<std::chrono::nanoseconds>(std::chrono::high_resolution_clock::now()).time_since_epoch().count() * 1e-3;
  std::vector<BTCMatchList> candidate_matcher_vec;
  candidate_selector(btcs_vec, candidate_matcher_vec);
  double t2 = std::chrono::time_point_cast<std::chrono::nanoseconds>(std::chrono::high_resolution_clock::now()).time_since_epoch().count() * 1e-3;

  // step2, select best candidates from rough candidates
  double best_score = 0;
  int best_candidate_id = -1;
  int triggle_candidate = -1;
  std::pair<Eigen::Vector3d, Eigen::Matrix3d> best_transform;
  std::vector<std::pair<BTC, BTC>> best_sucess_match_vec;
  // TODO: use statistics to select the best candidate, and break faster
  // std::pair<Eigen::Vector3d, Eigen::Matrix3d> second_best_transform;
  // std::vector<std::pair<BTC, BTC>> second_best_sucess_match_vec;
  for (size_t i = 0; i < candidate_matcher_vec.size(); i++) {
    double verify_score = -1;
    std::pair<Eigen::Vector3d, Eigen::Matrix3d> relative_pose;
    std::vector<std::pair<BTC, BTC>> sucess_match_vec;
    candidate_verify(candidate_matcher_vec[i], verify_score, relative_pose,
                     sucess_match_vec);
    if (print_debug_info_) {
      LBLUE << "[Retrieval] try frame: "
                << candidate_matcher_vec[i].match_id_.second << ", rough size: "
                << candidate_matcher_vec[i].match_list_.size()
                << ", score:" << verify_score << REND;
    }

    if (verify_score > best_score) {
      best_score = verify_score;
      best_candidate_id = candidate_matcher_vec[i].match_id_.second;
      best_transform = relative_pose;
      best_sucess_match_vec = sucess_match_vec;
      triggle_candidate = i;
      // LBLUE << "[Retrieval] best candidate:" << best_candidate_id
      //           << ", score:" << best_score << REND;
    }
  }
  double t3 = std::chrono::time_point_cast<std::chrono::nanoseconds>(std::chrono::high_resolution_clock::now()).time_since_epoch().count() * 1e-3;

  if (print_debug_info_) {
    LBLUE << "[Retrieval] best candidate: " << best_candidate_id
              << ", score: " << best_score << REND;
    LBLUE << "[Retrieval][Time] candidate selector: " << time_inc(t2, t1)
              << " ms, candidate verify: " << time_inc(t3, t2) << "ms"
              << REND;
  }

  // TODO(rum): 额外验证条件 1.最优和次优接近（位姿、打分、有效点数等） 2.位姿变化是否过大 3.与上次回环结果比较接近

  if (best_score > config_setting_.btc_overlap_threshold_) {
    loop_result = std::pair<int, double>(best_candidate_id, best_score);
    loop_transform = best_transform;
    loop_std_pair = best_sucess_match_vec;
  } else {
    loop_result = std::pair<int, double>(-1, 0);
  }
}

void BtcDescManager::AddBtcDescs(const std::vector<BTC> &btcs_vec) {
  // update frame id
  for (auto single_std : btcs_vec) {
    // calculate the position of single std
    BTC_LOC position;
    position.x = (int)(single_std.triangle_[0] + 0.5);
    position.y = (int)(single_std.triangle_[1] + 0.5);
    position.z = (int)(single_std.triangle_[2] + 0.5);
    auto iter = data_base_.find(position);
    if (iter != data_base_.end()) {
      data_base_[position].push_back(single_std);
    } else {
      std::vector<BTC> descriptor_vec;
      descriptor_vec.push_back(single_std);
      data_base_[position] = descriptor_vec;
    }
  }
  return;
}

void BtcDescManager::PlaneGeomrtricIcp(
    const RGBNCloudPtr &source_cloud, const RGBNCloudPtr &target_cloud,
    std::pair<Eigen::Vector3d, Eigen::Matrix3d> &transform, double &overlap,
    double &residual) {
  pcl::KdTreeFLANN<pcl::PointXYZ>::Ptr kd_tree(
      new pcl::KdTreeFLANN<pcl::PointXYZ>);
  pcl::PointCloud<pcl::PointXYZ>::Ptr input_cloud(
      new pcl::PointCloud<pcl::PointXYZ>);
  input_cloud->reserve(target_cloud->size());
  for (size_t i = 0; i < target_cloud->size(); i++) {
    pcl::PointXYZ pi;
    pi.x = target_cloud->points[i].x;
    pi.y = target_cloud->points[i].y;
    pi.z = target_cloud->points[i].z;
    input_cloud->push_back(pi);
  }
  kd_tree->setInputCloud(input_cloud);

  // ceres solver
  ceres::Solver::Options options;
  options.linear_solver_type = ceres::DENSE_QR; //ceres::SPARSE_NORMAL_CHOLESKY;
  options.max_num_iterations = 4;
  options.minimizer_progress_to_stdout = false;
  if (config_setting_.enable_ICP_test_) {
    options.minimizer_progress_to_stdout = true;
  }
  // options.function_tolerance = 1e-6;  // offcial default: 1e-6
  // options.gradient_tolerance = 1e-4 * options.function_tolerance;
  ceres::Solver::Summary summary;
  double residual_last{0.};
  Eigen::Matrix3d rot_last;
  Eigen::Vector3d t_last;

  // iterate
  int source_size = source_cloud->size();
  for (size_t opt_cnt = 0; opt_cnt < config_setting_.icp_max_opti_count_;
    ++opt_cnt) {
    Eigen::Matrix3d rot = transform.second;
    Eigen::Quaterniond q(rot);
    Eigen::Vector3d t = transform.first; // T_target_source
    double para_q[4] = {q.x(), q.y(), q.z(), q.w()};
    double para_t[3] = {t(0), t(1), t(2)};
    
    ceres::Problem problem;
    ceres::Manifold *quaternion_manifold = new ceres::EigenQuaternionManifold;
    problem.AddParameterBlock(para_q, 4, quaternion_manifold);
    problem.AddParameterBlock(para_t, 3);
    std::vector<int> pointIdxNKNSearch(1);
    std::vector<float> pointNKNSquaredDistance(1);
    int useful_match = 0;
    Eigen::Vector3d pi, ni, tpi, tni, normal_inc, normal_add;
    pcl::PointXYZ use_search_point;
    Eigen::Vector3d curr_point, curr_normal;

    float ratio = 1.;
    if (config_setting_.icp_dynamic_convege_ratio_ > 0)
      ratio = (opt_cnt + 1) * config_setting_.icp_dynamic_convege_ratio_;
    float max_plane_dis = config_setting_.icp_dis_threshold_ / ratio;
    max_plane_dis = std::max(max_plane_dis, config_setting_.voxel_size_ * 0.5f);
    float max_point_dis = max_plane_dis * 1.414f;
    float max_normal_diff = config_setting_.icp_normal_threshold_ / ratio;
    max_normal_diff = std::max(max_normal_diff, 0.2f);
    for (size_t i = 0; i < source_size; i++) {
      const RGBNPoint &searchPoint = source_cloud->points[i];
      pi <<searchPoint.x, searchPoint.y, searchPoint.z;
      pi = rot * pi + t;
      use_search_point.x = pi[0];
      use_search_point.y = pi[1];
      use_search_point.z = pi[2];
      ni << searchPoint.normal_x, searchPoint.normal_y, searchPoint.normal_z;
      ni = rot * ni;
      if (kd_tree->nearestKSearch(use_search_point, 1, pointIdxNKNSearch,
                                  pointNKNSquaredDistance) > 0 &&
          pointNKNSquaredDistance[0] < max_plane_dis * max_plane_dis) {
        RGBNPoint &nearstPoint = target_cloud->points[pointIdxNKNSearch[0]];
        tpi << nearstPoint.x, nearstPoint.y, nearstPoint.z;
        tni << nearstPoint.normal_x, nearstPoint.normal_y, nearstPoint.normal_z;
        normal_inc = ni - tni;
        normal_add = ni + tni;
        double point_to_point_dis = (pi - tpi).norm();
        double point_to_plane = fabs(tni.transpose() * (pi - tpi));
        if ((normal_inc.norm() < max_normal_diff ||
             normal_add.norm() < max_normal_diff) &&
            point_to_plane < max_plane_dis &&
            point_to_point_dis < max_point_dis) {
          useful_match++;
          curr_point << source_cloud->points[i].x, source_cloud->points[i].y,
              source_cloud->points[i].z;
          curr_normal << source_cloud->points[i].normal_x,
              source_cloud->points[i].normal_y,
              source_cloud->points[i].normal_z;
          ceres::CostFunction *cost_function =
              // PlaneSolver::Create(pi, ni, tpi, tni); // FIXME(rum): should be this?
              PlaneSolver::Create(curr_point, curr_normal, tpi, tni);
          ceres::LossFunction *loss_function =
              new ceres::HuberLoss(3); // nullptr;
          problem.AddResidualBlock(cost_function, loss_function, para_q,
                                   para_t);
        }
      }
    }
    overlap = (double)useful_match / source_cloud->size();

    ceres::Solve(options, &problem, &summary);
    Eigen::Quaterniond q_opt(para_q[3], para_q[0], para_q[1], para_q[2]);
    transform.first << para_t[0], para_t[1], para_t[2];
    transform.second = q_opt.toRotationMatrix();
    residual = summary.final_cost;
    double res_delta = abs(residual_last - residual);
    double rot_delta = abs(rot.trace() - rot_last.trace());
    double t_delta = abs(t.norm() - t_last.norm());
    bool conditon_res =
        res_delta < 1e-1 && residual < config_setting_.icp_dis_threshold_;
    bool conditon_delta = rot_delta < 1e-4 && t_delta < 1e-2;
    if (config_setting_.enable_ICP_test_) {
      RGBNCloudPtr aligned_source(new RGBNCloud);
      Eigen::Matrix4d T_target_source = Eigen::Matrix4d::Identity();
      T_target_source.block<3, 3>(0, 0) = transform.second;
      T_target_source.block<3, 1>(0, 3) = transform.first;
      pcl::transformPointCloud(*source_cloud, *aligned_source, T_target_source);
      pcl::PCDWriter pcd_writer;
      pcd_writer.writeBinary(
          "/home/<USER>/loop_BTC_aligned_source" + std::to_string(opt_cnt) + " " +
              std::to_string(overlap) + " " + std::to_string(residual) + ".pcd",
          *aligned_source);
      LERROR << "ICP param: ratio: " << ratio
             << " max_plane_dis: " << max_plane_dis
             << " max_normal_diff: " << max_normal_diff
             << " init cost: " << summary.initial_cost
             << " residual(m): " << residual << " overlap: " << overlap
             << " rot.trace: " << rot.trace() << " t.norm: " << t.norm()
             << " res_delta: " << res_delta << " trace_delta: " << rot_delta
             << " t_delta: " << t_delta << " conditon_res: " << conditon_res
             << " conditon_delta: " << conditon_delta << REND;
    }
    if (opt_cnt && (conditon_res || conditon_delta)) {
      LINFO << "[LC][PlaneGeomrtricIcp] converge. opt_cnt: " << opt_cnt
            << " max_plane_dis: " << max_plane_dis
            << " residual(m): " << residual << " overlap: " << overlap
            << " rot.trace: " << rot.trace() << " t.norm: " << t.norm()
            << " res_delta: " << res_delta << " trace_delta: " << rot_delta
            << " t_delta: " << t_delta << " conditon_res: " << conditon_res
            << " conditon_delta: " << conditon_delta << REND;
      break;
    }
    residual_last = residual;
    rot_last = rot;
    t_last = t;
  }
}

void BtcDescManager::init_voxel_map(
    const RGBNCloudPtr &input_cloud,
    std::unordered_map<VOXEL_LOC, OctoTree *> &voxel_map) {
  uint plsize = input_cloud->size();
  for (uint i = 0; i < plsize; i++) {
    Eigen::Vector3d p_c(input_cloud->points[i].x, input_cloud->points[i].y,
                        input_cloud->points[i].z);
    double loc_xyz[3];
    for (int j = 0; j < 3; j++) {
      loc_xyz[j] = p_c[j] / config_setting_.voxel_size_;
      if (loc_xyz[j] < 0) {
        loc_xyz[j] -= 1.0;
      }
    }
    VOXEL_LOC position((int64_t)loc_xyz[0], (int64_t)loc_xyz[1],
                       (int64_t)loc_xyz[2]);
    auto iter = voxel_map.find(position);
    if (iter != voxel_map.end()) {
      voxel_map[position]->voxel_points_.push_back(p_c);
    } else {
      OctoTree *octo_tree = new OctoTree(config_setting_);
      voxel_map[position] = octo_tree;
      voxel_map[position]->voxel_points_.push_back(p_c);
    }
  }
  std::vector<std::unordered_map<VOXEL_LOC, OctoTree *>::iterator> iter_list;
  std::vector<size_t> index;
  size_t i = 0;
  for (auto iter = voxel_map.begin(); iter != voxel_map.end(); ++iter) {
    index.push_back(i);
    i++;
    iter_list.push_back(iter);
    // iter->second->init_octo_tree();
  }
  // FIXME(rum): bool字段并行有风险
  std::for_each(
#if ENABLE_PARALELL_FOR_LOOP
      std::execution::par_unseq, index.begin(), index.end(),
#else
      std::execution::seq, index.begin(), index.end(),
#endif
      [&](const size_t &i) { iter_list[i]->second->init_octo_tree(); });
}

void BtcDescManager::get_plane(
    const std::unordered_map<VOXEL_LOC, OctoTree *> &voxel_map,
    RGBNCloudPtr &plane_cloud) {
  for (auto iter = voxel_map.begin(); iter != voxel_map.end(); iter++) {
    if (iter->second->plane_ptr_->is_plane_) {
      RGBNPoint pi;
      pi.x = iter->second->plane_ptr_->center_[0];
      pi.y = iter->second->plane_ptr_->center_[1];
      pi.z = iter->second->plane_ptr_->center_[2];
      pi.normal_x = iter->second->plane_ptr_->normal_[0];
      pi.normal_y = iter->second->plane_ptr_->normal_[1];
      pi.normal_z = iter->second->plane_ptr_->normal_[2];
      plane_cloud->push_back(pi);
    }
  }
}

void BtcDescManager::get_project_plane(
    std::unordered_map<VOXEL_LOC, OctoTree *> &voxel_map,
    std::vector<std::shared_ptr<Plane>> &project_plane_list) {
  std::vector<std::shared_ptr<Plane>> origin_list;
  for (auto iter = voxel_map.begin(); iter != voxel_map.end(); iter++) {
    if (iter->second->plane_ptr_->is_plane_) {
      origin_list.push_back(iter->second->plane_ptr_);
    }
  }
  for (size_t i = 0; i < origin_list.size(); i++) origin_list[i]->id_ = 0;
  int current_id = 1;
  for (auto iter = origin_list.end() - 1; iter != origin_list.begin(); iter--) {
    for (auto iter2 = origin_list.begin(); iter2 != iter; iter2++) {
      // RS OPT
      if ((*iter)->id_ != 0 && (*iter2)->id_ != 0) {
        continue;
      }
      Eigen::Vector3d normal_diff = (*iter)->normal_ - (*iter2)->normal_;
      Eigen::Vector3d normal_add = (*iter)->normal_ + (*iter2)->normal_;
      double dis1 =
          fabs((*iter)->normal_(0) * (*iter2)->center_(0) +
               (*iter)->normal_(1) * (*iter2)->center_(1) +
               (*iter)->normal_(2) * (*iter2)->center_(2) + (*iter)->d_);
      double dis2 =
          fabs((*iter2)->normal_(0) * (*iter)->center_(0) +
               (*iter2)->normal_(1) * (*iter)->center_(1) +
               (*iter2)->normal_(2) * (*iter)->center_(2) + (*iter2)->d_);
      if (normal_diff.norm() < config_setting_.plane_merge_normal_thre_ ||
          normal_add.norm() < config_setting_.plane_merge_normal_thre_)
        if (dis1 < config_setting_.plane_merge_dis_thre_ &&
            dis2 < config_setting_.plane_merge_dis_thre_) {
          double center_dis = ((*iter)->center_ - (*iter2)->center_).norm();
          if (center_dis > config_setting_.plane_merge_center_dis_thre_)
            continue;
          if ((*iter)->id_ == 0 && (*iter2)->id_ == 0) {
            (*iter)->id_ = current_id;
            (*iter2)->id_ = current_id;
            current_id++;
          } else if ((*iter)->id_ == 0 && (*iter2)->id_ != 0)
            (*iter)->id_ = (*iter2)->id_;
          else if ((*iter)->id_ != 0 && (*iter2)->id_ == 0)
            (*iter2)->id_ = (*iter)->id_;
        }
    }
  }
  std::vector<std::shared_ptr<Plane>> merge_list;
  std::vector<int> merge_flag;

  for (size_t i = 0; i < origin_list.size(); i++) {
    auto it =
        std::find(merge_flag.begin(), merge_flag.end(), origin_list[i]->id_);
    if (it != merge_flag.end()) continue;
    if (origin_list[i]->id_ == 0) {
      continue;
    }
    // 找到已经被聚类（有id）且未合并的平面
    std::shared_ptr<Plane> merge_plane(new Plane);
    (*merge_plane) = (*origin_list[i]);
    bool is_merge = false;
    for (size_t j = 0; j < origin_list.size(); j++) {
      if (i == j) continue;
      // 找到相同id的平面 进行合并
      if (origin_list[j]->id_ == origin_list[i]->id_) {
        is_merge = true;
        int new_size = merge_plane->points_size_ + origin_list[j]->points_size_;
        Eigen::Matrix3d P_PT1 =
            (merge_plane->covariance_ +
             merge_plane->center_ * merge_plane->center_.transpose()) *
            merge_plane->points_size_;
        Eigen::Matrix3d P_PT2 =
            (origin_list[j]->covariance_ +
             origin_list[j]->center_ * origin_list[j]->center_.transpose()) *
            origin_list[j]->points_size_;
        Eigen::Vector3d merge_center =
            (merge_plane->center_ * merge_plane->points_size_ +
             origin_list[j]->center_ * origin_list[j]->points_size_) /
            new_size;
        Eigen::Matrix3d merge_covariance =
            (P_PT1 + P_PT2) / new_size -
            merge_center * merge_center.transpose();
        merge_plane->covariance_ = merge_covariance;
        merge_plane->center_ = merge_center;
        merge_plane->points_size_ = new_size;
        merge_plane->sub_plane_num_++;
        // for (size_t k = 0; k < origin_list[j]->cloud.size(); k++) {
        //   merge_plane->cloud.points.push_back(origin_list[j]->cloud.points[k]);
        // }
        Eigen::EigenSolver<Eigen::Matrix3d> es(merge_plane->covariance_);
        Eigen::Matrix3cd evecs = es.eigenvectors();
        Eigen::Vector3cd evals = es.eigenvalues();
        Eigen::Vector3d evalsReal;
        evalsReal = evals.real();
        Eigen::Matrix3f::Index evalsMin, evalsMax;
        evalsReal.rowwise().sum().minCoeff(&evalsMin);
        evalsReal.rowwise().sum().maxCoeff(&evalsMax);
        Eigen::Vector3d evecMin = evecs.real().col(evalsMin);
        merge_plane->normal_ << evecs.real()(0, evalsMin),
            evecs.real()(1, evalsMin), evecs.real()(2, evalsMin);
        merge_plane->radius_ = sqrt(evalsReal(evalsMax));
        merge_plane->d_ = -(merge_plane->normal_(0) * merge_plane->center_(0) +
                            merge_plane->normal_(1) * merge_plane->center_(1) +
                            merge_plane->normal_(2) * merge_plane->center_(2));
        merge_plane->p_center_.x = merge_plane->center_(0);
        merge_plane->p_center_.y = merge_plane->center_(1);
        merge_plane->p_center_.z = merge_plane->center_(2);
        merge_plane->p_center_.normal_x = merge_plane->normal_(0);
        merge_plane->p_center_.normal_y = merge_plane->normal_(1);
        merge_plane->p_center_.normal_z = merge_plane->normal_(2);
      }
    }
    if (is_merge) {
      merge_flag.push_back(merge_plane->id_);
      merge_list.push_back(merge_plane);
    }
  }
  project_plane_list = merge_list;
}

void BtcDescManager::merge_plane(
    std::vector<std::shared_ptr<Plane>> &origin_list,
    std::vector<std::shared_ptr<Plane>> &merge_plane_list) {
  if (origin_list.size() == 1) {
    merge_plane_list = origin_list;
    return;
  }
  for (size_t i = 0; i < origin_list.size(); i++) origin_list[i]->id_ = 0;
  int current_id = 1;
  for (auto iter = origin_list.end() - 1; iter != origin_list.begin(); iter--) {
    for (auto iter2 = origin_list.begin(); iter2 != iter; iter2++) {
      // RS OPT
      if ((*iter)->id_ != 0 && (*iter2)->id_ != 0) {
        continue;
      }
      Eigen::Vector3d normal_diff = (*iter)->normal_ - (*iter2)->normal_;
      Eigen::Vector3d normal_add = (*iter)->normal_ + (*iter2)->normal_;
      double dis1 =
          fabs((*iter)->normal_(0) * (*iter2)->center_(0) +
               (*iter)->normal_(1) * (*iter2)->center_(1) +
               (*iter)->normal_(2) * (*iter2)->center_(2) + (*iter)->d_);
      double dis2 =
          fabs((*iter2)->normal_(0) * (*iter)->center_(0) +
               (*iter2)->normal_(1) * (*iter)->center_(1) +
               (*iter2)->normal_(2) * (*iter)->center_(2) + (*iter2)->d_);
      if (normal_diff.norm() < config_setting_.plane_merge_normal_thre_ ||
          normal_add.norm() < config_setting_.plane_merge_normal_thre_)
        if (dis1 < config_setting_.plane_merge_dis_thre_ &&
            dis2 < config_setting_.plane_merge_dis_thre_) {
          //  RS. FIXME: merged plane requires the near center
          double center_dis = ((*iter)->center_ - (*iter2)->center_).norm();
          if (center_dis > config_setting_.plane_merge_center_dis_thre_)
            continue;
          if ((*iter)->id_ == 0 && (*iter2)->id_ == 0) {
            (*iter)->id_ = current_id;
            (*iter2)->id_ = current_id;
            current_id++;
          } else if ((*iter)->id_ == 0 && (*iter2)->id_ != 0)
            (*iter)->id_ = (*iter2)->id_;
          else if ((*iter)->id_ != 0 && (*iter2)->id_ == 0)
            (*iter2)->id_ = (*iter)->id_;
        }
    }
  }
  std::vector<int> merge_flag;

  for (size_t i = 0; i < origin_list.size(); i++) {
    auto it =
        std::find(merge_flag.begin(), merge_flag.end(), origin_list[i]->id_);
    if (it != merge_flag.end()) continue;
    if (origin_list[i]->id_ == 0) {
      merge_plane_list.push_back(origin_list[i]);
      continue;
    }
    std::shared_ptr<Plane> merge_plane(new Plane);
    (*merge_plane) = (*origin_list[i]);
    bool is_merge = false;
    for (size_t j = 0; j < origin_list.size(); j++) {
      if (i == j) continue;
      if (origin_list[j]->id_ == origin_list[i]->id_) {
        is_merge = true;
        int new_size = merge_plane->points_size_ + origin_list[j]->points_size_;
        Eigen::Matrix3d P_PT1 =
            (merge_plane->covariance_ +
             merge_plane->center_ * merge_plane->center_.transpose()) *
            merge_plane->points_size_;
        Eigen::Matrix3d P_PT2 =
            (origin_list[j]->covariance_ +
             origin_list[j]->center_ * origin_list[j]->center_.transpose()) *
            origin_list[j]->points_size_;
        Eigen::Vector3d merge_center =
            (merge_plane->center_ * merge_plane->points_size_ +
             origin_list[j]->center_ * origin_list[j]->points_size_) /
            new_size;
        Eigen::Matrix3d merge_covariance =
            (P_PT1 + P_PT2) / new_size -
            merge_center * merge_center.transpose();
        merge_plane->covariance_ = merge_covariance;
        merge_plane->center_ = merge_center;
        merge_plane->points_size_ = new_size;
        merge_plane->sub_plane_num_ += origin_list[j]->sub_plane_num_;
        // for (size_t k = 0; k < origin_list[j]->cloud.size(); k++) {
        //   merge_plane->cloud.points.push_back(origin_list[j]->cloud.points[k]);
        // }
        Eigen::EigenSolver<Eigen::Matrix3d> es(merge_plane->covariance_);
        Eigen::Matrix3cd evecs = es.eigenvectors();
        Eigen::Vector3cd evals = es.eigenvalues();
        Eigen::Vector3d evalsReal;
        evalsReal = evals.real();
        Eigen::Matrix3f::Index evalsMin, evalsMax;
        evalsReal.rowwise().sum().minCoeff(&evalsMin);
        evalsReal.rowwise().sum().maxCoeff(&evalsMax);
        Eigen::Vector3d evecMin = evecs.real().col(evalsMin);
        merge_plane->normal_ << evecs.real()(0, evalsMin),
            evecs.real()(1, evalsMin), evecs.real()(2, evalsMin);
        merge_plane->radius_ = sqrt(evalsReal(evalsMax));
        merge_plane->d_ = -(merge_plane->normal_(0) * merge_plane->center_(0) +
                            merge_plane->normal_(1) * merge_plane->center_(1) +
                            merge_plane->normal_(2) * merge_plane->center_(2));
        merge_plane->p_center_.x = merge_plane->center_(0);
        merge_plane->p_center_.y = merge_plane->center_(1);
        merge_plane->p_center_.z = merge_plane->center_(2);
        merge_plane->p_center_.normal_x = merge_plane->normal_(0);
        merge_plane->p_center_.normal_y = merge_plane->normal_(1);
        merge_plane->p_center_.normal_z = merge_plane->normal_(2);
      }
    }
    if (is_merge) {
      merge_flag.push_back(merge_plane->id_);
      merge_plane_list.push_back(merge_plane);
    }
  }
}

void BtcDescManager::binary_extractor(
    const std::vector<std::shared_ptr<Plane>> proj_plane_list,
    const RGBNCloudPtr &input_cloud,
    std::vector<BinaryDescriptor> &binary_descriptor_list) {
  binary_descriptor_list.clear();
  std::vector<BinaryDescriptor> temp_binary_list;
  Eigen::Vector3d last_normal(0, 0, 0);
  int useful_proj_num = 0;
  for (int i = 0; i < proj_plane_list.size(); i++) {
    std::vector<BinaryDescriptor> prepare_binary_list;
    Eigen::Vector3d proj_center = proj_plane_list[i]->center_;
    Eigen::Vector3d proj_normal = proj_plane_list[i]->normal_;
    if (proj_normal.z() < 0) {
      proj_normal = -proj_normal;
    }
    if ((proj_normal - last_normal).norm() < 0.3 ||
        (proj_normal + last_normal).norm() > 0.3) {
      last_normal = proj_normal;
      // if (print_debug_info_)
      //   LBLUE << "[Description][binary_extractor]reference plane normal:"
      //         << proj_normal.transpose()
      //         << ", center:" << proj_center.transpose() << REND;
      useful_proj_num++;
      extract_binary(proj_center, proj_normal, input_cloud,
                     prepare_binary_list);
      for (auto bi : prepare_binary_list) {
        temp_binary_list.push_back(bi);
      }
      if (useful_proj_num == config_setting_.proj_plane_num_) {
        break;
      }
    }
  }
  non_maxi_suppression(temp_binary_list);
  if (config_setting_.useful_corner_num_ > temp_binary_list.size()) {
    binary_descriptor_list = temp_binary_list;
  } else {
    std::sort(temp_binary_list.begin(), temp_binary_list.end(),
              binary_greater_sort);
    for (size_t i = 0; i < config_setting_.useful_corner_num_; i++) {
      binary_descriptor_list.push_back(temp_binary_list[i]);
    }
  }
  return;
}

void BtcDescManager::extract_binary(
    const Eigen::Vector3d &project_center,
    const Eigen::Vector3d &project_normal,
    const RGBNCloudPtr &input_cloud,
    std::vector<BinaryDescriptor> &binary_list) {
  binary_list.clear();
  double binary_min_dis = config_setting_.summary_min_thre_;
  double resolution = config_setting_.proj_image_resolution_;
  double dis_threshold_min = config_setting_.proj_dis_min_;
  double dis_threshold_max = config_setting_.proj_dis_max_;
  double high_inc = config_setting_.proj_image_high_inc_;
  bool line_filter_enable = config_setting_.line_filter_enable_;
  double A = project_normal[0];
  double B = project_normal[1];
  double C = project_normal[2];
  double D =
      -(A * project_center[0] + B * project_center[1] + C * project_center[2]);
  std::vector<Eigen::Vector3d> projection_points;
  // Eigen::Vector3d x_axis(1, 1, 0);
  Eigen::Vector3d x_axis(1, 0, 0);
  if (C != 0) {
    x_axis[2] = -(A + B) / C;
  } else if (B != 0) {
    x_axis[1] = -A / B;
  } else {
    x_axis[0] = 0;
    x_axis[1] = 1;
  }
  x_axis.normalize();
  Eigen::Vector3d y_axis = project_normal.cross(x_axis);
  y_axis.normalize();
  double ax = x_axis[0];
  double bx = x_axis[1];
  double cx = x_axis[2];
  double dx = -(ax * project_center[0] + bx * project_center[1] +
                cx * project_center[2]);
  double ay = y_axis[0];
  double by = y_axis[1];
  double cy = y_axis[2];
  double dy = -(ay * project_center[0] + by * project_center[1] +
                cy * project_center[2]);
  std::vector<Eigen::Vector2d> point_list_2d;
  pcl::PointCloud<pcl::PointXYZ> point_list_3d;
  std::vector<double> dis_list_2d;
  for (size_t i = 0; i < input_cloud->size(); i++) {
    double x = input_cloud->points[i].x;
    double y = input_cloud->points[i].y;
    double z = input_cloud->points[i].z;
    double dis = x * A + y * B + z * C + D;
    pcl::PointXYZ pi;
    if (dis < dis_threshold_min || dis > dis_threshold_max) {
      continue;
    } else {
      // FIXME(rum): double check
      if (dis > dis_threshold_min && dis <= dis_threshold_max) {
        pi.x = x;
        pi.y = y;
        pi.z = z;
      }
    }
    Eigen::Vector3d cur_project;

    cur_project[0] = (-A * (B * y + C * z + D) + x * (B * B + C * C)) /
                     (A * A + B * B + C * C);
    cur_project[1] = (-B * (A * x + C * z + D) + y * (A * A + C * C)) /
                     (A * A + B * B + C * C);
    cur_project[2] = (-C * (A * x + B * y + D) + z * (A * A + B * B)) /
                     (A * A + B * B + C * C);
    pcl::PointXYZ p;
    p.x = cur_project[0];
    p.y = cur_project[1];
    p.z = cur_project[2];
    double project_x =
        cur_project[0] * ay + cur_project[1] * by + cur_project[2] * cy + dy;
    double project_y =
        cur_project[0] * ax + cur_project[1] * bx + cur_project[2] * cx + dx;
    Eigen::Vector2d p_2d(project_x, project_y);
    point_list_2d.push_back(p_2d);
    dis_list_2d.push_back(dis);
    point_list_3d.points.push_back(pi);
  }
  double min_x = 10;
  double max_x = -10;
  double min_y = 10;
  double max_y = -10;
  if (point_list_2d.size() <= 5) {
    return;
  }
  for (auto pi : point_list_2d) {
    if (pi[0] < min_x) {
      min_x = pi[0];
    }
    if (pi[0] > max_x) {
      max_x = pi[0];
    }
    if (pi[1] < min_y) {
      min_y = pi[1];
    }
    if (pi[1] > max_y) {
      max_y = pi[1];
    }
  }
  // segment project cloud
  int segmen_base_num = 5;
  double segmen_len = segmen_base_num * resolution;
  int x_segment_num = (max_x - min_x) / segmen_len + 1;
  int y_segment_num = (max_y - min_y) / segmen_len + 1;
  int x_axis_len = (int)((max_x - min_x) / resolution + segmen_base_num);
  int y_axis_len = (int)((max_y - min_y) / resolution + segmen_base_num);

  std::vector<double> **dis_container = new std::vector<double> *[x_axis_len];
  BinaryDescriptor **binary_container = new BinaryDescriptor *[x_axis_len];
  for (int i = 0; i < x_axis_len; i++) {
    dis_container[i] = new std::vector<double>[y_axis_len];
    binary_container[i] = new BinaryDescriptor[y_axis_len];
  }
  double **img_count = new double *[x_axis_len];
  for (int i = 0; i < x_axis_len; i++) {
    img_count[i] = new double[y_axis_len];
  }
  double **dis_array = new double *[x_axis_len];
  for (int i = 0; i < x_axis_len; i++) {
    dis_array[i] = new double[y_axis_len];
  }
  double **mean_x_list = new double *[x_axis_len];
  for (int i = 0; i < x_axis_len; i++) {
    mean_x_list[i] = new double[y_axis_len];
  }
  double **mean_y_list = new double *[x_axis_len];
  for (int i = 0; i < x_axis_len; i++) {
    mean_y_list[i] = new double[y_axis_len];
  }
  for (int x = 0; x < x_axis_len; x++) {
    for (int y = 0; y < y_axis_len; y++) {
      img_count[x][y] = 0;
      mean_x_list[x][y] = 0;
      mean_y_list[x][y] = 0;
      dis_array[x][y] = 0;
      std::vector<double> single_dis_container;
      dis_container[x][y] = single_dis_container;
    }
  }

  for (size_t i = 0; i < point_list_2d.size(); i++) {
    int x_index = (int)((point_list_2d[i][0] - min_x) / resolution);
    int y_index = (int)((point_list_2d[i][1] - min_y) / resolution);
    mean_x_list[x_index][y_index] += point_list_2d[i][0];
    mean_y_list[x_index][y_index] += point_list_2d[i][1];
    img_count[x_index][y_index]++;
    dis_container[x_index][y_index].push_back(dis_list_2d[i]);
  }

  for (int x = 0; x < x_axis_len; x++) {
    for (int y = 0; y < y_axis_len; y++) {
      // calc segment dis array
      if (img_count[x][y] > 0) {
        int cut_num = (dis_threshold_max - dis_threshold_min) / high_inc;
        std::vector<bool> occup_list;
        std::vector<double> cnt_list;
        BinaryDescriptor single_binary;
        for (size_t i = 0; i < cut_num; i++) {
          cnt_list.push_back(0);
          occup_list.push_back(false);
        }
        for (size_t j = 0; j < dis_container[x][y].size(); j++) {
          int cnt_index =
              (dis_container[x][y][j] - dis_threshold_min) / high_inc;
          cnt_list[cnt_index]++;
        }
        double segmnt_dis = 0;
        for (size_t i = 0; i < cut_num; i++) {
          if (cnt_list[i] >= 1) {
            segmnt_dis++;
            occup_list[i] = true;
          }
        }
        dis_array[x][y] = segmnt_dis;
        single_binary.occupy_array_ = occup_list;
        single_binary.summary_ = segmnt_dis;
        binary_container[x][y] = single_binary;
      }
    }
  }

  // filter by distance
  std::vector<double> max_dis_list;
  std::vector<int> max_dis_x_index_list;
  std::vector<int> max_dis_y_index_list;

  for (int x_segment_index = 0; x_segment_index < x_segment_num;
       x_segment_index++) {
    for (int y_segment_index = 0; y_segment_index < y_segment_num;
         y_segment_index++) {
      double max_dis = 0;
      int max_dis_x_index = -10;
      int max_dis_y_index = -10;
      for (int x_index = x_segment_index * segmen_base_num;
           x_index < (x_segment_index + 1) * segmen_base_num; x_index++) {
        for (int y_index = y_segment_index * segmen_base_num;
             y_index < (y_segment_index + 1) * segmen_base_num; y_index++) {
          if (dis_array[x_index][y_index] > max_dis) {
            max_dis = dis_array[x_index][y_index];
            max_dis_x_index = x_index;
            max_dis_y_index = y_index;
          }
        }
      }
      if (max_dis >= binary_min_dis) {
        max_dis_list.push_back(max_dis);
        max_dis_x_index_list.push_back(max_dis_x_index);
        max_dis_y_index_list.push_back(max_dis_y_index);
      }
    }
  }
  // calc line or not
  std::vector<Eigen::Vector2i> direction_list;
  Eigen::Vector2i d(0, 1);
  direction_list.push_back(d);
  d << 1, 0;
  direction_list.push_back(d);
  d << 1, 1;
  direction_list.push_back(d);
  d << 1, -1;
  direction_list.push_back(d);
  for (size_t i = 0; i < max_dis_list.size(); i++) {
    Eigen::Vector2i p(max_dis_x_index_list[i], max_dis_y_index_list[i]);
    if (p[0] <= 0 || p[0] >= x_axis_len - 1 || p[1] <= 0 ||
        p[1] >= y_axis_len - 1) {
      continue;
    }
    bool is_add = true;

    if (line_filter_enable) {
      for (int j = 0; j < 4; j++) {
        Eigen::Vector2i p(max_dis_x_index_list[i], max_dis_y_index_list[i]);
        if (p[0] <= 0 || p[0] >= x_axis_len - 1 || p[1] <= 0 ||
            p[1] >= y_axis_len - 1) {
          continue;
        }
        Eigen::Vector2i p1 = p + direction_list[j];
        Eigen::Vector2i p2 = p - direction_list[j];
        double threshold = dis_array[p[0]][p[1]] - 3;
        if (dis_array[p1[0]][p1[1]] >= threshold) {
          if (dis_array[p2[0]][p2[1]] >= 0.5 * dis_array[p[0]][p[1]]) {
            is_add = false;
          }
        }
        if (dis_array[p2[0]][p2[1]] >= threshold) {
          if (dis_array[p1[0]][p1[1]] >= 0.5 * dis_array[p[0]][p[1]]) {
            is_add = false;
          }
        }
        if (dis_array[p1[0]][p1[1]] >= threshold) {
          if (dis_array[p2[0]][p2[1]] >= threshold) {
            is_add = false;
          }
        }
        if (dis_array[p2[0]][p2[1]] >= threshold) {
          if (dis_array[p1[0]][p1[1]] >= threshold) {
            is_add = false;
          }
        }
      }
    }
    if (is_add) {
      double px =
          mean_x_list[max_dis_x_index_list[i]][max_dis_y_index_list[i]] /
          img_count[max_dis_x_index_list[i]][max_dis_y_index_list[i]];
      double py =
          mean_y_list[max_dis_x_index_list[i]][max_dis_y_index_list[i]] /
          img_count[max_dis_x_index_list[i]][max_dis_y_index_list[i]];
      Eigen::Vector3d coord = py * x_axis + px * y_axis + project_center;
      pcl::PointXYZ pi;
      pi.x = coord[0];
      pi.y = coord[1];
      pi.z = coord[2];
      BinaryDescriptor single_binary =
          binary_container[max_dis_x_index_list[i]][max_dis_y_index_list[i]];
      single_binary.location_ = coord;
      binary_list.push_back(single_binary);
    }
  }
  for (int i = 0; i < x_axis_len; i++) {
    delete[] binary_container[i];
    delete[] dis_container[i];
    delete[] img_count[i];
    delete[] dis_array[i];
    delete[] mean_x_list[i];
    delete[] mean_y_list[i];
  }
  delete[] binary_container;
  delete[] dis_container;
  delete[] img_count;
  delete[] dis_array;
  delete[] mean_x_list;
  delete[] mean_y_list;
}

void BtcDescManager::non_maxi_suppression(
    std::vector<BinaryDescriptor> &binary_list) {
  pcl::PointCloud<pcl::PointXYZ>::Ptr prepare_key_cloud(
      new pcl::PointCloud<pcl::PointXYZ>);
  pcl::KdTreeFLANN<pcl::PointXYZ> kd_tree;
  std::vector<int> pre_count_list;
  std::vector<bool> is_add_list;
  for (auto var : binary_list) {
    pcl::PointXYZ pi;
    pi.x = var.location_[0];
    pi.y = var.location_[1];
    pi.z = var.location_[2];
    prepare_key_cloud->push_back(pi);
    pre_count_list.push_back(var.summary_);
    is_add_list.push_back(true);
  }
  kd_tree.setInputCloud(prepare_key_cloud);
  std::vector<int> pointIdxRadiusSearch;
  std::vector<float> pointRadiusSquaredDistance;
  double radius = config_setting_.non_max_suppression_radius_;
  for (size_t i = 0; i < prepare_key_cloud->size(); i++) {
    pcl::PointXYZ searchPoint = prepare_key_cloud->points[i];
    if (kd_tree.radiusSearch(searchPoint, radius, pointIdxRadiusSearch,
                             pointRadiusSquaredDistance) > 0) {
      Eigen::Vector3d pi(searchPoint.x, searchPoint.y, searchPoint.z);
      for (size_t j = 0; j < pointIdxRadiusSearch.size(); ++j) {
        Eigen::Vector3d pj(
            prepare_key_cloud->points[pointIdxRadiusSearch[j]].x,
            prepare_key_cloud->points[pointIdxRadiusSearch[j]].y,
            prepare_key_cloud->points[pointIdxRadiusSearch[j]].z);
        if (pointIdxRadiusSearch[j] == i) {
          continue;
        }
        if (pre_count_list[i] <= pre_count_list[pointIdxRadiusSearch[j]]) {
          is_add_list[i] = false;
        }
      }
    }
  }
  std::vector<BinaryDescriptor> pass_binary_list;
  for (size_t i = 0; i < is_add_list.size(); i++) {
    if (is_add_list[i]) {
      pass_binary_list.push_back(binary_list[i]);
    }
  }
  binary_list.clear();
  for (auto var : pass_binary_list) {
    binary_list.push_back(var);
  }
  return;
}

void BtcDescManager::generate_btc(
    const std::vector<BinaryDescriptor> &binary_list, const int &frame_id,
    std::vector<BTC> &btc_list) {
  double scale = 1.0 / config_setting_.std_side_resolution_;
  std::unordered_map<VOXEL_LOC, bool> feat_map;
  pcl::PointCloud<pcl::PointXYZ> bin_cloud;
  for (auto var : binary_list) {
    pcl::PointXYZ pi;
    pi.x = var.location_[0];
    pi.y = var.location_[1];
    pi.z = var.location_[2];
    bin_cloud.push_back(pi);
  }
  pcl::KdTreeFLANN<pcl::PointXYZ>::Ptr kd_tree(
      new pcl::KdTreeFLANN<pcl::PointXYZ>);
  kd_tree->setInputCloud(bin_cloud.makeShared());
  int K = config_setting_.descriptor_near_num_;
  std::vector<int> pointIdxNKNSearch(K);
  std::vector<float> pointNKNSquaredDistance(K);
  for (size_t i = 0; i < bin_cloud.size(); i++) {
    // sequencely select 3 binarys to form a triangle
    pcl::PointXYZ searchPoint = bin_cloud.points[i];
    if (kd_tree->nearestKSearch(searchPoint, K, pointIdxNKNSearch,
                                pointNKNSquaredDistance) > 0) {
      for (int m = 1; m < K - 1; m++) {
        for (int n = m + 1; n < K; n++) {
          // triangle vertices and edges
          pcl::PointXYZ p1 = searchPoint;
          pcl::PointXYZ p2 = bin_cloud.points[pointIdxNKNSearch[m]];
          pcl::PointXYZ p3 = bin_cloud.points[pointIdxNKNSearch[n]];
          double a = sqrt(pow(p1.x - p2.x, 2) + pow(p1.y - p2.y, 2) +
                          pow(p1.z - p2.z, 2));
          double b = sqrt(pow(p1.x - p3.x, 2) + pow(p1.y - p3.y, 2) +
                          pow(p1.z - p3.z, 2));
          double c = sqrt(pow(p3.x - p2.x, 2) + pow(p3.y - p2.y, 2) +
                          pow(p3.z - p2.z, 2));
          if (a > config_setting_.descriptor_max_len_ ||
              b > config_setting_.descriptor_max_len_ ||
              c > config_setting_.descriptor_max_len_ ||
              a < config_setting_.descriptor_min_len_ ||
              b < config_setting_.descriptor_min_len_ ||
              c < config_setting_.descriptor_min_len_) {
            continue;
          }
          // make sure a<b<c
          double temp;
          Eigen::Vector3d A, B, C; // vertex position
          Eigen::Vector3i l1, l2, l3; // edge length
          Eigen::Vector3i l_temp;
          l1 << 1, 2, 0;
          l2 << 1, 0, 3;
          l3 << 0, 2, 3;
          if (a > b) {
            temp = a;
            a = b;
            b = temp;
            l_temp = l1;
            l1 = l2;
            l2 = l_temp;
          }
          if (b > c) {
            temp = b;
            b = c;
            c = temp;
            l_temp = l2;
            l2 = l3;
            l3 = l_temp;
          }
          if (a > b) {
            temp = a;
            a = b;
            b = temp;
            l_temp = l1;
            l1 = l2;
            l2 = l_temp;
          }
          if (fabs(c - (a + b)) < 0.2) {
            continue; // exclude obtuse angle
          }

          pcl::PointXYZ d_p;
          d_p.x = a * 1000; // length, m to mm
          d_p.y = b * 1000;
          d_p.z = c * 1000;
          VOXEL_LOC position((int64_t)d_p.x, (int64_t)d_p.y, (int64_t)d_p.z);
          auto iter = feat_map.find(position);
          Eigen::Vector3d normal_1, normal_2, normal_3;
          BinaryDescriptor binary_A;
          BinaryDescriptor binary_B;
          BinaryDescriptor binary_C;
          if (iter == feat_map.end()) {
            if (l1[0] == l2[0]) {
              A << p1.x, p1.y, p1.z;
              binary_A = binary_list[i];
            } else if (l1[1] == l2[1]) {
              A << p2.x, p2.y, p2.z;
              binary_A = binary_list[pointIdxNKNSearch[m]];
            } else {
              A << p3.x, p3.y, p3.z;
              binary_A = binary_list[pointIdxNKNSearch[n]];
            }
            if (l1[0] == l3[0]) {
              B << p1.x, p1.y, p1.z;
              binary_B = binary_list[i];
            } else if (l1[1] == l3[1]) {
              B << p2.x, p2.y, p2.z;
              binary_B = binary_list[pointIdxNKNSearch[m]];
            } else {
              B << p3.x, p3.y, p3.z;
              binary_B = binary_list[pointIdxNKNSearch[n]];
            }
            if (l2[0] == l3[0]) {
              C << p1.x, p1.y, p1.z;
              binary_C = binary_list[i];
            } else if (l2[1] == l3[1]) {
              C << p2.x, p2.y, p2.z;
              binary_C = binary_list[pointIdxNKNSearch[m]];
            } else {
              C << p3.x, p3.y, p3.z;
              binary_C = binary_list[pointIdxNKNSearch[n]];
            }
            BTC single_descriptor;
            single_descriptor.binary_A_ = binary_A;
            single_descriptor.binary_B_ = binary_B;
            single_descriptor.binary_C_ = binary_C;
            single_descriptor.center_ = (A + B + C) / 3;
            single_descriptor.triangle_ << scale * a, scale * b, scale * c;
            single_descriptor.angle_[0] = fabs(5 * normal_1.dot(normal_2)); // 5 is not understood
            single_descriptor.angle_[1] = fabs(5 * normal_1.dot(normal_3));
            single_descriptor.angle_[2] = fabs(5 * normal_3.dot(normal_2));
            // single_descriptor.angle << 0, 0, 0;
            single_descriptor.frame_number_ = frame_id;
            // single_descriptor.score_frame_.push_back(frame_number);
            // single_descriptor.triangle_scale_ = scale;
            feat_map[position] = true;
            btc_list.push_back(single_descriptor);
          }
        }
      }
    }
  }
}

void BtcDescManager::candidate_selector(
    const std::vector<BTC> &current_STD_list,
    std::vector<BTCMatchList> &candidate_matcher_vec) {
  int current_frame_id = current_STD_list[0].frame_number_;
  int outlier = 0;
  double max_dis = 50;
  double match_array[20000] = {0};
  std::vector<std::pair<BTC, BTC>> match_list;
  std::vector<int> match_list_index;

  // 27 neighborhood
  std::vector<Eigen::Vector3i> voxel_round;
  for (int x = -1; x <= 1; x++) {
    for (int y = -1; y <= 1; y++) {
      for (int z = -1; z <= 1; z++) {
        Eigen::Vector3i voxel_inc(x, y, z);
        voxel_round.push_back(voxel_inc);
      }
    }
  }
  // init
  int STD_size = current_STD_list.size();
  std::vector<int> useful_match(STD_size);
  std::vector<std::vector<size_t>> useful_match_index(STD_size);
  std::vector<std::vector<BTC_LOC>> useful_match_position(STD_size);
  std::vector<size_t> index(STD_size);
  for (size_t i = 0; i < STD_size; ++i) {
    index[i] = i;
    useful_match[i] = 0;
  }
  std::mutex mylock;
  auto t0 = std::chrono::high_resolution_clock::now();

  int query_num = 0;
  int pass_num = 0;
  std::for_each(
#if ENABLE_PARALELL_FOR_LOOP
      std::execution::par_unseq, index.begin(), index.end(),
#else
      std::execution::seq, index.begin(), index.end(),
#endif
      [&](const size_t &i) {
        const BTC &descriptor = current_STD_list[i];
        BTC_LOC position;
        int best_index = 0;
        BTC_LOC best_position;

        // TODO(rum): 没有几何意义 A+B+C?
        double dis_threshold =
            descriptor.triangle_.norm() *
            // descriptor.triangle_.sum() *
            config_setting_.rough_dis_threshold_; // old 0.005
        for (const auto &voxel_inc : voxel_round) {
          position.x = (int)(descriptor.triangle_[0] + voxel_inc[0]);
          position.y = (int)(descriptor.triangle_[1] + voxel_inc[1]);
          position.z = (int)(descriptor.triangle_[2] + voxel_inc[2]);
          Eigen::Vector3d voxel_center((double)position.x + 0.5,
                                       (double)position.y + 0.5,
                                       (double)position.z + 0.5);
          if ((descriptor.triangle_ - voxel_center).norm() < 1.5) {
            auto iter = data_base_.find(position);
            if (iter != data_base_.end()) {
              for (size_t j = 0; j < data_base_[position].size(); j++) {
                if ((descriptor.frame_number_ -
                     data_base_[position][j].frame_number_) >
                    config_setting_.skip_near_num_) {
                  // 边长相似度
                  double dis =
                      (descriptor.triangle_ - data_base_[position][j].triangle_)
                          .norm();
                  if (dis < dis_threshold) {
                    // 特征相似度
                    double similarity =
                        (binary_similarity(descriptor.binary_A_,
                                           data_base_[position][j].binary_A_) +
                         binary_similarity(descriptor.binary_B_,
                                           data_base_[position][j].binary_B_) +
                         binary_similarity(descriptor.binary_C_,
                                           data_base_[position][j].binary_C_)) /
                        3;
                    if (similarity > config_setting_.similarity_threshold_) {
                      useful_match[i] = 1;
                      useful_match_position[i].push_back(position);
                      useful_match_index[i].push_back(j);
                    }
                  }
                }
              }
            }
          }
        }
      });
  std::vector<Eigen::Vector2i, Eigen::aligned_allocator<Eigen::Vector2i>>
      index_recorder;
  auto t1 = std::chrono::high_resolution_clock::now();
  for (size_t i = 0; i < useful_match.size(); i++) {
    if (useful_match[i]) {
      for (size_t j = 0; j < useful_match_index[i].size(); j++) {
        match_array[data_base_[useful_match_position[i][j]]
                              [useful_match_index[i][j]]
                                  .frame_number_] += 1;
        Eigen::Vector2i match_index(i, j);
        index_recorder.push_back(match_index);
        // match_list.push_back(single_match_pair);
        match_list_index.push_back(
            data_base_[useful_match_position[i][j]][useful_match_index[i][j]]
                .frame_number_);
      }
    }
  }
  bool multi_thread_en = false;
  if (multi_thread_en) {
  std::for_each(
#if ENABLE_PARALELL_FOR_LOOP
      std::execution::par_unseq, index.begin(), index.end(),
#else
      std::execution::seq, index.begin(), index.end(),
#endif
        [&](const size_t &i) {
          if (useful_match[i]) {
            std::pair<BTC, BTC> single_match_pair;
            single_match_pair.first = current_STD_list[i];
            for (size_t j = 0; j < useful_match_index[i].size(); j++) {
              single_match_pair.second = data_base_[useful_match_position[i][j]]
                                                   [useful_match_index[i][j]];
              mylock.lock();
              match_array[single_match_pair.second.frame_number_] += 1;
              match_list.push_back(single_match_pair);
              match_list_index.push_back(
                  single_match_pair.second.frame_number_);
              mylock.unlock();
            }
          }
        });
  }

  auto t2 = std::chrono::high_resolution_clock::now();
  // LBLUE << "prepare match list size: " << match_list_index.size()
  //           << REND;
  // use index recorder

  for (int cnt = 0; cnt < config_setting_.candidate_num_; cnt++) {
    double max_vote = 1;
    int max_vote_index = -1;
    for (int i = 0; i < 20000; i++) {
      if (match_array[i] > max_vote) {
        max_vote = match_array[i];
        max_vote_index = i;
      }
    }
    BTCMatchList match_triangle_list;
    if (max_vote_index >= 0 && max_vote >= 5) {
      match_array[max_vote_index] = 0;
      match_triangle_list.match_frame_ = max_vote_index;
      match_triangle_list.match_id_.first = current_frame_id;
      match_triangle_list.match_id_.second = max_vote_index;
      double mean_dis = 0;
      for (size_t i = 0; i < index_recorder.size(); i++) {
        if (match_list_index[i] == max_vote_index) {
          std::pair<BTC, BTC> single_match_pair;
          single_match_pair.first = current_STD_list[index_recorder[i][0]];
          single_match_pair.second =
              data_base_[useful_match_position[index_recorder[i][0]]
                                              [index_recorder[i][1]]]
                        [useful_match_index[index_recorder[i][0]]
                                           [index_recorder[i][1]]];
          match_triangle_list.match_list_.push_back(single_match_pair);
        }
      }
      candidate_matcher_vec.push_back(match_triangle_list);
    }
  }
  // TODO(rum): 是否对候选排序，便于RANSAC取到约束强的点对
}

void BtcDescManager::candidate_verify(
    BTCMatchList &candidate_matcher, double &verify_score,
    std::pair<Eigen::Vector3d, Eigen::Matrix3d> &relative_pose,
    std::vector<std::pair<BTC, BTC>> &sucess_match_list) {
  sucess_match_list.clear();

  int pair_size = candidate_matcher.match_list_.size();
  if (pair_size < 3) {
    LERROR << "[candidate_verify] pair_size < 3" << REND;
    verify_score = -1;
    return;
  }

  // 1.RANSAC
  double dis_threshold = 3; // TODO(rum): 设为可配置参数
  std::time_t solve_time = 0;
  std::time_t verify_time = 0;
  int skip_len = (int)(candidate_matcher.match_list_.size() / 50) + 1; // TODO(rum): 设为可配置参数 减少
  int use_size = candidate_matcher.match_list_.size() / skip_len;
  std::vector<size_t> index(use_size);
  std::vector<int> vote_list(use_size);
  for (size_t i = 0; i < index.size(); i++) {
    index[i] = i;
  }
  std::mutex mylock;
  auto t0 = std::chrono::high_resolution_clock::now();

  std::for_each(
#if ENABLE_PARALELL_FOR_LOOP
      std::execution::par_unseq, index.begin(), index.end(),
#else
      std::execution::seq, index.begin(), index.end(),
#endif
      [&](const size_t &i) {
        Eigen::Matrix3d test_rot;
        Eigen::Vector3d test_t;
#if SOLVE_ONLY_ONE_PAIR
        int index = i * skip_len;
        auto& single_pair = candidate_matcher.match_list_[index];
        triangle_solver(single_pair, test_t, test_rot);
#else
        int index = i * skip_len;
        int index2 = index + 1;
        int index3 = index + 2;
        if (index3 >= pair_size) {
          index2 = index - 1;
          index3 = index - 2;
        }
        const auto &single_pair = candidate_matcher.match_list_[index];
        const auto &single_pair2 = candidate_matcher.match_list_[index2];
        const auto &single_pair3 = candidate_matcher.match_list_[index3];
        std::vector<std::pair<BTC, BTC>> std_pair_vec;
        std_pair_vec.push_back(single_pair);
        std_pair_vec.push_back(single_pair2);
        std_pair_vec.push_back(single_pair3);
        triangle_solver(std_pair_vec, test_t, test_rot, nullptr);
#endif
        // 投票
        // FIXME(rum): 这里可以优化， 局部变量太多
        int vote = 0;
        Eigen::Vector3d A, A_transform, B, B_transform, C, C_transform;
        double dis_A, dis_B, dis_C;
        for (size_t j = 0; j < pair_size; j++) {
          const auto& verify_pair = candidate_matcher.match_list_[j];
          A = verify_pair.first.binary_A_.location_;
          A_transform = test_rot * A + test_t;
          B = verify_pair.first.binary_B_.location_;
          B_transform = test_rot * B + test_t;
          C = verify_pair.first.binary_C_.location_;
          C_transform = test_rot * C + test_t;
          dis_A = (A_transform - verify_pair.second.binary_A_.location_).norm();
          dis_B = (B_transform - verify_pair.second.binary_B_.location_).norm();
          dis_C = (C_transform - verify_pair.second.binary_C_.location_).norm();
          if (dis_A < dis_threshold && dis_B < dis_threshold &&
              dis_C < dis_threshold) {
            vote++;
          }
        }
        // TODO(rum): 内点特征再匹配，或者点云ICP， 计算更准的位姿
    

        // FIXME(rum): 没必要加锁
        mylock.lock();
        vote_list[i] = vote;
        mylock.unlock();
      });

  int max_vote_index = 0;
  int max_vote = 0;
  for (size_t i = 0; i < vote_list.size(); i++) {
    if (max_vote < vote_list[i]) {
      max_vote_index = i;
      max_vote = vote_list[i];
    }
  }

  // 2. PlaneNN
  // old 4 TODO(rum): 设为可配置参数
  if (max_vote >= 4) {
    // TODO(rum): 避免再求一次， 空间换时间？ 下面代码都可优化掉
    Eigen::Matrix3d best_rot;
    Eigen::Vector3d best_t;
    std::shared_ptr<SolverResult> sol_res(new SolverResult);
#if SOLVE_ONLY_ONE_PAIR
    auto& best_pair = candidate_matcher.match_list_[max_vote_index * skip_len];
    triangle_solver(best_pair, best_t, best_rot);
#else
    int index = max_vote_index * skip_len;
    int index2 = index + 1;
    int index3 = index + 2;
    if (index3 >= pair_size) {
      index2 = index - 1;
      index3 = index - 2;
    }
    auto& best_pair = candidate_matcher.match_list_[index];
    auto& best_pair2 = candidate_matcher.match_list_[index2];
    auto& best_pair3 = candidate_matcher.match_list_[index3];
    std::vector<std::pair<BTC, BTC>> best_std_pair_vec;
    best_std_pair_vec.push_back(best_pair);
    best_std_pair_vec.push_back(best_pair2);
    best_std_pair_vec.push_back(best_pair3);

    // record the BTCs matched by current frame
    best_pair.second.match_frame_number_ = best_pair.first.frame_number_;
    best_pair2.second.match_frame_number_ = best_pair2.first.frame_number_;
    best_pair3.second.match_frame_number_ = best_pair3.first.frame_number_;

    triangle_solver(best_std_pair_vec, best_t, best_rot, sol_res);
#endif
    relative_pose.first = best_t;
    relative_pose.second = best_rot;
    for (size_t j = 0; j < candidate_matcher.match_list_.size(); j++) {
      auto verify_pair = candidate_matcher.match_list_[j];
      Eigen::Vector3d A = verify_pair.first.binary_A_.location_;
      Eigen::Vector3d A_transform = best_rot * A + best_t;
      Eigen::Vector3d B = verify_pair.first.binary_B_.location_;
      Eigen::Vector3d B_transform = best_rot * B + best_t;
      Eigen::Vector3d C = verify_pair.first.binary_C_.location_;
      Eigen::Vector3d C_transform = best_rot * C + best_t;
      double dis_A =
          (A_transform - verify_pair.second.binary_A_.location_).norm();
      double dis_B =
          (B_transform - verify_pair.second.binary_B_.location_).norm();
      double dis_C =
          (C_transform - verify_pair.second.binary_C_.location_).norm();
      if (dis_A < dis_threshold && dis_B < dis_threshold &&
          dis_C < dis_threshold) {
        sucess_match_list.push_back(verify_pair);
      }
    }
    verify_score = PlaneNN(
        plane_cloud_vec_.back(),
        plane_cloud_vec_[candidate_matcher.match_id_.second], relative_pose);

    //
    loop_res_->sol_res_ = sol_res;
  } else {
    verify_score = -1;
  }
  return;
}

void BtcDescManager::triangle_solver(std::pair<BTC, BTC> &std_pair,
                                     Eigen::Vector3d &t, Eigen::Matrix3d &rot) {
  Eigen::Matrix3d src = Eigen::Matrix3d::Zero();
  Eigen::Matrix3d ref = Eigen::Matrix3d::Zero();
  src.col(0) = std_pair.first.binary_A_.location_ - std_pair.first.center_;
  src.col(1) = std_pair.first.binary_B_.location_ - std_pair.first.center_;
  src.col(2) = std_pair.first.binary_C_.location_ - std_pair.first.center_;
  ref.col(0) = std_pair.second.binary_A_.location_ - std_pair.second.center_;
  ref.col(1) = std_pair.second.binary_B_.location_ - std_pair.second.center_;
  ref.col(2) = std_pair.second.binary_C_.location_ - std_pair.second.center_;

  // T_Q_P (trans cloud P to Q)
  // Q*P = UAV^T, R=U*V^T, t=center_q- R*center_p, where P means source, Q means target
  // R is rotation matrix, t is translation vector

  // T_source_target (trans target cloud to source)
  Eigen::Matrix3d covariance = src * ref.transpose();
  Eigen::JacobiSVD<Eigen::Matrix3d> svd(
      covariance, Eigen::ComputeThinU | Eigen::ComputeThinV);
  Eigen::Matrix3d V = svd.matrixV();
  Eigen::Matrix3d U = svd.matrixU();
  rot = V * U.transpose();
  if (rot.determinant() < 0) {
    // 将反射矩阵变为旋转矩阵
    Eigen::Matrix3d K;
    K << 1, 0, 0, 0, 1, 0, 0, 0, -1;
    rot = V * K * U.transpose();
  }
  t = -rot * std_pair.second.center_ + std_pair.first.center_;
}

bool IsEqual(const Eigen::Vector3d &v1, const Eigen::Vector3d &v2) {
  double thresh = 1e-5;
  return abs(v1.x() - v2.x() < thresh && abs(v1.y() - v2.y()) < thresh &&
             abs(v1.z() - v2.z()) < thresh);
}
void BtcDescManager::triangle_solver(
    std::vector<std::pair<BTC, BTC>> &std_pair_vec, Eigen::Vector3d &t,
    Eigen::Matrix3d &rot, std::shared_ptr<SolverResult> solver_res) {
  // 保持顶点唯一，避免加权
  std::vector<Eigen::Vector3d> src_vec;
  std::vector<Eigen::Vector3d> ref_vec;
  bool new_A, new_B, new_C;
  for (int i = 0; i < std_pair_vec.size(); ++i) {
    const auto &pair = std_pair_vec[i];
    new_A = new_B = new_C = true;
    for (int i = 0; i < src_vec.size(); ++i) {
      if (new_A && (IsEqual(src_vec[i], pair.first.binary_A_.location_) ||
                    IsEqual(ref_vec[i], pair.second.binary_A_.location_))) {
        new_A = false;
        break;
      }
    }
    if (new_A) {
      src_vec.push_back(pair.first.binary_A_.location_);
      ref_vec.push_back(pair.second.binary_A_.location_);
    }
    for (int i = 0; i < src_vec.size(); ++i) {
      if (new_B && (IsEqual(src_vec[i], pair.first.binary_B_.location_) ||
                    IsEqual(ref_vec[i], pair.second.binary_B_.location_))) {
        new_B = false;
        break;
      }
    }
    if (new_B) {
      src_vec.push_back(pair.first.binary_B_.location_);
      ref_vec.push_back(pair.second.binary_B_.location_);
    }
    for (int i = 0; i < src_vec.size(); ++i) {
      if (new_C && (IsEqual(src_vec[i], pair.first.binary_C_.location_) ||
                    IsEqual(ref_vec[i], pair.second.binary_C_.location_))) {
        new_C = false;
        break;
      }
    }
    if (new_C) {
      src_vec.push_back(pair.first.binary_C_.location_);
      ref_vec.push_back(pair.second.binary_C_.location_);
    }
  }
  if (src_vec.size() == 0)
    LERROR << "src_vec.size() == 0" << REND;
  else if (print_debug_info_) {
    // LINFO << "valid vertex: " << src_vec.size() << "/"
    //       << std_pair_vec.size() * 3 << REND;
    // for (int i = 0; i < src_vec.size(); ++i) {
    //   LINFO << src_vec[i].transpose() << " ";
    //   LERROR << ref_vec[i].transpose() << REND;
    // }
  }

  int cloud_size = src_vec.size();
  Eigen::Vector3d center_src = Eigen::Vector3d::Zero();
  Eigen::Vector3d center_ref = Eigen::Vector3d::Zero();
  for (int i = 0; i < cloud_size; ++i) {
    center_src += src_vec[i];
    center_ref += ref_vec[i];
  }
  center_src /= cloud_size;
  center_ref /= cloud_size;

  Eigen::MatrixXd src;
  Eigen::MatrixXd ref;
  src.resize(3, cloud_size);
  ref.resize(3, cloud_size);
  for (int i = 0; i < cloud_size; ++i) {
    src.col(i) = src_vec[i] - center_src;
    ref.col(i) = ref_vec[i] - center_ref;
  }
  // T_Q_P (trans cloud P to Q)
  // Q*P = UAV^T, R=U*V^T, t=center_q- R*center_p, where P means source, Q means target
  // R is rotation matrix, t is translation vector

  // T_source_target (trans target cloud to source)
  Eigen::Matrix3d covariance = src * ref.transpose();
  Eigen::JacobiSVD<Eigen::Matrix3d> svd(
      covariance, Eigen::ComputeThinU | Eigen::ComputeThinV);
  Eigen::Matrix3d V = svd.matrixV();
  Eigen::Matrix3d U = svd.matrixU();
  rot = V * U.transpose();
  if (rot.determinant() < 0) {
    // 将反射矩阵变为旋转矩阵
    Eigen::Matrix3d K;
    K << 1, 0, 0, 0, 1, 0, 0, 0, -1;
    rot = V * K * U.transpose();
  }
  t = -rot * center_ref + center_src;

  // result
  if (solver_res != nullptr) {
    solver_res->src_vec_ = src_vec;
    solver_res->ref_vec_ = ref_vec;
    solver_res->std_pair_vec_ = std_pair_vec;
    solver_res->rot_ = rot;
    solver_res->t_ = t;
  }
}

double BtcDescManager::PlaneNN(
    const RGBNCloudPtr &source_cloud,
    const RGBNCloudPtr &target_cloud,
    const std::pair<Eigen::Vector3d, Eigen::Matrix3d> &transform) {
  Eigen::Vector3d t = transform.first;
  Eigen::Matrix3d rot = transform.second;
  pcl::KdTreeFLANN<pcl::PointXYZ>::Ptr kd_tree(
      new pcl::KdTreeFLANN<pcl::PointXYZ>);
  pcl::PointCloud<pcl::PointXYZ>::Ptr input_cloud(
      new pcl::PointCloud<pcl::PointXYZ>);
  for (size_t i = 0; i < target_cloud->size(); i++) {
    pcl::PointXYZ pi;
    pi.x = target_cloud->points[i].x;
    pi.y = target_cloud->points[i].y;
    pi.z = target_cloud->points[i].z;
    input_cloud->push_back(pi);
  }

  kd_tree->setInputCloud(input_cloud);
  // 创建两个向量，分别存放近邻的索引值、近邻的中心距
  std::vector<int> pointIdxNKNSearch(1);
  std::vector<float> pointNKNSquaredDistance(1);
  double useful_match = 0;
  double normal_threshold = config_setting_.btc_verify_normal_threshold_;
  double dis_threshold = config_setting_.btc_verify_dis_threshold_;
  for (size_t i = 0; i < source_cloud->size(); i++) {
    RGBNPoint searchPoint = source_cloud->points[i];
    pcl::PointXYZ use_search_point;
    use_search_point.x = searchPoint.x;
    use_search_point.y = searchPoint.y;
    use_search_point.z = searchPoint.z;
    Eigen::Vector3d pi(searchPoint.x, searchPoint.y, searchPoint.z);
    pi = rot * pi + t;
    use_search_point.x = pi[0];
    use_search_point.y = pi[1];
    use_search_point.z = pi[2];
    Eigen::Vector3d ni(searchPoint.normal_x, searchPoint.normal_y,
                       searchPoint.normal_z);
    ni = rot * ni;
    if (kd_tree->nearestKSearch(use_search_point, 1, pointIdxNKNSearch,
                                pointNKNSquaredDistance) > 0) {
      RGBNPoint nearstPoint =
          target_cloud->points[pointIdxNKNSearch[0]];
      Eigen::Vector3d tpi(nearstPoint.x, nearstPoint.y, nearstPoint.z);
      Eigen::Vector3d tni(nearstPoint.normal_x, nearstPoint.normal_y,
                          nearstPoint.normal_z);
      Eigen::Vector3d normal_inc = ni - tni;
      Eigen::Vector3d normal_add = ni + tni;
      double point_to_plane = fabs(tni.transpose() * (pi - tpi));
      if ((normal_inc.norm() < normal_threshold ||
           normal_add.norm() < normal_threshold) &&
          point_to_plane < dis_threshold) {
        useful_match++;
      }
    }
  }

  // TODO(rum): 点面ICP
  return useful_match / source_cloud->size();
}
