#include <filesystem>
#include <pcl/io/pcd_io.h>
#include "relocalization/common.h"

namespace relocalization {
const std::string MapInfo::lidar_pose_file_name_{"/Twi_lidar.txt"};
const std::string MapInfo::vision_pose_file_name_{"/Twi_vision.txt"};
const std::string MapInfo::map_info_file_name_{"/map_info.txt"};
const std::string MapInfo::log_file_name_("/log.txt");

std::string getCurTime()
{
  auto now = std::chrono::system_clock::now();
  auto duration = now.time_since_epoch();
  auto milliseconds = std::chrono::duration_cast<std::chrono::milliseconds>(duration).count();
  return std::to_string(milliseconds);
}

Eigen::Matrix3d vectorToSkewSymmetric(const Eigen::Vector3d& _v) 
{
  Eigen::Matrix3d S;
  S << 0, -_v.z(), _v.y(),
        _v.z(), 0, -_v.x(),
        -_v.y(), _v.x(), 0;
  return S;
}

bool inBorder(cv::Size _img_size, cv::Point2f _pt, const int _border_size)
{
  return _pt.x >= _border_size && _pt.y >= _border_size && _pt.x < _img_size.width - _border_size && _pt.y < _img_size.height - _border_size;
}

std::pair<double, double> calNormalParam(std::vector<double> _meas, double _ratio)
{
  double mean = 0.;
  double var_sum = 0.;
  int n = 0;
  for(double &m:_meas)
  {
    n++;
    double delta = m - mean;
    mean += delta/n;
    var_sum += delta * (m - mean);
  }
  double var = (n > 1) ? var_sum / n : 0.0;
  double std = std::sqrt(var);
  if(n<=1)
  {
    return std::make_pair(mean, std);
  }
  double threshold = _ratio*std;
  std::vector<double> meas_filt;
  for(double &m:_meas)
  {
    if(std::abs(m - mean) < threshold)
      meas_filt.push_back(m);
  }
  if(_meas.size() == meas_filt.size())
  {
    return std::make_pair(mean, std);
  }
  else
  {
    return calNormalParam(meas_filt, _ratio);
  }
}

std::string intToStringWithFixedWidth(int num, int width) {
    std::stringstream ss;
    ss << std::setw(width) << std::setfill('0') << num;
    return ss.str();
}

bool createDir(const std::string _path)
{
  try 
  {
    if (std::filesystem::create_directories(_path)) 
    {
        LINFO << "Directory created: " << _path << REND;
    } else 
    {
        LINFO << "Directory already exists: " << _path << REND;
    }
    return true;
  } 
  catch (...) 
  {
    LERROR << "Fail to create directory: " << _path << REND;
    return false;
  }
}

void InExtrinsic::logInfo()
{
  LINFO << "Ril: \n" << Ril_ << REND;
  LINFO << "til: " << til_.transpose() << REND;
  LINFO << "Ric: \n" << Ric_ << REND;
  LINFO << "tic: " << tic_.transpose() << REND;
  LINFO << "fx: " << fx_ << ", fy: " << fy_ << ", cx: " << cx_ << ", cy: " << cy_ << REND;
}

void ReConfig::logInfo()
{
  LINFO << "lidar_en: " << lidar_en_ << ", vision_en: " << vision_en_ << ", max_kf_num: " << max_kf_num_ << REND;
  LINFO << "lidar_save_en: " << lidar_save_en_ << ", lidar_rgb_save_en: " << lidar_rgb_save_en_ << ", vision_save_en: " << vision_save_en_ << REND;
  LINFO << "plane_k: " << plane_k_ << ", inliner_dist1: " << inliner_dist1_ << ", inliner_dist2: " << inliner_dist2_ << ", inliner_epipolar: " << inliner_epipolar_ << ", damping_ratio: " << damping_ratio_ << REND;
  LINFO << "kf_dist: " << kf_dist_thres_ << ", kf_angle: " << kf_angle_thres_ 
    << ", kf_parallax: " << kf_parallax_thres_ << ", kf_track_ratio: " << kf_track_ratio_thres_ << REND;
  LINFO << "img_scale: " << img_scale_ << ", feature_num: " << feature_num_
    << ", pyramid_level: " << pyramid_level_ << ", window_size: " << window_size_
    << ", min_dist: " << min_dist_ << ", max_layer: " << max_layer_ 
    << ", f_ransac: " << f_ransac_ << REND;
  LINFO << "img_enhance: " << img_enhance_ << ", clip_limit: " << clip_limit_ << ", size: " << size_ << REND;
}

MapInfo::MapInfo(const int _map_id, const std::string &_root_path, const InExtrinsic &_in_extrinsic, const ReConfig &_config)
: map_id_(_map_id), in_extrinsic_(_in_extrinsic), config_(_config)
{
  map_path_ = _root_path + "/" + intToStringWithFixedWidth(_map_id, 2);
  lidar_path_ = map_path_ + "/lidar";
  vision_path_ = map_path_ + "/vision";
  lidar_rgb_path_ = map_path_ + "/lidar_rgb";
  if(createDir(map_path_))
  {
    LINFO << "createDir successfully: " << (map_path_) << REND;
    ofs_lidar_pose_.open(map_path_ + lidar_pose_file_name_, std::ios::out);
    if (ofs_lidar_pose_.is_open()) 
    {
      LINFO << "File opened successfully." << REND;
    } 
    else 
    {
      LERROR << "Fail to open file: " << (map_path_ + lidar_pose_file_name_) << REND;
      abort();
    }
    ofs_vision_pose_.open(map_path_ + vision_pose_file_name_, std::ios::out);
    if (ofs_vision_pose_.is_open()) 
    {
      LINFO << "File opened successfully." << REND;
    } 
    else 
    {
      LERROR << "Fail to open file: " << (map_path_ + vision_pose_file_name_) << REND;
      abort();
    }
    ofs_log_.open(map_path_ + log_file_name_, std::ios::app);
    if (ofs_vision_pose_.is_open()) 
    {
      LINFO << "File opened successfully." << REND;
    } 
    else 
    {
      LERROR << "Fail to open file: " << (map_path_ + vision_pose_file_name_) << REND;
      abort();
    }
  }
  else
  {
    LERROR << "Fail to createDir: " << (map_path_) << REND;
    abort();
  }
  if(createDir(lidar_path_))
  {
    LINFO << "createDir successfully: " << (lidar_path_) << REND;
  }
  else
  {
    LERROR << "Fail to createDir: " << (lidar_path_) << REND;
    abort();
  }
  if(createDir(vision_path_))
  {
    LINFO << "createDir successfully: " << (vision_path_) << REND;
  }
  else
  {
    LERROR << "Fail to createDir: " << (vision_path_) << REND;
    abort();
  }
  if(createDir(lidar_rgb_path_))
  {
    LINFO << "createDir successfully: " << (lidar_rgb_path_) << REND;
  }
  else
  {
    LERROR << "Fail to createDir: " << (lidar_rgb_path_) << REND;
    abort();
  }
  in_extrinsic_.logInfo();
  config_.logInfo();
}
MapInfo::~MapInfo()
{
  ofs_lidar_pose_.close();
  ofs_vision_pose_.close();
  std::ofstream ofs_map(map_path_ + map_info_file_name_, std::ios::out);
  if(ofs_map.is_open())
  {
    ofs_map << "#start_time end_time end_good_time\n";
    ofs_map << std::to_string(start_time_) << " " << std::to_string(end_time_) << " " << std::to_string(end_good_time_) << "\n";
    ofs_map << "#T_imu_from_lidar\n";
    ofs_map << in_extrinsic_.Ril_(0,0) << " " << in_extrinsic_.Ril_(0,1) << " " << in_extrinsic_.Ril_(0,2) << " " << in_extrinsic_.til_(0) << "\n";
    ofs_map << in_extrinsic_.Ril_(1,0) << " " << in_extrinsic_.Ril_(1,1) << " " << in_extrinsic_.Ril_(1,2) << " " << in_extrinsic_.til_(1) << "\n";
    ofs_map << in_extrinsic_.Ril_(2,0) << " " << in_extrinsic_.Ril_(2,1) << " " << in_extrinsic_.Ril_(2,2) << " " << in_extrinsic_.til_(2) << "\n";
    ofs_map << "#T_imu_from_camera\n";
    ofs_map << in_extrinsic_.Ric_(0,0) << " " << in_extrinsic_.Ric_(0,1) << " " << in_extrinsic_.Ric_(0,2) << " " << in_extrinsic_.tic_(0) << "\n";
    ofs_map << in_extrinsic_.Ric_(1,0) << " " << in_extrinsic_.Ric_(1,1) << " " << in_extrinsic_.Ric_(1,2) << " " << in_extrinsic_.tic_(1) << "\n";
    ofs_map << in_extrinsic_.Ric_(2,0) << " " << in_extrinsic_.Ric_(2,1) << " " << in_extrinsic_.Ric_(2,2) << " " << in_extrinsic_.tic_(2) << "\n";
    ofs_map << "#fx fy cx cy\n";
    ofs_map << in_extrinsic_.fx_ << " " << in_extrinsic_.fy_ << " " << in_extrinsic_.cx_ << " " << in_extrinsic_.cy_ << "\n";
    ofs_map.close();
  }
  else
  {
    LERROR << "Fail to open file: " << (map_path_ + map_info_file_name_) << REND;
    abort();
  }
}

void MapInfo::addFrame(std::shared_ptr<LidarInfo> _frame_ptr)
{
  if(start_time_ < 0.)
  {
    start_time_ = _frame_ptr->timestamp_;
  }
  newest_frame_time_ = _frame_ptr->timestamp_;
  lidar_frame_queue_.push(_frame_ptr);
}

void MapInfo::addKeyFrame(std::shared_ptr<LidarInfo> _frame_ptr, bool _check_en)
{
  if(lidar_keyframe_list_.empty())
  {
    lidar_keyframe_list_.emplace_back(_frame_ptr);
  }
  else if(_check_en)
  {
    double dist = (_frame_ptr->twi_ - lidar_keyframe_list_.back()->twi_).norm();
    double dot = _frame_ptr->qwi_.coeffs().dot(lidar_keyframe_list_.back()->qwi_.coeffs());
    dot = std::max(-1.0, std::min(1.0, dot));
    double angle = std::acos(dot) * 2 * R2D;
    if(dist > config_.kf_dist_thres_ || angle > config_.kf_angle_thres_)
    {
      lidar_keyframe_list_.emplace_back(_frame_ptr);
    }
  }
  else
  {
    lidar_keyframe_list_.emplace_back(_frame_ptr);
  }
}

void MapInfo::addFrame(std::shared_ptr<VisionInfo> _frame_ptr)
{
  if(start_time_ < 0.)
  {
    start_time_ = _frame_ptr->timestamp_;
  }
  newest_frame_time_ = _frame_ptr->timestamp_;
  vision_frame_queue_.push(_frame_ptr);
}

void MapInfo::addKeyFrame(std::shared_ptr<VisionInfo> _frame_ptr, bool _check_en)
{
  if(vision_keyframe_list_.empty())
  {
    vision_keyframe_list_.emplace_back(_frame_ptr);
  }
  else if(_check_en)
  {
    std::vector<cv::Point2f> corners_ref;
    std::vector<cv::Point2f> corners_cur;
    featureTrack(vision_keyframe_list_.back(), _frame_ptr, config_.window_size_*config_.img_scale_, config_.pyramid_level_, config_.f_ransac_, corners_ref, corners_cur);
    double ratio = double(corners_cur.size())/vision_keyframe_list_.back()->corners_.size();
    if(ratio < config_.kf_track_ratio_thres_ )
    {
      vision_keyframe_list_.emplace_back(_frame_ptr);
    }
    else
    {
      double parallax_max{0}, parallax_avg{0};
      size_t corners_out_size = corners_cur.size();
      for(int i=0; i< corners_out_size; i++)
      {
        double dist = cv::norm(corners_ref[i] - corners_cur[i]);
        parallax_avg += dist;
        parallax_max = parallax_max < dist ? dist : parallax_max;
      }
      parallax_avg /= corners_out_size;
      if(parallax_avg > config_.kf_parallax_thres_ || parallax_max > 2*config_.kf_parallax_thres_)
      {
        vision_keyframe_list_.emplace_back(_frame_ptr);
      }
    }
  }
  else
  {
    vision_keyframe_list_.emplace_back(_frame_ptr);
  }
}

void MapInfo::addFrame(const double _time, RGBCloudPtr _rgb_world_cloud)
{
  time_rgb_cloud_queue_.push(std::make_pair(_time, _rgb_world_cloud));
}

void MapInfo::updateEndTime(const double _time)
{
  if(_time >= 0.)
  {
    end_time_ = _time;
  }
  else if(end_time_<0.)
  {
    end_time_ = newest_frame_time_;
  }
}

void MapInfo::saveFrame(std::shared_ptr<LidarInfo> _frame)
{
  if(!config_.lidar_save_en_)
  {
    return;
  }
  static std::ofstream of_time(map_path_+"/lidar_time.txt", std::ios::out);
  auto start = std::chrono::high_resolution_clock::now();
  if(_frame->cloud_world_ptr_)
  {
    pcl::io::savePCDFileBinary(lidar_path_+"/"+std::to_string(_frame->timestamp_)+".pcd", *_frame->cloud_world_ptr_);
  }
  auto end = std::chrono::high_resolution_clock::now();
  std::chrono::duration<double, std::milli> duration = end - start;
  of_time << std::to_string(_frame->timestamp_) << " " << std::to_string(duration.count()) << std::endl;
  ofs_lidar_pose_ << std::to_string(_frame->timestamp_) << " " << _frame->twi_.x() << " " << _frame->twi_.y() << " " << _frame->twi_.z() << " "
    << _frame->qwi_.x() << " " << _frame->qwi_.y() << " " << _frame->qwi_.z() << " " << _frame->qwi_.w() << "\n";
}

void MapInfo::saveFrame(std::shared_ptr<VisionInfo> _frame)
{
  if(!config_.vision_save_en_)
  {
    return;
  }
  if(!_frame->raw_img_.empty())
  {
    cv::imwrite(vision_path_+"/"+std::to_string(_frame->timestamp_)+".jpeg", _frame->raw_img_);
  }
  ofs_vision_pose_ << std::to_string(_frame->timestamp_) << " " << _frame->twi_.x() << " " << _frame->twi_.y() << " " << _frame->twi_.z() << " "
    << _frame->qwi_.x() << " " << _frame->qwi_.y() << " " << _frame->qwi_.z() << " " << _frame->qwi_.w() << "\n";
}

void MapInfo::saveFrame(std::pair<double, RGBCloudPtr> _time_rgb_cloud)
{
  if(!config_.lidar_rgb_save_en_)
  {
    return;
  }
  if(_time_rgb_cloud.second)
  {
    PointCloudXYZRGB cloud_cp = *_time_rgb_cloud.second;
    pcl::io::savePCDFileBinary(lidar_rgb_path_+"/"+std::to_string(_time_rgb_cloud.first)+".pcd", cloud_cp);
  }
}

void featureTrack(std::shared_ptr<VisionInfo> _frame_ref_ptr, std::shared_ptr<VisionInfo> _frame_cur_ptr, const double _window_size, const int _pyramid_level, const double _f_ransac, std::vector<cv::Point2f> &_corners_ref, std::vector<cv::Point2f> &_corners_cur)
{
  std::vector<uchar> tracked_status;
  std::vector<float> tracked_err;
  std::vector<cv::Point2f> tracked_corners;
  cv::calcOpticalFlowPyrLK(_frame_ref_ptr->pyramid_img_, _frame_cur_ptr->pyramid_img_, _frame_ref_ptr->corners_, tracked_corners, tracked_status, tracked_err, cv::Size(_window_size, _window_size), _pyramid_level);

  cv::Size img_size = _frame_cur_ptr->enhance_img_.size();
  std::vector<cv::Point2f> ref_corners, cur_corners;
  ref_corners.reserve(tracked_status.size());
  cur_corners.reserve(tracked_status.size());
  for(int i=0; i<tracked_status.size(); i++)
  {
    if(tracked_status[i] && inBorder(img_size, tracked_corners[i]))
    {
      ref_corners.emplace_back(_frame_ref_ptr->corners_[i]);
      cur_corners.emplace_back(tracked_corners[i]);
    }
  }
  std::vector<uchar> status;
  cv::Mat F = cv::findFundamentalMat(ref_corners, cur_corners, cv::FM_RANSAC, _f_ransac, 0.99, status);
  _corners_ref.clear();
  _corners_cur.clear();
  for(int i=0; i<status.size(); i++)
  {
    if(status[i])
    {
      _corners_ref.emplace_back(ref_corners[i]);
      _corners_cur.emplace_back(cur_corners[i]);
    }
  }
}

} // namespace relocalization