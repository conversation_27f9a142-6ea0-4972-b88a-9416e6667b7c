#include <chrono>
#include <iomanip>
#include <sstream>

#include <pcl/point_types.h>
#include <pcl/common/transforms.h>
#include <pcl/kdtree/kdtree_flann.h>

#include "relocalization/relocalization.h"

namespace relocalization
{
ReLocalization::ReLocalization(std::string _root_path, const InExtrinsic &_in_extrinsic, const ReConfig &_config)
: in_extrinsic_(_in_extrinsic), config_(_config)
{
  auto now = std::chrono::system_clock::now();
  std::time_t now_c = std::chrono::system_clock::to_time_t(now);
  std::tm* now_tm = std::localtime(&now_c);
  std::ostringstream oss;
  oss << std::put_time(now_tm, "%Y-%m-%d-%H-%M-%S");
  root_path_ = _root_path + "/" + oss.str();
  voxel_filter_.setLeafSize(0.1, 0.1, 0.1);
  current_map_.reset(new MapInfo(map_id_++, root_path_, in_extrinsic_, config_));
  restart_info_.cur_map_id_ = current_map_->map_id_;
  inliner_ratio_ = 1.;
  stm_alive_ = true;
  stm_thread_ = std::thread(&ReLocalization::stmLoop, this);
  mtm_alive_ = true;
  mtm_thread_ = std::thread(&ReLocalization::mtmLoop, this);
  ltm_alive_ = true;
  ltm_thread_ = std::thread(&ReLocalization::ltmLoop, this);
}

ReLocalization::~ReLocalization()
{
  LINFO << "~ReLocalization()..." << REND;
  stm_alive_ = false;
  if(stm_thread_.joinable())
  {
    stm_available_.notify_one();
    stm_thread_.join();
  }
  LINFO << "stm_thread_ released successfully." << REND;
  endCurMap();
  LINFO << "endCurMap successfully." << REND;
  mtm_alive_ = false;
  if(mtm_thread_.joinable())
  {
    mtm_available_.notify_one();
    mtm_thread_.join();
  }
  LINFO << "mtm_thread_ released successfully." << REND;
  ltm_alive_ = false;
  if(ltm_thread_.joinable())
  {
    ltm_available_.notify_one();
    ltm_thread_.join();
  }
  LINFO << "ltm_thread_ released successfully." << REND;
}

void ReLocalization::setInExtrinsic(const InExtrinsic &_in_extrinsic)
{
  LINFO << "setInExtrinsic()..." << REND;
  in_extrinsic_ = _in_extrinsic;
  in_extrinsic_.logInfo();
  LINFO << "setInExtrinsic() done..." << REND;
}

void ReLocalization::loadConfig(const ReConfig &_config)
{
  LINFO << "loadConfig()..." << REND;
  config_ = _config;
  config_.logInfo();
  clahe_ = cv::createCLAHE(config_.clip_limit_, cv::Size(config_.size_, config_.size_));
  LINFO << "loadConfig() done..." << REND;
}

void ReLocalization::addFrame(const double _time, Eigen::Quaterniond _qwi, Eigen::Vector3d _twi, int _res_num, double _res_value, int _total_num, CloudPtr _undistorted_world_cloud)
{
  if(!config_.lidar_en_)
  {
    return;
  }
  auto start = std::chrono::high_resolution_clock::now();
  stm_mutex_.lock();
  if(current_map_ && stm_alive_)
  {
    current_map_->addFrame(std::make_shared<LidarInfo>(_time, _qwi, _twi, _res_num, _res_value, _total_num, _undistorted_world_cloud));
    stm_available_.notify_one();
  }
  stm_mutex_.unlock();
  auto end = std::chrono::high_resolution_clock::now();
  std::chrono::duration<double, std::milli> duration = end - start;
}

void ReLocalization::addFrame(const double _time, Eigen::Quaterniond _qwi, Eigen::Vector3d _twi, int _res_num, double _res_value, int _total_num, cv::Mat _raw_img, cv::Mat _gray_img)
{
  if(!config_.vision_en_)
  {
    return;
  }
  auto start = std::chrono::high_resolution_clock::now();
  stm_mutex_.lock();
  if(current_map_ && stm_alive_)
  {
    current_map_->addFrame(std::make_shared<VisionInfo>(_time, _qwi, _twi, _res_num, _res_value, _total_num, _raw_img, _gray_img));
    stm_available_.notify_one();
  }
  stm_mutex_.unlock();
  auto end = std::chrono::high_resolution_clock::now();
  std::chrono::duration<double, std::milli> duration = end - start;
}

void ReLocalization::addFrame(const double _time, RGBCloudPtr _rgb_world_cloud)
{
  if(!config_.lidar_en_ || !_rgb_world_cloud)
  {
    return;
  }
  auto start = std::chrono::high_resolution_clock::now();
  stm_mutex_.lock();
  if(current_map_ && stm_alive_)
  {
    current_map_->addFrame(_time, _rgb_world_cloud);
  }
  stm_mutex_.unlock();
  auto end = std::chrono::high_resolution_clock::now();
  std::chrono::duration<double, std::milli> duration = end - start;
}

void ReLocalization::stmLoop()
{
  while(stm_alive_)
  {
    std::unique_lock<std::mutex> stm_lock(stm_mutex_);
    stm_available_.wait(stm_lock);
    stm_lock.unlock();
    while(true)
    {
      stm_lock.lock();
      if(!current_map_)
      {
        stm_lock.unlock();
        break;
      }
      bool lidar_ready = current_map_->lidar_frame_queue_.size()>1;
      bool vision_ready = current_map_->vision_frame_queue_.size()>1;
      if(!lidar_ready && !vision_ready)
      {
        stm_lock.unlock();
        break;
      }
      if(lidar_ready && vision_ready)
      {
        if(current_map_->lidar_frame_queue_.front()->timestamp_ <= current_map_->vision_frame_queue_.front()->timestamp_)
        {
          vision_ready = false;
        }
        else
        {
          lidar_ready = false;
        }
      }
      std::shared_ptr<MapInfo> stm_map_ptr = current_map_;
      stm_lock.unlock();

      if(lidar_ready)
      {
        stm_lock.lock();
        std::shared_ptr<LidarInfo> lidar_info_ref_ptr = stm_map_ptr->lidar_frame_queue_.front();
        stm_map_ptr->lidar_frame_queue_.pop();
        std::shared_ptr<LidarInfo> lidar_info_cur_ptr = stm_map_ptr->lidar_frame_queue_.front();
        stm_lock.unlock();
        if(checkFrameBad(stm_map_ptr, lidar_info_ref_ptr, lidar_info_cur_ptr))
        {
          break;
        }
      }
      else
      {
        stm_lock.lock();
        std::shared_ptr<VisionInfo> vision_info_ref_ptr = stm_map_ptr->vision_frame_queue_.front();
        stm_map_ptr->vision_frame_queue_.pop();
        std::shared_ptr<VisionInfo> vision_info_cur_ptr = stm_map_ptr->vision_frame_queue_.front();
        stm_lock.unlock();
        if(checkFrameBad(stm_map_ptr, vision_info_ref_ptr, vision_info_cur_ptr))
        {
          break;
        }
      }
    }
  }
}

void ReLocalization::mtmLoop()
{
  while(mtm_alive_)
  {
    std::unique_lock<std::mutex> mtm_lock(mtm_mutex_);
    mtm_available_.wait(mtm_lock);
    mtm_lock.unlock();
    while(true)
    {
      std::shared_ptr<MapInfo> map_ptr;
      mtm_lock.lock();
      if(map_mtm_cache_.size())
      {
        map_ptr = map_mtm_cache_.front();
        map_mtm_cache_.pop();
      }
      mtm_lock.unlock();
      if(!map_ptr)
      {
        stm_mutex_.lock();
        if(current_map_)
        {
          map_ptr = current_map_;
        }
        stm_mutex_.unlock();
      }
      if(map_ptr)
      {
        if(map_ptr->end_time_<0.) // need to lock
        {
          mtm_lock.lock();
          while(map_ptr->lidar_keyframe_list_.size()>config_.max_kf_num_)
          {
            map_ptr->lidar_keyframe_list_.pop_front();
          }
          while(map_ptr->vision_keyframe_list_.size()>config_.max_kf_num_)
          {
            map_ptr->vision_keyframe_list_.pop_front();
          }
          mtm_lock.unlock();
          break;
        }
        else // no need to lock
        {
          while(map_ptr->lidar_frame_queue_.size())
          {
            std::shared_ptr<LidarInfo> lidar_frame_ptr = map_ptr->lidar_frame_queue_.front();
            map_ptr->lidar_frame_queue_.pop();
            if(lidar_frame_ptr->timestamp_ <= map_ptr->end_time_)
            {
              map_ptr->saveFrame(lidar_frame_ptr);
              map_ptr->addKeyFrame(lidar_frame_ptr);
            }
          }
          while(map_ptr->vision_frame_queue_.size())
          {
            std::shared_ptr<VisionInfo> vision_frame_ptr = map_ptr->vision_frame_queue_.front();
            map_ptr->vision_frame_queue_.pop();
            if(vision_frame_ptr->timestamp_ <= map_ptr->end_time_)
            {
              map_ptr->saveFrame(vision_frame_ptr);
              map_ptr->addKeyFrame(vision_frame_ptr);
            }
          }
          while(map_ptr->time_rgb_cloud_queue_.size())
          {
            auto time_rgb_cloud = map_ptr->time_rgb_cloud_queue_.front();
            map_ptr->time_rgb_cloud_queue_.pop();
            if(time_rgb_cloud.first <= map_ptr->end_time_)
            {
              map_ptr->saveFrame(time_rgb_cloud);
            }
          }
          int good_idx_lidar = map_ptr->lidar_keyframe_list_.size()-1;
          while(good_idx_lidar>0)
          {
            std::shared_ptr<LidarInfo> lidar_info_ref_ptr = map_ptr->lidar_keyframe_list_[good_idx_lidar-1];
            std::shared_ptr<LidarInfo> lidar_info_cur_ptr = map_ptr->lidar_keyframe_list_[good_idx_lidar];
            Eigen::Matrix4d transform = Eigen::Matrix4d::Identity();
            transform.block<3,3>(0,0) = lidar_info_ref_ptr->qwi_.toRotationMatrix().transpose();
            transform.block<3,1>(0,3) = transform.block<3,3>(0,0)*(-lidar_info_ref_ptr->twi_);
            CloudPtr cloud_ptr_ref(new PointCloudXYZI);
            CloudPtr cloud_ptr_cur(new PointCloudXYZI);
            pcl::transformPointCloud(*lidar_info_ref_ptr->cloud_world_ptr_, *cloud_ptr_ref, transform);
            pcl::transformPointCloud(*lidar_info_cur_ptr->cloud_world_ptr_, *cloud_ptr_cur, transform);

            voxel_filter_.setInputCloud(cloud_ptr_ref);
            voxel_filter_.filter(*cloud_ptr_ref);
            voxel_filter_.setInputCloud(cloud_ptr_cur);
            voxel_filter_.filter(*cloud_ptr_cur);
            if(checkBad(cloud_ptr_ref, cloud_ptr_cur, config_.plane_k_, config_.inliner_dist1_, config_.inliner_dist2_, 0.5, 0.8))
            {
              good_idx_lidar--;
            }
            else
            {
              break;
            }
          }
          int good_idx_vision = map_ptr->vision_keyframe_list_.size()-1;
          while(good_idx_vision>0)
          {
            std::shared_ptr<VisionInfo> vision_info_ref_ptr = map_ptr->vision_keyframe_list_[good_idx_vision-1];
            std::shared_ptr<VisionInfo> vision_info_cur_ptr = map_ptr->vision_keyframe_list_[good_idx_vision];
            auto ratio_pair = checkBad(vision_info_ref_ptr, vision_info_cur_ptr);
            if(ratio_pair.first<0.05 || ratio_pair.second/ratio_pair.first<0.8)
            {
              good_idx_vision--;
            }
            else
            {
              break;
            }
          }
          double end_good_time_lidar = good_idx_lidar>=0 ? map_ptr->lidar_keyframe_list_[good_idx_lidar]->timestamp_ : -10.;
          double end_good_time_vision = good_idx_vision>=0 ? map_ptr->vision_keyframe_list_[good_idx_vision]->timestamp_ : -10.;
          map_ptr->end_good_time_ = std::min(end_good_time_lidar, end_good_time_vision);
        }
      }
      else
      {
        break;
      }
    }
  }
}

void ReLocalization::ltmLoop()
{
  while(ltm_alive_)
  {
    std::unique_lock<std::mutex> mtm_lock(ltm_mutex_);
    ltm_available_.wait(mtm_lock);
  }
}

bool ReLocalization::mustBeBad(const LidarInfo &_info) const
{
  if(_info.res_num_<10 || _info.res_value_>0.2) 
  {
    return true;
  }
  const double ratio = double(_info.res_num_)/_info.total_num_;
  if(ratio<0.)
  {
    return true;
  }
  return false;
}
bool ReLocalization::mayBeBad(const LidarInfo &_info) const
{
  if(_info.total_num_<100 || _info.res_value_>0.1)
  {
    return true;
  }
  const double ratio = double(_info.res_num_)/_info.total_num_;
  if(ratio<0.5)
  {
    return true;
  }
  return false;
}

bool ReLocalization::checkBad(CloudPtr _cloud_ref, CloudPtr _cloud_cur, const int _k, const double _thres1, const double _thres2, const double _ratio1, const double _ratio2) const
{
  double ppoint_inliner_ratio = 0.;
  double pplane_inliner_ratio = 0.;
  double plane_ratio = 0.;

  pcl::KdTreeFLANN<PointType> kdtree_ref;
  kdtree_ref.setInputCloud(_cloud_ref);
  
  const double thres1_squared = _thres1*_thres1;
  int num1 = 0;
  int num2 = 0;
  int den1 = _cloud_cur->size();
  int den2 = 0;
  
  auto &points_ref = _cloud_ref->points;
  for(int i=0; i<den1; i++)
  {
    std::vector<int> pointIdxNKNSearch(_k);
    std::vector<float> pointNKNSquaredDistance(_k);
    PointType &searchPoint = _cloud_cur->points[i];
    if (kdtree_ref.nearestKSearch(searchPoint, _k, pointIdxNKNSearch, pointNKNSquaredDistance) > 0) 
    {
      if(pointNKNSquaredDistance[0]<thres1_squared)
      {
        num1++;
      }
      {
        if(pointNKNSquaredDistance.back()>5.)
        {
          continue;
        }
        Eigen::Matrix<double, 4, 1> pca_result;
        Eigen::Matrix<double, 5, 3> A;
        Eigen::Matrix<double, 5, 1> b;
        b.setOnes();
        b *= -1.0f;

        for (int j = 0; j < 5; j++)
        {
            A(j,0) = points_ref[pointIdxNKNSearch[j]].x;
            A(j,1) = points_ref[pointIdxNKNSearch[j]].y;
            A(j,2) = points_ref[pointIdxNKNSearch[j]].z;
        }

        Eigen::Matrix<double, 3, 1> normvec = A.colPivHouseholderQr().solve(b);

        double n = normvec.norm();
        pca_result(0) = normvec(0) / n;
        pca_result(1) = normvec(1) / n;
        pca_result(2) = normvec(2) / n;
        pca_result(3) = 1.0 / n;

        for (int j = 0; j < 5; j++)
        {
          if (std::abs(pca_result(0) * points_ref[pointIdxNKNSearch[j]].x + pca_result(1) * points_ref[pointIdxNKNSearch[j]].y + pca_result(2) * points_ref[pointIdxNKNSearch[j]].z + pca_result(3)) > _thres2)
          {
            continue;
          }
        }
        den2++;
        if (std::abs(pca_result(0) * searchPoint.x + pca_result(1) * searchPoint.y + pca_result(2) * searchPoint.z + pca_result(3)) < _thres2)
        {
          num2++;
        }
      }
    }
    else
    {
      ;
    }
  }
  ppoint_inliner_ratio = double(num1)/den1;
  if(den2>0)
  {
    pplane_inliner_ratio = double(num2)/den2;
  }
  plane_ratio = double(den2)/den1;
  LINFO << "ppoint_inliner_ratio: " << ppoint_inliner_ratio << ", pplane_inliner_ratio: " << pplane_inliner_ratio << ", plane_ratio: " << plane_ratio
    << ", _ratio1: " << _ratio1 << ", _ratio2: " << _ratio2 << REND;
  return ppoint_inliner_ratio< _ratio1 || pplane_inliner_ratio<_ratio2;
}

std::pair<double, double> ReLocalization::checkBad(std::shared_ptr<VisionInfo> _vision_ref, std::shared_ptr<VisionInfo> _vision_cur) const
{
  if(_vision_ref->corners_.empty())
  {
    LERROR << "corners_ref is empty!" << REND;
    abort();
  }
  std::vector<cv::Point2f> corners_ref;
  std::vector<cv::Point2f> corners_cur;
  featureTrack(_vision_ref, _vision_cur, config_.window_size_*config_.img_scale_, config_.pyramid_level_, config_.f_ransac_, corners_ref, corners_cur);

  double ratio = _vision_ref->corners_.size() > 0 ? double(corners_cur.size())/_vision_ref->corners_.size() : 0.;
  _vision_cur->corners_ = corners_cur;
  int inliner{0};
  Eigen::Matrix3d Rwc1 = _vision_ref->qwi_.toRotationMatrix()*in_extrinsic_.Ric_;
  Eigen::Matrix3d Rwc2 = _vision_cur->qwi_.toRotationMatrix()*in_extrinsic_.Ric_;
  Eigen::Vector3d twc1 = _vision_ref->qwi_.toRotationMatrix()*in_extrinsic_.tic_ + _vision_ref->twi_;
  Eigen::Vector3d twc2 = _vision_cur->qwi_.toRotationMatrix()*in_extrinsic_.tic_ + _vision_cur->twi_;

  Eigen::Matrix3d deltaR = Rwc1.transpose()*Rwc2;
  Eigen::Vector3d deltat = Rwc1.transpose()*(twc2 -twc1);
  Eigen::Matrix3d E = vectorToSkewSymmetric(deltat)*deltaR;
  
  Eigen::Matrix3d K = Eigen::Matrix3d::Identity();
  K(0,0) = in_extrinsic_.fx_*config_.img_scale_;
  K(1,1) = in_extrinsic_.fy_*config_.img_scale_;
  K(0,2) = in_extrinsic_.cx_*config_.img_scale_;
  K(1,2) = in_extrinsic_.cy_*config_.img_scale_;
  Eigen::Matrix3d K_inv = K.inverse();
  Eigen::Matrix3d F = K_inv.transpose()*E*K_inv;

  size_t pair_size = corners_cur.size();
  for(int i=0; i<pair_size; i++)
  {
    Eigen::Vector3d p_1(corners_ref[i].x, corners_ref[i].y, 1.0);
    Eigen::Vector3d p_2(corners_cur[i].x, corners_cur[i].y, 1.0);
    Eigen::Vector3d l_1 = F*p_2;
    double error = std::abs(p_1.transpose()*l_1)/l_1.head(2).norm();

    if(error<config_.inliner_epipolar_)
    {
      inliner++;
    }
  }
  double ratio2 = pair_size > 0 ? double(inliner)/pair_size : 0.;
  return std::make_pair(ratio, ratio2);
}

bool ReLocalization::checkFrameBad(std::shared_ptr<MapInfo> _map_ptr, std::shared_ptr<LidarInfo> _frame_ref_ptr, std::shared_ptr<LidarInfo> _frame_cur_ptr)
{
  if(mustBeBad(*_frame_cur_ptr))
  {
    _map_ptr->ofs_log_ << "Restart due to mustBeBad(Lidar) at " << std::to_string(_frame_cur_ptr->timestamp_) 
      << " with res_num:" << _frame_cur_ptr->res_num_ << ", res_value:" << _frame_cur_ptr->res_value_
      << ", res_ratio:" << double(_frame_cur_ptr->res_num_)/_frame_cur_ptr->total_num_
      << "\n";
    _map_ptr->saveFrame(_frame_ref_ptr);
    mtm_mutex_.lock();
    _map_ptr->addKeyFrame(_frame_ref_ptr, false);
    mtm_mutex_.unlock();
    _map_ptr->updateEndTime(_frame_ref_ptr->timestamp_);
    endCurMap();/// 发布重启信号，记录上下文，开始判断地图可用截止时间
    return true;
  }
  else if(mayBeBad(*_frame_cur_ptr))
  {
    if(_frame_cur_ptr->timestamp_ > last_check_time_ + check_T_)
    {
      last_check_time_ = _frame_cur_ptr->timestamp_;
      Eigen::Matrix4d transform = Eigen::Matrix4d::Identity();
      transform.block<3,3>(0,0) = _frame_ref_ptr->qwi_.toRotationMatrix().transpose();
      transform.block<3,1>(0,3) = transform.block<3,3>(0,0)*(-_frame_ref_ptr->twi_);
      CloudPtr cloud_ptr_ref(new PointCloudXYZI);
      CloudPtr cloud_ptr_cur(new PointCloudXYZI);
      pcl::transformPointCloud(*_frame_ref_ptr->cloud_world_ptr_, *cloud_ptr_ref, transform);
      pcl::transformPointCloud(*_frame_cur_ptr->cloud_world_ptr_, *cloud_ptr_cur, transform);

      voxel_filter_.setInputCloud(cloud_ptr_ref);
      voxel_filter_.filter(*cloud_ptr_ref);
      voxel_filter_.setInputCloud(cloud_ptr_cur);
      voxel_filter_.filter(*cloud_ptr_cur);
      if(checkBad(cloud_ptr_ref, cloud_ptr_cur, config_.plane_k_, config_.inliner_dist1_, config_.inliner_dist2_, 0.5, 0.6))
      {
        _map_ptr->ofs_log_ << "Restart due to checkBad(Lidar) at " << std::to_string(_frame_cur_ptr->timestamp_) << " with thres: 0.5 0.6 " 
          << ", with res_num:" << _frame_cur_ptr->res_num_ << ", res_value:" << _frame_cur_ptr->res_value_
          << ", res_ratio:" << double(_frame_cur_ptr->res_num_)/_frame_cur_ptr->total_num_
          << "\n";
        _map_ptr->saveFrame(_frame_ref_ptr);
        mtm_mutex_.lock();
        _map_ptr->addKeyFrame(_frame_ref_ptr, false);
        mtm_mutex_.unlock();
        _map_ptr->updateEndTime(_frame_ref_ptr->timestamp_);
        endCurMap();/// 发布重启信号，记录上下文，开始判断地图可用截止时间
        return true;
      }
    }
  }
  _map_ptr->saveFrame(_frame_ref_ptr);
  mtm_mutex_.lock();
  _map_ptr->addKeyFrame(_frame_ref_ptr);
  if(_map_ptr->lidar_keyframe_list_.size()>config_.max_kf_num_+10)
  {
    mtm_available_.notify_one();
  }
  mtm_mutex_.unlock();
  while(true)
  {
    stm_mutex_.lock();
    if(_map_ptr->time_rgb_cloud_queue_.empty() || _map_ptr->time_rgb_cloud_queue_.front().first > _frame_ref_ptr->timestamp_)
    {
      stm_mutex_.unlock();
      break;
    }
    auto time_rgb_cloud = _map_ptr->time_rgb_cloud_queue_.front();
    _map_ptr->time_rgb_cloud_queue_.pop();
    stm_mutex_.unlock();
    _map_ptr->saveFrame(time_rgb_cloud);
  }
  return false;
}

bool ReLocalization::checkFrameBad(std::shared_ptr<MapInfo> _map_ptr, std::shared_ptr<VisionInfo> _frame_ref_ptr, std::shared_ptr<VisionInfo> _frame_cur_ptr)
{
  imgPreProcess(_frame_ref_ptr);
  imgPreProcess(_frame_cur_ptr);
  bool add_kf{false};
  if(_frame_ref_ptr->corners_.size()<0.6*config_.feature_num_)
  {
    add_kf = true;
    featureExtract(_frame_ref_ptr);
  }
  auto ratio_pair = checkBad(_frame_ref_ptr, _frame_cur_ptr);
  if(ratio_pair.first>0.2)
  {
    inliner_ratio_ = config_.damping_ratio_*inliner_ratio_ + (1-config_.damping_ratio_)*ratio_pair.second/ratio_pair.first;
  }
  if(ratio_pair.first>0.2 && inliner_ratio_<0.5)
  {
    _map_ptr->ofs_log_ << "Restart due to checkBad(Vision) at " << std::to_string(_frame_cur_ptr->timestamp_) << " with pair: " << ratio_pair.first << ":" << ratio_pair.second << ", inliner_ratio: " << inliner_ratio_ << ", thres: 0.2 0.5" << "\n";
    _map_ptr->saveFrame(_frame_ref_ptr);
    mtm_mutex_.lock();
    featureExtract(_frame_ref_ptr);
    _map_ptr->addKeyFrame(_frame_ref_ptr, false);
    mtm_mutex_.unlock();
    _map_ptr->updateEndTime(_frame_ref_ptr->timestamp_);
    endCurMap();/// 发布重启信号，记录上下文，开始判断地图可用截止时间
    return true;
  }
  _map_ptr->saveFrame(_frame_ref_ptr);
  ;
  mtm_mutex_.lock();
  if(add_kf)
  {
    _map_ptr->addKeyFrame(_frame_ref_ptr, false);
  }
  if(_map_ptr->vision_keyframe_list_.size()>config_.max_kf_num_+10)
  {
    mtm_available_.notify_one();
  }
  mtm_mutex_.unlock();
  return false;
}

void ReLocalization::endCurMap()
{
  stm_mutex_.lock();
  if(!current_map_)
  {
    stm_mutex_.unlock();
    return;
  }
  std::shared_ptr<MapInfo> stm_map_ptr = current_map_;
  current_map_.reset();
  stm_mutex_.unlock();
  restart_info_.cur_map_id_ = stm_map_ptr->map_id_+1;
  if(restart_info_func_)
  {
    restart_info_func_(restart_info_);
  }

  stm_map_ptr->updateEndTime();

  mtm_mutex_.lock();
  map_mtm_cache_.push(stm_map_ptr);
  mtm_mutex_.unlock();
  mtm_available_.notify_one();
  need_reset_ = true;
}

void ReLocalization::startNewMap()
{
  need_reset_ = false;
  stm_mutex_.lock();
  if(current_map_)
  {
    stm_mutex_.unlock();
    endCurMap();
    stm_mutex_.lock();
  }
  current_map_.reset(new MapInfo(map_id_++, root_path_, in_extrinsic_, config_));
  restart_info_.cur_map_id_ = current_map_->map_id_;
  inliner_ratio_ = 1.;
  stm_mutex_.unlock();
  
  if(restart_info_func_)
  {
    restart_info_func_(restart_info_);
  }
}

int ReLocalization::getCurMapId() const
{
  int id_out = -1;
  stm_mutex_.lock();
  if(current_map_)
  {
    id_out = current_map_->map_id_;
  }
  stm_mutex_.unlock();
  
  return id_out;
}

void ReLocalization::registerInfoCallback(std::function<void(const RestartInfo&)> _restart_info_func)
{
  restart_info_func_ = _restart_info_func;
  restart_info_func_(restart_info_);
}

void ReLocalization::imgPreProcess(std::shared_ptr<VisionInfo> _vision_frame_ptr, bool _check_en)
{
  if(_vision_frame_ptr->enhance_img_.empty() || !_check_en)
  {
    cv::Mat resize_img;
    cv::resize(_vision_frame_ptr->gray_img_, resize_img, cv::Size(), config_.img_scale_, config_.img_scale_);
    if(config_.img_enhance_ && clahe_)
    {
      clahe_->apply(resize_img, _vision_frame_ptr->enhance_img_);
    }
    else
    {
      _vision_frame_ptr->enhance_img_ = resize_img;
    }
  }
  if(_vision_frame_ptr->pyramid_img_.empty() || !_check_en)
  {
    _vision_frame_ptr->pyramid_img_.clear();
    auto start = std::chrono::high_resolution_clock::now();
    cv::buildOpticalFlowPyramid(_vision_frame_ptr->enhance_img_, _vision_frame_ptr->pyramid_img_, cv::Size(config_.window_size_*config_.img_scale_, config_.window_size_*config_.img_scale_), config_.pyramid_level_);
    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> duration = end - start;
  }
}

bool ReLocalization::featureExtract(std::shared_ptr<VisionInfo> _vision_frame_ptr, bool _check_en)
{
  int extract_num = config_.feature_num_;
  if(_check_en)
  {
    extract_num = config_.feature_num_ - _vision_frame_ptr->corners_.size();
  }
  else
  {
    _vision_frame_ptr->corners_.clear();
  }
  if(extract_num>0)
  {
    double min_dist = config_.img_scale_*config_.min_dist_;
    cv::Mat mask = cv::Mat(_vision_frame_ptr->enhance_img_.size(), CV_8UC1, cv::Scalar(255));
    for(auto pt:_vision_frame_ptr->corners_)
    {
      cv::circle(mask, pt, min_dist, 0, -1);
    }
    std::vector<cv::Point2f> corners_new_;
    if(config_.max_layer_ >= 0)
    {
      min_dist *= 0.3;
      extract_num *= 10;
    }
    cv::goodFeaturesToTrack(_vision_frame_ptr->enhance_img_, corners_new_, extract_num, 0.01, min_dist, mask);
    if(config_.max_layer_>=0)
    {
      // TODO: 四叉树均匀化
      LERROR << "四叉树均匀化 not implemented" << REND;
      abort();
    }
    _vision_frame_ptr->corners_.reserve(_vision_frame_ptr->corners_.size()+corners_new_.size());
    for(auto &pt:corners_new_)
    {
      _vision_frame_ptr->corners_.emplace_back(pt);
    }
    return true;
  }
  return false;
}

} // namespace relocalization