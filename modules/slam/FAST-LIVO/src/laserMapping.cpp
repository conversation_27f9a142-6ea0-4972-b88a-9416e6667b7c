// This is an advanced implementation of the algorithm described in the
// following paper:
//   <PERSON><PERSON> and <PERSON><PERSON>. LOAM: Lidar Odometry and Mapping in Real-time.
//     Robotics: Science and Systems Conference (RSS). Berkeley, CA, July 2014.

// Modifier: Livox               <EMAIL>

// Copyright 2013, <PERSON>, Carnegie Mellon University
// Further contributions copyright (c) 2016, Southwest Research Institute
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//
// 1. Redistributions of source code must retain the above copyright notice,
//    this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright notice,
//    this list of conditions and the following disclaimer in the documentation
//    and/or other materials provided with the distribution.
// 3. Neither the name of the copyright holder nor the names of its
//    contributors may be used to endorse or promote products derived from this
//    software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
// AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
// LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.
#include "laserMapping.h"

namespace robosense {
namespace slam {

void RsSlam::resetStatics()
{
#ifdef ADAPTIVE_INIT
  livo_initial::PreIntergration::resetStatics();
#endif
  lidar_selection::Feature::resetStatics();
  lidar_selection::Frame::resetStatics();
  lidar_selection::Point::resetStatics();
}

bool RsSlam::IsValid(int type, double ts, double new_iter_ts) {
  if (ts < new_iter_ts - delay_t) {
    // static ofstream log_file;
    if (!IsValid_log_file_.is_open()) {
      IsValid_log_file_.open(log_dir + "/drop_msg.txt");
    }
    IsValid_log_file_ << fixed << "drop msg: " << type << " " << ts << " " << new_iter_ts
             << std::endl;
    return false;
  }
  return true;
}

void RsSlam::standard_pcl_cbk(const CloudPtr &msg, double ts) {
  SensorData data;
  data.type = SensorType::LIDAR;
  data.timestamp = ts;
//  data.cloud = boost::make_shared<PointCloudXYZI>(*msg);
  data.cloud.reset(new PointCloudXYZI(*msg));
  std::lock_guard<std::mutex> lk(mtx_raw_msg_buffer);
  if (ts_msg_buffer.size() &&
      !IsValid(1, ts, ts_msg_buffer.rbegin()->second.timestamp))
    return;

  if (ts_msg_buffer.find(ts) == ts_msg_buffer.end()) {
    ts_msg_buffer[ts] = data;
  } else {
    ts_msg_buffer[ts + 0.000001] = data;
  }
  // 通知buffer线程
  cv_raw_msg.notify_one();
}

// 旋转矩阵，从IMU(右下前坐标系)到lidar(前左上坐标系)
void RsSlam::imu_cbk(const slam::Imu::ConstPtr &msg_in) {
  SensorData data;
  data.type = SensorType::IMU;
  data.imu = msg_in;

  std::lock_guard<std::mutex> lk(mtx_raw_msg_buffer);
  double ts = msg_in->header.ToSec();
  data.timestamp = ts;

  // 非空则维护最新数据早前的一段窗口，窗口外的数据丢弃
  if (ts_msg_buffer.size() &&
      !IsValid(0, ts, ts_msg_buffer.rbegin()->second.timestamp))
    return;

  if (ts_msg_buffer.find(ts) == ts_msg_buffer.end()) {
    ts_msg_buffer[ts] = data;
  } else {
    ts_msg_buffer[ts + 0.000001] = data;
  }
  cv_raw_msg.notify_one();
}

void RsSlam::img_cbk(const std::shared_ptr<cv::Mat> &msg,
                           double timestamp) {
  SensorData data;
  data.type = SensorType::CAMERA;
  data.timestamp = timestamp;
  data.mat = *msg;

  std::lock_guard<std::mutex> lk(mtx_raw_msg_buffer);
  double ts = timestamp;
  if (ts_msg_buffer.size() &&
      !IsValid(2, ts, ts_msg_buffer.rbegin()->second.timestamp))
    return;
  if (ts_msg_buffer.find(ts) == ts_msg_buffer.end()) {
    ts_msg_buffer[ts] = data;
    // TODO: 做一些防呆，和降采样
  } else {
    ts_msg_buffer[ts + 0.000001] = data;
  }
  cv_raw_msg.notify_one();
}

cv::Mat RsSlam::getImageFromMsg(const cv::Mat img_msg) {
  cv::Mat img = img_msg;
  if (img_scaling_ratio < 1.0) {
    cv::Mat resized_img;
    int new_width = static_cast<int>(img.cols * img_scaling_ratio);
    int new_height = static_cast<int>(img.rows * img_scaling_ratio);
    cv::resize(img, resized_img, cv::Size(new_width, new_height));
    cv::Mat img_undist;
    lidar_selector->cam->undistortImage(resized_img, img_undist);
    return img_undist;
  } else {
    cv::Mat img_undist;
    lidar_selector->cam->undistortImage(img_msg, img_undist);
    return img_undist;
  }
}

void RsSlam::ProcessMsgBufferLoop() {
  LDEBUG << name() << " ProcessMsgBufferLoop start. " << REND;
  while (run_flag_) {
    {
      std::unique_lock<std::mutex> lk(mtx_raw_msg_buffer);
      cv_raw_msg.wait(lk);
      if (ts_msg_buffer.empty()) {
        continue;
      }
    }

    while (true) {
      // 处理窗口内最早的数据
      mtx_raw_msg_buffer.lock();
      auto front_iter = ts_msg_buffer.begin();
      SensorData data = front_iter->second;
      if (data.timestamp > ts_msg_buffer.rbegin()->second.timestamp - delay_t) {
        mtx_raw_msg_buffer.unlock();
        break;
      }
      ts_msg_buffer.erase(front_iter);
      mtx_raw_msg_buffer.unlock();

      double timestamp = data.timestamp;
      SensorType msg_type = data.type;
      switch (msg_type) {
      case SensorType::IMU: {
        slam::Imu::ConstPtr msg_in = data.imu;

        // imu消息变到lidar系
        slam::Imu::ConstPtr msg;
        if (trans_imu_to_lidar) {
          slam::Imu imu_L;
          imu_L.header = msg_in->header;
          rotateVector(msg_in->angular_velocity, R_L_I, imu_L.angular_velocity);
          rotateVector(msg_in->linear_acceleration, R_L_I,
                      imu_L.linear_acceleration);
          msg = std::make_shared<slam::Imu>(imu_L);
        } else {
          msg = msg_in;
        }

        mtx_buffer.lock();
        if (save_log) {
          if (last_timestamp_imu < 0)
            f_sensor_buf << fixed << " first imu " << timestamp << endl;
          else
            f_sensor_buf << fixed << " imu " << timestamp << " cnt: " << process_msg_buffer_loop_imu_cnt_++
                         << endl;
        }
        
        double ts = msg->header.ToSec();
        if (ts < last_timestamp_imu) {
          PRINT("imu loop back, clear buffer");
          f_sensor_buf << "imu loop back, clear buffer\n";
          imu_buffer.clear();
          flg_imu_reset_ = true;
        }
        last_timestamp_imu = ts;

        imu_buffer.push_back(msg);
        mtx_buffer.unlock();
#ifdef ADAPTIVE_INIT
      if(livo_init_ptr && img_en)
      {
        livo_init_ptr->addImu(msg->header.ToSec(), 
          Eigen::Vector3d(msg->linear_acceleration.x, msg->linear_acceleration.y, msg->linear_acceleration.z), 
          Eigen::Vector3d(msg->angular_velocity.x, msg->angular_velocity.y, msg->angular_velocity.z)
          );
      }
#endif

        mtx_loop.lock();
        cv_core_loop_.notify_all();
        mtx_loop.unlock();
        break;
      }

      case SensorType::LIDAR: {
        CloudPtr msg = data.cloud;

        mtx_buffer.lock();
        f_sensor_buf << std::fixed << "lid " << timestamp << std::endl;
        if (timestamp < last_timestamp_lidar) {
          PRINT("lidar loop back, clear buffer");
          f_sensor_buf << "lidar loop back, clear buffer\n";
          lidar_buffer.clear();
        }

        lidar_buffer.push_back(msg);
        time_buffer.push_back(timestamp);
        last_timestamp_lidar = timestamp; // 帧尾
        mtx_buffer.unlock();
#ifdef ADAPTIVE_INIT
        if (livo_init_ptr && img_en) {
          // livo_init_ptr->addLidar(timestamp, msg);
        }
#endif
        break;
      }
      
      case SensorType::CAMERA: {
        cv::Mat msg = data.mat;

        mtx_buffer.lock();
        f_sensor_buf << std::fixed << "cam " << timestamp << " ";
        if (!img_en) {
          f_sensor_buf << "\n";
          mtx_buffer.unlock();
          break;
        }
        // static double last_msg_header_time = 0;
        double msg_header_time = timestamp + delta_time;
        // NOTE: 避免偶发的时间戳早的图像被晚接受导致的积分崩溃
        if (msg_header_time < last_timestamp_lidar && last_timestamp_lidar > 0) {
          LERROR << "img timestamp is earlier than lidar timestamp. "
                    "msg_header_time: "
                << msg_header_time
                << " last_timestamp_lidar: " << last_timestamp_lidar << REND;
          f_sensor_buf << "msg_header_time < last_timestamp_lidar: "
                      << msg_header_time << " " << last_timestamp_lidar << "\n";
          mtx_buffer.unlock();
          break;
        }
        process_msg_buffer_loop_cam_cnt_++;
        f_sensor_buf << "img_cnt:" << process_msg_buffer_loop_cam_cnt_ << " " << cam_keep_period << " "
                     << p_imu->imu_need_init_ << " ";
        if (cam_keep_period != -1 && p_imu->imu_need_init_ == false) {
          if (process_msg_buffer_loop_cam_cnt_ % cam_keep_period != 0) {
            f_sensor_buf << "downsample dropping\n";
            mtx_buffer.unlock();
            break;
          }
        }
        if (msg_header_time < last_timestamp_lidar) {
          f_sensor_buf << "last_msg_header_time < last_timestamp_lidar\n";
          mtx_buffer.unlock();
          break;
        }

        double time_dif = (last_timestamp_lidar + 0.1) - msg_header_time;
        double img_minus_last_img = msg_header_time - process_msg_buffer_loop_last_msg_header_time_;
        f_sensor_buf << "img-lidar"
                    << " " << time_dif << " img-last img: " << img_minus_last_img
                    << " imu: " << imu_buffer.size() << std::endl;
        PRINT("[ ProcessMsgBufferLoop ] get img at time: %.3f. img-lidar: %.3f. img-last_img: "
              "%.3f. imu: %ld\n",
              msg_header_time, time_dif, msg_header_time - process_msg_buffer_loop_last_msg_header_time_,
              imu_buffer.size());
        process_msg_buffer_loop_last_msg_header_time_ = msg_header_time;
        if (msg_header_time < last_timestamp_img) {
          PRINT("img loop back, clear buffer");
          img_buffer.clear();
          img_time_buffer.clear();
          img_valid_buffer.clear();
          f_sensor_buf << "img loop back, clear buffer";
        }

        // mtx_buffer.lock();
        img_buffer.push_back(getImageFromMsg(msg));
        img_time_buffer.push_back(msg_header_time);
        if(cam_keep_period != -1 && process_msg_buffer_loop_cam_cnt_ % cam_keep_period != 0)
        {
          img_valid_buffer.push_back(false);
        }
        else
        {
          img_valid_buffer.push_back(true);
        }
        last_timestamp_img = msg_header_time;
        mtx_buffer.unlock();
#ifdef ADAPTIVE_INIT
        if (livo_init_ptr && img_en) {
          livo_init_ptr->addImage(msg_header_time, msg);
        }
#endif
        break;
      }
      default: {
        break;
      }
      } // end switch
    }
  }
  LWARNING << name() << " ProcessMsgBufferLoop exit. " << REND;
}

bool RsSlam::CombineSensorMsgs(LidarMeasureGroup &meas) {
  if (run_flag_ == false) {
    return false;
  }
  if ((lidar_buffer.empty() && img_buffer.empty())) {
    return false;
  }
  if (imu_buffer.size() < 3) {
    return false;
  }
  
#ifdef ADAPTIVE_INIT
  if(p_imu->init_data_ready_ && img_en)
  {
    static int count{0};
    if(count<10)
    {
      mtx_buffer.lock();
      int dst{0};
      for(int i=0; i<img_buffer.size(); i++)
      {
        if(img_time_buffer[i]<=p_imu->init_time_ || img_valid_buffer[i])
        {
          img_buffer[dst] = img_buffer[i];
          img_time_buffer[dst] = img_time_buffer[i];
          img_valid_buffer[dst] = img_valid_buffer[i];
          dst++;
        }
      }
      img_buffer.resize(dst);
      img_time_buffer.resize(dst);
      img_valid_buffer.resize(dst);
      mtx_buffer.unlock();
    }
    count++;
  }
#endif

  if (meas.is_lidar_end) { // If meas.is_lidar_end==true, means it just after
                           // scan end, clear all buffer in meas.
    // 上一帧在处理LIO, 假设V频率高于L, 所以这一帧处理VIO
    meas.measures.clear();
    meas.is_lidar_end = false;
  } else {
    // 上一帧在处理VIO
  }

  // lidar消息未被记录
  if (!lidar_pushed) { // If not in lidar scan, need to generate new meas
    if (lidar_buffer.empty()) {
      return false;
    }
    meas.lidar = lidar_buffer.front(); // push the firsrt lidar topic
    if (meas.lidar->points.size() <= 1) {
      mtx_buffer.lock();
      if (img_buffer.size() >
          0) // temp method, ignore img topic when no lidar points, keep sync
      {
        lidar_buffer.pop_front();
        img_buffer.pop_front();
        img_time_buffer.pop_front();
        img_valid_buffer.pop_front();
      }
      mtx_buffer.unlock();
      LERROR << "meas.lidar->points.size() <= 1" << REND;
      return false;
    }
    sort(meas.lidar->points.begin(), meas.lidar->points.end(),
         time_list); // sort by sample timestamp

    meas.last_lidar_time = lidar_end_time;
    if (lidar_time_is_tail)
      meas.lidar_beg_time =
          time_buffer.front() - meas.lidar->points.back().curvature /
                                    double(1000); // generate lidar_beg_time
    else
      meas.lidar_beg_time = time_buffer.front();
    lidar_end_time =
        meas.lidar_beg_time + meas.lidar->points.back().curvature /
                                  double(1000); // calc lidar scan end time
    meas.lidar_end_time = lidar_end_time;
    lidar_pushed = true; // 表示最早的lidar消息被记录
    LINFO << "[ CombineSensorMsgs ] beg time: " << meas.lidar_beg_time
         << " first pt rel t(ms): " << meas.lidar->points[0].curvature
         << " last rel t(ms): " << meas.lidar->points.back().curvature << REND;
  }
  // 保证imu消息在lidar之后
  if (imu_buffer.back()->header.ToSec() < lidar_end_time + 2.5e-3) {
    return false;
  }

  // 没有img topic，只处理lidar topic
  if (img_buffer.empty()) { // no img topic, means only has lidar topic
    if (last_timestamp_imu <
        lidar_end_time + 2.5e-3) { // imu message needs to be larger than lidar_end_time,
                          // keep complete propagate.
      return false;
    }
    struct MeasureGroup m; // standard method to keep imu message.
    double imu_time = imu_buffer.front()->header.ToSec();
    m.imu.clear();
    mtx_buffer.lock();
    // 取出lidar帧尾前的imu消息
    LTITLE << "[ CombineSensorMsgs ] LIO meas.measures lidar t(s) from:"
           << meas.lidar_beg_time << " to " << lidar_end_time << REND;
    LTITLE << "[ CombineSensorMsgs ] LIO get imu from buffer, t(s) from:"
           << imu_buffer.front()->header.ToSec() << " to "
           << imu_buffer.back()->header.ToSec() << REND;

    while ((!imu_buffer.empty() && (imu_time < lidar_end_time))) {
      imu_time = imu_buffer.front()->header.ToSec();
      if (imu_time > lidar_end_time)
        break;
      m.imu.push_back(imu_buffer.front());
      imu_buffer.pop_front();
    }

    // 丢弃已经记录的lidar消息， 记录下一帧imu消息
    CombineSensorMsgs_last_lidar_t_ = lidar_end_time;
    lidar_buffer.pop_front();
    time_buffer.pop_front();
    if (imu_buffer.empty()) {
      LERROR << "=============imu_buffer empty" << REND;
      m.imu_next = m.imu.back();
    } else {
      m.imu_next = imu_buffer.front(); // RS
    }
    mtx_buffer.unlock();

    lidar_pushed = false; // sync one whole lidar scan.
    meas.is_lidar_end = true;
    meas.measures.push_back(m);

    // if(meas.measures.size())
    // {
    //     LTITLE<<"meas.is_lidar_end:"<<meas.is_lidar_end << "
    //     measures,size()"<< meas.measures.size()<<REND; for(auto
    //     &meas_i:meas.measures)
    //     {
    //         if(meas_i.imu.size())
    //             LTITLE<<std::fixed<<"meas imu t(s)
    //             from:"<<meas_i.imu.front()->header.stamp.toSec()<<"
    //             to:"<<meas_i.imu.back()->header.stamp.toSec() << "
    //             total:"<<meas_i.imu.back()->header.stamp.toSec()-meas_i.imu.front()->header.stamp.toSec()<<
    //             " "<<meas_i.imu.size()<<REND;
    //         else
    //             LERROR<<"no imu"<<REND;
    //     }
    // }
    return true;
  }

  // 有img topic，需要处理lidar或img topic
  struct MeasureGroup m;
  LINFO << "[ CombineSensorMsgs ] buffer size, lidar: " << lidar_buffer.size()
       << " img: " << img_buffer.size() << " imu: " << imu_buffer.size()
       << endl;
  double lidar_t_minus_img_t = lidar_end_time - img_time_buffer.front();
  LINFO << "[ CombineSensorMsgs ] first img: " << std::fixed
        << img_time_buffer.front() << " lidar: " << lidar_end_time
        << " imu: " << last_timestamp_imu
        << " lidar-img(s): " << lidar_t_minus_img_t << END;
  // lidar在前，仅处理lidar
  if ((img_time_buffer.front() > lidar_end_time)) {
    LTITLE << "[ CombineSensorMsgs ] ===only process lidar, lidar-img(s): "
           << lidar_t_minus_img_t << REND;
    if (last_timestamp_imu < lidar_end_time + 2.5e-3) {
      return false;
    }
    double imu_time = imu_buffer.front()->header.ToSec();
    m.imu.clear();
    mtx_buffer.lock();
    while ((!imu_buffer.empty() && (imu_time < lidar_end_time))) {
      imu_time = imu_buffer.front()->header.ToSec();
      if (imu_time > lidar_end_time)
        break;
      m.imu.push_back(imu_buffer.front());
      imu_buffer.pop_front();
    }
    CombineSensorMsgs_last_lidar_t_ = lidar_end_time;
    lidar_buffer.pop_front();
    time_buffer.pop_front();
    m.imu_next = imu_buffer.front(); // RS
    mtx_buffer.unlock();

    lidar_pushed = false;
    meas.is_lidar_end = true;
    meas.measures.push_back(m);
  } else {
    if (img_time_buffer.front() < CombineSensorMsgs_last_lidar_t_ ||
        img_time_buffer.front() < CombineSensorMsgs_last_cam_t_) {
      LERROR << "WRONG CAM TIME:" << std::fixed << img_time_buffer.front()
             << " last_lidar_t: " << CombineSensorMsgs_last_lidar_t_
             << " last_cam_t: " << CombineSensorMsgs_last_cam_t_ << " \n\n\n\n\n"
             << REND;
      mtx_buffer.lock();
      img_buffer.pop_front();
      img_time_buffer.pop_front();
      img_valid_buffer.pop_front();
      mtx_buffer.unlock();
      return false; // NOTE: 避免相机时间戳逆序（相对lidar）
    }
    // img在前，仅处理img
    LDEBUG << "[ CombineSensorMsgs ] ===only process img, lidar-img(s): "
           << lidar_t_minus_img_t << REND;
    double img_start_time =
        img_time_buffer.front(); // process img topic, record timestamp
    if (last_timestamp_imu < img_start_time + 2.5e-3) {
      return false;
    }
    double imu_time = imu_buffer.front()->header.ToSec();
    m.imu.clear();
    m.img_offset_time =
        img_start_time -
        meas.lidar_beg_time; // record img offset time, it shoule be the Kalman
                             // update timestamp.
    m.img = img_buffer.front();
    m.last_img_time = img_start_time;
    mtx_buffer.lock();
    // 取出img前的imu消息
    while ((!imu_buffer.empty() && (imu_time < img_start_time))) {
      imu_time = imu_buffer.front()->header.ToSec();
      if (imu_time > img_start_time)
        break;
      m.imu.push_back(imu_buffer.front());
      imu_buffer.pop_front();
    }
    CombineSensorMsgs_last_cam_t_ = img_start_time;
    img_buffer.pop_front();
    img_time_buffer.pop_front();
    img_valid_buffer.pop_front();
    m.imu_next = imu_buffer.front(); // RS
    mtx_buffer.unlock();

    meas.is_lidar_end = false;
    meas.measures.push_back(m);
  }
  return true;
}

void RsSlam::CoreLoop() {
  LDEBUG << name() << "CoreLoop start. " << REND;
  while (run_flag_) {

    std::unique_lock<std::mutex> cb_mutex_lg(mtx_loop);
    cv_core_loop_.wait(cb_mutex_lg);
    cb_mutex_lg.unlock();

    while (true) {
      if (!CombineSensorMsgs(LidarMeasures)) {
        break;
      }
      std::lock_guard<std::mutex> lg(mtx_front_end_); // isolated with back end

      if (flg_imu_reset_) {
        p_imu->Reset();
        flg_imu_reset_ = false;
        continue;
      }

      // record time-cost of each module
      match_time = kdtree_search_time = kdtree_search_counter = solve_time =
          solve_const_H_time = 0;

      loop_beg_t = omp_get_wtime();

      // 动态初始化
#ifdef ADAPTIVE_INIT
      if (img_en && p_imu->imu_need_init_ && !p_imu->init_data_ready_) {
        double tgt_time = 0.;
        if (LidarMeasures.is_lidar_end) {
          tgt_time = LidarMeasures.lidar_beg_time + LidarMeasures.lidar->points.back().curvature / double(1000);
        } else {
          tgt_time = LidarMeasures.lidar_beg_time + LidarMeasures.measures.back().img_offset_time;
        }

        while (run_flag_ && livo_init_ptr && p_imu) {
          p_imu->init_data_ready_ = livo_init_ptr->getInitResult(p_imu->init_time_, p_imu->init_R_, p_imu->init_bg_, p_imu->init_v_, p_imu->init_g_);
          if (p_imu->init_time_ >= tgt_time) {
            LINFO << "[ADAPTIVE_INIT] p_imu->init_time_ >= tgt_time " << REND;
            break;
          } else {
            std::this_thread::sleep_for(std::chrono::milliseconds(2));
          }
        }
        if (livo_init_ptr && p_imu && p_imu->init_data_ready_) {
          livo_init_ptr->clearBuf();
        } else {
          LERROR << "[ADAPTIVE_INIT] " << (livo_init_ptr == nullptr) << " "
                 << (p_imu == nullptr) << " " << REND;
          LERROR << "[ADAPTIVE_INIT] " << p_imu->init_data_ready_ << REND;
        }
      }
#endif
      // 前向传播+反向传播去畸变
      double process2_t_start = omp_get_wtime();
      p_imu->PredicteStateAndUndistortCloud(LidarMeasures, state,
                                            feats_undistort);
      state_propagat = state;
      process2_t = omp_get_wtime() - process2_t_start;
      livo_result_->time_cost->imu_process_t = process2_t;

      if (lidar_selector->debug) {
        LidarMeasures.debug_show();
      }

      // LIO初始化条件
      if (feats_undistort->empty() || (feats_undistort == nullptr)) {
        if (!is_ready) {
          first_lidar_time = LidarMeasures.lidar_beg_time;
          p_imu->first_lidar_time = first_lidar_time;
          LidarMeasures.measures.clear();
          LERROR << "[ IMU initialing] FAST-LIO not ready because of no lidar points. " << REND;
          livo_result_->is_ready = is_ready;
          if(full_result_cb_){
            full_result_cb_(*livo_result_);
          }
          continue;
        }
      }
      is_ready = true;
      flg_EKF_inited = LidarMeasures.lidar_beg_time - first_lidar_time < 0.5 ? false : true;

      // get KdTree cloud
      if ((!LidarMeasures.is_lidar_end && use_map_proj) ||
          (LidarMeasures.is_lidar_end &&
          (show_lidar_map || boundary_point_remove))) {
        auto tt = omp_get_wtime();
        PointVector().swap(ikdtree.PCL_Storage);
        ikdtree.flatten(ikdtree.Root_Node, ikdtree.PCL_Storage, NOT_RECORD);
        featsFromMap->points = ikdtree.PCL_Storage;
        flatten_t = omp_get_wtime() - tt;
        livo_result_->cloud_process_result->current_size = featsFromMap->size();
      }

      // VIO
      if (!LidarMeasures.is_lidar_end) {
  #if 1
        // TODO: 完善VIO初始化条件
  #else
        if (core_loop_first_) {
          first_lidar_time_ = LidarMeasures.lidar_beg_time;
          core_loop_first_ = false;
        }
        time_past = LidarMeasures.lidar_beg_time - first_lidar_time_;
        if (time_past < VIO_wait_time) {
          LDEBUG << "[ VIO ] return for init" << REND;
          if (show_lidar_map)
            publish_map(pubLaserCloudMap);
          continue;
        }
  #endif
        if (img_en) {
          auto tt = omp_get_wtime();
          EstimateVIOState();
          livo_result_->time_cost->vio_t = omp_get_wtime() - tt;

          tt = omp_get_wtime();
          PublishVIOResult();
#ifdef BACK_END
          /*** save data for loop detection and correction ***/
          if (enable_loop_detection_ && rgb_scan->size()) {
            LoopData loop_data;
            loop_data.type = SensorType::CAMERA; // has rgb
            loop_data.LIO_residual = livo_result_->lio_result->log->res_mean_last;
            // loop_data.VIO_residual = livo_result_->lio_result->log->res_mean_last; // FIXME
            loop_data.lidar_timestamp = livo_result_->lio_result->T_W_I.timestamp;
            loop_data.cam_timestamp = LidarMeasures.measures.back().last_img_time;
            loop_data.raw_I_pose = livo_result_->lio_result->T_W_I; // cloud pose
            loop_data.raw_I_pose_pre = LIO_T_W_I_pose_last;
            loop_data.raw_V_pose = livo_result_->vio_result->T_W_C;
            loop_data.raw_cloud_L = livo_result_->lio_result->scan_L;
            loop_data.raw_cloud_I = livo_result_->lio_result->scan_I;
            loop_data.raw_cloud_W = livo_result_->lio_result->scan;
            RGB2RGBN(livo_result_->lio_result->rgb_scan,
                     loop_data.raw_rgb_cloud_W);
            RGB2RGBN(livo_result_->lio_result->rgb_scan_L,
                     loop_data.raw_rgb_cloud_L);
            RGB2RGBN(livo_result_->lio_result->rgb_scan_I,
                     loop_data.raw_rgb_cloud_I);       
            loop_data_queue_mutex_.lock();
            loop_data_queue_.push_back(loop_data);
            loop_data_queue_mutex_.unlock();
            LBLUE << "[ LD ][ VIO ] save loop_data for LD, cur lidar ts: "
                  << loop_data.lidar_timestamp << REND;
            if (loop_data_queue_.size()) {
              LBLUE << " queue: " << loop_data_queue_.size() << " from "
                    << loop_data_queue_.front().lidar_timestamp << "(pre: "
                    << loop_data_queue_.front().raw_I_pose_pre.timestamp
                    << ") to " << loop_data_queue_.back().lidar_timestamp
                    << "(pre: "
                    << loop_data_queue_.back().raw_I_pose_pre.timestamp << ")"
                    << REND;
            }
            // TODO: size limit

            // cache for ResetFrontEndLoop thread
            mtx_reset_fe_.lock();
            if (rebuild_thread_running_) {
              LBLUE << "[ LD ] save loop_cache_data for rebuild" << REND;
              LoopCacheData loop_cache(loop_data);
              loop_cache.SaveCache(loop_data.lidar_timestamp, LIO_T_W_I_pose_last,
                                   LIO_T_W_I_pose, loop_cache_queue_,
                                   loop_cache_queue_full_);
              mtx_reset_fe_.unlock();
              // TODO: rebuild within 1 frame, need more
              if (1) {
                cv_reset_fe_.notify_all(); // wake up ResetFrontEndLoop
                while (rebuild_thread_running_) {
                  std::this_thread::sleep_for(std::chrono::milliseconds(10));
                }
                // ResetFrontEndLoop done
              }
            } else {
              livo_result_->be_res_->fixed_I_pose[loop_data.lidar_timestamp] =
                  loop_data.raw_I_pose;
              mtx_reset_fe_.unlock();
            }
          }
#endif
        }
        loop_end_t = omp_get_wtime();
        continue; // VIO end loop
      }

      // LIO prepare
      /*** crop kdtree, keep the rest in lidar FOV ***/
      CropKdTree(state);

      /*** remove noisy cloud in scan, downsample scan, calc VIO weight***/
      PreprocessCloud(feats_undistort);

      /*** initialize the map kdtree ***/
      if (ikdtree.Root_Node == nullptr) {
        BuildKdTree(feats_down_body);
        continue;
      }

      // LIO state update
      /*** iterated state estimation ***/
      double t_update_start = omp_get_wtime();
      if (lidar_en) {
        EstimateLIOState();
      }
      update_LIO_state_t = omp_get_wtime() - t_update_start;

      /*** kdtree grow, add the feature points to map kdtree ***/
      double kdtree_grow_t_start = omp_get_wtime();
      if (stop_grow_kdtree_map == false) {
        KdTreeGrow();
      } else {
        LWARNING << "[ LIO ] stop grow map at " << state.timestamp << REND;
      }
      kdtree_grow_t = omp_get_wtime() - kdtree_grow_t_start;
      loop_end_t = omp_get_wtime();
      livo_result_->time_cost->lio_t =
          kdtree_delete_time + update_LIO_state_t + kdtree_grow_t;

      PublishLIOResult();
#ifdef BACK_END
      if (enable_loop_detection_ && !img_en && xyzi_scan->size()) {
        LoopData loop_data;
        loop_data.type = SensorType::LIDAR; // has not rgb
        loop_data.LIO_residual = livo_result_->lio_result->log->res_mean_last;
        loop_data.lidar_timestamp = livo_result_->lio_result->T_W_I.timestamp;
        loop_data.raw_L_pose = livo_result_->lio_result->T_W_L;
        loop_data.raw_I_pose = livo_result_->lio_result->T_W_I;
        loop_data.raw_I_pose_pre = LIO_T_W_I_pose_last;
        loop_data.raw_cloud_L = livo_result_->lio_result->scan_L;
        loop_data.raw_cloud_I = livo_result_->lio_result->scan_I;
        loop_data.raw_cloud_W = livo_result_->lio_result->scan;
        IN2RGBN(livo_result_->lio_result->scan, loop_data.raw_rgb_cloud_W);
        IN2RGBN(livo_result_->lio_result->scan_L, loop_data.raw_rgb_cloud_L);
        IN2RGBN(livo_result_->lio_result->scan_I, loop_data.raw_rgb_cloud_I);
        loop_data_queue_mutex_.lock();
        loop_data_queue_.push_back(loop_data);
        loop_data_queue_mutex_.unlock();
        LBLUE << "[ LD ][ LIO ] save loop_data for LD, cur lidar ts: "
              << loop_data.lidar_timestamp << REND;
        if (loop_data_queue_.size()) {
          LBLUE << " queue: " << loop_data_queue_.size() << " from "
                << loop_data_queue_.front().lidar_timestamp
                << "(pre: " << loop_data_queue_.front().raw_I_pose_pre.timestamp
                << ") to " << loop_data_queue_.back().lidar_timestamp
                << "(pre: " << loop_data_queue_.back().raw_I_pose_pre.timestamp
                << ")" << REND;
            }
        // TODO: size limit

        // cache for ResetFrontEndLoop thread
        mtx_reset_fe_.lock();
        if (rebuild_thread_running_) {
          LBLUE << "[ LD ] save LIO loop_cache_data for rebuild" << REND;
          LoopCacheData loop_cache(loop_data);
          loop_cache.SaveCache(loop_data.lidar_timestamp, LIO_T_W_I_pose_last,
                               LIO_T_W_I_pose, loop_cache_queue_,
                               loop_cache_queue_full_);
          mtx_reset_fe_.unlock();
          cv_reset_fe_.notify_all(); // wake up ResetFrontEndLoop
          while (rebuild_thread_running_) {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
          }
        } else {
          livo_result_->be_res_->fixed_I_pose[loop_data.lidar_timestamp] =
              loop_data.raw_I_pose;
          mtx_reset_fe_.unlock();
        }
      }
#endif
    }
  }

  /**************** save map ****************/
  SaveMap();

  fout_out.close();
  fout_pre.close();
  f_state_utm.close();
  f_lidar_state_utm.close();
  LWARNING << name() << " CoreLoop exit." << REND;
}

//
void RsSlam::PreprocessCloud(CloudPtr feats_undistort) {
  auto tt = omp_get_wtime();

  // 去除点云拖点
  int feats_size = feats_undistort->size();
  if (boundary_point_remove && feats_size > 100 &&
      featsFromMap->points.size() > 0) {
#if 0
            // 计算source法向量
            int num_near = 5;
            vector<PointVector> nearest_feat_vec; 
            nearest_feat_vec.resize(feats_size);
            int near_map_cnt{0}, far_map_cnt{0};
#ifdef MP_EN
                omp_set_num_threads(MP_PROC_NUM);
#pragma omp parallel for
#endif
            for (int i = 0; i < feats_size; ++i)
            {
                PointType &point_body = feats_undistort->points[i];
                PointType point_world;
                pointBodyToWorld(&point_body, &point_world, state_propagat);
                vector<float> sq_dis_near(num_near);
                ikdtree.Nearest_Search(point_world, num_near,
                                       nearest_feat_vec[i], sq_dis_near);
                bool kdtree_condition = nearest_feat_vec[i].size() == 0 || sq_dis_near[0] > 5;
                if (kdtree_condition) {
                    point_body.curvature = 999; // 远离地图的点
                    ++far_map_cnt;
                } else {
                    double angle = CalcDiffNormalAngle(nearest_feat_vec[i], sq_dis_near) * R2D;
                    point_body.curvature = angle > 99 ? 99 : angle; // 平均法向量夹角
                    ++near_map_cnt;
                }                 
            }
#else
    for (int i = 0; i < feats_size; ++i) {
      feats_undistort->points[i].curvature = 99;
    }
#endif
    Pose T_W_I_pose = Pose(state_propagat.rot_end, state_propagat.pos_end,
                      state.timestamp);
    T_W_I_pose.updatePoseRight(T_I_L_pose);
    lidar_odometry_->AddLiDAR(feats_undistort, T_W_I_pose);
    *feats_undistort = lidar_odometry_->GetValidCloud();

    auto seg_res = lidar_odometry_->GetSegResult();
    auto filter_res = lidar_odometry_->GetFilterResult();

    auto& pro_res =  livo_result_->cloud_process_result;
    pro_res->valid_size = seg_res->fullCloudIndex->size();
    pro_res->valid_seg_size = seg_res->segmentedCloud->size();
    pro_res->boundary_semantic_size = filter_res->segmentedCloud->size();
    pro_res->boundary_size = filter_res->outlierCloud->size();
    pro_res->non_boundary_size = filter_res->validCloud->size();    
  }

  // 过滤远处点
  feats_size = feats_undistort->size();
  CloudPtr rm_far_pt_cloud(new pcl::PointCloud<PointType>);
  rm_far_pt_cloud->reserve(feats_size);
  for (int i = 0; i < feats_size; ++i) {
    PointType &pt = feats_undistort->points[i];
    double range = std::sqrt(pt.x * pt.x + pt.y * pt.y + pt.z * pt.z);
    if (range < blind_max) {
      rm_far_pt_cloud->push_back(pt);
    }
  }
  *feats_undistort = *rm_far_pt_cloud;

  /*** downsample the feature points in a scan ***/
  if (downsample_source_cloud) {
    downSizeFilterSurf.setInputCloud(feats_undistort);
    downSizeFilterSurf.filter(*feats_down_body);
  } else {
    *feats_down_body = *feats_undistort;
  }
  feats_down_size = feats_down_body->points.size();
  {
    for (auto &pt : feats_down_body->points) {
      double range = std::sqrt(pt.x * pt.x + pt.y * pt.y + pt.z * pt.z);
      if (range < 0.3) {
        pt.curvature = 0.05;
      } else if (range < 1.) {
        pt.curvature = 0.05 + (0.02 - 0.05) * (range - 0.3) / (1. - 0.3);
      } else {
        pt.curvature = 0.02;
      }
    }
  }

  preprocess_t = omp_get_wtime() - tt;
  livo_result_->time_cost->cloud_process_t = preprocess_t;
  livo_result_->cloud_process_result->undistort_size = feats_size;
  livo_result_->cloud_process_result->feats_down_size = feats_down_size;
}

// LIO
void RsSlam::BuildKdTree(CloudPtr feats_down_body) {
  if (feats_down_size > 5) {
    ikdtree.set_downsample_param(filter_size_map_min);
    feats_down_world->resize(feats_down_size);
    for (int i = 0; i < feats_down_size; i++) {
      pointBodyToWorld(&(feats_down_body->points[i]),
                       &(feats_down_world->points[i]));
    }
    auto normals = getNormals(feats_down_world, 5, 0);
    for (int i = 0; i < feats_down_size; i++) {
      feats_down_world->points[i].normal_x = normals->points[i].normal_x;
      feats_down_world->points[i].normal_y = normals->points[i].normal_y;
      feats_down_world->points[i].normal_z = normals->points[i].normal_z;
    }
    ikdtree.Build(feats_down_world->points);

    livo_result_->cloud_process_result->build_size = feats_down_world->size();
  }
}

void RsSlam::CropKdTree(const StatesGroup &state) {
  cub_needrm.clear();
  kdtree_delete_counter = 0;
  kdtree_delete_time = 0.0;
  const V3D& pos_LiD = state.pos_end;
  bool need_move = false;
#if 1 // RS, simply remove near 26 voxel where min = half_len and max = lid_pos + max_range
  std::vector<Eigen::Vector3i> far_directions;
  for (int x = -1; x <= 1; x++) {
    for (int y = -1; y <= 1; y++) {
      for (int z = -1; z <= 1; z++) {
        if (x || y || z)
          far_directions.push_back(Eigen::Vector3i(x, y, z));
      }
    }
  }
  double half_len = cube_len / 2.0;
  BoxPointType rm_box;
  for (const auto &dir : far_directions) {
    for (int i = 0; i < 3; ++i) {
      rm_box.vertex_min[i] = pos_LiD(i) + half_len * dir[i];
      rm_box.vertex_max[i] =
          pos_LiD(i) + std::max(cube_len * 10., 150.) * dir[i]; // cover max_range
      if (rm_box.vertex_min[i] > rm_box.vertex_max[i])
        std::swap(rm_box.vertex_min[i], rm_box.vertex_max[i]);

      if (dir[i] == 0) {
        rm_box.vertex_min[i] = pos_LiD(i) - half_len;
        rm_box.vertex_max[i] = pos_LiD(i) + half_len;
      }
    }
    cub_needrm.push_back(rm_box);
  }
  livo_result_->cloud_process_result->need_move = need_move = true;
#else // bug in official
  PRINT("Local Map is (%0.2f,%0.2f) (%0.2f,%0.2f) (%0.2f,%0.2f)\n",
          LocalMap_Points.vertex_min[0], LocalMap_Points.vertex_max[0],
          LocalMap_Points.vertex_min[1], LocalMap_Points.vertex_max[1],
          LocalMap_Points.vertex_min[2], LocalMap_Points.vertex_max[2]);
  if (!Localmap_Initialized) {
    for (int i = 0; i < 3; i++) {
      LocalMap_Points.vertex_min[i] = pos_LiD(i) - cube_len / 2.0;
      LocalMap_Points.vertex_max[i] = pos_LiD(i) + cube_len / 2.0;
    }
    Localmap_Initialized = true;
    return;
  }
  float dist_to_map_edge[3][2];
  for (int i = 0; i < 3; i++) {
    dist_to_map_edge[i][0] = fabs(pos_LiD(i) - LocalMap_Points.vertex_min[i]);
    dist_to_map_edge[i][1] = fabs(pos_LiD(i) - LocalMap_Points.vertex_max[i]);
    if (dist_to_map_edge[i][0] <= MOV_THRESHOLD * DET_RANGE ||
        dist_to_map_edge[i][1] <= MOV_THRESHOLD * DET_RANGE)
      livo_result_->cloud_process_result->need_move = need_move = true;
  }
  if (!need_move)
    return;
  BoxPointType New_LocalMap_Points, tmp_boxpoints;
  New_LocalMap_Points = LocalMap_Points;
  float mov_dist = max((cube_len - 2.0 * MOV_THRESHOLD * DET_RANGE) * 0.5 * 0.9,
                       double(DET_RANGE * (MOV_THRESHOLD - 1)));
  for (int i = 0; i < 3; i++) {
    tmp_boxpoints = LocalMap_Points;
    if (dist_to_map_edge[i][0] <= MOV_THRESHOLD * DET_RANGE) {
      New_LocalMap_Points.vertex_max[i] -= mov_dist;
      New_LocalMap_Points.vertex_min[i] -= mov_dist;
      tmp_boxpoints.vertex_min[i] = LocalMap_Points.vertex_max[i] - mov_dist;
      cub_needrm.push_back(tmp_boxpoints);
      // PRINT("Delete Box is (%0.2f,%0.2f) (%0.2f,%0.2f) (%0.2f,%0.2f)\n",
      // tmp_boxpoints.vertex_min[0],tmp_boxpoints.vertex_max[0],tmp_boxpoints.vertex_min[1],tmp_boxpoints.vertex_max[1],tmp_boxpoints.vertex_min[2],tmp_boxpoints.vertex_max[2]);
    } else if (dist_to_map_edge[i][1] <= MOV_THRESHOLD * DET_RANGE) {
      New_LocalMap_Points.vertex_max[i] += mov_dist;
      New_LocalMap_Points.vertex_min[i] += mov_dist;
      tmp_boxpoints.vertex_max[i] = LocalMap_Points.vertex_min[i] + mov_dist;
      cub_needrm.push_back(tmp_boxpoints);
      // PRINT("Delete Box is (%0.2f,%0.2f) (%0.2f,%0.2f) (%0.2f,%0.2f)\n",
      // tmp_boxpoints.vertex_min[0],tmp_boxpoints.vertex_max[0],tmp_boxpoints.vertex_min[1],tmp_boxpoints.vertex_max[1],tmp_boxpoints.vertex_min[2],tmp_boxpoints.vertex_max[2]);
    }
  }
  LocalMap_Points = New_LocalMap_Points;
#endif

  RemovePointsFromKdTree();
  double delete_begin = omp_get_wtime();
  if (cub_needrm.size() > 0)
    kdtree_delete_counter = ikdtree.Delete_Point_Boxes(cub_needrm);
  kdtree_delete_time = omp_get_wtime() - delete_begin;

  // PRINT("Delete time: %0.6f, delete size: %d\n", kdtree_delete_time,
  //       kdtree_delete_counter);
  // PRINT("Delete Box: %d\n", int(cub_needrm.size()));

  livo_result_->cloud_process_result->need_move= need_move;
  livo_result_->cloud_process_result->kdtree_delete_counter = kdtree_delete_counter;
  livo_result_->cloud_process_result->kdtree_delete_time = kdtree_delete_time;
}

void RsSlam::EstimateLIOState() {
  int featsFromMapNum = ikdtree.size();
  LDEBUG << "[ LIO ] Raw feature num: " << feats_undistort->points.size()
         << " feats_down_size: " << feats_down_size
         << " Map num: " << featsFromMapNum << "." << REND;

  euler_cur = RotMtoEuler(state.rot_end);
  fout_pre << std::fixed << state.timestamp
           << " " << euler_cur.transpose() * R2D << " "
           << state.pos_end.transpose() << " " << state.vel_end.transpose()
           << " " << state.bias_g.transpose() << " " << state.bias_a.transpose()
           << " " << state.gravity.transpose() << endl;

  if (boundary_point_remove && feats_down_size < 200 ||
      !boundary_point_remove && feats_down_size < 500) {
    LERROR << "feats_down_size too small" << REND;
    // return; // TODO: 完善退出条件
  }
#ifdef MP_EN
  PRINT("[ LIO ]: Using multi-processor, used core number: %d.\n",
         MP_PROC_NUM);
#endif
  /*** ICP and iterated Kalman filter update ***/
  normvec->resize(feats_down_size);
  feats_down_world->resize(feats_down_size);
  res_last.resize(feats_down_size, 1000.0);

  point_selected_surf.resize(feats_down_size, 1);
  pointSearchInd_surf.resize(feats_down_size);
  Nearest_Points.resize(feats_down_size);
  int rematch_num = 0;
  bool nearest_search_en = true;

  for (iterCount = -1; iterCount < NUM_MAX_ITERATIONS && flg_EKF_inited;
       iterCount++) {
    lidar_selector->lidar_iter_num = iterCount + 2;
    match_start = omp_get_wtime();
    PointCloudXYZI().swap(*laserCloudOri);
    PointCloudXYZI().swap(*corr_normvect);
    valid_source_indices.reset(new pcl::PointIndices);
    invalid_source_indices.reset(new pcl::PointIndices);
    invalid_reason.clear();
    total_residual = 0.0;

/** closest surface search and residual computation **/
#ifdef MP_EN
    omp_set_num_threads(MP_PROC_NUM);
#pragma omp parallel for
#endif
    for (int i = 0; i < feats_down_size; i++) {
      PointType &point_body = feats_down_body->points[i];
      PointType &point_world = feats_down_world->points[i];
      point_world.normal_x = point_world.normal_y = point_world.normal_z = 99;
      V3D p_body(point_body.x, point_body.y, point_body.z);
      pointBodyToWorld(&point_body, &point_world);
      vector<float> pointSearchSqDis(NUM_MATCH_POINTS);
      auto &points_near = Nearest_Points[i];
      if (nearest_search_en) {
        double search_start = omp_get_wtime();
        /** Find the closest surfaces in the map **/
        ikdtree.Nearest_Search(point_world, NUM_MATCH_POINTS, points_near,
                               pointSearchSqDis);
        if(pointSearchSqDis.size()<NUM_MATCH_POINTS)
        {
          point_selected_surf[i] = 0;
          continue;
        }
        point_selected_surf[i] =
            pointSearchSqDis[NUM_MATCH_POINTS - 1] > 5 ? 0 : 1;
        kdtree_search_time += omp_get_wtime() - search_start;
        kdtree_search_counter++;
      }
      if (!point_selected_surf[i] || points_near.size() < NUM_MATCH_POINTS)
        continue;

      VF(4) pabcd;
      point_selected_surf[i] = 0;
      if (esti_plane(pabcd, points_near, 0.1f)) //(planeValid)
      {
        float pd2 = pabcd(0) * point_world.x + pabcd(1) * point_world.y +
                    pabcd(2) * point_world.z + pabcd(3);
        float s = 1 - 0.9 * fabs(pd2) / sqrt(p_body.norm());

        if (s > 0.9) {
          point_selected_surf[i] = 1;
          normvec->points[i].x = pabcd(0);
          normvec->points[i].y = pabcd(1);
          normvec->points[i].z = pabcd(2);
          normvec->points[i].intensity = pd2;
          res_last[i] = abs(pd2);
        }
        point_world.normal_x = pabcd(0);
        point_world.normal_y = pabcd(1);
        point_world.normal_z = pabcd(2);
        point_body.normal_x = pabcd(0);
        point_body.normal_y = pabcd(1);
        point_body.normal_z = pabcd(2);
      }
    }
    // LINFO<<"pca time test: "<<pca_time1<<" "<<pca_time2<<END;
    effct_feat_num = 0;
    laserCloudOri->resize(feats_down_size);
    corr_normvect->resize(feats_down_size);
    valid_source_indices->indices.reserve(feats_down_size);
    invalid_source_indices->indices.reserve(feats_down_size);
    invalid_reason.resize(feats_down_size);

    for (int i = 0; i < feats_down_size; i++) {
      if (point_selected_surf[i] && (res_last[i] <= p2plane_thresh)) {
        laserCloudOri->points[effct_feat_num] = feats_down_body->points[i];
        // feats_down_body->points[i].curvature = abs(res_last[i]);
        // feats_down_world->points[i].curvature = abs(res_last[i]);
        corr_normvect->points[effct_feat_num] = normvec->points[i];
        valid_source_indices->indices.push_back(i);
        total_residual += res_last[i];
        effct_feat_num++;
      } else {
        invalid_source_indices->indices.push_back(i);
        if (!point_selected_surf[i])
          invalid_reason[i] = 10;
        else if (res_last[i] > 0.5)
          invalid_reason[i] = 20;
      }
    }

    res_mean_last = total_residual / effct_feat_num;
    match_time += omp_get_wtime() - match_start;
    LTITLE << "[ LIO mapping ] Effective feature num: " << effct_feat_num
           << " res_mean_last " << res_mean_last << REND;

    solve_start = omp_get_wtime();
    lidar_selector->lidar_feat_num = effct_feat_num;

    /*** Computation of Measuremnt Jacobian matrix H and measurents vector ***/
    MatrixXd Hsub(effct_feat_num, 6);
    VectorXd meas_vec(effct_feat_num);
    // LTITLE<<"iterCount: "<<iterCount+1<<", effct_feat_num:
    // "<<effct_feat_num<<REND;
    for (int i = 0; i < effct_feat_num; i++) {
      const PointType &laser_p = laserCloudOri->points[i];
      V3D point_this(laser_p.x, laser_p.y, laser_p.z);
      point_this = Lidar_rot_to_IMU * point_this + Lidar_offset_to_IMU;
      M3D point_crossmat;
      point_crossmat << SKEW_SYM_MATRX(point_this);

      /*** get the normal vector of closest surface/corner ***/
      const PointType &norm_p = corr_normvect->points[i];
      V3D norm_vec(norm_p.x, norm_p.y, norm_p.z);

      /*** calculate the Measuremnt Jacobian matrix H ***/
      V3D A(point_crossmat * state.rot_end.transpose() * norm_vec);
      Hsub.row(i) << VEC_FROM_ARRAY(A), norm_p.x, norm_p.y, norm_p.z;

      /*** Measuremnt: distance to the closest surface/corner ***/
      meas_vec(i) = -norm_p.intensity;
    }
    solve_const_H_time += omp_get_wtime() - solve_start;

    MatrixXd K(DIM_STATE, effct_feat_num);

    EKF_stop_flg = false;
    flg_EKF_converged = false;

    /*** Iterative Kalman Filter Update ***/
    if (!flg_EKF_inited) {
      LINFO << "||||||||||Initiallizing LiDar||||||||||" << endl;
      /*** only run in initialization period ***/
      MatrixXd H_init(MD(9, DIM_STATE)::Zero());
      MatrixXd z_init(VD(9)::Zero());
      H_init.block<3, 3>(0, 0) = M3D::Identity();
      H_init.block<3, 3>(3, 3) = M3D::Identity();
      H_init.block<3, 3>(6, 15) = M3D::Identity();
      z_init.block<3, 1>(0, 0) = -Log(state.rot_end);
      z_init.block<3, 1>(0, 0) = -state.pos_end;

      auto H_init_T = H_init.transpose();
      auto &&K_init =
          state.cov * H_init_T *
          (H_init * state.cov * H_init_T + 0.0001 * MD(9, 9)::Identity())
              .inverse();
      solution = K_init * z_init;
      state.resetpose();
      EKF_stop_flg = true;
    } else {
      auto &&Hsub_T = Hsub.transpose();
      auto &&HTz = Hsub_T * meas_vec;
      H_T_H.block<6, 6>(0, 0) = Hsub_T * Hsub;
      // EigenSolver<Matrix<double, 6, 6>> es(H_T_H.block<6,6>(0,0));
      MD(DIM_STATE, DIM_STATE) &&K_1 =
          (H_T_H + (state.cov / LASER_POINT_COV).inverse()).inverse();
      G.block<DIM_STATE, 6>(0, 0) =
          K_1.block<DIM_STATE, 6>(0, 0) * H_T_H.block<6, 6>(0, 0);
      auto vec = state_propagat - state;
      solution = K_1.block<DIM_STATE, 6>(0, 0) * HTz + vec -
                 G.block<DIM_STATE, 6>(0, 0) * vec.block<6, 1>(0, 0);
      state += solution;

      rot_add = solution.block<3, 1>(0, 0);
      t_add = solution.block<3, 1>(3, 0);

      if ((rot_add.norm() * R2D < 0.01) && (t_add.norm() * 100 < 0.015)) {
        flg_EKF_converged = true;
      }

      deltaR = rot_add.norm() * R2D;
      deltaT = t_add.norm() * 100;
    }
    euler_cur = RotMtoEuler(state.rot_end);

    /*** Rematch Judgement ***/
    nearest_search_en = false;
    if (flg_EKF_converged ||
        ((rematch_num == 0) && (iterCount == (NUM_MAX_ITERATIONS - 2)))) {
      nearest_search_en = true;
      rematch_num++;
    }

    /*** Convergence Judgements and Covariance Update ***/
    if (!EKF_stop_flg &&
        (rematch_num >= 2 || (iterCount == NUM_MAX_ITERATIONS - 1))) {
      if (flg_EKF_inited) {
        /*** Covariance Update ***/
        // G.setZero();
        // G.block<DIM_STATE,6>(0,0) = K * Hsub;
        state.cov = (I_STATE - G) * state.cov;
        total_distance += (state.pos_end - position_last).norm();
        position_last = state.pos_end;

        VD(DIM_STATE) K_sum = K.rowwise().sum();
        VD(DIM_STATE) P_diag = state.cov.diagonal();
        // LINFO<<"K: "<<K_sum.transpose()<<endl;
        // LINFO<<"P: "<<P_diag.transpose()<<endl;
        // LINFO<<"position: "<<state.pos_end.transpose()<<" total distance:
        // "<<total_distance<<endl;
      }
      EKF_stop_flg = true;

      // 方向退化检测
      if (detect_lidar_degenetate) {
        Eigen::MatrixXd t_J = Hsub.rightCols(3);
        Eigen::SelfAdjointEigenSolver<Eigen::MatrixXd> es(t_J.transpose() *
                                                          t_J);
        Eigen::VectorXd eigen_values = es.eigenvalues();
        double min_eigen_value = eigen_values.minCoeff() / t_J.rows();
        auto is_degenerate_ = false;
        if (min_eigen_value < lidar_degenerate_thresh) {
          is_degenerate_ = true;
          LERROR << "[ LIO mapping ] lidar degenerate, " << min_eigen_value << " per point"
                 << REND;
        }
        lidar_selector->is_lidar_degenerate = is_degenerate_;
        lidar_selector->lidar_degenerate_score = min_eigen_value;
        if (is_degenerate_ && reset_state_when_degenerate) {
          state = state_propagat;
          // TODO: 仅重置退化方向状态
        }
      }
    }
    solve_time += omp_get_wtime() - solve_start;

    if (EKF_stop_flg)
      break;
  }
  state.timestamp = LidarMeasures.lidar_end_time;
  // LINFO<<"[ mapping ]: iteration count: "<<iterCount+1<<END;

  // log
  // opt
  livo_result_->lio_result->log->flg_EKF_inited = flg_EKF_inited;
  livo_result_->lio_result->log->opt_iter = iterCount+1;
  livo_result_->lio_result->log->valid_size = effct_feat_num;
  livo_result_->lio_result->log->res_mean_last = res_mean_last;
  livo_result_->lio_result->log->solution = solution;
  livo_result_->lio_result->state = state;
  // eskf
  // livo_result_->lio_result->log->state_pos = state.pos_end;

  if (rs_debug && feats_down_world->size()) {
    auto source_normal_cloud = DrawCloudNormal(feats_down_world);
    pcl::PCDWriter pcd_writer;
    if (source_normal_cloud->size())
      pcd_writer.writeBinary("/apollo/data/log/vis-test2.pcd",
                             *source_normal_cloud);
  }
}

void RsSlam::KdTreeGrow() {
  if (!feats_down_size)
    return;
  int size = filter_size_map_min;
#if 1 // fast-lio2
  PointVector PointToAdd;
  PointVector PointNoNeedDownsample;
  PointToAdd.reserve(feats_down_size);
  PointNoNeedDownsample.reserve(feats_down_size);
  for (int i = 0; i < feats_down_size; i++) {
    /* transform to world frame */
    pointBodyToWorld(&(feats_down_body->points[i]),
                     &(feats_down_world->points[i]));
    /* decide if need add to map */
    if (!Nearest_Points[i].empty() && flg_EKF_inited) {
      const PointVector &points_near = Nearest_Points[i];
      bool need_add = true;
      BoxPointType Box_of_Point;
      PointType downsample_result, mid_point;
      mid_point.x =
          floor(feats_down_world->points[i].x / size) * size + 0.5 * size;
      mid_point.y =
          floor(feats_down_world->points[i].y / size) * size + 0.5 * size;
      mid_point.z =
          floor(feats_down_world->points[i].z / size) * size + 0.5 * size;
      float dist = calc_dist(feats_down_world->points[i], mid_point);
      if (fabs(points_near[0].x - mid_point.x) > 0.5 * size &&
          fabs(points_near[0].y - mid_point.y) > 0.5 * size &&
          fabs(points_near[0].z - mid_point.z) > 0.5 * size) {
        PointNoNeedDownsample.push_back(feats_down_world->points[i]);
        continue;
      }
      for (int readd_i = 0; readd_i < NUM_MATCH_POINTS; readd_i++) {
        if (points_near.size() < NUM_MATCH_POINTS)
          break;
        if (calc_dist(points_near[readd_i], mid_point) < dist) {
          need_add = false;
          break;
        }
      }
      if (need_add)
        PointToAdd.push_back(feats_down_world->points[i]);
    } else {
      PointToAdd.push_back(feats_down_world->points[i]);
    }
  }

  int add_point_size = ikdtree.Add_Points(PointToAdd, true);
  ikdtree.Add_Points(PointNoNeedDownsample, false);
  add_point_size = PointToAdd.size() + PointNoNeedDownsample.size();

#else // fast-livo
  for (int i = 0; i < feats_down_size; i++) {
    /* transform to world frame */
    pointBodyToWorld(&(feats_down_body->points[i]),
                     &(feats_down_world->points[i]));
  }
  ikdtree.Add_Points(feats_down_world->points, true);
#endif
}

// VIO
void RsSlam::EstimateVIOState() {
  auto tt = omp_get_wtime();
  const auto &valid_source_cloud_for_VIO = valid_source_cloud;
  if (valid_source_cloud_for_VIO->empty()) {
    LERROR << "[ VIO ] continue, source size not enough: "
           << valid_source_cloud_for_VIO->size() << REND;
    livo_result_->vio_result->log->vio_valid = false;
    return;
  }
  // 先验状态
  euler_cur = RotMtoEuler(state.rot_end);
  fout_pre << std::fixed << state.timestamp
           << " " << euler_cur.transpose() * R2D << " "
           << state.pos_end.transpose() << " " << state.vel_end.transpose()
           << " " << state.bias_g.transpose() << " " << state.bias_a.transpose()
           << " " << state.gravity.transpose() << endl;

  // 将 scan+地图 投影到图像
  lidar_selector->noise_cloud.reset(new PointCloudXYZI());
  int scan_size = valid_source_cloud_for_VIO->size();
  lidar_selector->noise_cloud->reserve(scan_size + featsFromMap->size());
  V3D pos_I = state.pos_end;
  M3D rot_I = state.rot_end;

  // 过滤scan
  std::vector<int> is_scan_selected(scan_size, 0);
  std::vector<PointType> scan_vec(scan_size);
  bool normal_filter = flg_EKF_inited && enable_normal_filter;
#ifdef MP_EN
  omp_set_num_threads(MP_PROC_NUM);
#pragma omp parallel for
#endif
  for (int i = 0; i < scan_size; ++i) {
    const auto &pt = valid_source_cloud_for_VIO->points[i];
    // 仅保留lidar自身周围一定范围
    V3D pt_beam{pt.x - pos_I.x(), pt.y - pos_I.y(), pt.z - pos_I.z()};
    M3D T_I_W = rot_I.transpose();
    V3D pt_beam_I = T_I_W * (pt_beam);
    if (fabs(pt_beam_I.z()) > z_thresh || fabs(pt_beam_I.y()) > y_thresh ||
        fabs(pt_beam_I.x()) > x_thresh) {
      continue;
    }
    auto pt_copy = pt;
    pt_copy.intensity = 1.0;
    if (normal_filter) {
      if (pt.normal_x > 1) // means invalid point in LIO
        continue;
      // 仅保留法线与与光心射线夹角小的点
      V3D pt_normal{pt.normal_x, pt.normal_y, pt.normal_z};
      pt_beam.normalize();
      double dot = fabs(pt_normal.dot(pt_beam));
      if (dot < cos(norm_thresh)) {
        pt_copy.intensity = weight;
        // pt_copy.curvature =1;
        is_scan_selected[i] = 2;
      }
    }
    is_scan_selected[i] += 1;
    scan_vec[i] = pt_copy;
  }
  CloudPtr scan_W_filtered(new PointCloudXYZI());
  scan_W_filtered->reserve(scan_size);
  int scan_large_angle_cnt{0};
  for (int i = 0; i < scan_size; ++i) {
    if (is_scan_selected[i] == 0)
      continue;
    else if (is_scan_selected[i] == 1) {
      scan_W_filtered->push_back(scan_vec[i]);
    } else if (is_scan_selected[i] == 3) {
      scan_W_filtered->push_back(scan_vec[i]);
      lidar_selector->noise_cloud->push_back(scan_vec[i]);
      ++scan_large_angle_cnt;
    }
  }
  filter_scan_t = omp_get_wtime() - tt;

  tt = omp_get_wtime();
  // 将地图补充到当前帧，扩大lidarFOV
  int map_size = featsFromMap->points.size();
  CloudPtr map_W_filtered(new PointCloudXYZI());
  std::vector<int> map_out_FOV_vec(map_size, 0);
  int out_FOV_cnt{0}, map_large_angle_cnt{0};
  if (use_map_proj && map_size) {
    std::vector<int> is_map_selected(map_size, 0);
    std::vector<PointType> map_vec(map_size);
#ifdef MP_EN
    omp_set_num_threads(MP_PROC_NUM);
#pragma omp parallel for
#endif
    for (int i = 0; i < map_size; ++i) {
      const auto &pt = featsFromMap->points[i];
      V3D pt_beam{pt.x - pos_I.x(), pt.y - pos_I.y(), pt.z - pos_I.z()};
      M3D T_I_W = rot_I.transpose();
      V3D pt_beam_I = T_I_W * (pt_beam);
      if (fabs(pt_beam_I.z()) > z_thresh || fabs(pt_beam_I.y()) > y_thresh ||
          fabs(pt_beam_I.x()) > x_thresh) {
        map_out_FOV_vec[i] = 1;
        continue;
      }

      auto pt_copy = pt;
      pt_copy.intensity = 1.0;
      if (normal_filter) {
        if (pt.normal_x > 1)
          continue;
        V3D pt_normal{pt.normal_x, pt.normal_y, pt.normal_z};
        pt_beam.normalize();
        double dot = fabs(pt_normal.dot(pt_beam));
        if (dot < cos(norm_thresh)) {
          // pt_copy.curvature = 2;
          pt_copy.intensity = weight;
          is_map_selected[i] = 2;
        }
      }
      is_map_selected[i] += 1;
      map_vec[i] = pt_copy;
    }

    map_W_filtered->reserve(map_size);
    for (int i = 0; i < map_size; ++i) {
      if (is_map_selected[i] == 0)
        continue;
      else if (is_map_selected[i] == 1) {
        map_W_filtered->push_back(map_vec[i]);
      } else if (is_map_selected[i] == 3) {
        map_W_filtered->push_back(map_vec[i]);
        lidar_selector->noise_cloud->push_back(map_vec[i]);
        ++map_large_angle_cnt;
      }
      if (map_out_FOV_vec[i])
        ++out_FOV_cnt;
    }
  }
  filter_map_t = omp_get_wtime() - tt;

  LDEBUG << "[ VIO filter scan and map] size scan: " << feats_undistort->size()
         << " downsampled scan: " << feats_down_body->size()
         << " valid size: " << valid_source_cloud->size()
         << " scan_filtered: " << scan_W_filtered->size()
         << " scan_large_angle_cnt:" << scan_large_angle_cnt
         << ", map: " << featsFromMap->size()
         << " map_filtered:" << map_W_filtered->size()
         << " out_FOV_cnt:" << out_FOV_cnt
         << " map_large_angle_cnt:" << map_large_angle_cnt
         << " noise: " << lidar_selector->noise_cloud->size() << REND;

  // estimate state 
  tt = omp_get_wtime();
  lidar_selector->detect(LidarMeasures.measures.back().last_img_time,
                         LidarMeasures.measures.back().img, scan_W_filtered,
                         map_W_filtered);
  state.timestamp = LidarMeasures.measures.back().last_img_time;
  detect_t = omp_get_wtime() - tt;
  livo_result_->vio_result->log->vio_valid = true;
}

void RsSlam::PublishVIOResult() {
  auto pub_beg_t = omp_get_wtime();
  publish_visual_world_sub_map(lidar_selector);
  if (rgb_image_cb_) {
    rgb_image_cb_(lidar_selector->img_cp);
  }
  if (noise_image_cb_ && lidar_selector->pub_noise_cloud) {
    noise_image_cb_(lidar_selector->img_noise);
  }
  if (raw_image_cb_){// && lidar_selector->pub_all_cloud) {
    raw_image_cb_(lidar_selector->img_raw);
  }
  if (all_cloud_image_cb_ && lidar_selector->pub_all_cloud) {
    all_cloud_image_cb_(lidar_selector->img_all_cloud);
  }
  // rgb cloud
  publish_frame_world_rgb(lidar_selector);
  // odometry
  Eigen::Vector3d t = state.pos_end;
  Eigen::Quaterniond q(state.rot_end);
  Pose pose(q, t, state.timestamp);
  if (odom_aft_mapped_cb_) {
    odom_aft_mapped_cb_(pose);
  }
  if (path_cb_) {
    path_cb_(pose);
  }
  // full result
  livo_result_->update_ts = state.timestamp;
  livo_result_->source = SensorType::CAMERA;
  livo_result_->vio_result->T_W_I = pose;
  livo_result_->vio_result->cam_pose = pose;
  livo_result_->lio_result->rgb_scan = rgb_scan;
  if (rgb_scan->size()) {
    pcl::transformPointCloud(
        *rgb_scan, *livo_result_->lio_result->rgb_scan_I,
        livo_result_->lio_result->T_W_I.inverse().transform());
    pcl::transformPointCloud(*livo_result_->lio_result->rgb_scan_I,
                             *livo_result_->lio_result->rgb_scan_L,
                             T_L_I_pose.transform());
  }

  VIO_T_W_I_pose_last = VIO_T_W_I_pose;
  VIO_T_W_I_pose = pose;
  pose.updatePoseRight(T_I_C_pose);
  livo_result_->vio_result->T_W_C = pose;
  livo_result_->lio_result->rgb_map = rgb_map;
  livo_result_->vio_result->img_cp = lidar_selector->img_cp;
  livo_result_->vio_result->img_noise = lidar_selector->img_noise;
  livo_result_->vio_result->img_raw = lidar_selector->img_raw;
  livo_result_->vio_result->img_gray = lidar_selector->img_gray;
  livo_result_->vio_result->img_all_cloud = lidar_selector->img_all_cloud;
  livo_result_->vio_result->log->valid_size = lidar_selector->result_->valid_patch_num;
  livo_result_->vio_result->log->res_mean_last = lidar_selector->result_->valid_pixel_residual_mean;
  livo_result_->is_ready = is_ready;
  if (full_result_cb_) {
    full_result_cb_(*livo_result_);
  }

  // state
  euler_cur = RotMtoEuler(state.rot_end);
  fout_out << std::fixed << LidarMeasures.is_lidar_end << " "
           << LidarMeasures.last_update_time - first_lidar_time << " "
           << euler_cur.transpose() * R2D << " " << state.pos_end.transpose()
           << " " << state.vel_end.transpose() << " "
           << state.bias_g.transpose() << " " << state.bias_a.transpose() << " "
           << state.gravity.transpose() << " " << feats_undistort->points.size()
           << endl;

  // pose
  f_state_utm << std::fixed << std::setprecision(6)
              << LidarMeasures.measures.back().last_img_time << " " << t.x()
              << " " << t.y() << " " << t.z() << " " << q.x() << " " << q.y()
              << " " << q.z() << " " << q.w() << std::endl;

  // time-cost
  // VIO_total_t = omp_get_wtime() - t_VIO_beg;
  LINFO << "[ VIO time-cost(s)]" << std::fixed << std::setprecision(3)
       << " pre: " << process2_t << " flatten: " << flatten_t
       << " filter scan: " << filter_scan_t << " filter_map_t: " << filter_map_t
       << " detect: " << detect_t << " VIO total: " << VIO_total_t
       << std::setprecision(6) << REND;

  if (rs_debug && lidar_selector->noise_cloud->size()) {
    double t1 = omp_get_wtime();
    pcl::PCDWriter pcd_writer;
    pcd_writer.writeBinary("/apollo/data/log/vis-test3.pcd",
                           *lidar_selector->noise_cloud);
  }
  livo_result_->time_cost->pub_vio_t = omp_get_wtime() - pub_beg_t;
  if (1) {
    livo_result_->vio_result->update_ts = state.timestamp;
    livo_result_->vio_result->log->update_ts = state.timestamp;
    if (print_log) {
      livo_result_->Print(0, 0, 0, 1, 0);
    }
    if (save_log) {
      livo_result_->SaveLog(0, 0, 0, 1, 1);
    }
    livo_result_->Print(0, 0, 0, 0, 1);
  }
  livo_result_->time_cost->vio_t = livo_result_->time_cost->pub_vio_t = 0;
}

void RsSlam::PublishLIOResult() {
  auto pub_beg_t = omp_get_wtime();
  /******* Publish points *******/
  CloudPtr cloud_registered_I(feats_down_body);
  int size = cloud_registered_I->points.size();
  CloudPtr cloud_registered_W(new PointCloudXYZI(size, 1));
  for (int i = 0; i < size; i++) {
    RGBpointBodyToWorld(&cloud_registered_I->points[i],
                        &cloud_registered_W->points[i]);
    cloud_registered_W->points[i].curvature =
        cloud_registered_I->points[i].curvature;
    cloud_registered_W->points[i].normal_x =
        feats_down_world->points[i].normal_x;
    cloud_registered_W->points[i].normal_y =
        feats_down_world->points[i].normal_y;
    cloud_registered_W->points[i].normal_z =
        feats_down_world->points[i].normal_z;
  }
  if (downsample_source_cloud && pub_dense_map) {
    int size = feats_undistort->points.size();
    CloudPtr full_cloud_registered_W(new PointCloudXYZI(size, 1));
    for (int i = 0; i < size; i++) {
      RGBpointBodyToWorld(&feats_undistort->points[i],
                          &full_cloud_registered_W->points[i]);
    }
    xyzi_scan = full_cloud_registered_W;
  } else {
    xyzi_scan = cloud_registered_W;
  }

  /******* Publish LIO odometry *******/
  {
    Eigen::Quaterniond q(state.rot_end);
    Pose pose(q, state.pos_end, state.timestamp);
    if (odom_aft_mapped_cb_) {
      odom_aft_mapped_cb_(pose);
    }
    if (path_cb_) {
      path_cb_(pose);
    }
    livo_result_->update_ts = state.timestamp;
    livo_result_->source = SensorType::LIDAR;
    livo_result_->lio_result->T_W_I = pose;
    LIO_T_W_I_pose_last = LIO_T_W_I_pose;
    LIO_T_W_I_pose = pose;
    pose.updatePoseRight(T_I_L_pose);
    livo_result_->lio_result->T_W_L = pose;
    livo_result_->lio_result->lidar_pose = pose;
    livo_result_->lio_result->scan = xyzi_scan;
    livo_result_->lio_result->map = xyzi_map;
    livo_result_->lio_result->kdtree_map = featsFromMap;
    livo_result_->lio_result->log->downsample_cloud_size = feats_down_size;
    livo_result_->is_ready = is_ready;
    if (xyzi_scan->size()) {
      pcl::transformPointCloud(*xyzi_scan, *livo_result_->lio_result->scan_I,
                               LIO_T_W_I_pose.inverse().transform());
      pcl::transformPointCloud(*livo_result_->lio_result->scan_I,
                               *livo_result_->lio_result->scan_L,
                               T_L_I_pose.transform());
    }
    if (full_result_cb_) {
      full_result_cb_(*livo_result_);
    }
    
  }

  valid_source_cloud->clear();
  pcl::copyPointCloud(*cloud_registered_W, *valid_source_indices,
                      *valid_source_cloud);

  if (lidar_selector->pub_all_cloud) {
    lidar_selector->all_cloud = cloud_registered_W;
    lidar_selector->all_cloud_L = cloud_registered_I;
  }

  if (rs_debug) {
    pcl::copyPointCloud(*cloud_registered_W, *invalid_source_indices,
                        *invalid_source_cloud);
    for (int i = 0; i < invalid_source_cloud->points.size(); ++i) {
      invalid_source_cloud->points[i].normal_x = invalid_reason[i];
    }
    pcl::PCDWriter pcd_writer;
    if (invalid_source_cloud->size())
      pcd_writer.writeBinary("/apollo/data/log/vis-test4.pcd",
                             *invalid_source_cloud);
  }

  if (!show_rgb_map || !img_en)
    publish_frame_world();
  // publish_visual_world_map(pubVisualCloud);
  publish_effect_world();
  if (show_lidar_map)
    publish_map();

  // save time-cost to log
  // total_t = kdtree_delete_time + update_LIO_state_t + pubcloud + kdtree_grow_t
  // update_LIO_state_t = match_time + solve_time
  // solve_time = solve_const_H_time + 退化检测
  ++frame_num;
  double total_t = (loop_end_t - loop_beg_t);
  aver_total_t = aver_total_t * (frame_num - 1) / frame_num + total_t / frame_num;
  aver_update_lio_state_t = aver_update_lio_state_t * (frame_num - 1) / frame_num +
                  update_LIO_state_t / frame_num;
  aver_time_match =
      aver_time_match * (frame_num - 1) / frame_num + match_time / frame_num;
  aver_time_solve =
      aver_time_solve * (frame_num - 1) / frame_num + solve_time / frame_num;
  aver_time_const_H_time =
      aver_time_const_H_time * (frame_num - 1) / frame_num +
      solve_const_H_time / frame_num;

  // kdtree_delete_time
  time_log_counter++;

  if (1) {
    // time-cost
    PRINT("[ LIO time-cost]: lidar_ts: %f PredicteStateAndUndistortCloud: "
           "%0.3f preprocess_t: %0.3f total=(crop+update+grow+pub): %0.3f "
           "CropKdTree: %0.3f "
           "ave_match: "
           "%0.3f ave_solve: "
           "%0.3f EstimateLIOState: %0.3f KdTreeGrow: %0.3f ave total: %0.3f "
           "ave update state : %0.3f "
           "ave construct H: %0.3f.\n",
           state.timestamp, process2_t, preprocess_t, total_t,
           kdtree_delete_time, aver_time_match, aver_time_solve,
           update_LIO_state_t, kdtree_grow_t, aver_total_t,
           aver_update_lio_state_t, aver_time_const_H_time); 
    f_LIO_t << std::fixed << std::setprecision(6)
            << LidarMeasures.last_lidar_time << " " << process2_t << " "
            << preprocess_t << " " << total_t << " " << aver_time_match << " "
            << aver_time_solve << " " << update_LIO_state_t << " "
            << kdtree_grow_t << " " << aver_total_t << " "
            << aver_update_lio_state_t << " " << aver_time_const_H_time << endl;

    // state
    euler_cur = RotMtoEuler(state.rot_end);
    fout_out << std::fixed << LidarMeasures.is_lidar_end << " "
             << state.timestamp << " "
             << euler_cur.transpose() * R2D << " " << state.pos_end.transpose()
             << " " << state.vel_end.transpose() << " "
             << state.bias_g.transpose() << " " << state.bias_a.transpose()
             << " " << state.gravity.transpose() << " "
             << feats_undistort->points.size() << endl;

    // pose
    Eigen::Vector3d t = state.pos_end;
    Eigen::Quaterniond q(state.rot_end);
    f_state_utm << std::fixed << std::setprecision(6)
                << state.timestamp << " " << t.x() << " " << t.y()
                << " " << t.z() << " " << q.x() << " " << q.y() << " " << q.z()
                << " " << q.w() << std::endl;

    f_lidar_state_utm << std::fixed << std::setprecision(6)
                      << state.timestamp << " " << t.x() << " "
                      << t.y() << " " << t.z() << " " << q.x() << " " << q.y()
                      << " " << q.z() << " " << q.w() << std::endl;
  }
  livo_result_->time_cost->pub_lio_t = omp_get_wtime() - pub_beg_t;
  if (1) {
    livo_result_->cloud_process_result->update_ts =
        state.timestamp;
    livo_result_->lio_result->update_ts = state.timestamp;
    livo_result_->lio_result->log->update_ts = state.timestamp;

    if (print_log) {
      livo_result_->Print(0, 1, 1, 0, 0);
    }
    if (save_log) {
      livo_result_->SaveLog(0, 1, 1, 0, 1);
    }
    livo_result_->Print(0, 0, 0, 0, 1);
    livo_result_->time_cost->lio_t = livo_result_->time_cost->pub_lio_t = 0;
  }
}

void RsSlam::SaveMap() {
  /* 1. make sure you have enough memories
  /* 2. pcd save will largely influence the real-time performences **/
  if (rgb_map->size() > 0 && pcd_save_en) {
    string map_dir(pcd_dir + "/rgb_map.pcd");
    pcl::PCDWriter pcd_writer;
    LINFO << "rgb map saved" << REND;
    pcd_writer.writeBinary(map_dir, *rgb_map);

    if (ply_save_en) {
      pcl::io::savePLYFileBinary(pcd_dir + "/rgb_map.ply", *rgb_map);
    }
    if (voxel_size) {
      auto rgb_map_ds = uniformSample<PointTypeRGB>(rgb_map, voxel_size);
      string file_name = "rgb_map_voxel_" + std::to_string(voxel_size) + ".pcd";
      pcd_writer.writeBinary(pcd_dir + file_name, *rgb_map_ds);

      if (ply_save_en) {
        pcl::io::savePLYFileBinary(pcd_dir + "rgb_map_voxel_" +
                                       std::to_string(voxel_size) + ".ply",
                                   *rgb_map_ds);
      }
    }
  }

  if (xyzi_map->size() > 0 && pcd_save_en) {
    string map_dir(pcd_dir + "intensity_map.pcd");
    pcl::PCDWriter pcd_writer;
    LINFO << "intensity map saved" << REND;
    pcd_writer.writeBinary(map_dir, *xyzi_map);
    if (voxel_size) {
      auto xyzi_map_ds = uniformSample<PointType>(xyzi_map, voxel_size);
      string file_name =
          "intensity_map_voxel_" + std::to_string(voxel_size) + ".pcd";
      pcd_writer.writeBinary(pcd_dir + file_name, *xyzi_map_ds);
      if (ply_save_en) {
        pcl::io::savePLYFileBinary(pcd_dir + "intensity_map_voxel_" +
                                       std::to_string(voxel_size) + ".ply",
                                   *xyzi_map_ds);
      }
    }
  }
}

#ifdef BACK_END
// back end loop
void RsSlam::ResetBackEnd() {
  loop_detection_.reset(new LoopDetection(ld_lc_cfg_));
  loop_correction_.reset(new LoopCorrection);
  auto& cfg = ld_lc_cfg_;
  loop_correction_->SetLoopNoise(cfg.loop_R_noise_, cfg.loop_t_noise_);
  loop_correction_->SetOdomNoise(cfg.odom_R_noise_, cfg.odom_t_noise_);

  LBLUE << "[ResetBackEnd] done" << REND;
}

void RsSlam::LoopDetectionLoop() {
  if (!enable_loop_correction_ && !enable_loop_detection_)
    return;
  LDEBUG << name() << " LoopDetection loop start. " << REND;

  std::shared_ptr<LoopDetection>& ld = loop_detection_;
  std::shared_ptr<LoopCorrection>& lc = loop_correction_;
  std::shared_ptr<BtcDescManager> bm = ld->btc_manager_;
  auto& cfg = ld_lc_cfg_;
  lc->SetLoopNoise(cfg.loop_R_noise_, cfg.loop_t_noise_);
  lc->SetOdomNoise(cfg.odom_R_noise_, cfg.odom_t_noise_);
  if (cfg.enable_ICP_test_) {
    HighOverlapICPtest(cfg, bm);
    return;
  }
#if BTC_DEBUG
  std::shared_ptr<BTCDebug> btc_debug(new BTCDebug(cfg));
  bm->print_debug_info_ = true;
#endif

  while (run_flag_) {

    // get single frame info
    int loop_queue_size;
    double queue_beg_ts, queue_end_ts;
    LoopData loop_data;
    loop_data_queue_mutex_.lock();
    if (loop_data_queue_.size()) {
      loop_queue_size = loop_data_queue_.size();
      queue_beg_ts = loop_data_queue_.front().lidar_timestamp;
      queue_end_ts = loop_data_queue_.back().lidar_timestamp;
      loop_data = loop_data_queue_.front();
      loop_data_queue_.pop_front();
      loop_data_queue_mutex_.unlock();
    } else {
      loop_data_queue_mutex_.unlock();
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
      continue;
    }
    RGBNCloudPtr raw_rgb_cloud_W = loop_data.raw_rgb_cloud_W;
    RGBNCloudPtr raw_rgb_cloud_I = loop_data.raw_rgb_cloud_I;
    int raw_size = raw_rgb_cloud_I->size();
    if (raw_size < 1) {
      continue;
    }
    LBLUE << "[LD] raw cloud: [" << ld->cloud_idx << "] size: " << raw_size
          << " ts: " << loop_data.lidar_timestamp
          << " queue: " << loop_queue_size << " from " << queue_beg_ts << " to "
          << queue_end_ts << REND;
    livo_result_->time_cost->loop_correction_t = 0.;
    double LD_beg = omp_get_wtime();
    Eigen::Matrix4d pose = loop_data.raw_I_pose.transform();
    double source_voxel_size =
        std::min(cfg.localmap_voxel_size_ * 0.5, source_voxel_size);
    if (source_voxel_size > 0.01) {
      raw_rgb_cloud_W =
          uniformSample<RGBNPoint>(raw_rgb_cloud_W, source_voxel_size);
      // down_sampling_voxel(*raw_rgb_cloud_W, source_voxel_size);
      LBLUE << "[LD] downsampled cloud: " << raw_size << REND;
    }

    // save raw single frame cloud, for map reconsturction
    if (0) {
      // down sample body cloud
      raw_rgb_cloud_I = uniformSample<RGBNPoint>(raw_rgb_cloud_I, 0.5);
      down_sampling_voxel(*raw_rgb_cloud_I, 0.5);
    }
    ld->raw_cloud_vec.push_back(raw_rgb_cloud_I);

    // save original pose, for pose refinement
    ld->fixed_pose_vec.push_back(pose);
    ld->origin_pose_vec.push_back(pose);
    ld->pose_ts_vec.push_back(loop_data.lidar_timestamp);

    // 累积localmap
    *ld->key_localmap += *raw_rgb_cloud_W;
    if (cfg.localmap_voxel_size_ > 0.01) {
      ld->key_localmap =
          uniformSample<RGBNPoint>(ld->key_localmap, cfg.localmap_voxel_size_);
      // down_sampling_voxel(*ld->key_localmap, cfg.localmap_voxel_size_);
    }

    // PGO 3.添加初始值到初始估计集合
    lc->initial.insert(ld->cloud_idx, gtsam::Pose3(pose));

    // PGO 4.添加LIO里程计因子
    if (cfg.add_LIO_factor_) {
      if (cfg.enable_dynamic_weight_) {
        double scale = loop_data.LIO_residual / cfg.LIO_residual_base_;
        int exp = int(scale * 100) / 10 - 2; // -1(<0.2) 0(<0.3) 1(<0.4) 1cm残差对应1个量级
        scale = pow(10, exp * 1); // 让小残差尺度衰减快 TODO: better method
        double ratio = std::min(1e10, std::max(1e-10, scale));
        lc->SetOdomNoise(cfg.odom_R_noise_ * ratio, cfg.odom_t_noise_ * ratio);
      }
      if (ld->cloud_idx == 0) {
        LBLUE << "[LD] add first pose factor" << REND;
        lc->graph.add(gtsam::PriorFactor<gtsam::Pose3>(0, gtsam::Pose3(pose),
                                                      lc->odometry_noise));
      } else {
        auto prev_pose = gtsam::Pose3(ld->origin_pose_vec[ld->cloud_idx - 1]);
        auto curr_pose = gtsam::Pose3(pose);
        lc->graph.add(gtsam::BetweenFactor<gtsam::Pose3>(
            ld->cloud_idx - 1, ld->cloud_idx, prev_pose.between(curr_pose),
            lc->odometry_noise));
      }
    }
    // 
    if(cfg.add_VIO_factor_){
      if (cfg.enable_dynamic_weight_) {
      }
    }

    // TODO: better method
    // check if keyframe
    if (ld->cloud_idx % cfg.sub_frame_num_ == 0 && ld->cloud_idx != 0) {
      LBLUE << "[LD] is keyframe, ts: " << loop_data.lidar_timestamp
            << ", cloud idx: [" << ld->cloud_idx << "], localmap_idx: ["
            << ld->localmap_idx
            << "], localmap size: " << ld->key_localmap->size() << REND;

      // ================================loop detection==================================
      // step1. Descriptor Extraction
      auto t_descriptor_begin = omp_get_wtime();
      std::vector<BTC> btcs_vec;
      if (!bm->GenerateBtcDescs(ld->key_localmap, ld->localmap_idx, btcs_vec)) {
        LERROR << "[LD][BTC] No BTCs generated!" << REND;
      }
      auto t_descriptor_end = omp_get_wtime();
      ld->descriptor_time.push_back(time_inc(t_descriptor_end, t_descriptor_begin));

      // step2. Searching Loop
      auto t_query_begin = omp_get_wtime();
      std::pair<int, double> search_result(-1, 0);
      std::pair<Eigen::Vector3d, Eigen::Matrix3d> loop_transform;
      loop_transform.first << 0, 0, 0;
      loop_transform.second = Eigen::Matrix3d::Identity();
      std::vector<std::pair<BTC, BTC>> loop_std_pair;

      if (ld->localmap_idx > cfg.skip_near_num_) {
        bm->SearchLoop(btcs_vec, search_result, loop_transform, loop_std_pair);
      }
      auto t_query_end = omp_get_wtime();
      ld->querying_time.push_back(time_inc(t_query_end, t_query_begin));

      // step3. Add descriptors to the database
      auto t_map_update_begin = omp_get_wtime();
      if (stop_grow_kdtree_map == false) { // TODO: better condition
        bm->AddBtcDescs(btcs_vec);
      }
      auto t_map_update_end = omp_get_wtime();
      ld->update_time.push_back(time_inc(t_map_update_end, t_map_update_begin));
      bm->key_cloud_vec_.push_back(ld->key_localmap->makeShared());
      LBLUE << "[LD][BTC][time ms] total: "
            << ld->descriptor_time.back() + ld->querying_time.back() +
                   ld->update_time.back()
            << ". descriptor extraction: " << ld->descriptor_time.back()
            << ", query: " << ld->querying_time.back()
            << ", update map: " << ld->update_time.back() << REND;
      livo_result_->time_cost->loop_detection_t = t_map_update_end - t_descriptor_begin;
      // ================================loop detection end===================================

      const auto& bins = bm->history_binary_list_;
      double point_overlap;
      if (search_result.first > 0) {
        LINFO << "[LD] triggle loop: " << ld->localmap_idx << "--"
              << search_result.first
              << ", PlaneNN overlap score:"
              << search_result.second << "\nsource -- id: " << ld->localmap_idx
              << ", size: " << ld->key_localmap->size()
              << ", feat size: " << bins.back().size()
              << "\ntarget -- id: " << search_result.first
              << ", size: " << bm->key_cloud_vec_[search_result.first]->size()
              << ", feat size: " << bins[search_result.first].size() << REND;
        int locmap_diff = ld->localmap_idx - search_result.first;
        if (0.1 * locmap_diff * cfg.sub_frame_num_ > cfg.time_diff_thresh_) {
          ld->has_loop_flag = true;
          int match_frame = search_result.first;
          Eigen::Matrix4d T_target_source = Eigen::Matrix4d::Identity();
          T_target_source.block<3, 3>(0, 0) = loop_transform.second;
          T_target_source.block<3, 1>(0, 3) = loop_transform.first;
#if BTC_DEBUG
          pcl::PCDWriter pcd_writer;
          RGBNCloudPtr aligned_source(new RGBNCloud);
          pcl::transformPointCloud(*bm->plane_cloud_vec_.back(),
                                   *aligned_source, T_target_source);
          pcd_writer.writeBinary(pcd_dir + "/loop_BTC_aligned_source.pcd",
                                 *aligned_source);
          pcd_writer.writeBinary(pcd_dir + "/loop_ICP_source.pcd",
                                 *bm->plane_cloud_vec_.back());
          pcd_writer.writeBinary(pcd_dir + "/loop_ICP_target.pcd",
                                 *bm->plane_cloud_vec_[match_frame]);
#endif

          bool valid_plane_icp = true;
          double plane_overlap = -1.;
          double plane_residual = -1.;
          if (cfg.enable_ICP_fix_pose_) {
            // obtain optimal transform
            LBLUE
                << "[LC] coares pose from BTC alignment: "
                << Eigen::Quaterniond(loop_transform.second).coeffs().transpose()
                << " t: " << loop_transform.first.transpose() << REND;
            bm->PlaneGeomrtricIcp(bm->plane_cloud_vec_.back(),
                                  bm->plane_cloud_vec_[match_frame],
                                  loop_transform, plane_overlap, plane_residual);
            LBLUE
                << "[LC] pose fixed by ICP, valid plane_overlap: " << plane_overlap
                << " plane_residual: " << plane_residual << " q: "
                << Eigen::Quaterniond(loop_transform.second).coeffs().transpose()
                << " t: " << loop_transform.first.transpose() << REND;
            T_target_source.block<3, 3>(0, 0) = loop_transform.second;
            T_target_source.block<3, 1>(0, 3) = loop_transform.first;
            valid_plane_icp = plane_overlap > cfg.plane_icp_overlap_threshold_;
#if BTC_DEBUG
            pcl::transformPointCloud(*bm->plane_cloud_vec_.back(),
                                     *aligned_source, T_target_source);
            pcd_writer.writeBinary(pcd_dir + "/loop_ICP_aligned_source.pcd",
                                   *aligned_source);
#endif
          }
          RGBNCloudPtr aligned_localmap_cloud(new RGBNCloud);
          pcl::transformPointCloud(*bm->plane_cloud_vec_.back(),
                                   *aligned_localmap_cloud, T_target_source);
          point_overlap = calc_overlap(
              aligned_localmap_cloud, bm->plane_cloud_vec_[match_frame],
              cfg.verify_dis_NN_ratio_ * cfg.voxel_size_);
          float thre = cfg.point_icp_overlap_threshold_;
          std::string icp_str = "[LC][calc_overlap] point_overlap: " +
                                std::to_string(point_overlap) +
                                " thresh: " + std::to_string(thre);
          ld->has_loop_flag = false;

          bool valid_point_icp = false;
          if (point_overlap >= thre) {
            valid_point_icp = true;
            LINFO << icp_str << ". Valid ICP" << REND;
          } else if (point_overlap < 0.5 * thre) {
            LERROR << icp_str << ". Invalid ICP" << REND;
          } else if (point_overlap < thre) {
            LWARNING << icp_str << ". Invalid ICP" << REND;
          }

          Eigen::Vector3d euler_angles = T_target_source.block<3, 3>(0, 0).eulerAngles(2, 1, 0);
          bool trans_valid_condtion =
              euler_angles.norm() * R2D < cfg.valid_delta_angle_ &&
              T_target_source.block<3, 1>(0, 3).norm() < cfg.valid_delta_trans_;
          if (valid_plane_icp && valid_point_icp && trans_valid_condtion) {
            ld->has_loop_flag = true;
            LINFO << "[LC] Delta euler: " << euler_angles.norm() * R2D
                  << " delta t: " << T_target_source.block<3, 1>(0, 3).norm()
                  << REND;
            if (cfg.enable_ICP_fix_pose_) {
              if (plane_overlap < cfg.point_icp_overlap_threshold_) {
                LERROR << "plane_overlap: " << plane_overlap << " "
                       << cfg.point_icp_overlap_threshold_ << REND;
                ld->has_loop_flag = false;
              }
            }
            if (ld->has_loop_flag) {
              // notify FE to stop map growing
              if (enable_loop_correction_ && rebuild_map_) {
                stop_grow_kdtree_map = true;
              }
            }
          } else {
            LWARNING << "[LC] Delta euler: " << euler_angles.norm() * R2D
                     << " delta t: " << T_target_source.block<3, 1>(0, 3).norm()
                     << ". plane icp valid: " << valid_plane_icp
                     << " overlap: " << plane_overlap
                     << " residual: " << plane_residual
                     << " point icp valid: " << valid_point_icp
                     << " overlap: " << point_overlap << REND;
            if (!trans_valid_condtion) {
              LERROR << "[LC] Delta euler invalid, valid_delta_angle: "
                     << cfg.valid_delta_angle_
                     << " valid_delta_trans: " << cfg.valid_delta_trans_
                     << REND;
            }
            if (!valid_plane_icp) {
              LERROR << "[LC] plane icp invalid, overlap: " << plane_overlap
                     << "<" << cfg.plane_icp_overlap_threshold_ << REND;
            }
            if (!valid_point_icp) {
              LERROR << "[LC] point icp invalid, overlap: " << point_overlap
                     << "<" << cfg.point_icp_overlap_threshold_ << REND;
            }
          }
          /*
            add connection between loop frame.
            e.g. if src_key_frame_id 5 with sub frames 51~60 triggle loop with
                  tar_key_frame_id 1 with sub frames 11~20, add connection
            between each sub frame, 51-11, 52-12,...,60-20.

          */
          int sub_frame_num = cfg.sub_frame_num_;
          if (cfg.add_Loop_factor_ && ld->has_loop_flag) {
            for (size_t j = 1; j <= sub_frame_num; j++) {
              int src_frame = ld->cloud_idx + j - sub_frame_num;
              Eigen::Matrix4d src_pose_refined =
                  T_target_source * ld->fixed_pose_vec[src_frame];

              int tar_frame = match_frame * sub_frame_num + j;
              Eigen::Matrix4d tar_pose = ld->origin_pose_vec[tar_frame];

              // PGO 4.添加回环因子
              lc->graph.add(gtsam::BetweenFactor<gtsam::Pose3>(
                  tar_frame, src_frame,
                  gtsam::Pose3(tar_pose).between(gtsam::Pose3(src_pose_refined)),
                  lc->robust_loop_noise));

              ld->loop_container.push_back({tar_frame, src_frame});
            }
          }
        } else {
          LWARNING << "[LD] loop too near, time diff: "
                   << 0.1 * locmap_diff * cfg.sub_frame_num_
                   << ", thresh: " << cfg.time_diff_thresh_ << REND;
        }
      } else {
        LBLUE << "[Node][Loop Detection] no loop detected" << REND;
      }

#if BTC_DEBUG
      btc_debug->btc_manager = bm;
      btc_debug->pose_id = ld->cloud_idx;
      btc_debug->search_result = search_result;
      btc_debug->loop_transform = loop_transform;
      btc_debug->loop_std_pair = loop_std_pair;
      btc_debug->pose_list.push_back(loop_transform);
      btc_debug->localmap_cloud = *ld->key_localmap;
      btc_debug->cloud_overlap = point_overlap;
      btc_debug->cloud_overlap_thr = cfg.point_icp_overlap_threshold_;

      btc_debug->T_target_source = Eigen::Matrix4d::Identity();
      btc_debug->T_target_source.block<3, 3>(0, 0) = loop_transform.second;
      btc_debug->T_target_source.block<3, 1>(0, 3) = loop_transform.first;
      btc_debug->PublishLDResult(btcs_vec);
      if (bm->proj_plane_ != nullptr && btcs_vec.size()) {
        LDEBUG << "[LD][debug]current plane size: " << bm->plane_cloud_vec_.size()
        << ", proj plane size: " << bm->proj_plane_->size()
        << ", [binary size] currnet: " << bins.back().size()
        << ", database: " << bins.size() << REND;
      }
#endif
      ld->key_localmap->clear();
      ++ld->localmap_idx;
    } else {
      livo_result_->time_cost->loop_detection_t = 0;
#if BTC_DEBUG
      btc_debug->CleanRviz();
#endif
    }

    // ================================loop correction==================================
    // 将因子图加入优化器
    auto t_isam_update_begin = omp_get_wtime();
    lc->isam.update(lc->graph, lc->initial);
    lc->isam.update();

    if (ld->has_loop_flag) {
      for (int i = 0; i < cfg.update_time_; ++i) {
        lc->isam.update();
      }
    }
    auto t_isam_update_end = omp_get_wtime();
    lc->isam_update_time.push_back(
        time_inc(t_isam_update_end, t_isam_update_begin));

    // 清空当前因子图和初始值。 因子图已经加入优化器，因此需要清空为下一次因子图做准备
    lc->graph.resize(0);
    lc->initial.clear();

    // ISAM优化
    auto t_est_begin = omp_get_wtime();
    lc->curr_estimate = lc->isam.calculateEstimate();
    auto t_est_end = omp_get_wtime();
    lc->est_time.push_back(time_inc(t_est_end, t_est_begin));
    LBLUE << "[LC][ISAM][time ms] update: " << lc->isam_update_time.back()
          << ", estimate: " << lc->est_time.back() << std::endl;

    // 修正pose
    lc->UpdatePoses(lc->curr_estimate, ld->fixed_pose_vec);
    double LD_t = omp_get_wtime() - LD_beg;
    livo_result_->time_cost->loop_correction_t = LD_t;
    LBLUE << "[LD+LC][time ms]: " << LD_t * 1000 << REND;
    // ================================loop correction end==============================

    // 保存修正后的pose
    if (cfg.save_pose_ && ld->has_loop_flag) {
      std::string tail_str = std::to_string(ld->cloud_idx) + "_" +
                             std::to_string(loop_data.lidar_timestamp) + ".txt";
      std::ofstream f_corr(log_dir + "/loop_corrected_" + tail_str, ios::out);
      std::ofstream f_ori(log_dir + "/loop_original_" + tail_str, ios::out);
      ld->SavePose(f_corr, f_ori);
    }
#if BTC_DEBUG
    if (ld->has_loop_flag) {
      // publish correct cloud map
      btc_debug->PubCorrectedMap(ld->raw_cloud_vec, ld->fixed_pose_vec);

      // publish correct path
      btc_debug->PubCorrectedPose(ld->fixed_pose_vec);
    }
    // publish hitorical loop constraints
    btc_debug->PubLoopConstraint(ld->loop_container, ld->fixed_pose_vec);
#endif
    ++ld->cloud_idx;
    if (ld->has_loop_flag) {
      ++ld->loop_cnt;
      // record fixed global pose
      mtx_front_end_.lock();
      for (int i = 0; i < ld->fixed_pose_vec.size(); ++i) {
        const Eigen::Matrix4d &T = ld->fixed_pose_vec[i];
        livo_result_->be_res_->fixed_I_pose[ld->pose_ts_vec[i]] =
            Pose(T.block<3, 3>(0, 0), T.block<3, 1>(0, 3), ld->pose_ts_vec[i]);
      }
      mtx_front_end_.unlock();

      // record loop base data
      rebuild_base_pose_ =
          Pose(ld->fixed_pose_vec.back(), ld->pose_ts_vec.back());
      LWARNING << "[LC] loop occurs at: " << loop_data.lidar_timestamp
               << ". rebuild_base_pose t: " << rebuild_base_pose_.timestamp
               << " q: " << rebuild_base_pose_.q.coeffs().transpose() << REND;
      LoopCacheData loop_cache(loop_data);
      loop_cache.SaveCache(loop_data.lidar_timestamp, loop_data.raw_I_pose,
                           loop_data.raw_I_pose, loop_cache_queue_,
                           loop_cache_queue_full_);

      // TODO(rum): 判断是否需要反馈给前端，触发重建
      if (1 && reset_front_end_thread_ptr_ != nullptr) {
        // save cache in FE (CoreLoop thread)
        if (loop_data_queue_.size()) {
          // LD maybe not real-time, need release historical loop_data_queue to
          // loop_cache_queue
          mtx_reset_fe_.lock();
          loop_data_queue_mutex_.lock();
          if (loop_cache_queue_.size() > 1 ||
              loop_cache_queue_full_.size() > 1) {
            LWARNING << "not empty loop_cache_queue" << REND;
          }
          LWARNING << "[LC] loop_data_queue_ (aft loop) size: "
                   << loop_data_queue_.size() << " ("
                   << loop_data_queue_.front().lidar_timestamp << "~"
                   << loop_data_queue_.back().lidar_timestamp << ")" << REND;
          while (loop_data_queue_.size()) {
            LoopData& l_data = loop_data_queue_.front();
            LoopCacheData loop_cache(l_data);
            loop_cache.SaveCache(l_data.lidar_timestamp, l_data.raw_I_pose_pre,
                                 l_data.raw_I_pose, loop_cache_queue_,
                                 loop_cache_queue_full_);
            loop_data_queue_.pop_front();
          }
          loop_data_queue_mutex_.unlock();
          mtx_reset_fe_.unlock();
        } else {
          LWARNING << "[LC] empty loop_cache_queue" << REND;
          // occurs when LC faster than next LIO
        }

        mtx_reset_fe_.lock();
        rebuild_thread_running_ = true; // notice FE to save LoopCacheData
        mtx_reset_fe_.unlock();
        LWARNING << "[LC] rebuilding, freeze LoopDetectionLoop thread untill "
                    "ResetFrontEndLoop break.\nfix pose from "
                 << ld->pose_ts_vec.front() << " to " << ld->pose_ts_vec.back()
                 << ", base pose: " << rebuild_base_pose_.timestamp
                 << ", q: " << rebuild_base_pose_.q.coeffs().transpose()
                 << REND;
#if LC_DEBUG
        pcl::PCDWriter pcd_writer;
        auto info = loop_cache_queue_.front();
        LERROR << "save loop cloud: " << info.loop_data.lidar_timestamp << REND;
        CloudPtr test(new PointCloudXYZI);
        *test = *(info.loop_data.raw_cloud_I);
        pcl::transformPointCloud(*test, *test, ld->origin_pose_vec.back());
        pcd_writer.writeBinary("/apollo/data/log/vis-test2.pcd", *test);
        *test = *(info.loop_data.raw_cloud_I);
        pcl::transformPointCloud(*test, *test, ld->fixed_pose_vec.back());
        pcd_writer.writeBinary("/apollo/data/log/vis-test3.pcd", *test);
#endif
        std::unique_lock<std::mutex> lk(loop_data_queue_mutex_);
        cv_loop_.wait(lk); // freeze untill rebuild done
        loop_data_queue_mutex_.unlock();
        LWARNING << "[LC] freeze done" << REND;

        // save current loop result
        SaveLoopResult(ld, lc);

        // reset for next loop
        ResetBackEnd();
      }
    }
    ld->has_loop_flag = false;
  }

  LWARNING << name() << " LoopDetection loop exit." << REND;
}

void RsSlam::SaveLoopResult(std::shared_ptr<LoopDetection> ld,
                                  std::shared_ptr<LoopCorrection> lc) {
  if (!ld->config_.save_map_)
    return;
  LTITLE << "[LC] saving log/pcd, wait a moment..." << REND;
  std::string tail_str = "with_loop_" + std::to_string(ld->loop_cnt);

  RGBNCloudPtr corrected_map(new RGBNCloud);
  RGBNCloudPtr corrected_cloud(new RGBNCloud);
  int size = ld->fixed_pose_vec.size();
  for (int i = 0; i < size; ++i) {
    pcl::transformPointCloud(*ld->raw_cloud_vec[i], *corrected_cloud,
                             ld->fixed_pose_vec[i]);
    *corrected_map += *corrected_cloud;
  }
  corrected_map =
      uniformSample<RGBNPoint>(corrected_map, ld->config_.save_map_voxel_size_);
  // down_sampling_voxel(*corrected_map, cfg.save_map_voxel_size_);
  pcl::PCDWriter pcd_writer;
  pcd_writer.writeBinary((pcd_dir + "/map_refined_" + tail_str + ".pcd"),
                         *corrected_map);
  LWARNING << "[LC] save refined map done." << REND;
  if (!pcd_save_en) {
    RGBNCloudPtr raw_map(new RGBNCloud);
    RGBNCloudPtr raw_cloud(new RGBNCloud);
    for (int i = 0; i < size; ++i) {
      pcl::transformPointCloud(*ld->raw_cloud_vec[i], *raw_cloud,
                               ld->origin_pose_vec[i]);
      *raw_map += *raw_cloud;
    }
    raw_map = uniformSample<RGBNPoint>(raw_map, ld->config_.save_map_voxel_size_);
    // down_sampling_voxel(*raw_map, cfg.save_map_voxel_size_);
    pcd_writer.writeBinary((pcd_dir + "/map_original_" + tail_str + ".pcd"),
                           *raw_map);
  }
  LWARNING << "[LC] save original map done." << REND;

  std::string txt_tail_str = tail_str + ".txt";
  std::ofstream f_corr(log_dir + "/loop_corrected_" + txt_tail_str, ios::out);
  std::ofstream f_ori(log_dir + "/loop_original_" + txt_tail_str, ios::out);
  ld->SavePose(f_corr, f_ori);
  LWARNING << "[LC] save pose done." << REND;

  // save time cost
  {
    double mean_descriptor_time =
        std::accumulate(ld->descriptor_time.begin(), ld->descriptor_time.end(),
                        0) *
        1.0 / ld->descriptor_time.size();
    double mean_query_time =
        std::accumulate(ld->querying_time.begin(), ld->querying_time.end(), 0) *
        1.0 / ld->querying_time.size();
    double mean_update_time =
        std::accumulate(ld->update_time.begin(), ld->update_time.end(), 0) *
        1.0 / ld->update_time.size();
    double mean_isam_update_time =
        std::accumulate(lc->isam_update_time.begin(),
                        lc->isam_update_time.end(), 0) *
        1.0 / lc->isam_update_time.size();
    double mean_isam_est_time =
        std::accumulate(lc->est_time.begin(), lc->est_time.end(), 0) * 1.0 /
        lc->est_time.size();

    double mean_LD_t =
        mean_descriptor_time + mean_query_time + mean_update_time;
    double mean_LC_t = mean_isam_update_time + mean_isam_est_time;
    LBLUE << "[LD][Mean time (ms)] descriptor extraction: "
          << mean_descriptor_time << ", query: " << mean_query_time
          << ", update: " << mean_update_time << ", total: " << mean_LD_t
          << REND;
    LBLUE << "[LC][Mean time (ms)] update : " << mean_isam_update_time
          << ", estimate: " << mean_isam_est_time << ", total: " << mean_LC_t
          << REND;
    LBLUE << "[LD+LC][Mean time (ms)]: " << mean_LD_t + mean_LC_t << REND
          << REND;
    f_loop << mean_LD_t + mean_LC_t << " " << mean_LD_t << " " << mean_LC_t
           << " " << mean_descriptor_time << " " << mean_query_time << " "
           << mean_update_time << " " << mean_isam_update_time << " "
           << mean_isam_est_time << std::endl;
  }
}

// reset front end thread
void RsSlam::ResetFrontEndLoop() {
  LDEBUG << name() << " ResetFrontEndLoop start." << REND;

  double kdtree_add_time_sum = 0.0;
  int kdtree_add_sum = 0;
  int reset_count = 0;
  double last_reset_time = 0.0;
  double current_reset_time = 999.;
  bool kdtree_rebuild_ok = false;
  bool reset_eskf_ok = false;
  bool reset_VIO_ok = false;
  Pose T_W_I_fixed;

  while (run_flag_) {
    if (loop_cache_queue_.empty() || loop_cache_queue_full_.empty()) {
      livo_result_->time_cost->reset_front_end_t = 0;
      std::unique_lock<std::mutex> lk(mtx_reset_fe_);
      cv_reset_fe_.wait(lk); // notify by new LoopCacheData or destuction
      // continue; // wait cache
    }
    if(!run_flag_)
      break;

    // TODO: sleep for a whlie in case duplicate rebuild
    if (reset_count && current_reset_time - last_reset_time < 10.) {
      continue;
    }
    mtx_reset_fe_.unlock();

    LBLUE << "[ResetFrontEndLoop] cache size: " << loop_cache_queue_.size()
          << " rebuild_type: " << rebuild_map_
          << " reset_eskf_: " << reset_eskf_ << " reset_VIO_: " << reset_VIO_
          << " reset_count: " << reset_count
          << " current_reset_time: " << current_reset_time
          << " last_reset_time: " << last_reset_time << REND;
    double reset_fe_begin = omp_get_wtime();
    // ==reset Front End
    // 1. rebuild ikdtree (TODO: use rgb)
    PointVector new_tree_cloud;
    int insert_count = 0;
    while (loop_cache_queue_.size()) {

      // get raw cloud from buffer
      mtx_reset_fe_.lock();
      auto rebuild_info = loop_cache_queue_.back(); // use newest to oldest
      loop_cache_queue_.pop_back();
      mtx_reset_fe_.unlock();

      if (rebuild_info.loop_data.raw_cloud_I->empty()) {
        LERROR << "[ResetFrontEndLoop] new_tree_cloud is empty" << REND;
        continue;
      }
      if (1) {
      }
      // raw cloud is valid and not too far
      LWARNING << "[ResetFrontEndLoop] rebuild_info: " << rebuild_info.timestamp
               << REND;

      // way 1. replace. bad because it will cause construct and destory waste
      // way 2. delele raw_cloud and insert new_tree_cloud continously on raw
      // tree delete only at first time
      if (insert_count == 0 && rebuild_map_) {
        RemovePointsFromKdTree();
        double delete_begin = omp_get_wtime();
        V3D pos_LiD = state.pos_end;
        BoxPointType del_cub;
        for (int i = 0; i < 3; i++) {
          del_cub.vertex_min[i] = pos_LiD(i) - cube_len / 2.0 - 10;
          del_cub.vertex_max[i] = pos_LiD(i) + cube_len / 2.0 + 10;
        }
        vector<BoxPointType> cub_needrm;
        cub_needrm.push_back(del_cub);
        kdtree_delete_counter = ikdtree.Delete_Point_Boxes(cub_needrm);
        double kdtree_delete_time = omp_get_wtime() - delete_begin;
        LBLUE << "[ResetFrontEndLoop] delete cnt: " << kdtree_delete_counter
              << " t(ms): " << kdtree_delete_time * 1000 << " continue grow map"
              << REND;
        stop_grow_kdtree_map = false; // continue grow map
      }

      // correct cloud
      CloudPtr raw_cloud(new PointCloudXYZI);
      *raw_cloud = *(rebuild_info.loop_data.raw_cloud_I);
      T_W_I_fixed = rebuild_base_pose_; // T_W_Iloop
      T_W_I_fixed.timestamp = rebuild_info.timestamp;
      const Pose &delta_pose = rebuild_info.rel_LIO_pose; // T_Iloop_Inow
      T_W_I_fixed.updatePoseRight(delta_pose); // T_W_Inow
      pcl::transformPointCloud(*raw_cloud, *raw_cloud, T_W_I_fixed.transform());
      livo_result_->be_res_->fixed_I_pose[T_W_I_fixed.timestamp] = T_W_I_fixed;
#if BTC_DEBUG
      pcl::PCDWriter pcd_writer;
      std::string path = pcd_dir + "/scan_refined_" +
                         std::to_string(rebuild_info.timestamp) + "_" +
                         std::to_string(rebuild_info.cache_id) + ".pcd";
      pcd_writer.writeBinary(path, *raw_cloud);
      LERROR << "refined pcd saved to: " << path << REND;
#endif
      int raw_cloud_size = raw_cloud->size();
      new_tree_cloud.reserve(raw_cloud_size);
      for (int i = 0; i < raw_cloud_size; ++i) {
        new_tree_cloud.push_back(raw_cloud->points[i]);
      }

      // insert continously
      if (rebuild_map_) {
        double add_begin = omp_get_wtime();
        int add_point_size = ikdtree.Add_Points(new_tree_cloud, true);
        kdtree_add_sum += add_point_size;
        double kdtree_add_time = omp_get_wtime() - add_begin;
        kdtree_add_time_sum += kdtree_add_time;
        LBLUE << "[ResetFrontEndLoop] kdtree add: " << add_point_size
              << " t(ms): " << kdtree_add_time * 1000 << REND;
      }
      ++insert_count;

      // break when kdtree is ok
      // if (rebuild_map_ && kdtree_add_sum > rebuild_kdtree_min_num_) {
      if (rebuild_map_ && kdtree_add_sum ) {
        kdtree_rebuild_ok = true;
        LBLUE << "[ResetFrontEndLoop] rebuild tree done with " << insert_count
              << " cloud, total size: " << kdtree_add_sum
              << " in: " << kdtree_add_time_sum * 1000 << " (ms)" << REND;
        break;
      } else {
        LBLUE << "[ResetFrontEndLoop] rebuild tree num not enough, "
              << kdtree_add_sum << "/" << rebuild_kdtree_min_num_ << REND;
      }
    }

    // 2.reset eskf
    if (reset_eskf_ && !reset_eskf_ok) {
      Eigen::Matrix3d R_W_I_raw = state.rot_end;
      Eigen::Matrix3d R_W_I_fixed = T_W_I_fixed.q.toRotationMatrix();
      Eigen::Matrix3d R = R_W_I_fixed * R_W_I_raw.transpose() ;

      state.rot_end = R_W_I_fixed;
      state.pos_end = T_W_I_fixed.xyz;
      // state.vel_end = R * state.vel_end;

      reset_eskf_ok = true;
      LBLUE << "[ResetFrontEndLoop] Reset eskf" << REND;
      //
    }
    // 3.reset VIO
    if (reset_VIO_ && !reset_VIO_ok) {
      lidar_selector->ClearMap();
      reset_VIO_ok = true;
      LBLUE << "[ResetFrontEndLoop] Reset VIO" << REND;
    }

    if (rebuild_map_ && !kdtree_rebuild_ok) {
      continue;
    }

    // ==reset Back End
    {
      loop_data_queue_mutex_.lock();
      while (loop_data_queue_.size()) {
        loop_data_queue_.pop_back();
      }
      while (loop_cache_queue_full_.size()) {
        // save global pose
        Pose T_W_I_fixed;
        T_W_I_fixed = rebuild_base_pose_; // T_W_Iloop
        auto loop_cache = loop_cache_queue_full_.front();
        T_W_I_fixed.updatePoseRight(loop_cache.rel_LIO_pose); // T_W_Inow
        T_W_I_fixed.timestamp = loop_cache.timestamp;
        T_W_I_fixed.status = "fixed";
        livo_result_->be_res_->fixed_I_pose[loop_cache.timestamp] = T_W_I_fixed;
        loop_cache_queue_full_.pop_front();
      }
      loop_data_queue_mutex_.unlock();
    }

    current_reset_time = omp_get_wtime();
    ++reset_count;
    LBLUE << "[ResetFrontEndLoop] rebuilt done, deleted: "
          << kdtree_delete_counter << " t: " << kdtree_delete_time
          << ", added: " << kdtree_add_sum << " t: " << kdtree_add_time_sum
          << ", insert_count: " << insert_count
          << ", reset count: " << reset_count << REND;

    LWARNING << "[ResetFrontEndLoop] rebuild_thread_running_ false" << REND;
    rebuild_thread_running_ = false;
    cv_loop_.notify_all(); // wake up LoopDetectionLoop thread, close cache in FE

    livo_result_->time_cost->reset_front_end_t = current_reset_time - reset_fe_begin;
  }

  LWARNING << name() << " ResetFrontEndLoop exit. reset_count: " << reset_count
           << REND;
}
#endif
} // namespace slam
} // namespace robosense
