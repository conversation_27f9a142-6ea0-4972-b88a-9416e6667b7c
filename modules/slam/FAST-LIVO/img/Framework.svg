<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- 由 Microsoft Visio, SVG Export 生成 Framework.svg Page-1 -->
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:ev="http://www.w3.org/2001/xml-events"
		xmlns:v="http://schemas.microsoft.com/visio/2003/SVGExtensions/" width="10.4954in" height="3.84181in"
		viewBox="0 0 755.665 276.61" xml:space="preserve" color-interpolation-filters="sRGB" class="st35">
	<v:documentProperties v:langID="2052" v:metric="true" v:viewMarkup="false">
		<v:userDefs>
			<v:ud v:nameU="msvNoAutoConnect" v:val="VT0(1):26"/>
		</v:userDefs>
	</v:documentProperties>

	<style type="text/css">
	<![CDATA[
		.st1 {fill:#00b0f0;fill-opacity:0.58;stroke:#000000;stroke-linecap:round;stroke-linejoin:round;stroke-width:2.25}
		.st2 {fill:none;stroke:none;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.75}
		.st3 {fill:#000000;font-family:Calibri;font-size:1.33333em;font-weight:bold}
		.st4 {font-size:1em}
		.st5 {marker-end:url(#mrkr13-12);stroke:#00b0f0;stroke-linecap:round;stroke-linejoin:round;stroke-width:2.5}
		.st6 {fill:#00b0f0;fill-opacity:1;stroke:#00b0f0;stroke-opacity:1;stroke-width:0.49800796812749}
		.st7 {marker-end:url(#mrkr13-29);stroke:#f9c499;stroke-linecap:round;stroke-linejoin:round;stroke-width:2.5}
		.st8 {fill:#f9c499;fill-opacity:1;stroke:#f9c499;stroke-opacity:1;stroke-width:0.49800796812749}
		.st9 {fill:#ff0000;fill-opacity:0.5;stroke:#000000;stroke-linecap:round;stroke-linejoin:round;stroke-width:2.25}
		.st10 {marker-end:url(#mrkr13-41);stroke:#ff0000;stroke-linecap:round;stroke-linejoin:round;stroke-opacity:0.5;stroke-width:2.5}
		.st11 {fill:#ff0000;fill-opacity:0.5;stroke:#ff0000;stroke-opacity:0.5;stroke-width:0.49800796812749}
		.st12 {fill:#00b050;fill-opacity:0.8;stroke:#000000;stroke-linecap:round;stroke-linejoin:round;stroke-width:2.25}
		.st13 {marker-end:url(#mrkr13-58);stroke:#00b0f0;stroke-linecap:round;stroke-linejoin:round;stroke-width:2.25}
		.st14 {fill:#00b0f0;fill-opacity:1;stroke:#00b0f0;stroke-opacity:1;stroke-width:0.55555555555556}
		.st15 {marker-start:url(#mrkr13-64);stroke:#ff0000;stroke-linecap:round;stroke-linejoin:round;stroke-opacity:0.5;stroke-width:2.25}
		.st16 {fill:#ff0000;fill-opacity:0.5;stroke:#ff0000;stroke-opacity:0.5;stroke-width:0.55555555555556}
		.st17 {marker-end:url(#mrkr13-70);stroke:#ff0000;stroke-linecap:round;stroke-linejoin:round;stroke-opacity:0.5;stroke-width:2.5}
		.st18 {fill:#ff0000;fill-opacity:0.5;stroke:#ff0000;stroke-opacity:0.5;stroke-width:0.58139534883721}
		.st19 {fill:#0070c0;fill-opacity:0.8;stroke:#000000;stroke-linecap:round;stroke-linejoin:round;stroke-width:2.25}
		.st20 {fill:#000000;font-family:Calibri;font-size:1.5em;font-weight:bold}
		.st21 {marker-start:url(#mrkr13-81);stroke:#00b050;stroke-linecap:round;stroke-linejoin:round;stroke-opacity:0.8;stroke-width:2.5}
		.st22 {fill:#00b050;fill-opacity:0.8;stroke:#00b050;stroke-opacity:0.8;stroke-width:0.49800796812749}
		.st23 {marker-start:url(#mrkr13-95);stroke:#00b0f0;stroke-linecap:round;stroke-linejoin:round;stroke-width:2.5}
		.st24 {fill:#000000;font-family:Calibri;font-size:2.50001em;font-style:italic;font-weight:bold}
		.st25 {marker-end:url(#mrkr13-104);stroke:#f9c499;stroke-linecap:round;stroke-linejoin:round;stroke-width:2.25}
		.st26 {fill:#f9c499;fill-opacity:1;stroke:#f9c499;stroke-opacity:1;stroke-width:0.55555555555556}
		.st27 {fill:#000000;font-family:Calibri;font-size:1.00001em;font-weight:bold}
		.st28 {fill:#fbd7bb;fill-opacity:0.8;stroke:#000000;stroke-linecap:round;stroke-linejoin:round;stroke-width:2.25}
		.st29 {fill:#000000;font-family:Calibri;font-size:1.41666em;font-weight:bold}
		.st30 {fill:#ab9ac0;fill-opacity:0.35;stroke:#ff0000;stroke-dasharray:3,6;stroke-linecap:round;stroke-linejoin:round;stroke-width:3}
		.st31 {fill:none;stroke:#7f7f7f;stroke-linecap:round;stroke-linejoin:round;stroke-width:2.5}
		.st32 {fill:#000000;font-family:Calibri;font-size:1.75001em;font-weight:bold}
		.st33 {fill:#000000;font-family:Calibri;font-size:1.66667em;font-weight:bold}
		.st34 {fill:#ff0000;font-family:Calibri;font-size:1.83334em;font-weight:bold}
		.st35 {fill:none;fill-rule:evenodd;font-size:12px;overflow:visible;stroke-linecap:square;stroke-miterlimit:3}
	]]>
	</style>

	<defs id="Markers">
		<g id="lend13">
			<path d="M 3 1 L 0 0 L 3 -1 L 3 1 " style="stroke:none"/>
		</g>
		<marker id="mrkr13-12" class="st6" v:arrowType="13" v:arrowSize="2" v:setback="6.024" refX="-6.024" orient="auto"
				markerUnits="strokeWidth" overflow="visible">
			<use xlink:href="#lend13" transform="scale(-2.008,-2.008) "/>
		</marker>
		<marker id="mrkr13-29" class="st8" v:arrowType="13" v:arrowSize="2" v:setback="6.024" refX="-6.024" orient="auto"
				markerUnits="strokeWidth" overflow="visible">
			<use xlink:href="#lend13" transform="scale(-2.008,-2.008) "/>
		</marker>
		<marker id="mrkr13-41" class="st11" v:arrowType="13" v:arrowSize="2" v:setback="6.024" refX="-6.024" orient="auto"
				markerUnits="strokeWidth" overflow="visible">
			<use xlink:href="#lend13" transform="scale(-2.008,-2.008) "/>
		</marker>
		<marker id="mrkr13-58" class="st14" v:arrowType="13" v:arrowSize="1" v:setback="5.4" refX="-5.4" orient="auto"
				markerUnits="strokeWidth" overflow="visible">
			<use xlink:href="#lend13" transform="scale(-1.8,-1.8) "/>
		</marker>
		<marker id="mrkr13-64" class="st16" v:arrowType="13" v:arrowSize="1" v:setback="5.24" refX="5.24" orient="auto"
				markerUnits="strokeWidth" overflow="visible">
			<use xlink:href="#lend13" transform="scale(1.8) "/>
		</marker>
		<marker id="mrkr13-70" class="st18" v:arrowType="13" v:arrowSize="1" v:setback="5.16" refX="-5.16" orient="auto"
				markerUnits="strokeWidth" overflow="visible">
			<use xlink:href="#lend13" transform="scale(-1.72,-1.72) "/>
		</marker>
		<marker id="mrkr13-81" class="st22" v:arrowType="13" v:arrowSize="2" v:setback="5.88" refX="5.88" orient="auto"
				markerUnits="strokeWidth" overflow="visible">
			<use xlink:href="#lend13" transform="scale(2.008) "/>
		</marker>
		<marker id="mrkr13-95" class="st6" v:arrowType="13" v:arrowSize="2" v:setback="5.88" refX="5.88" orient="auto"
				markerUnits="strokeWidth" overflow="visible">
			<use xlink:href="#lend13" transform="scale(2.008) "/>
		</marker>
		<marker id="mrkr13-104" class="st26" v:arrowType="13" v:arrowSize="1" v:setback="5.4" refX="-5.4" orient="auto"
				markerUnits="strokeWidth" overflow="visible">
			<use xlink:href="#lend13" transform="scale(-1.8,-1.8) "/>
		</marker>
	</defs>
	<g v:mID="0" v:index="1" v:groupContext="foregroundPage">
		<title>页-1</title>
		<v:pageProperties v:drawingScale="0.0393701" v:pageScale="0.0393701" v:drawingUnits="24" v:shadowOffsetX="8.50394"
				v:shadowOffsetY="-8.50394"/>
		<v:layer v:name="连接线" v:index="0"/>
		<g id="shape20-1" v:mID="20" v:groupContext="shape" transform="translate(170.823,-162.845)">
			<title>工作表.20</title>
			<rect x="0" y="243.529" width="126.222" height="33.0812" rx="8.50394" ry="8.50394" class="st1"/>
		</g>
		<g id="shape21-3" v:mID="21" v:groupContext="shape" transform="translate(185.588,-162.845)">
			<title>工作表.21</title>
			<desc>Backward Propagation</desc>
			<v:textBlock v:margins="rect(4,4,4,4)" v:tabSpace="42.5197"/>
			<v:textRect cx="48.3461" cy="260.07" width="96.7" height="33.0812"/>
			<rect x="0" y="243.529" width="96.6923" height="33.0812" class="st2"/>
			<text x="15.68" y="256.67" class="st3" v:langID="2052"><v:paragraph v:spLine="-0.85" v:horizAlign="1"/><v:tabList/>Backward <tspan
						x="7.63" dy="0.85em" class="st4">Propagation</tspan></text>		</g>
		<g id="shape22-7" v:mID="22" v:groupContext="shape" v:layerMember="0" transform="translate(141.14,-172.608)">
			<title>动态连接线</title>
			<path d="M0 269.52 L14.62 269.52" class="st5"/>
		</g>
		<g id="shape25-13" v:mID="25" v:groupContext="shape" transform="translate(328.226,-162.872)">
			<title>工作表.25</title>
			<rect x="0" y="243.529" width="192.756" height="33.0812" rx="8.50394" ry="8.50394" class="st1"/>
		</g>
		<g id="shape26-15" v:mID="26" v:groupContext="shape" transform="translate(339.565,-162.872)">
			<title>工作表.26</title>
			<desc>Point-to-plane Residual Computation</desc>
			<v:textBlock v:margins="rect(4,4,4,4)" v:tabSpace="42.5197"/>
			<v:textRect cx="86.811" cy="260.07" width="173.63" height="33.0812"/>
			<rect x="0" y="243.529" width="173.622" height="33.0812" class="st2"/>
			<text x="8.73" y="256.67" class="st3" v:langID="2052"><v:paragraph v:spLine="-0.85" v:horizAlign="1"/><v:tabList/>Point-to-plane Residual <tspan
						x="43.13" dy="0.85em" class="st4">Computation</tspan></text>		</g>
		<g id="shape27-19" v:mID="27" v:groupContext="shape" v:layerMember="0" transform="translate(297.045,-172.313)">
			<title>动态连接线.27</title>
			<path d="M0 269.52 L16.12 269.52" class="st5"/>
		</g>
		<g id="shape35-24" v:mID="35" v:groupContext="shape" v:layerMember="0" transform="translate(141.14,-120.347)">
			<title>动态连接线.35</title>
			<path d="M0 269.52 L14.62 269.52" class="st7"/>
		</g>
		<g id="shape38-30" v:mID="38" v:groupContext="shape" transform="translate(170.823,-58.935)">
			<title>工作表.38</title>
			<rect x="0" y="243.529" width="126.222" height="33.0812" rx="8.50394" ry="8.50394" class="st9"/>
		</g>
		<g id="shape39-32" v:mID="39" v:groupContext="shape" transform="translate(185.588,-63.6857)">
			<title>工作表.39</title>
			<desc>Outlier Rejection</desc>
			<v:textBlock v:margins="rect(4,4,4,4)" v:tabSpace="42.5197"/>
			<v:textRect cx="48.3461" cy="264.82" width="96.7" height="23.5798"/>
			<rect x="0" y="253.03" width="96.6923" height="23.5798" class="st2"/>
			<text x="25.07" y="261.42" class="st3" v:langID="2052"><v:paragraph v:spLine="-0.85" v:horizAlign="1"/><v:tabList/>Outlier <tspan
						x="17.07" dy="0.85em" class="st4">Rejection</tspan></text>		</g>
		<g id="shape41-36" v:mID="41" v:groupContext="shape" v:layerMember="0" transform="translate(141.14,-68.389)">
			<title>动态连接线.41</title>
			<path d="M0 269.52 L14.62 269.52" class="st10"/>
		</g>
		<g id="shape43-42" v:mID="43" v:groupContext="shape" v:layerMember="0" transform="translate(297.045,-120.347)">
			<title>动态连接线.43</title>
			<path d="M0 269.52 L63.25 269.52" class="st7"/>
		</g>
		<g id="shape54-47" v:mID="54" v:groupContext="shape" transform="translate(375.359,-110.893)">
			<title>工作表.54</title>
			<rect x="0" y="243.529" width="92.8218" height="33.0812" rx="8.50394" ry="8.50394" class="st12"/>
		</g>
		<g id="shape55-49" v:mID="55" v:groupContext="shape" transform="translate(379.394,-115.644)">
			<title>工作表.55</title>
			<desc>State Estimation</desc>
			<v:textBlock v:margins="rect(4,4,4,4)" v:tabSpace="42.5197"/>
			<v:textRect cx="42.3754" cy="264.82" width="84.76" height="23.5798"/>
			<rect x="0" y="253.03" width="84.7508" height="23.5798" class="st2"/>
			<text x="25.07" y="261.42" class="st3" v:langID="2052"><v:paragraph v:spLine="-0.85" v:horizAlign="1"/><v:tabList/>State <tspan
						x="6.75" dy="0.85em" class="st4">Estimation</tspan></text>		</g>
		<g id="shape56-53" v:mID="56" v:groupContext="shape" v:layerMember="0" transform="translate(414.683,-162.872)">
			<title>动态连接线.56</title>
			<path d="M7.09 276.61 L7.09 283.36" class="st13"/>
		</g>
		<g id="shape57-59" v:mID="57" v:groupContext="shape" v:layerMember="0" transform="translate(414.683,-110.893)">
			<title>动态连接线.57</title>
			<path d="M7.09 288.4 L7.09 288.76 L7.09 295.49" class="st15"/>
		</g>
		<g id="shape61-65" v:mID="61" v:groupContext="shape" v:layerMember="0" transform="translate(594.571,-41.5086)">
			<title>动态连接线.61</title>
			<path d="M0 276.61 L-360.74 276.61 L-360.74 272.08" class="st17"/>
		</g>
		<g id="shape84-71" v:mID="84" v:groupContext="shape" transform="translate(359.731,-210.106)">
			<title>工作表.84</title>
			<rect x="0" y="243.529" width="96.6923" height="33.0812" rx="14.1732" ry="14.1732" class="st19"/>
		</g>
		<g id="shape85-73" v:mID="85" v:groupContext="shape" transform="translate(359.731,-210.106)">
			<title>工作表.85</title>
			<desc>Odometry</desc>
			<v:textBlock v:margins="rect(4,4,4,4)" v:tabSpace="42.5197"/>
			<v:textRect cx="48.3461" cy="260.07" width="96.7" height="33.0812"/>
			<rect x="0" y="243.529" width="96.6923" height="33.0812" class="st2"/>
			<text x="10.16" y="265.47" class="st20" v:langID="2052"><v:paragraph v:horizAlign="1"/><v:tabList/>Odometry</text>		</g>
		<g id="shape86-76" v:mID="86" v:groupContext="shape" v:layerMember="0" transform="translate(456.424,-226.646)">
			<title>动态连接线.86</title>
			<path d="M14.7 276.61 L15.06 276.61 L90.07 276.61 L90.07 376.29" class="st21"/>
		</g>
		<g id="shape87-82" v:mID="87" v:groupContext="shape" transform="translate(456.35,-228.111)">
			<title>工作表.87</title>
			<desc>20~150 Hz</desc>
			<v:textBlock v:margins="rect(4,4,4,4)" v:tabSpace="42.5197"/>
			<v:textRect cx="54.9934" cy="264.82" width="109.99" height="23.5798"/>
			<rect x="0" y="253.03" width="109.987" height="23.5798" class="st2"/>
			<text x="16.41" y="270.22" class="st20" v:langID="2052"><v:paragraph v:horizAlign="1"/><v:tabList/>20~150 Hz</text>		</g>
		<g id="shape92-85" v:mID="92" v:groupContext="shape" v:layerMember="0" transform="translate(594.571,-134.053)">
			<title>动态连接线.92</title>
			<path d="M-14.7 283.7 L-15.06 283.7 L-126.39 283.7" class="st21"/>
		</g>
		<g id="shape101-90" v:mID="101" v:groupContext="shape" v:layerMember="0" transform="translate(520.982,-173.072)">
			<title>动态连接线.80</title>
			<path d="M14.7 269.52 L15.06 269.52 L23.15 269.52 A2.3622 2.3622 0 1 1 27.87 269.52 L74.34 269.52" class="st23"/>
		</g>
		<g id="shape103-96" v:mID="103" v:groupContext="shape" transform="translate(34.8406,-205.408)">
			<title>工作表.103</title>
			<desc>System Overview</desc>
			<v:textBlock v:margins="rect(4,4,4,4)" v:tabSpace="42.5197"/>
			<v:textRect cx="134.646" cy="253.005" width="269.3" height="47.211"/>
			<rect x="0" y="229.399" width="269.291" height="47.211" class="st2"/>
			<text x="27.7" y="262" class="st24" v:langID="2052"><v:paragraph v:horizAlign="1"/><v:tabList/>System Overview</text>		</g>
		<g id="shape104-99" v:mID="104" v:groupContext="shape" v:layerMember="0" transform="translate(226.848,-143.974)">
			<title>动态连接线.104</title>
			<path d="M7.09 276.61 L7.09 269.89" class="st25"/>
		</g>
		<g id="shape105-105" v:mID="105" v:groupContext="shape" transform="translate(297.045,-110.276)">
			<title>工作表.105</title>
			<desc>Prior estimation</desc>
			<v:textBlock v:margins="rect(4,4,4,4)" v:tabSpace="42.5197"/>
			<v:textRect cx="34.039" cy="259.761" width="68.08" height="33.6987"/>
			<rect x="0" y="242.912" width="68.0781" height="33.6987" class="st2"/>
			<text x="21.88" y="256.16" class="st27" v:langID="2052"><v:paragraph v:horizAlign="1"/><v:tabList/>Prior<v:newlineChar/><tspan
						x="7.23" dy="1.2em" class="st4">estimation</tspan></text>		</g>
		<g id="shape107-109" v:mID="107" v:groupContext="shape" transform="translate(374.944,-95.8567)">
			<title>工作表.107</title>
			<desc>Visual</desc>
			<v:textBlock v:margins="rect(4,4,4,4)" v:tabSpace="42.5197"/>
			<v:textRect cx="23.4259" cy="271.837" width="46.86" height="9.54654"/>
			<rect x="0" y="267.064" width="46.8517" height="9.54654" class="st2"/>
			<text x="8.36" y="275.44" class="st27" v:langID="2052"><v:paragraph v:horizAlign="1"/><v:tabList/>Visual</text>		</g>
		<g id="shape108-112" v:mID="108" v:groupContext="shape" transform="translate(482.95,-112.132)">
			<title>工作表.108</title>
			<desc>Update new scan</desc>
			<v:textBlock v:margins="rect(4,4,4,4)" v:tabSpace="42.5197"/>
			<v:textRect cx="28.9373" cy="260.07" width="57.88" height="33.0812"/>
			<rect x="0" y="243.529" width="57.8747" height="33.0812" class="st2"/>
			<text x="10.52" y="256.47" class="st27" v:langID="2052"><v:paragraph v:horizAlign="1"/><v:tabList/>Update<v:newlineChar/><tspan
						x="5.78" dy="1.2em" class="st4">new scan</tspan></text>		</g>
		<g id="shape110-116" v:mID="110" v:groupContext="shape" v:layerMember="0" transform="translate(226.848,-110.893)">
			<title>动态连接线.110</title>
			<path d="M7.09 276.61 L7.09 283.34" class="st25"/>
		</g>
		<g id="group112-121" transform="translate(328.226,-58.935)" v:mID="112" v:groupContext="group">
			<title>工作表.112</title>
			<g id="shape44-122" v:mID="44" v:groupContext="shape">
				<title>工作表.44</title>
				<rect x="0" y="243.529" width="192.756" height="33.0812" rx="8.50394" ry="8.50394" class="st9"/>
			</g>
			<g id="shape45-124" v:mID="45" v:groupContext="shape" transform="translate(14.6273,-4.7507)">
				<title>工作表.45</title>
				<desc>Sparse-Direct Visual Alignment</desc>
				<v:textBlock v:margins="rect(4,4,4,4)" v:tabSpace="42.5197"/>
				<v:textRect cx="81.7507" cy="264.82" width="163.51" height="23.5798"/>
				<rect x="0" y="253.03" width="163.501" height="23.5798" class="st2"/>
				<text x="15.31" y="261.42" class="st3" v:langID="2052"><v:paragraph v:spLine="-0.85" v:horizAlign="1"/><v:tabList/>Sparse-Direct Visual <tspan
							x="47.29" dy="0.85em" class="st4">Alignment</tspan></text>			</g>
		</g>
		<g id="group113-128" transform="translate(31.1529,-58.935)" v:mID="113" v:groupContext="group">
			<title>工作表.113</title>
			<g id="shape18-129" v:mID="18" v:groupContext="shape">
				<title>工作表.18</title>
				<rect x="0" y="243.529" width="109.987" height="33.0812" rx="8.50394" ry="8.50394" class="st9"/>
			</g>
			<g id="shape19-131" v:mID="19" v:groupContext="shape" transform="translate(16.6535,-3.42342)">
				<title>工作表.19</title>
				<desc>Camera 10~50 Hz</desc>
				<v:textBlock v:margins="rect(4,4,4,4)" v:tabSpace="42.5197"/>
				<v:textRect cx="38.3399" cy="263.493" width="76.68" height="26.2344"/>
				<rect x="0" y="250.376" width="76.6798" height="26.2344" class="st2"/>
				<text x="9.64" y="259.67" class="st20" v:langID="2052"><v:paragraph v:spLine="-0.85" v:horizAlign="1"/><v:tabList/>Camera<v:newlineChar/><tspan
							x="4.32" dy="0.85em" class="st4">10</tspan>~50 Hz</text>			</g>
		</g>
		<g id="group114-135" transform="translate(31.1529,-110.893)" v:mID="114" v:groupContext="group">
			<title>工作表.114</title>
			<g id="shape16-136" v:mID="16" v:groupContext="shape">
				<title>工作表.16</title>
				<rect x="0" y="243.529" width="109.987" height="33.0812" rx="8.50394" ry="8.50394" class="st28"/>
			</g>
			<g id="shape17-138" v:mID="17" v:groupContext="shape" transform="translate(24.9475,-2.36738)">
				<title>工作表.17</title>
				<desc>IMU</desc>
				<v:textBlock v:margins="rect(4,4,4,4)" v:tabSpace="42.5197"/>
				<v:textRect cx="29.6575" cy="264.82" width="59.32" height="23.5798"/>
				<rect x="0" y="253.03" width="59.315" height="23.5798" class="st2"/>
				<text x="14.41" y="268.43" class="st29" v:langID="2052"><v:paragraph v:spLine="-0.85" v:horizAlign="1"/><v:tabList/>IMU</text>			</g>
		</g>
		<g id="group115-141" transform="translate(31.1529,-162.845)" v:mID="115" v:groupContext="group">
			<title>工作表.115</title>
			<g id="shape4-142" v:mID="4" v:groupContext="shape" transform="translate(7.99361E-15,-0.617471)">
				<title>工作表.4</title>
				<rect x="0" y="243.529" width="109.987" height="33.0812" rx="8.50394" ry="8.50394" class="st1"/>
			</g>
			<g id="shape13-144" v:mID="13" v:groupContext="shape" transform="translate(7.728,5.68434E-14)">
				<title>工作表.13</title>
				<desc>LiDAR 10~100 Hz</desc>
				<v:textBlock v:margins="rect(4,4,4,4)" v:tabSpace="42.5197"/>
				<v:textRect cx="48.3461" cy="260.07" width="96.7" height="33.0812"/>
				<rect x="0" y="243.529" width="96.6923" height="33.0812" class="st2"/>
				<text x="27.37" y="256.46" class="st29" v:langID="2052"><v:paragraph v:spLine="-0.85" v:horizAlign="1"/><v:tabList/>LiDAR<v:newlineChar/><tspan
							x="11.91" dy="0.85em" class="st4">10</tspan>~100 Hz</text>			</g>
		</g>
		<g id="group116-148" transform="translate(170.823,-110.893)" v:mID="116" v:groupContext="group">
			<title>工作表.116</title>
			<g id="shape32-149" v:mID="32" v:groupContext="shape">
				<title>工作表.32</title>
				<rect x="0" y="243.529" width="126.222" height="33.0812" rx="8.50394" ry="8.50394" class="st28"/>
			</g>
			<g id="shape33-151" v:mID="33" v:groupContext="shape" transform="translate(14.7648,-4.7507)">
				<title>工作表.33</title>
				<desc>Forward Propagation</desc>
				<v:textBlock v:margins="rect(4,4,4,4)" v:tabSpace="42.5197"/>
				<v:textRect cx="48.3461" cy="264.82" width="96.7" height="23.5798"/>
				<rect x="0" y="253.03" width="96.6923" height="23.5798" class="st2"/>
				<text x="20.48" y="261.42" class="st3" v:langID="2052"><v:paragraph v:spLine="-0.85" v:horizAlign="1"/><v:tabList/>Forward <tspan
							x="7.63" dy="0.85em" class="st4">Propagation</tspan></text>			</g>
		</g>
		<g id="shape117-155" v:mID="117" v:groupContext="shape" v:layerMember="0" transform="translate(297.045,-67.439)">
			<title>动态连接线.40</title>
			<path d="M0 269.52 L16.12 269.52" class="st10"/>
		</g>
		<g id="group123-160" transform="translate(594.571,-25.8538)" v:mID="123" v:groupContext="group">
			<title>工作表.123</title>
			<g id="shape58-161" v:mID="58" v:groupContext="shape">
				<title>工作表.58</title>
				<rect x="0" y="243.529" width="109.987" height="33.0812" rx="8.50394" ry="8.50394" class="st9"/>
			</g>
			<g id="shape59-163" v:mID="59" v:groupContext="shape" transform="translate(-3.19744E-14,-4.7507)">
				<title>工作表.59</title>
				<desc>Visual Submap</desc>
				<v:textBlock v:margins="rect(4,4,4,4)" v:tabSpace="42.5197"/>
				<v:textRect cx="54.9934" cy="264.82" width="109.99" height="23.5798"/>
				<rect x="0" y="253.03" width="109.987" height="23.5798" class="st2"/>
				<text x="5.97" y="268.22" class="st3" v:langID="2052"><v:paragraph v:spLine="-0.85" v:horizAlign="1"/><v:tabList/>Visual Submap</text>			</g>
		</g>
		<g id="shape130-166" v:mID="130" v:groupContext="shape" transform="translate(567.116,-80.1949)">
			<title>工作表.130</title>
			<rect x="0" y="113.618" width="155.126" height="162.992" rx="14.1732" ry="14.1732" class="st30"/>
		</g>
		<g id="shape131-168" v:mID="131" v:groupContext="shape" transform="translate(19.25,-19.25)">
			<title>工作表.131</title>
			<rect x="0" y="38.5" width="717.165" height="238.11" rx="8.50394" ry="8.50394" class="st31"/>
		</g>
		<g id="shape135-170" v:mID="135" v:groupContext="shape" v:layerMember="0" transform="translate(596.987,-169.486)">
			<title>动态连接线.135</title>
			<path d="M-14.7 279.44 L-15.06 279.44 L-50.49 279.44 L-50.49 287.95" class="st21"/>
		</g>
		<g id="group136-175" transform="translate(595.318,-158.148)" v:mID="136" v:groupContext="group">
			<title>工作表.136</title>
			<g id="shape137-176" v:mID="137" v:groupContext="shape">
				<title>工作表.137</title>
				<rect x="0" y="229.399" width="109.987" height="47.211" rx="8.50394" ry="8.50394" class="st1"/>
			</g>
			<g id="shape138-178" v:mID="138" v:groupContext="shape" transform="translate(15.9065,-11.8156)">
				<title>工作表.138</title>
				<desc>ikdTree</desc>
				<v:textBlock v:margins="rect(4,4,4,4)" v:tabSpace="42.5197"/>
				<v:textRect cx="38.3399" cy="264.82" width="76.68" height="23.5798"/>
				<rect x="0" y="253.03" width="76.6798" height="23.5798" class="st2"/>
				<text x="5.58" y="271.12" class="st32" v:langID="2052"><v:paragraph v:horizAlign="1"/><v:tabList/>ikdTree</text>			</g>
		</g>
		<g id="shape142-181" v:mID="142" v:groupContext="shape" v:layerMember="0" transform="translate(642.478,-98.6201)">
			<title>动态连接线.69</title>
			<path d="M7.09 276.61 L7.09 301.24" class="st10"/>
		</g>
		<g id="group143-186" transform="translate(594.571,-98.6201)" v:mID="143" v:groupContext="group">
			<title>工作表.143</title>
			<g id="shape144-187" v:mID="144" v:groupContext="shape">
				<title>工作表.144</title>
				<rect x="0" y="229.399" width="109.987" height="47.211" rx="8.50394" ry="8.50394" class="st9"/>
			</g>
			<g id="shape145-189" v:mID="145" v:groupContext="shape">
				<title>工作表.145</title>
				<desc>Visual Global Map</desc>
				<v:textBlock v:margins="rect(4,4,4,4)" v:tabSpace="42.5197"/>
				<v:textRect cx="54.9934" cy="253.005" width="109.99" height="47.211"/>
				<rect x="0" y="229.399" width="109.987" height="47.211" class="st2"/>
				<text x="29.88" y="248.5" class="st33" v:langID="2052"><v:paragraph v:spLine="-0.9" v:horizAlign="1"/><v:tabList/>Visual <tspan
							x="6.73" dy="0.9em" class="st4">Global Map</tspan></text>			</g>
		</g>
		<g id="shape146-193" v:mID="146" v:groupContext="shape" transform="translate(584.682,-210.683)">
			<title>工作表.146</title>
			<desc>Global Map</desc>
			<v:textBlock v:margins="rect(4,4,4,4)" v:tabSpace="42.5197"/>
			<v:textRect cx="63.1109" cy="264.82" width="126.23" height="23.5798"/>
			<rect x="0" y="253.03" width="126.222" height="23.5798" class="st2"/>
			<text x="10.02" y="271.42" class="st34" v:langID="2052"><v:paragraph v:horizAlign="1"/><v:tabList/>Global Map</text>		</g>
		<g id="shape147-196" v:mID="147" v:groupContext="shape" transform="translate(397.595,-21.1066)">
			<title>工作表.147</title>
			<desc>Project</desc>
			<v:textBlock v:margins="rect(4,4,4,4)" v:tabSpace="42.5197"/>
			<v:textRect cx="23.4259" cy="266.2" width="46.86" height="20.8205"/>
			<rect x="0" y="255.79" width="46.8517" height="20.8205" class="st2"/>
			<text x="5.73" y="269.8" class="st27" v:langID="2052"><v:paragraph v:horizAlign="1"/><v:tabList/>Project</text>		</g>
		<g id="shape148-199" v:mID="148" v:groupContext="shape" transform="translate(420.522,-91.148)">
			<title>工作表.148</title>
			<desc>measurement</desc>
			<v:textBlock v:margins="rect(4,4,4,4)" v:tabSpace="42.5197"/>
			<v:textRect cx="42.3754" cy="266.2" width="84.76" height="20.8205"/>
			<rect x="0" y="255.79" width="84.7508" height="20.8205" class="st2"/>
			<text x="7.54" y="269.8" class="st27" v:langID="2052"><v:paragraph v:horizAlign="1"/><v:tabList/>measurement</text>		</g>
		<g id="shape149-202" v:mID="149" v:groupContext="shape" transform="translate(420.667,-143.15)">
			<title>工作表.149</title>
			<desc>measurement</desc>
			<v:textBlock v:margins="rect(4,4,4,4)" v:tabSpace="42.5197"/>
			<v:textRect cx="42.3754" cy="266.2" width="84.76" height="20.8205"/>
			<rect x="0" y="255.79" width="84.7508" height="20.8205" class="st2"/>
			<text x="7.54" y="269.8" class="st27" v:langID="2052"><v:paragraph v:horizAlign="1"/><v:tabList/>measurement</text>		</g>
		<g id="shape150-205" v:mID="150" v:groupContext="shape" transform="translate(377.11,-148.298)">
			<title>工作表.150</title>
			<desc>LiDAR</desc>
			<v:textBlock v:margins="rect(4,4,4,4)" v:tabSpace="42.5197"/>
			<v:textRect cx="23.4259" cy="271.837" width="46.86" height="9.54654"/>
			<rect x="0" y="267.064" width="46.8517" height="9.54654" class="st2"/>
			<text x="8.62" y="275.44" class="st27" v:langID="2052"><v:paragraph v:horizAlign="1"/><v:tabList/>LiDAR</text>		</g>
	</g>
</svg>
