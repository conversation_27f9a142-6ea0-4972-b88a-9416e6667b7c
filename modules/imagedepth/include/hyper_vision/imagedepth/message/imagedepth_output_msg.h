/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef HYPER_VISION_IMAGEDEPTH_MESSAGE_IMAGEDEPTH_OUTPUT_MSG_H_
#define HYPER_VISION_IMAGEDEPTH_MESSAGE_IMAGEDEPTH_OUTPUT_MSG_H_

#include "hyper_vision/common/common.h"

namespace robosense {
namespace imagedepth {

struct ImageDepthOutputMsg {
  using Ptr = std::shared_ptr<ImageDepthOutputMsg>;

  common::Header header;

  common::DepthImage image_depth;
  common::RgbImage left_rgb_image;
  common::RgbImage right_rgb_image;
};

} // namespace imagedepth
} // namespace robosense

#endif // HYPER_VISION_IMAGEDEPTH_MESSAGE_IMAGEDEPTH_OUTPUT_MSG_H_
