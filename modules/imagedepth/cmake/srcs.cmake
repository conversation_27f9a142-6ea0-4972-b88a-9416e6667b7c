#========================
# libs
#========================

set(CMAKE_POSITION_INDEPENDENT_CODE ON)

set(CUR_SRCS "")
set(CUR_INCLUDES "include")

set(CUR_SUB_DIR "")
LIST(APPEND CUR_SUB_DIR include)
LIST(APPEND CUR_SUB_DIR src)

foreach (dir ${CUR_SUB_DIR})
    file(GLOB_RECURSE tmp_srcs ${dir}/*.cpp ${dir}/*.h)
    list(APPEND CUR_SRCS ${tmp_srcs})
endforeach ()

if(WIN32)
    set(CUSTOM_FLANN_DIR  "C:\\Program Files (x86)\\flann\\include")
endif(WIN32)

add_library(${CUR_LIB} STATIC
        ${CUR_SRCS}
        )

target_include_directories(${CUR_LIB}
        PUBLIC
        ${CUR_INCLUDES}
        ${PCL_INCLUDE_DIRS}
        ${CUSTOM_FLANN_DIR}
        )
target_link_libraries(${CUR_LIB}
        PUBLIC
        rally_utils
        #supersensor
        common
        ${PCL_LIBRARIES}
        ${OpenCV_LIBS}
        )

target_compile_definitions(${CUR_LIB} PRIVATE MODULE_NAME="image_depth")
