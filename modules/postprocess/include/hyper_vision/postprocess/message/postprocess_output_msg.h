/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef HYPER_VISION_POSTPROCESS_MESSAGE_POSTPROCESS_OUTPUT_MSG_H_
#define HYPER_VISION_POSTPROCESS_MESSAGE_POSTPROCESS_OUTPUT_MSG_H_

#include "hyper_vision/common/common.h"

namespace robosense {
namespace postprocess {

struct PostprocessOutputMsg {
  using Ptr = std::shared_ptr<PostprocessOutputMsg>;

  common::Header header;

  common::Odom odom;
  common::TriangleFacet triangle_facet;
  common::PointCloud point_cloud;
  common::DepthImage depth_image;
  common::RgbImage rgb_image;
};

}  // namespace postprocess
}  // namespace robosense

#endif  // HYPER_VISION_POSTPROCESS_MESSAGE_POSTPROCESS_OUTPUT_MSG_H_
