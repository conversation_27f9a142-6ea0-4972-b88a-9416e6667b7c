/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef HYPER_VISION_POSTPROCESS_SYNCQUEUE_H_
#define HYPER_VISION_POSTPROCESS_SYNCQUEUE_H_
#include <queue>
template <typename T>
class SyncVector
{
public:
  SyncVector() = default;
  ~SyncVector() = default;

  void add(const T &element)
  {
    std::lock_guard<std::mutex> lock(mutex_);
    if (data_.size() >5) {
      data_.pop_front();
    }
    data_.push_back(element);
  }

  T front() const
  {
    std::lock_guard<std::mutex> lock(mutex_);
    if (data_.empty())
    {
      throw std::out_of_range("Vector is empty");
    }
    return data_.front();
  }

  T back() const
  {
    std::lock_guard<std::mutex> lock(mutex_);
    if (data_.empty())
    {
      throw std::out_of_range("Vector is empty");
    }
    return data_.back();
  }


  bool remove(const T &element)
  {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = std::find(data_.begin(), data_.end(), element);
    if (it != data_.end())
    {
      data_.erase(it);
      return true;
    }
    return false;
  }

  T get(size_t index) const
  {
    std::lock_guard<std::mutex> lock(mutex_);
    if (index < data_.size())
    {
      return data_[index];
    }
    throw std::out_of_range("Index out of range");
  }

  size_t size() const
  {
    std::lock_guard<std::mutex> lock(mutex_);
    return data_.size();
  }

private:
  std::deque<T> data_;
  mutable std::mutex mutex_;
};


template <typename T>
class SyncQueue
{
public:
  SyncQueue(size_t max_size) : max_size_(max_size) {}
  inline size_t push(const T& value)
  {
#ifndef ENABLE_WAIT_IF_QUEUE_EMPTY
     bool empty = false;
#endif
     size_t size = 0;

    {
      std::lock_guard<std::mutex> lg(mtx_);
#ifndef ENABLE_WAIT_IF_QUEUE_EMPTY
      empty = queue_.empty();
#endif
      queue_.push_back(value);
      if (queue_.size() > max_size_) {
        queue_.pop_front();
      }
      size = queue_.size();
    }

#ifndef ENABLE_WAIT_IF_QUEUE_EMPTY
    if (empty)
      cv_.notify_one();
#endif

    return size;
  }

  inline T pop()
  {
    T value;

    std::lock_guard<std::mutex> lg(mtx_);
    if (!queue_.empty())
    {
      value = queue_.front();
      queue_.pop_front();
    }

    return value;
  }

  inline T popWait(unsigned int usec = 1000000)
  {
    //
    // Low latency, or low CPU usage, that is the question.
    //                                            - Hamlet

#ifdef ENABLE_WAIT_IF_QUEUE_EMPTY
    T value;

    {
      std::lock_guard<std::mutex> lg(mtx_);
      if (!queue_.empty())
      {
        value = queue_.front();
        queue_.pop_front();
        return value;
      }
    }

    std::this_thread::sleep_for(std::chrono::microseconds(1000));
    return value;
#else

    T value;

    std::unique_lock<std::mutex> ul(mtx_);
    cv_.wait_for(ul, std::chrono::microseconds(usec), [this] { return (!queue_.empty()); });

    if (!queue_.empty())
    {
      value = queue_.front();
      queue_.pop_front();
    }

    return value;
#endif
  }

  inline void clear()
  {
    std::queue<T> empty;
    std::lock_guard<std::mutex> lg(mtx_);
    swap(empty, queue_);
  }

  inline bool empty() {
    std::lock_guard<std::mutex> lg(mtx_);
    return queue_.empty();
  }

  inline size_t size() {
    std::lock_guard<std::mutex> lg(mtx_);
    return queue_.size();
  }

private:
  std::deque<T> queue_;
  size_t max_size_;
  std::mutex mtx_;
#ifndef ENABLE_WAIT_IF_QUEUE_EMPTY
  std::condition_variable cv_;
#endif
};

#endif // HYPER_VISION_POSTPROCESS_SYNCQUEUE_H_

