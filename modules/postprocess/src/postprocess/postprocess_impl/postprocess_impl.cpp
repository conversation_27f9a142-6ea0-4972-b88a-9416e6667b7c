#include "hyper_vision/postprocess/postprocess_impl/postprocess_impl.h"
#include "hyper_vision/postprocess/postprocess_impl/imu_module.h"
#include "hyper_vision/postprocess/postprocess_impl/camera_model.h"

namespace robosense {
namespace postprocess {
void FromMsg(const robosense::common::DepthFrame& msg, pcl::PointCloud<PointXYZIRT>& cloud) {
  cloud.points.resize(msg.point_nums);
  for (int i = 0; i < msg.point_nums; ++i) {
    auto pointi = msg.points.get()[i];
    cloud.points[i].x = pointi.x;
    cloud.points[i].y = pointi.y;
    cloud.points[i].z = pointi.z;
    cloud.points[i].intensity = pointi.intensity;
    cloud.points[i].ring = pointi.ring;
    cloud.points[i].timestamp = pointi.timestamp;
  }
}

bool PostprocessImpl::ProcessPointCloud() {
  // while (!to_exit_process_) {
    auto start = std::chrono::high_resolution_clock::now();
    auto msg = point_cloud_queue_.popWait(1000);

    if (msg == nullptr)
      return false;

    pcl::PointCloud<PointXYZIRT>::Ptr ori_cloud(new pcl::PointCloud<PointXYZIRT>());
    FromMsg(*msg, *ori_cloud);
    pcl::PointCloud<PointXYZIRT>::Ptr cm_cloud(new pcl::PointCloud<PointXYZIRT>());

    int point_index = 0;
    double head_stamp = ori_cloud->points.front().timestamp;
    double tail_stamp = ori_cloud->points.back().timestamp;
    double points_stamp = frame_tail_ ? tail_stamp : head_stamp;
    RINFO << name() << ": " << rally::Time::getNow().toString() << " process cloud msg " << std::to_string(points_stamp);

    RTRACE << name() << " Montion Correct Process, head stamp: " + std::to_string(head_stamp) + " tail stamp: " + std::to_string(tail_stamp) + " using stamp: " + std::to_string(points_stamp);

    MCStatus status = MCStatus::SKIPT;
    std::size_t count = 0, count_thres = 5;
    while (count++ < count_thres && !imu_module_->setHeadStamp(points_stamp, status, frame_tail_)) {
      RERROR << name() << ": " << count << " setHeadStamp failed status: " << int(status) << std::endl;
      if (MCStatus::WAITING == status || MCStatus::SKIPT == status) {
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
        continue;
      }
      break;
    }
    if (MCStatus::SKIPT == status || MCStatus::WAITING == status) {
      // RWARN << "Montion Correct Warning, SetHeadStamp Failed find Frames, Skip Montion Correct!";
      // continue;
      return false;
    }
    RDEBUG << name() << ": SetHeadStamp success find Frames, pts timestamp: "
        + std::to_string(points_stamp)
        + " odom head: " + std::to_string(imu_module_->imu_window_.front().imu_data.timestamp)
        + " odom tail: " + std::to_string(imu_module_->imu_window_.back().imu_data.timestamp)
        + " odom size: " + std::to_string(imu_module_->imu_window_.size());

    std::size_t search_idx = 0;
    Eigen::Matrix4d T_i1_i2 = Eigen::Matrix4d::Identity();
    for (std::size_t i = 0, num_pts = ori_cloud->points.size(); i < num_pts; ++i) {
      const auto& current_point = ori_cloud->points.at(point_index);
      double cur_stamp = current_point.timestamp;
      if (cur_stamp > tail_stamp) {
        RERROR << "points stamp error than head_stamp or tail_stamp:  "
        << std::to_string(head_stamp) << " " << std::to_string(cur_stamp) << " " << std::to_string(tail_stamp);
        RERROR << "points stamp: " << std::to_string(ori_cloud->points[0].timestamp) << " " << std::to_string(ori_cloud->points[1].timestamp)
        << " " << std::to_string(ori_cloud->points[2].timestamp) << " " << std::to_string(ori_cloud->points[msg->point_nums-1].timestamp) ;

      }
      if (!imu_module_->calHeadIMURot(cur_stamp, T_i1_i2, search_idx, frame_tail_)) {
        RWARN << "Montion Correct Warning, cal calHeadIMURot failed " + std::to_string(head_stamp) + " " +
                    std::to_string(cur_stamp) + " " + std::to_string(tail_stamp);
        point_index++;
        continue;
      }
      if (frame_tail_) {
        Eigen::Matrix<double, 4, 4> _T_i1_i2_inv = T_i1_i2.eval();
        T_i1_i2 = _T_i1_i2_inv.inverse();
      }
      Eigen::Vector4d pt2(current_point.x, current_point.y, current_point.z, 1.0);

      Eigen::Matrix4d T_l1_l2 = T_lidar_imu_ * T_i1_i2 * T_imu_lidar_;
      Eigen::Vector4d trans_pt1 = T_l1_l2 * pt2;
      PointXYZIRT cm_pt;
      cm_pt.x = trans_pt1[0];
      cm_pt.y = trans_pt1[1];
      cm_pt.z = trans_pt1[2];
      cm_pt.intensity = current_point.intensity;
      cm_pt.ring = point_index;
      cm_pt.timestamp = points_stamp;
      cm_cloud->push_back(cm_pt);
      point_index++;
    }

    mc_point_cloud_vec_.add(cm_cloud);
    point_cloud_free_vec_.add(ori_cloud);

    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> duration = end - start;
    RINFO << name() << ": " << rally::Time::getNow().toString() + " " << "Succ Process PointCloud, Cost time: " + std::to_string(duration.count()) + " ms";
  // }
    return true;
}

inline bool PostprocessImpl::findNearestPoint(double _cam_stamp,  std::size_t& _search_idx) {
  std::size_t search_idx = _search_idx;
  if (mc_point_cloud_vec_.size() == 0)
  {
    RERROR << "mc_point_cloud_vec_ empty" << std::endl;
    return false;
  }

  double front_stamp = mc_point_cloud_vec_.front()->points.front().timestamp;
  double back_stamp = mc_point_cloud_vec_.back()->points.front().timestamp;

  // 如果时间戳差距过大就跳过
  if (_cam_stamp < front_stamp || _cam_stamp > back_stamp)
  {
    RERROR << std::fixed << std::setprecision(9) << "findNearestPoint"
              << " head_stamp: " << front_stamp << " cam_stamp: " << _cam_stamp << " back_stamp: " << back_stamp
              << std::endl;
    return false;
  }

  // 找到距离当前相机帧最近的Lidar帧
  while (search_idx > 1 && mc_point_cloud_vec_.get(search_idx - 1)->points.front().timestamp >= _cam_stamp)
  {
    search_idx--;
  }

  while (search_idx < mc_point_cloud_vec_.size() && _cam_stamp > mc_point_cloud_vec_.get(search_idx)->points.front().timestamp)
  {
    search_idx++;
  }

  if (search_idx >= 1 && _cam_stamp > mc_point_cloud_vec_.get(search_idx - 1)->points.front().timestamp &&
      search_idx < mc_point_cloud_vec_.size() &&
      _cam_stamp <= mc_point_cloud_vec_.get(search_idx)->points.front().timestamp)
  {
    auto lhs_pts = mc_point_cloud_vec_.get(search_idx - 1);
    auto rhs_pts = mc_point_cloud_vec_.get(search_idx);

    double lhs_diff = std::abs(_cam_stamp - lhs_pts->points.front().timestamp);
    double rhs_diff = std::abs(rhs_pts->points.front().timestamp - _cam_stamp);

    _search_idx = (lhs_diff < rhs_diff) ? search_idx - 1 : search_idx;
  }
  else
  {
    return false;
  }

  return true;
}

inline void PostprocessImpl::labelImage(cv::Mat& img, const std::string& label) {
  int fontFace = cv::FONT_HERSHEY_SIMPLEX;
  double fontScale = 1.2;
  int thickness = 2;
  cv::Point textOrg(30, 120);
  cv::putText(img, label, textOrg, fontFace, fontScale, cv::Scalar(0, 0, 255), thickness, 8);
}

std::shared_ptr<robosense::common::ImageFrame> PostprocessImpl::findNearestCam(double point_stamp) {
  std::unique_lock<std::mutex> lck(cam_mt_);
  std::shared_ptr<robosense::common::ImageFrame> res_msg = nullptr;
  double min_dur = std::numeric_limits<double>::max();
  for (size_t i = 0; i < cam_queue_.size(); i++) {
    auto msg = cam_queue_[i];
    auto cur_stamp = msg->capture_time.tv_sec + msg->capture_time.tv_usec / 1e6;
    auto cam_point_dura = std::abs(cur_stamp - point_stamp);
    if (cam_point_dura < thres_ && cam_point_dura < min_dur) {
      res_msg = msg;
      min_dur = cam_point_dura;
    }
  }
  if (res_msg == nullptr) {
    auto tail_stamp = cam_queue_.back()->capture_time.tv_sec + cam_queue_.back()->capture_time.tv_usec / 1e6;
    RERROR << "Failed to find cam, tail cam is " << std::to_string(tail_stamp)
           << " " << std::to_string(point_stamp - tail_stamp);
  }
  return res_msg;
}

bool PostprocessImpl::ProcessCompressedImage() {
  std::size_t img_count = 0;
  auto start = std::chrono::high_resolution_clock::now();

  pcl::PointCloud<PointXYZIRT>::Ptr ori_pts, cm_pts;
  ori_pts = point_cloud_free_vec_.back();
  cm_pts = mc_point_cloud_vec_.back();
  auto pts_stamp = cm_pts->points.front().timestamp;
  auto msg = findNearestCam(pts_stamp);
  if (msg == nullptr || msg->data == nullptr) {
    RERROR << "msg is null";
    return false;
  }
  double cam_stamp = msg->capture_time.tv_sec + msg->capture_time.tv_usec / 1e6;
  RINFO << name() << ": " << rally::Time::getNow().toString() << " process image msg " << std::to_string(cam_stamp) << " with point stamp " << std::to_string(pts_stamp);

  cv::Mat img(cv::Size(msg->width, msg->height), CV_8UC3, msg->data.get());

  // 4. Get the transformation matrix from camera to lidar
  Eigen::Matrix4d T_ic_il = Eigen::Matrix4d::Identity(), T_il_ic = Eigen::Matrix4d::Identity();
  std::size_t imu_search_idx = 0;
  if (motion_correct_) {
    if (cam_stamp < pts_stamp) {
      if (!imu_image_module_->calIMURot(cam_stamp, pts_stamp, T_ic_il, imu_search_idx)) {
        RWARN << "Image proj CalIMURot Error, cam_stamp: " + std::to_string(cam_stamp) + " pts_stamp: " + std::to_string(pts_stamp);
      }
      T_il_ic = T_ic_il.inverse();
    } else if (cam_stamp > pts_stamp) {
      if (!imu_image_module_->calIMURot(pts_stamp, cam_stamp, T_il_ic, imu_search_idx)) {
        RWARN << "Image proj CalIMURot Error, cam_stamp: " + std::to_string(cam_stamp) + " pts_stamp: " + std::to_string(pts_stamp);
      }
      T_ic_il = T_il_ic.inverse();
    }

    if (imu_search_idx > 20) {
      imu_image_module_->clearData(imu_search_idx - 20);
      imu_search_idx = imu_search_idx - 20;
    }
  }
  // 5. Get the angle and linear velocity
  Eigen::Matrix3d rotation_matrix = T_ic_il.block<3, 3>(0, 0);
  Eigen::AngleAxisd angle_axis(rotation_matrix);
  double angle = angle_axis.angle() * (180.0 / M_PI);
  double angle_velocity = angle / std::abs(pts_stamp - cam_stamp);
  Eigen::Vector3d translation = T_ic_il.block<3, 1>(0, 3);
  double linear_velocity = translation.norm() / std::abs(pts_stamp - cam_stamp);
  Eigen::Matrix4d T_cam_cl = T_cam_imu_ * T_ic_il * T_imu_lidar_;

  // 6. Project the point cloud to image
#define ORI_IMG_RGB
#ifdef ORI_IMG_RGB
    std::vector<cv::Point2f> image_points;
    cv::Mat img_proj = img.clone();
    DrawProjImage(img_proj, cm_pts, T_cam_cl, image_points);
    // labelImage(img_proj, "lt:" + std::to_string(pts_stamp) + " ct:" + std::to_string(cam_stamp) + " w: " + std::to_string(angle_velocity) + " x: " + std::to_string(linear_velocity));
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr rgb_pts(new pcl::PointCloud<pcl::PointXYZRGB>());
    Eigen::Matrix4d pub_trans = Eigen::Matrix4d::Identity();

    // 构造输出的点云消息
    out_msg_ptr_->point_cloud.points_vec.resize(image_points.size());
    for (size_t i = 0; i < image_points.size(); ++i) {
      auto& pt = out_msg_ptr_->point_cloud.points_vec[i];
      const auto& cm_pt = ori_pts->points[i]; // ? 为啥不是cm_pts->points[i];
      Eigen::Vector4d pt_eigen(cm_pt.x, cm_pt.y, cm_pt.z, 1.0);
      Eigen::Vector4d pt_trans = pub_trans * pt_eigen;

      pt.x = pt_trans[0];
      pt.y = pt_trans[1];
      pt.z = pt_trans[2];

      int u = image_points[i].x;
      int v = image_points[i].y;
      cv::Vec3b pixel_value(0, 0, 0);
      if (u >= 0 && u< img.cols && v >= 0 && v < img.rows) {
        pixel_value = img.at<cv::Vec3b>(v, u);
      }
      pt.r = pixel_value[0];
      pt.g = pixel_value[1];
      pt.b = pixel_value[2];
      pt.a = 255;

      pt.intensity = cm_pt.intensity;
      pt.timeStamp = static_cast<unsigned long long>(cm_pt.timestamp * 1e9);
    }
    if (!projection_root_.empty()) {
      std::string file_name =
        projection_root_ + std::to_string(cam_stamp) + ".png";
      RINFO << name() << ": Save Image: " + file_name;
      cv::cvtColor(img_proj, img_proj, cv::COLOR_BGR2RGB);
      cv::imwrite(file_name, img_proj);
    }
#endif
    // 构造输出的图像消息
    out_msg_ptr_->rgb_image.width = msg->width;
    out_msg_ptr_->rgb_image.height = msg->height;
    out_msg_ptr_->rgb_image.header.frame_id = "rs_camera";
    out_msg_ptr_->rgb_image.header.timestamp = msg->capture_time.tv_sec * 1e9 + msg->capture_time.tv_usec * 1e3;
    out_msg_ptr_->rgb_image.header.seq_id = msg->sequence;
    // auto data = reinterpret_cast<unsigned char*>(msg->data);
    auto data = img_proj.data;
    out_msg_ptr_->rgb_image.data_vec = std::vector<unsigned char>(data, data + msg->data_bytes);

    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> duration = end - start;
    RINFO << name() << ": " << rally::Time::getNow().toString()+ " " << "Succ Process Camera, Cost time: " << duration.count() << " ms" << std::endl;
    return true;
}

void PostprocessImpl::CreateColormapLUT(cv::Mat& colormap_lut, float min_z, float max_z) {
#if defined(_WIN32) && false 
static float jet_r[] = {0,0,0,0,0,0,0,0,
                        0,0,0,0,0,0,0,0,
                        0,0,0,0,0,0,0,0,
                        0,0,0,0,0,0,0,0,
                        0,0,0,0,0,0,0,0,
                        0,0,0,0,0,0,0,0,
                        0,0,0,0,0,0,0,0,
                        0,0,0,0,0,0,0,0,
                        0,0,0,0,0,0,0,0,
                        0,0,0,0,0,0,0,0,
                        0,0,0,0,0,0,0,0,
                        0,0,0,0,0,0,0,0,
                        0.00588235294117645f,0.02156862745098032f,0.03725490196078418f,0.05294117647058827f,0.06862745098039214f,0.084313725490196f,0.1000000000000001f,0.115686274509804f,
                        0.1313725490196078f,0.1470588235294117f,0.1627450980392156f,0.1784313725490196f,0.1941176470588235f,0.2098039215686274f,0.2254901960784315f,0.2411764705882353f,
                        0.2568627450980392f,0.2725490196078431f,0.2882352941176469f,0.303921568627451f,0.3196078431372549f,0.3352941176470587f,0.3509803921568628f,0.3666666666666667f,
                        0.3823529411764706f,0.3980392156862744f,0.4137254901960783f,0.4294117647058824f,0.4450980392156862f,0.4607843137254901f,0.4764705882352942f,0.4921568627450981f,
                        0.5078431372549019f,0.5235294117647058f,0.5392156862745097f,0.5549019607843135f,0.5705882352941174f,0.5862745098039217f,0.6019607843137256f,0.6176470588235294f,
                        0.6333333333333333f,0.6490196078431372f,0.664705882352941f,0.6803921568627449f,0.6960784313725492f,0.7117647058823531f,0.7274509803921569f,0.7431372549019608f,
                        0.7588235294117647f,0.7745098039215685f,0.7901960784313724f,0.8058823529411763f,0.8215686274509801f,0.8372549019607844f,0.8529411764705883f,0.8686274509803922f,
                        0.884313725490196f,0.8999999999999999f,0.9156862745098038f,0.9313725490196076f,0.947058823529412f,0.9627450980392158f,0.9784313725490197f,0.9941176470588236f,
                        1,1,1,1,1,1,1,1,
                        1,1,1,1,1,1,1,1,
                        1,1,1,1,1,1,1,1,
                        1,1,1,1,1,1,1,1,
                        1,1,1,1,1,1,1,1,
                        1,1,1,1,1,1,1,1,
                        1,1,1,1,1,1,1,1,
                        1,1,1,1,1,1,1,1,
                        0.9862745098039216f,0.9705882352941178f,0.9549019607843139f,0.93921568627451f,0.9235294117647062f,0.9078431372549018f,0.892156862745098f,0.8764705882352941f,
                        0.8607843137254902f,0.8450980392156864f,0.8294117647058825f,0.8137254901960786f,0.7980392156862743f,0.7823529411764705f,0.7666666666666666f,0.7509803921568627f,
                        0.7352941176470589f,0.719607843137255f,0.7039215686274511f,0.6882352941176473f,0.6725490196078434f,0.6568627450980391f,0.6411764705882352f,0.6254901960784314f,
                        0.6098039215686275f,0.5941176470588236f,0.5784313725490198f,0.5627450980392159f,0.5470588235294116f,0.5313725490196077f,0.5156862745098039f,0.5f};

static float jet_g[] = {0,0,0,0,0,0,0,0,
                        0,0,0,0,0,0,0,0,
                        0,0,0,0,0,0,0,0,
                        0,0,0,0,0,0,0,0,
                        0.001960784313725483f,0.01764705882352935f,0.03333333333333333f,0.0490196078431373f,0.06470588235294117f,0.08039215686274503f,0.09607843137254901f,0.111764705882353f,
                        0.1274509803921569f,0.1431372549019607f,0.1588235294117647f,0.1745098039215687f,0.1901960784313725f,0.2058823529411764f,0.2215686274509804f,0.2372549019607844f,
                        0.2529411764705882f,0.2686274509803921f,0.2843137254901961f,0.3f,0.3156862745098039f,0.3313725490196078f,0.3470588235294118f,0.3627450980392157f,
                        0.3784313725490196f,0.3941176470588235f,0.4098039215686274f,0.4254901960784314f,0.4411764705882353f,0.4568627450980391f,0.4725490196078431f,0.4882352941176471f,
                        0.503921568627451f,0.5196078431372548f,0.5352941176470587f,0.5509803921568628f,0.5666666666666667f,0.5823529411764705f,0.5980392156862746f,0.6137254901960785f,
                        0.6294117647058823f,0.6450980392156862f,0.6607843137254901f,0.6764705882352942f,0.692156862745098f,0.7078431372549019f,0.723529411764706f,0.7392156862745098f,
                        0.7549019607843137f,0.7705882352941176f,0.7862745098039214f,0.8019607843137255f,0.8176470588235294f,0.8333333333333333f,0.8490196078431373f,0.8647058823529412f,
                        0.8803921568627451f,0.8960784313725489f,0.9117647058823528f,0.9274509803921569f,0.9431372549019608f,0.9588235294117646f,0.9745098039215687f,0.9901960784313726f,
                        1,1,1,1,1,1,1,1,
                        1,1,1,1,1,1,1,1,
                        1,1,1,1,1,1,1,1,
                        1,1,1,1,1,1,1,1,
                        1,1,1,1,1,1,1,1,
                        1,1,1,1,1,1,1,1,
                        1,1,1,1,1,1,1,1,
                        1,1,1,1,1,1,1,1,
                        0.9901960784313726f,0.9745098039215687f,0.9588235294117649f,0.943137254901961f,0.9274509803921571f,0.9117647058823528f,0.8960784313725489f,0.8803921568627451f,
                        0.8647058823529412f,0.8490196078431373f,0.8333333333333335f,0.8176470588235296f,0.8019607843137253f,0.7862745098039214f,0.7705882352941176f,0.7549019607843137f,
                        0.7392156862745098f,0.723529411764706f,0.7078431372549021f,0.6921568627450982f,0.6764705882352944f,0.6607843137254901f,0.6450980392156862f,0.6294117647058823f,
                        0.6137254901960785f,0.5980392156862746f,0.5823529411764707f,0.5666666666666669f,0.5509803921568626f,0.5352941176470587f,0.5196078431372548f,0.503921568627451f,
                        0.4882352941176471f,0.4725490196078432f,0.4568627450980394f,0.4411764705882355f,0.4254901960784316f,0.4098039215686273f,0.3941176470588235f,0.3784313725490196f,
                        0.3627450980392157f,0.3470588235294119f,0.331372549019608f,0.3156862745098041f,0.2999999999999998f,0.284313725490196f,0.2686274509803921f,0.2529411764705882f,
                        0.2372549019607844f,0.2215686274509805f,0.2058823529411766f,0.1901960784313728f,0.1745098039215689f,0.1588235294117646f,0.1431372549019607f,0.1274509803921569f,
                        0.111764705882353f,0.09607843137254912f,0.08039215686274526f,0.06470588235294139f,0.04901960784313708f,0.03333333333333321f,0.01764705882352935f,0.001960784313725483f,
                        0,0,0,0,0,0,0,0,
                        0,0,0,0,0,0,0,0,
                        0,0,0,0,0,0,0,0,
                        0,0,0,0,0,0,0,0};

static float jet_b[] = {0.5f,0.5156862745098039f,0.5313725490196078f,0.5470588235294118f,0.5627450980392157f,0.5784313725490196f,0.5941176470588235f,0.6098039215686275f,
                        0.6254901960784314f,0.6411764705882352f,0.6568627450980392f,0.6725490196078432f,0.6882352941176471f,0.7039215686274509f,0.7196078431372549f,0.7352941176470589f,
                        0.7509803921568627f,0.7666666666666666f,0.7823529411764706f,0.7980392156862746f,0.8137254901960784f,0.8294117647058823f,0.8450980392156863f,0.8607843137254902f,
                        0.8764705882352941f,0.892156862745098f,0.907843137254902f,0.9235294117647059f,0.9392156862745098f,0.9549019607843137f,0.9705882352941176f,0.9862745098039216f,
                        1,1,1,1,1,1,1,1,
                        1,1,1,1,1,1,1,1,
                        1,1,1,1,1,1,1,1,
                        1,1,1,1,1,1,1,1,
                        1,1,1,1,1,1,1,1,
                        1,1,1,1,1,1,1,1,
                        1,1,1,1,1,1,1,1,
                        1,1,1,1,1,1,1,1,
                        0.9941176470588236f,0.9784313725490197f,0.9627450980392158f,0.9470588235294117f,0.9313725490196079f,0.915686274509804f,0.8999999999999999f,0.884313725490196f,
                        0.8686274509803922f,0.8529411764705883f,0.8372549019607844f,0.8215686274509804f,0.8058823529411765f,0.7901960784313726f,0.7745098039215685f,0.7588235294117647f,
                        0.7431372549019608f,0.7274509803921569f,0.7117647058823531f,0.696078431372549f,0.6803921568627451f,0.6647058823529413f,0.6490196078431372f,0.6333333333333333f,
                        0.6176470588235294f,0.6019607843137256f,0.5862745098039217f,0.5705882352941176f,0.5549019607843138f,0.5392156862745099f,0.5235294117647058f,0.5078431372549019f,
                        0.4921568627450981f,0.4764705882352942f,0.4607843137254903f,0.4450980392156865f,0.4294117647058826f,0.4137254901960783f,0.3980392156862744f,0.3823529411764706f,
                        0.3666666666666667f,0.3509803921568628f,0.335294117647059f,0.3196078431372551f,0.3039215686274508f,0.2882352941176469f,0.2725490196078431f,0.2568627450980392f,
                        0.2411764705882353f,0.2254901960784315f,0.2098039215686276f,0.1941176470588237f,0.1784313725490199f,0.1627450980392156f,0.1470588235294117f,0.1313725490196078f,
                        0.115686274509804f,0.1000000000000001f,0.08431372549019622f,0.06862745098039236f,0.05294117647058805f,0.03725490196078418f,0.02156862745098032f,0.00588235294117645f,
                        0,0,0,0,0,0,0,0,
                        0,0,0,0,0,0,0,0,
                        0,0,0,0,0,0,0,0,
                        0,0,0,0,0,0,0,0,
                        0,0,0,0,0,0,0,0,
                        0,0,0,0,0,0,0,0,
                        0,0,0,0,0,0,0,0,
                        0,0,0,0,0,0,0,0,
                        0,0,0,0,0,0,0,0,
                        0,0,0,0,0,0,0,0,
                        0,0,0,0,0,0,0,0,
                        0,0,0,0,0,0,0,0};
    int lut_size = 256;
    colormap_lut = cv::Mat(lut_size, 1, CV_8UC3);
    for(int i = 0; i < lut_size; ++i)
    {
      colormap_lut.at<cv::Vec3b>(i, 0) = cv::Vec3b(jet_r[i] * 255, jet_g[i] * 255, jet_b[i] * 255);
    }
#else
  int lut_size = 256;  // You can adjust the LUT size for more granularity
  cv::Mat colormap_lut_gray = cv::Mat(lut_size, 1, CV_8UC1);
  colormap_lut = cv::Mat(lut_size, 1, CV_8UC3);

  // Fill the LUT with values from 0 to 255
  for (int i = 0; i < lut_size; ++i)
  {
    colormap_lut_gray.at<uchar>(i, 0) = static_cast<uchar>(i);
  }

  // Apply the colormap to the LUT
  cv::applyColorMap(colormap_lut_gray, colormap_lut, cv::COLORMAP_JET);
#endif // _WIN32
}

int PostprocessImpl::GetColormapIndex(float depth, float min_z, float max_z, int lut_size) {
  depth = depth > max_z ? max_z : (depth<min_z ? min_z : depth);
  float normalized_depth = (depth - min_z) / (max_z - min_z);
  return static_cast<int>(normalized_depth * (lut_size - 1));
}

void PostprocessImpl::DrawProjImage(cv::Mat& _img, const pcl::PointCloud<PointXYZIRT>::Ptr& _cm_pts,
                                    const Eigen::Matrix4d& _transform, std::vector<cv::Point2f>& _image_points) {
#if defined(_WIN32) && false 
    cv::Mat transform(4, 4, CV_64FC1); 
    for(size_t i = 0; i < 4; i++){
      for(size_t j = 0; j < 4; j++){
        transform.at<double>(i, j) = _transform(i, j); 
      }
    }
    PinholeModel::Ptr pinhole_model_ptr_(new PinholeModel(transform, camera_intrisic_, distortion_coeffs_, _img.size())); 

    std::vector<Eigen::Vector3d> pts_3d; 
    pts_3d.resize(_cm_pts->points.size()); 
    for(size_t i = 0; i < _cm_pts->points.size(); ++i){
      const auto& point = _cm_pts->points[i]; 
      pts_3d[i] = Eigen::Vector3d(point.x, point.y, point.z); 
    }

    std::vector<cv::Point2i> pts_2d; 
    bool isSuccessed = pinhole_model_ptr_->ProjectPts(pts_3d, pts_2d, true); 
    if(isSuccessed == false){
      return; 
    }
    _image_points.resize(pts_2d.size()); 
    for(size_t i = 0; i < pts_2d.size(); ++i){
      _image_points[i] = cv::Point2f(pts_2d[i].x, pts_2d[i].y); 
    }
    for (size_t i = 0; i < _image_points.size(); ++i) {
      int u = _image_points[i].x;
      int v = _image_points[i].y;
      if (u<0 || u >= _img.cols || v<0 || v >= _img.rows) {
        continue;
      }
      float depth = pts_3d[i].z();
      int colormap_index = GetColormapIndex(depth, min_z_, max_z_, lut_size_);
      cv::Vec3b color = colormap_lut_.at<cv::Vec3b>(colormap_index, 0);
      cv::Scalar point_color(color[0], color[1], color[2]);
      cv::circle(_img, _image_points[i], 3, point_color, -1);
    }
#else   
  std::vector<cv::Point3f> transform_points;
  for (size_t i = 0; i< _cm_pts->points.size(); ++i) {
    Eigen::Vector4d pt(_cm_pts->points[i].x, _cm_pts->points[i].y, _cm_pts->points[i].z, 1.0);
    Eigen::Vector4d pt_trans = _transform * pt;
    transform_points.push_back(cv::Point3f(pt_trans[0], pt_trans[1], pt_trans[2]));
  }

  cv::projectPoints(transform_points, cv::Mat::zeros(3, 1, CV_64F), cv::Mat::zeros(3, 1, CV_64F),
                      camera_intrisic_, distortion_coeffs_, _image_points);

  for (size_t i = 0; i < _image_points.size(); ++i) {
    int u = _image_points[i].x;
    int v = _image_points[i].y;
    if (u<0 || u >= _img.cols || v<0 || v >= _img.rows) {
      continue;
    }
    float depth = transform_points[i].z;
    int colormap_index = GetColormapIndex(depth, min_z_, max_z_, lut_size_);
    cv::Vec3b color = colormap_lut_.at<cv::Vec3b>(colormap_index, 0);
    cv::Scalar point_color(color[0], color[1], color[2]);
    cv::circle(_img, _image_points[i], 3, point_color, -1);
  }
#endif 
}

}  // namespace postprocess
}  // namespace robosense