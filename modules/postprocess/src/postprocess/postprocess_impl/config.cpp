#include "hyper_vision/postprocess/postprocess_impl/config.h"
#include <Eigen/Core>
#include <Eigen/Geometry>
namespace robosense {
namespace postprocess {

MotionDriverConfig parse_config(const YAML::Node& cfg) {
  MotionDriverConfig res_cfg;
  YAML::Node lidar_cfg, cam_cfg;
  YAML::Node meta_calib, sensor_cfg;
  rally::yamlSubNode(cfg, "meta_calib", meta_calib);
  rally::yamlSubNode(meta_calib, "Sensor", sensor_cfg);
  rally::yamlSubNode(sensor_cfg, "Lidar", lidar_cfg);
  rally::yamlSubNode(sensor_cfg, "Camera", cam_cfg);

  {
    YAML::Node lidar2imu_cfg;
    rally::yamlSubNode(lidar_cfg, "extrinsic", lidar2imu_cfg);
    rally::yamlRead(lidar2imu_cfg["quaternion"], "x", res_cfg.ldiar_config.T_Lidar2IMU.qx);
    rally::yamlRead(lidar2imu_cfg["quaternion"], "y", res_cfg.ldiar_config.T_Lidar2IMU.qy);
    rally::yamlRead(lidar2imu_cfg["quaternion"], "z", res_cfg.ldiar_config.T_Lidar2IMU.qz);
    rally::yamlRead(lidar2imu_cfg["quaternion"], "w", res_cfg.ldiar_config.T_Lidar2IMU.qw);
    rally::yamlRead(lidar2imu_cfg["translation"], "x",  res_cfg.ldiar_config.T_Lidar2IMU.x);
    rally::yamlRead(lidar2imu_cfg["translation"], "y",  res_cfg.ldiar_config.T_Lidar2IMU.y);
    rally::yamlRead(lidar2imu_cfg["translation"], "z",  res_cfg.ldiar_config.T_Lidar2IMU.z);

    YAML::Node cam2imu_cfg;
    TransformXYZQuat cam2imu;
    rally::yamlSubNode(cam_cfg, "extrinsic", cam2imu_cfg);
    rally::yamlRead(cam2imu_cfg["quaternion"], "x", cam2imu.qx);
    rally::yamlRead(cam2imu_cfg["quaternion"], "y", cam2imu.qy);
    rally::yamlRead(cam2imu_cfg["quaternion"], "z", cam2imu.qz);
    rally::yamlRead(cam2imu_cfg["quaternion"], "w", cam2imu.qw);
    rally::yamlRead(cam2imu_cfg["translation"], "x",  cam2imu.x);
    rally::yamlRead(cam2imu_cfg["translation"], "y",  cam2imu.y);
    rally::yamlRead(cam2imu_cfg["translation"], "z",  cam2imu.z);

    Eigen::Matrix4d T_liadr2imu = Eigen::Matrix4d::Identity();
    Eigen::Matrix4d T_cam2imu = Eigen::Matrix4d::Identity();
    Eigen::Matrix4d T_lidar2cam = Eigen::Matrix4d::Identity();
    Eigen::Quaterniond q_lidar2imu(res_cfg.ldiar_config.T_Lidar2IMU.qw, res_cfg.ldiar_config.T_Lidar2IMU.qx, res_cfg.ldiar_config.T_Lidar2IMU.qy, res_cfg.ldiar_config.T_Lidar2IMU.qz);
    // RDEBUG << name() << ": lidar2imu: w " << q_lidar2imu.w() << " x "  << q_lidar2imu.x() << " y "  << q_lidar2imu.y() << " z "  << q_lidar2imu.z() << std::endl;
    T_liadr2imu.block<3, 3>(0, 0) = q_lidar2imu.toRotationMatrix();
    T_liadr2imu.block<3, 1>(0, 3) = Eigen::Vector3d(res_cfg.ldiar_config.T_Lidar2IMU.x, res_cfg.ldiar_config.T_Lidar2IMU.y, res_cfg.ldiar_config.T_Lidar2IMU.z);
    // std::cout << "T lidar2imu:\n" << T_liadr2imu << std::endl;
    Eigen::Quaterniond q_cam2imu(cam2imu.qw, cam2imu.qx, cam2imu.qy, cam2imu.qz);

    T_cam2imu.block<3, 3>(0, 0) = q_cam2imu.toRotationMatrix();
    T_cam2imu.block<3, 1>(0, 3) = Eigen::Vector3d(cam2imu.x, cam2imu.y, cam2imu.z);
    // std::cout << "T cam2imu:\n" << T_cam2imu << std::endl;
    T_lidar2cam = T_cam2imu.inverse() * T_liadr2imu;
    // std::cout << "T lidar2cam:\n" << T_lidar2cam << std::endl;
    // std::cout << "T cam2lidar:\n" << T_lidar2cam.inverse() << std::endl;
    Eigen::Quaterniond quant(T_lidar2cam.block<3, 3>(0, 0));
    Eigen::Vector3d trans(T_lidar2cam.block<3, 1>(0, 3));
    res_cfg.ldiar_config.T_Lidar2Cam.qw = quant.w();
    res_cfg.ldiar_config.T_Lidar2Cam.qx = quant.x();
    res_cfg.ldiar_config.T_Lidar2Cam.qy = quant.y();
    res_cfg.ldiar_config.T_Lidar2Cam.qz = quant.z();
    res_cfg.ldiar_config.T_Lidar2Cam.x  = trans.x();
    res_cfg.ldiar_config.T_Lidar2Cam.y  = trans.y();
    res_cfg.ldiar_config.T_Lidar2Cam.z  = trans.z();
  }

  {
    std::vector<double> distortion_coeffs;
    for (size_t i = 0; i < cam_cfg["intrinsic"]["dist_coeff"].size(); ++i) {
      distortion_coeffs.push_back(cam_cfg["intrinsic"]["dist_coeff"][i].as<double>());
    }
    res_cfg.camera_config.intrinsics.distortion_coeffs = distortion_coeffs;

    std::vector<double> intrinsic_matrix;
    if (cam_cfg["intrinsic"] && cam_cfg["intrinsic"]["int_matrix"])
    {
      for (size_t i = 0; i < cam_cfg["intrinsic"]["int_matrix"].size(); ++i)
      {
        intrinsic_matrix.push_back(cam_cfg["intrinsic"]["int_matrix"][i].as<double>());
      }
    }
    res_cfg.camera_config.intrinsics.camera_matrix = intrinsic_matrix;
  }
  // std::cout << "camera_config.intrinsics.distortion_coeffs:\n";
  // for (auto& i : res_cfg.camera_config.intrinsics.distortion_coeffs) {
  //   std::cout << i << " ";
  // }
  // std::cout << "\ncamera_config.intrinsics.camera_matrix:\n";
  // for (auto& i : res_cfg.camera_config.intrinsics.camera_matrix) {
  //   std::cout << i << " ";
  // }
  {
    rally::yamlRead(cfg, "MOTION_CORRECT",                res_cfg.motion_config.motion_correct                );
    rally::yamlRead(cfg, "USING_IMU_LINEAR_ACCELERATION", res_cfg.motion_config.using_imu_linear_acceleration );
    rally::yamlRead(cfg, "USING_ODOM_LINEAR_VELOCITY",    res_cfg.motion_config.using_odom_linear_velocity    );
    rally::yamlRead(cfg, "FRAME_TAIL",                    res_cfg.motion_config.frame_tail                    );
    rally::yamlRead(cfg, "NUM_DRIFT",                     res_cfg.motion_config.num_drift                     );
    rally::yamlRead(cfg, "PROJECTION_ROOT",               res_cfg.motion_config.projection_root               );
  }
  return res_cfg;
}
}  // namespace postprocess
}  // namespace robosense