/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "hyper_vision/front_end/front_end.h"

namespace robosense::front_end
{
  int FrontEnd::Init(const YAML::Node& cfg_node)
  {
    cfg_node_ = cfg_node;

    ResetPostProcess();
    RINFO << name() << "Postprocess algorithm module initialization successful.";

    ResetSlam();
    RINFO << name() << "Slam algorithm module initialization successful.";

    return 0;
  }

  int FrontEnd::Start()
  {
    post_algorithm_->Start();
    slam_algorithm_->Start();

    return 0;
  }

  int FrontEnd::Stop()
  {
    post_algorithm_->Stop();
    slam_algorithm_->Stop();

    return 0;
  }

  int FrontEnd::Reset()
  {
    ResetPostProcess();
    ResetSlam();
    ResetImageDepth();
    return 0;
  }

  int FrontEnd::ResetPostProcess()
  {
    post_algorithm_.reset(new postprocess::MultiSensorPostprocess());
    post_algorithm_->Init(cfg_node_);
    post_algorithm_->Start();

    return 0;
  }

  int FrontEnd::ResetSlam()
  {
    slam_algorithm_.reset(new slam::Slam());
    slam_algorithm_->Init(cfg_node_);
    slam_algorithm_->Start();

    return 0;
  }

  int FrontEnd::ResetImageDepth()
  {
    return 0;
  }

  int FrontEnd::EnableSlamRelocalization(bool is_enable)
  {
    if (slam_algorithm_)
      slam_algorithm_->SetRelocalizationFlag(is_enable);

    return 0;
  }

  int FrontEnd::EnableSlamOfflineOpt(bool isEnable)
  {
    return 0;
  }

  void FrontEnd::AddData(const shared_ptr<robosense::common::MotionFrame>& msg_ptr)
  {
    if (post_algorithm_)
      post_algorithm_->AddData(msg_ptr);

    if (slam_algorithm_)
      slam_algorithm_->AddData(msg_ptr);
  }

  void FrontEnd::AddData(const shared_ptr<robosense::common::DepthFrame>& msg_ptr)
  {
    if (post_algorithm_)
      post_algorithm_->AddData(msg_ptr);

    if (slam_algorithm_)
      slam_algorithm_->AddData(msg_ptr);
  }

  void FrontEnd::AddDataImage(const shared_ptr<robosense::common::ImageFrame>& msg_ptr)
  {
    if (post_algorithm_)
      post_algorithm_->AddData(msg_ptr);

    if (slam_algorithm_)
      slam_algorithm_->AddData(msg_ptr);
  }

  void FrontEnd::AddDataImageLeft(const std::shared_ptr<robosense::common::ImageFrame>& msg_ptr)
  {

  }

  void FrontEnd::AddDataImageRight(const std::shared_ptr<robosense::common::ImageFrame>& msg_ptr)
  {

  }

  void FrontEnd::SetCallback(
      const std::function<void(const postprocess::PostprocessOutputMsg::Ptr&)>& callback)
  {
    post_algorithm_->SetCallback(callback);
  }

  void FrontEnd::SetCallback(
      const function<void(const slam::SlamOutputMsg::Ptr&)>& callback)
  {
    slam_algorithm_->SetCallback(callback);
  }

  void FrontEnd::SetCallback(
      const std::function<void(const imagedepth::ImageDepthOutputMsg::Ptr&)>& callback)
  {

  }
}  // namespace robosense::front_end
