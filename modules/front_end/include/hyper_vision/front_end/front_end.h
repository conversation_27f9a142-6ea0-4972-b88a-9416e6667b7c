/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef HYPER_VISION_FRONT_END_FRONT_END_H_
#define HYPER_VISION_FRONT_END_FRONT_END_H_

// Left图像+Right图像
#include "hyper_vision/imagedepth/imagedepth.h"
// 点云投影+着色
#include "hyper_vision/postprocess/multi_sensor_postprocess.h"
// SLAM
#include "slam/slam.h"


namespace robosense::front_end
{

  class FrontEnd
  {
  public:
    using Ptr = std::shared_ptr<FrontEnd>;
    using ConstPtr = std::shared_ptr<const FrontEnd>;

    using FRONT_END_POSTPROCESS_OUTPUT_CALLBACK =
        std::function<void(const robosense::postprocess::PostprocessOutputMsg::Ptr&)>;
    using FRONT_END_SLAM_OUTPUT_CALLBACK =
        std::function<void(const robosense::slam::SlamOutputMsg::Ptr&)>;
    using FRONT_END_IMAGE_DEPTH_OUTPUT_CALLBACK =
        std::function<void(const robosense::imagedepth::ImageDepthOutputMsg::Ptr&)>;

  public:
    FrontEnd() = default;

    ~FrontEnd() { Stop(); }

    // 初始化
    int Init(const YAML::Node& cfg_node);

    // 开始功能
    int Start();

    // 结束功能
    int Stop();

    // 重置所有模块
    int Reset();

    // 重置后处理模块
    int ResetPostProcess();

    // 重置Slam模块
    int ResetSlam();

    // 重置图像深度图模块
    int ResetImageDepth();

    // 设置Slam模块是否开启Re-localization功能
    int EnableSlamRelocalization(bool is_enable);

    // 设置Slam模块是否进行离线优化
    int EnableSlamOfflineOpt(bool isEnable);

    // 输入Imu数据
    void AddData(const std::shared_ptr<robosense::common::MotionFrame>& msg_ptr);

    // 输入点云数据
    void AddData(const std::shared_ptr<robosense::common::DepthFrame>& msg_ptr);

    // 输入AC1 图像数据
    void AddDataImage(const std::shared_ptr<robosense::common::ImageFrame>& msg_ptr);

    // 输入AC2: Left 图像数据
    void AddDataImageLeft(const std::shared_ptr<robosense::common::ImageFrame>& msg_ptr);

    // 输入AC2：Right 图像数据
    void AddDataImageRight(const std::shared_ptr<robosense::common::ImageFrame>& msg_ptr);

    // 设置后处理结果回调函数
    void SetCallback(const std::function<void(const postprocess::PostprocessOutputMsg::Ptr&)>& callback);

    // 设置Slam结果回调函数
    void SetCallback(const std::function<void(const slam::SlamOutputMsg::Ptr&)>& callback);

    // 设置图像深度图回调函数
    void SetCallback(const std::function<void(const imagedepth::ImageDepthOutputMsg::Ptr&)>& callback);

  private:
    static std::string name() { return "FrontEnd: "; }

  private:
    YAML::Node cfg_node_;

    // postprocess 算法模块的实例
    postprocess::MultiSensorPostprocess::Ptr post_algorithm_;

    // slam 算法模块的实例
    slam::Slam::Ptr slam_algorithm_;

    // depth image 算法模块的实例 TODO
  };

} // namespace robosense::front_end


#endif // HYPER_VISION_FRONT_END_FRONT_END_H_
