/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "rally/utils/global/configure_manager.h"

#include <filesystem>

#include "rally/utils/file.h"

namespace rally {

void ConfigureManager::setConfigFile(const std::string& config_file) {
    // 检查文件路径长度
    if (config_file.size() <= 4) {
        RWARN << "rally_utils: init failed with config_file: " << config_file;
        return;
    }

    // 检查文件格式是否为 YAML
    std::filesystem::path config_path(config_file);
    std::string file_extension = config_path.extension().string();
    if (file_extension != ".yaml" && file_extension != ".yml") {
        RWARN << "rally_utils: init with only yaml format file! init failed! " << config_file;
        return;
    }

    // 获取文件所在目录
    std::filesystem::path root_dir = config_path.parent_path();

    cfg_node_ = YAML::Null;
    RENSURE(loadFile(config_file, cfg_node_));
    cfg_path_ = root_dir.string();
    cfg_node_["root_dir"] = cfg_path_;
    catYAML(cfg_node_);
}

bool ConfigureManager::catYAML(YAML::Node &node) {
    if (node.Type() != YAML::NodeType::Map && node.Type() != YAML::NodeType::Sequence) {
        return true;
    }
    if (node.Type() == YAML::NodeType::Map) {
        std::string include_str;
        if (yamlRead(node, "include", include_str)) {
            auto path = getAbsolutePath(cfg_path_, include_str);
            YAML::Node sub_node;
            if (loadFile(path, sub_node)) {
                if (!catYAML(sub_node)) {
                    return false;
                }
                node = sub_node;
            }
        } else {
            for (auto it = node.begin(); it != node.end(); ++it) {
                YAML::Node &sub_node = it->second;
                if (!catYAML(sub_node)) {
                    return false;
                }
            }
        }
    } else {  // process sequence
        for (auto it = node.begin(); it != node.end(); ++it) {
            YAML::Node sub_node = *it;
            catYAML(sub_node);
        }
    }
    return true;
}

}  // namespace rally
