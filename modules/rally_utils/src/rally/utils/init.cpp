/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "cyber/init.h"
#include "rally/utils/file.h"
#include "rally/utils/global/configure_manager.h"
#include "rally/utils/init.h"
#include "rally/utils/logger/logger.h"
#include <filesystem>

namespace rally {

void init(const std::string &config_file,
          const std::string &app_runtime_dir_path) {
  if (config_file.size() <= 4) {
    RWARN << "rally_utils: init failed with config_file: " << config_file;
    return;
  }

  std::string::size_type format_begin = config_file.find_last_of('.') + 1;
  std::string file_format =
      config_file.substr(format_begin, config_file.length() - format_begin);
  if (file_format != "yaml") {
    RWARN << "rally_utild: init with only yaml format file! init failed! "
          << config_file;
    return;
  }

  ConfigureManager::getInstance().setConfigFile(config_file);
  YAML::Node cfg_node = ConfigureManager::getInstance().getCfgNode();
  YAML::Node general_node;
  yamlSubNode(cfg_node, "general", general_node);

  { // logger
    YAML::Node logger_node;
    yamlSubNode(general_node, "logger", logger_node);
    std::string log_level = "INFO";
    yamlRead(logger_node, "level", log_level);
    std::filesystem::path log_dir;
    if (!app_runtime_dir_path.empty()) {
      log_dir = std::filesystem::path(app_runtime_dir_path) / "log";
      RINFO << "create log directory path from app_runtime_dir_path = "
            << app_runtime_dir_path;
    } else {
      log_dir = std::filesystem::current_path() / "log";
      RINFO << "create log directory path from current_path = "
            << std::filesystem::current_path().string();
    }
    rally::ensureDirectory(log_dir.string());
    FLAGS_log_dir = log_dir.string();
    // 可选：控制日志输出到 stderr（默认 0，即不输出到终端）
    FLAGS_logtostderr = 0;
    FLAGS_alsologtostderr = 1; // 设为 1 则同时输出到终端和文件
    FLAGS_minloglevel = 0;     // 0:INFO, 1:WARNING, 2:ERROR, 3:FATAL
    if (log_level == "INFO") {
      FLAGS_minloglevel = 0; // 0:INFO, 1:WARNING, 2:ERROR, 3:FATAL
    } else if (log_level == "WARN") {
      FLAGS_minloglevel = 1; // 0:INFO, 1:WARNING, 2:ERROR, 3:FATAL
    } else if (log_level == "ERROR") {
      FLAGS_minloglevel = 2; // 0:INFO, 1:WARNING, 2:ERROR, 3:FATAL
    }
    apollo::cyber::Init("ACViwer_APP");
  }
}

} // namespace rally
