/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include <filesystem>
#include "rally/utils/details/throw.h"
#include "rally/utils/file.h"

namespace rally {

bool ensureDirectory(const std::string& path) {
  std::filesystem::path dir(path);
  std::error_code ec;

  // 如果路径不存在，则创建目录（包括所有父目录）
  if (!std::filesystem::exists(dir, ec)) {
    if (!std::filesystem::create_directories(dir, ec)) {
      RERROR << "ensureDirectory: create directory " << path << " failed!";
      return false;
    }
  } else if (!std::filesystem::is_directory(dir, ec)) {
    RWARN << "ensureDirectory: " << path << " is not a directory!";
    return false;
  }
  return true;
}

std::string getAbsolutePath(const std::string &prefix, const std::string &relative_path) {
  if (relative_path.empty()) {
    return prefix;
  }

  // 将路径转换为 fs::path 对象以处理平台差异
  std::filesystem::path prefix_path(prefix);
  std::filesystem::path rel_path(relative_path);

  // 如果前缀为空 或 相对路径是绝对路径，直接返回相对路径
  if (prefix.empty() || rel_path.is_absolute()) {
      return rel_path.string();
  }

  // 拼接路径（自动处理分隔符）
  std::filesystem::path combined_path = prefix_path / rel_path;

  // 返回规范化后的路径字符串（替换为平台分隔符）
  return combined_path.lexically_normal().string();
}

bool pathExists(const std::string &path) {
  return std::filesystem::exists(path);
}

}  // namespace rally
