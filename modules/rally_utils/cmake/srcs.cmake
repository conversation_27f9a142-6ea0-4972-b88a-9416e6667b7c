#========================
# libs
#========================

set(CMAKE_POSITION_INDEPENDENT_CODE ON)

set(CUR_SRCS "")
LIST(APPEND CUR_INCLUDES "include")

set(CUR_SUB_DIR "")
LIST(APPEND CUR_SUB_DIR include)
LIST(APPEND CUR_SUB_DIR src)

foreach (dir ${CUR_SUB_DIR})
    file(GLOB_RECURSE tmp_srcs ${dir}/*.cpp ${dir}/*.h)
    list(APPEND CUR_SRCS ${tmp_srcs})
endforeach ()

add_library(${CUR_LIB} STATIC
        ${CUR_SRCS}
        )
target_include_directories(${CUR_LIB}
        PUBLIC
        ${CUR_INCLUDES}
        ${PCL_INCLUDE_DIRS}
        ${EIGEN3_INCLUDE_DIR}
        )
target_compile_definitions(${CUR_LIB} PRIVATE MODULE_NAME="rally_utils")

if(APPLE)
target_include_directories(${CUR_LIB}
        PUBLIC
        ${YAML_CPP_INCLUDE_DIR}
)
target_link_directories(${CUR_LIB}
        PUBLIC
        ${YAML_CPP_LIBRARY_DIR}
        ${PCL_LIBRARIES}
        ${OpenCV_LIBS}
        )
endif()

target_link_libraries(${CUR_LIB}
        PUBLIC
        cyber
        ${YAML_CPP_LIBRARIES}
        ${PCL_LIBRARIES}
        ${OpenCV_LIBS}
        )