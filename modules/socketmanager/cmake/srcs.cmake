#========================
# libs
#========================

set(CMAKE_POSITION_INDEPENDENT_CODE ON)

set(CUR_SRCS "")
set(CUR_INCLUDES "include")

set(CUR_SUB_DIR "")
LIST(APPEND CUR_SUB_DIR include)
LIST(APPEND CUR_SUB_DIR src)
LIST(APPEND CUR_SUB_DIR websocketpp)
LIST(APPEND CUR_SUB_DIR proto)

# protobuf 
if(WIN32)
    set(PROTOC_EXE "Q:\\Downloads\\vcpkg-master\\installed\\x64-windows\\tools\\protobuf\\protoc.exe")
    message("Windows System PROTOC_EXE = "${PROTOC_EXE})
    execute_process(COMMAND ${PROTOC_EXE} -I=${CMAKE_CURRENT_SOURCE_DIR}/proto --cpp_out=${CMAKE_CURRENT_SOURCE_DIR}/proto ${CMAKE_CURRENT_SOURCE_DIR}/proto/proto_compress.proto)
    execute_process(COMMAND ${PROTOC_EXE} -I=${CMAKE_CURRENT_SOURCE_DIR}/proto --cpp_out=${CMAKE_CURRENT_SOURCE_DIR}/proto ${CMAKE_CURRENT_SOURCE_DIR}/proto/hmi.proto)
    execute_process(COMMAND ${PROTOC_EXE} -I=${CMAKE_CURRENT_SOURCE_DIR}/proto --cpp_out=${CMAKE_CURRENT_SOURCE_DIR}/proto ${CMAKE_CURRENT_SOURCE_DIR}/proto/config.proto)
    execute_process(COMMAND ${PROTOC_EXE} -I=${CMAKE_CURRENT_SOURCE_DIR}/proto --cpp_out=${CMAKE_CURRENT_SOURCE_DIR}/proto ${CMAKE_CURRENT_SOURCE_DIR}/proto/acviewer.proto)
    execute_process(COMMAND ${PROTOC_EXE} -I=${CMAKE_CURRENT_SOURCE_DIR}/proto --cpp_out=${CMAKE_CURRENT_SOURCE_DIR}/proto ${CMAKE_CURRENT_SOURCE_DIR}/proto/timesync.proto)
else() 
    set(PROTOC_EXE "protoc")
    message("Linux/Mac System PROTOC_EXE = "${PROTOC_EXE})
    execute_process(COMMAND ${PROTOC_EXE} -I=${CMAKE_CURRENT_SOURCE_DIR}/proto --cpp_out=${CMAKE_CURRENT_SOURCE_DIR}/proto ${CMAKE_CURRENT_SOURCE_DIR}/proto/proto_compress.proto)
    execute_process(COMMAND ${PROTOC_EXE} -I=${CMAKE_CURRENT_SOURCE_DIR}/proto --cpp_out=${CMAKE_CURRENT_SOURCE_DIR}/proto ${CMAKE_CURRENT_SOURCE_DIR}/proto/hmi.proto)
    execute_process(COMMAND ${PROTOC_EXE} -I=${CMAKE_CURRENT_SOURCE_DIR}/proto --cpp_out=${CMAKE_CURRENT_SOURCE_DIR}/proto ${CMAKE_CURRENT_SOURCE_DIR}/proto/config.proto)
    execute_process(COMMAND ${PROTOC_EXE} -I=${CMAKE_CURRENT_SOURCE_DIR}/proto --cpp_out=${CMAKE_CURRENT_SOURCE_DIR}/proto ${CMAKE_CURRENT_SOURCE_DIR}/proto/acviewer.proto)
    execute_process(COMMAND ${PROTOC_EXE} -I=${CMAKE_CURRENT_SOURCE_DIR}/proto --cpp_out=${CMAKE_CURRENT_SOURCE_DIR}/proto ${CMAKE_CURRENT_SOURCE_DIR}/proto/timesync.proto)
endif() 

if(WIN32)
    add_definitions(-DWIN32_LEAN_AND_MEAN)
endif()

foreach (dir ${CUR_SUB_DIR})
    file(GLOB_RECURSE tmp_srcs ${dir}/*.cpp ${dir}/*.h ${dir}/*.hpp ${dir}/*.cc)
    list(APPEND CUR_SRCS ${tmp_srcs})
endforeach ()

add_library(${CUR_LIB} STATIC
        ${CUR_SRCS}
        )

target_include_directories(${CUR_LIB}
        PUBLIC
        ${CUR_INCLUDES}
        ./
        ${CMAKE_CURRENT_BINARY_DIR}/../../third_party/
        )

if(WIN32)
        target_link_libraries(${CUR_LIB}
                PUBLIC
                rally_utils
                lz4 
                zstd 
                codec 
                # ${PROTOBUF_LIBRARY}
                protobuf::libprotobuf 
                protobuf::libprotobuf-lite
                ZLIB::ZLIB
                ${Boost_LIBRARIES}
                OpenSSL::SSL 
                OpenSSL::Crypto 
                )
else()
        target_link_libraries(${CUR_LIB}
                PUBLIC
                rally_utils  
                lz4 
                zstd 
                codec  
                ${PROTOBUF_LIBRARY}
                ${Boost_LIBRARIES}
                OpenSSL::SSL 
                OpenSSL::Crypto
                )
endif(WIN32)

target_compile_definitions(${CUR_LIB} PRIVATE MODULE_NAME="socket")

set(enable_test true)
if(enable_test)
        message("enable socket test !")
        add_executable(socket_client_test ./test/socket_client_test.cpp)
        target_link_libraries(socket_client_test -lssl socket)
else() 
        message("disable socket test !")
endif(enable_test)
