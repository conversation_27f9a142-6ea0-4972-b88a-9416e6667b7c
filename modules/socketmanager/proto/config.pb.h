// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: config.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_config_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_config_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3014000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3014000 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "proto_compress.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_config_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_config_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[15]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_config_2eproto;
namespace robosense {
namespace rs_hmi {
namespace config {
class Config;
class ConfigDefaultTypeInternal;
extern ConfigDefaultTypeInternal _Config_default_instance_;
class HmiSocketTaskConfig;
class HmiSocketTaskConfigDefaultTypeInternal;
extern HmiSocketTaskConfigDefaultTypeInternal _HmiSocketTaskConfig_default_instance_;
class HmiSocketTopicTaskConfig;
class HmiSocketTopicTaskConfigDefaultTypeInternal;
extern HmiSocketTopicTaskConfigDefaultTypeInternal _HmiSocketTopicTaskConfig_default_instance_;
class HmiTaskSocketConfig;
class HmiTaskSocketConfigDefaultTypeInternal;
extern HmiTaskSocketConfigDefaultTypeInternal _HmiTaskSocketConfig_default_instance_;
class SocketTaskConfig;
class SocketTaskConfigDefaultTypeInternal;
extern SocketTaskConfigDefaultTypeInternal _SocketTaskConfig_default_instance_;
class UdpBufferControlConfig;
class UdpBufferControlConfigDefaultTypeInternal;
extern UdpBufferControlConfigDefaultTypeInternal _UdpBufferControlConfig_default_instance_;
class UdpControlConfig;
class UdpControlConfigDefaultTypeInternal;
extern UdpControlConfigDefaultTypeInternal _UdpControlConfig_default_instance_;
class UdpMulticastEndPointConfig;
class UdpMulticastEndPointConfigDefaultTypeInternal;
extern UdpMulticastEndPointConfigDefaultTypeInternal _UdpMulticastEndPointConfig_default_instance_;
class UdpMulticastTaskConfig;
class UdpMulticastTaskConfigDefaultTypeInternal;
extern UdpMulticastTaskConfigDefaultTypeInternal _UdpMulticastTaskConfig_default_instance_;
class UdpP2PEndPointConfig;
class UdpP2PEndPointConfigDefaultTypeInternal;
extern UdpP2PEndPointConfigDefaultTypeInternal _UdpP2PEndPointConfig_default_instance_;
class UdpP2PTaskConfig;
class UdpP2PTaskConfigDefaultTypeInternal;
extern UdpP2PTaskConfigDefaultTypeInternal _UdpP2PTaskConfig_default_instance_;
class WebsocketEndPointConfig;
class WebsocketEndPointConfigDefaultTypeInternal;
extern WebsocketEndPointConfigDefaultTypeInternal _WebsocketEndPointConfig_default_instance_;
class WebsocketRenderTaskConfig;
class WebsocketRenderTaskConfigDefaultTypeInternal;
extern WebsocketRenderTaskConfigDefaultTypeInternal _WebsocketRenderTaskConfig_default_instance_;
class WebsocketSendControlConfig;
class WebsocketSendControlConfigDefaultTypeInternal;
extern WebsocketSendControlConfigDefaultTypeInternal _WebsocketSendControlConfig_default_instance_;
class WebsocketTaskConfig;
class WebsocketTaskConfigDefaultTypeInternal;
extern WebsocketTaskConfigDefaultTypeInternal _WebsocketTaskConfig_default_instance_;
}  // namespace config
}  // namespace rs_hmi
}  // namespace robosense
PROTOBUF_NAMESPACE_OPEN
template<> ::robosense::rs_hmi::config::Config* Arena::CreateMaybeMessage<::robosense::rs_hmi::config::Config>(Arena*);
template<> ::robosense::rs_hmi::config::HmiSocketTaskConfig* Arena::CreateMaybeMessage<::robosense::rs_hmi::config::HmiSocketTaskConfig>(Arena*);
template<> ::robosense::rs_hmi::config::HmiSocketTopicTaskConfig* Arena::CreateMaybeMessage<::robosense::rs_hmi::config::HmiSocketTopicTaskConfig>(Arena*);
template<> ::robosense::rs_hmi::config::HmiTaskSocketConfig* Arena::CreateMaybeMessage<::robosense::rs_hmi::config::HmiTaskSocketConfig>(Arena*);
template<> ::robosense::rs_hmi::config::SocketTaskConfig* Arena::CreateMaybeMessage<::robosense::rs_hmi::config::SocketTaskConfig>(Arena*);
template<> ::robosense::rs_hmi::config::UdpBufferControlConfig* Arena::CreateMaybeMessage<::robosense::rs_hmi::config::UdpBufferControlConfig>(Arena*);
template<> ::robosense::rs_hmi::config::UdpControlConfig* Arena::CreateMaybeMessage<::robosense::rs_hmi::config::UdpControlConfig>(Arena*);
template<> ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* Arena::CreateMaybeMessage<::robosense::rs_hmi::config::UdpMulticastEndPointConfig>(Arena*);
template<> ::robosense::rs_hmi::config::UdpMulticastTaskConfig* Arena::CreateMaybeMessage<::robosense::rs_hmi::config::UdpMulticastTaskConfig>(Arena*);
template<> ::robosense::rs_hmi::config::UdpP2PEndPointConfig* Arena::CreateMaybeMessage<::robosense::rs_hmi::config::UdpP2PEndPointConfig>(Arena*);
template<> ::robosense::rs_hmi::config::UdpP2PTaskConfig* Arena::CreateMaybeMessage<::robosense::rs_hmi::config::UdpP2PTaskConfig>(Arena*);
template<> ::robosense::rs_hmi::config::WebsocketEndPointConfig* Arena::CreateMaybeMessage<::robosense::rs_hmi::config::WebsocketEndPointConfig>(Arena*);
template<> ::robosense::rs_hmi::config::WebsocketRenderTaskConfig* Arena::CreateMaybeMessage<::robosense::rs_hmi::config::WebsocketRenderTaskConfig>(Arena*);
template<> ::robosense::rs_hmi::config::WebsocketSendControlConfig* Arena::CreateMaybeMessage<::robosense::rs_hmi::config::WebsocketSendControlConfig>(Arena*);
template<> ::robosense::rs_hmi::config::WebsocketTaskConfig* Arena::CreateMaybeMessage<::robosense::rs_hmi::config::WebsocketTaskConfig>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace robosense {
namespace rs_hmi {
namespace config {

enum RS_SOCKET_ROLE_TYPE : int {
  RS_WEBSOCKET_SERVER_ROLE = 1,
  RS_WEBSOCKET_CLIENT_ROLE = 2,
  RS_UDP_MULTICAST_SENDER_ROLE = 3,
  RS_UDP_MULTICAST_RECEIVER_ROLE = 4,
  RS_UDP_P2P_SENDER_ROLE = 5,
  RS_UDP_P2P_RECEIVER_ROLE = 6,
  RS_UDP_P2P_BOTH_ROLE = 7
};
bool RS_SOCKET_ROLE_TYPE_IsValid(int value);
constexpr RS_SOCKET_ROLE_TYPE RS_SOCKET_ROLE_TYPE_MIN = RS_WEBSOCKET_SERVER_ROLE;
constexpr RS_SOCKET_ROLE_TYPE RS_SOCKET_ROLE_TYPE_MAX = RS_UDP_P2P_BOTH_ROLE;
constexpr int RS_SOCKET_ROLE_TYPE_ARRAYSIZE = RS_SOCKET_ROLE_TYPE_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RS_SOCKET_ROLE_TYPE_descriptor();
template<typename T>
inline const std::string& RS_SOCKET_ROLE_TYPE_Name(T enum_t_value) {
  static_assert(::std::is_same<T, RS_SOCKET_ROLE_TYPE>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function RS_SOCKET_ROLE_TYPE_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    RS_SOCKET_ROLE_TYPE_descriptor(), enum_t_value);
}
inline bool RS_SOCKET_ROLE_TYPE_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, RS_SOCKET_ROLE_TYPE* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<RS_SOCKET_ROLE_TYPE>(
    RS_SOCKET_ROLE_TYPE_descriptor(), name, value);
}
enum RS_UDP_CONTROL_MODE_TYPE : int {
  RS_UDP_CONTROL_NOTHING = 1,
  RS_UDP_CONTROL_TOTAL_CONTROL_TIME = 2,
  RS_UDP_CONTROL_DATA_CONTROL_TIME = 3
};
bool RS_UDP_CONTROL_MODE_TYPE_IsValid(int value);
constexpr RS_UDP_CONTROL_MODE_TYPE RS_UDP_CONTROL_MODE_TYPE_MIN = RS_UDP_CONTROL_NOTHING;
constexpr RS_UDP_CONTROL_MODE_TYPE RS_UDP_CONTROL_MODE_TYPE_MAX = RS_UDP_CONTROL_DATA_CONTROL_TIME;
constexpr int RS_UDP_CONTROL_MODE_TYPE_ARRAYSIZE = RS_UDP_CONTROL_MODE_TYPE_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RS_UDP_CONTROL_MODE_TYPE_descriptor();
template<typename T>
inline const std::string& RS_UDP_CONTROL_MODE_TYPE_Name(T enum_t_value) {
  static_assert(::std::is_same<T, RS_UDP_CONTROL_MODE_TYPE>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function RS_UDP_CONTROL_MODE_TYPE_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    RS_UDP_CONTROL_MODE_TYPE_descriptor(), enum_t_value);
}
inline bool RS_UDP_CONTROL_MODE_TYPE_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, RS_UDP_CONTROL_MODE_TYPE* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<RS_UDP_CONTROL_MODE_TYPE>(
    RS_UDP_CONTROL_MODE_TYPE_descriptor(), name, value);
}
enum RS_UDP_BUFFER_CONTROL_MODE_TYPE : int {
  RS_UDP_BUFFER_CONTROL_NOTHING = 1,
  RS_UDP_BUFFER_CONTROL_ENABLE = 2
};
bool RS_UDP_BUFFER_CONTROL_MODE_TYPE_IsValid(int value);
constexpr RS_UDP_BUFFER_CONTROL_MODE_TYPE RS_UDP_BUFFER_CONTROL_MODE_TYPE_MIN = RS_UDP_BUFFER_CONTROL_NOTHING;
constexpr RS_UDP_BUFFER_CONTROL_MODE_TYPE RS_UDP_BUFFER_CONTROL_MODE_TYPE_MAX = RS_UDP_BUFFER_CONTROL_ENABLE;
constexpr int RS_UDP_BUFFER_CONTROL_MODE_TYPE_ARRAYSIZE = RS_UDP_BUFFER_CONTROL_MODE_TYPE_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RS_UDP_BUFFER_CONTROL_MODE_TYPE_descriptor();
template<typename T>
inline const std::string& RS_UDP_BUFFER_CONTROL_MODE_TYPE_Name(T enum_t_value) {
  static_assert(::std::is_same<T, RS_UDP_BUFFER_CONTROL_MODE_TYPE>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function RS_UDP_BUFFER_CONTROL_MODE_TYPE_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    RS_UDP_BUFFER_CONTROL_MODE_TYPE_descriptor(), enum_t_value);
}
inline bool RS_UDP_BUFFER_CONTROL_MODE_TYPE_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, RS_UDP_BUFFER_CONTROL_MODE_TYPE* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<RS_UDP_BUFFER_CONTROL_MODE_TYPE>(
    RS_UDP_BUFFER_CONTROL_MODE_TYPE_descriptor(), name, value);
}
enum RS_HMI_TASK_TYPE : int {
  RS_HMI_TASK_CARAPP = 1,
  RS_HMI_TASK_RENDER = 2,
  RS_HMI_TASK_SYSTEMSTATUS = 3,
  RS_HMI_TASK_PNCAPP = 4,
  RS_HMI_TASK_EGOCARSTATUS = 5,
  RS_HMI_TASK_FAULTREPORTER = 6,
  RS_HMI_TASK_VERSION = 7,
  RS_HMI_TASK_FREQUENCE = 8,
  RS_HMI_TASK_AUDIO = 9,
  RS_HMI_TASK_RTK_TIEM_SYNC = 10,
  RS_HMI_TASK_PREFABRICATEDROUTE = 11,
  RS_HMI_TASK_LOCALIZATION = 12,
  RS_HMI_TASK_REMOTE_CONTROL = 13,
  RS_HMI_TASK_QUICK_DATA = 14
};
bool RS_HMI_TASK_TYPE_IsValid(int value);
constexpr RS_HMI_TASK_TYPE RS_HMI_TASK_TYPE_MIN = RS_HMI_TASK_CARAPP;
constexpr RS_HMI_TASK_TYPE RS_HMI_TASK_TYPE_MAX = RS_HMI_TASK_QUICK_DATA;
constexpr int RS_HMI_TASK_TYPE_ARRAYSIZE = RS_HMI_TASK_TYPE_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RS_HMI_TASK_TYPE_descriptor();
template<typename T>
inline const std::string& RS_HMI_TASK_TYPE_Name(T enum_t_value) {
  static_assert(::std::is_same<T, RS_HMI_TASK_TYPE>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function RS_HMI_TASK_TYPE_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    RS_HMI_TASK_TYPE_descriptor(), enum_t_value);
}
inline bool RS_HMI_TASK_TYPE_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, RS_HMI_TASK_TYPE* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<RS_HMI_TASK_TYPE>(
    RS_HMI_TASK_TYPE_descriptor(), name, value);
}
enum RS_HD_MAP_TRANSFORM_LEVEL : int {
  RS_HD_MAP_TRANSFORM_BASIC = 1,
  RS_HD_MAP_TRANSFORM_FULL = 2
};
bool RS_HD_MAP_TRANSFORM_LEVEL_IsValid(int value);
constexpr RS_HD_MAP_TRANSFORM_LEVEL RS_HD_MAP_TRANSFORM_LEVEL_MIN = RS_HD_MAP_TRANSFORM_BASIC;
constexpr RS_HD_MAP_TRANSFORM_LEVEL RS_HD_MAP_TRANSFORM_LEVEL_MAX = RS_HD_MAP_TRANSFORM_FULL;
constexpr int RS_HD_MAP_TRANSFORM_LEVEL_ARRAYSIZE = RS_HD_MAP_TRANSFORM_LEVEL_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RS_HD_MAP_TRANSFORM_LEVEL_descriptor();
template<typename T>
inline const std::string& RS_HD_MAP_TRANSFORM_LEVEL_Name(T enum_t_value) {
  static_assert(::std::is_same<T, RS_HD_MAP_TRANSFORM_LEVEL>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function RS_HD_MAP_TRANSFORM_LEVEL_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    RS_HD_MAP_TRANSFORM_LEVEL_descriptor(), enum_t_value);
}
inline bool RS_HD_MAP_TRANSFORM_LEVEL_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, RS_HD_MAP_TRANSFORM_LEVEL* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<RS_HD_MAP_TRANSFORM_LEVEL>(
    RS_HD_MAP_TRANSFORM_LEVEL_descriptor(), name, value);
}
// ===================================================================

class WebsocketEndPointConfig PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.rs_hmi.config.WebsocketEndPointConfig) */ {
 public:
  inline WebsocketEndPointConfig() : WebsocketEndPointConfig(nullptr) {}
  virtual ~WebsocketEndPointConfig();

  WebsocketEndPointConfig(const WebsocketEndPointConfig& from);
  WebsocketEndPointConfig(WebsocketEndPointConfig&& from) noexcept
    : WebsocketEndPointConfig() {
    *this = ::std::move(from);
  }

  inline WebsocketEndPointConfig& operator=(const WebsocketEndPointConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline WebsocketEndPointConfig& operator=(WebsocketEndPointConfig&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const WebsocketEndPointConfig& default_instance();

  static inline const WebsocketEndPointConfig* internal_default_instance() {
    return reinterpret_cast<const WebsocketEndPointConfig*>(
               &_WebsocketEndPointConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(WebsocketEndPointConfig& a, WebsocketEndPointConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(WebsocketEndPointConfig* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(WebsocketEndPointConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline WebsocketEndPointConfig* New() const final {
    return CreateMaybeMessage<WebsocketEndPointConfig>(nullptr);
  }

  WebsocketEndPointConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<WebsocketEndPointConfig>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const WebsocketEndPointConfig& from);
  void MergeFrom(const WebsocketEndPointConfig& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WebsocketEndPointConfig* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.rs_hmi.config.WebsocketEndPointConfig";
  }
  protected:
  explicit WebsocketEndPointConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_config_2eproto);
    return ::descriptor_table_config_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kServerIpFieldNumber = 2,
    kRoleTypeFieldNumber = 1,
    kServerPortFieldNumber = 3,
    kRunThreadCntFieldNumber = 4,
    kSendThreadCntFieldNumber = 5,
    kServerClientTimeoutMsFieldNumber = 6,
    kServerCheckTimeoutMsFieldNumber = 7,
    kCompressFormatFieldNumber = 8,
    kCompressLevelFieldNumber = 9,
  };
  // optional string server_ip = 2 [default = "0.0.0.0"];
  bool has_server_ip() const;
  private:
  bool _internal_has_server_ip() const;
  public:
  void clear_server_ip();
  const std::string& server_ip() const;
  void set_server_ip(const std::string& value);
  void set_server_ip(std::string&& value);
  void set_server_ip(const char* value);
  void set_server_ip(const char* value, size_t size);
  std::string* mutable_server_ip();
  std::string* release_server_ip();
  void set_allocated_server_ip(std::string* server_ip);
  private:
  const std::string& _internal_server_ip() const;
  void _internal_set_server_ip(const std::string& value);
  std::string* _internal_mutable_server_ip();
  public:

  // optional .robosense.rs_hmi.config.RS_SOCKET_ROLE_TYPE role_type = 1;
  bool has_role_type() const;
  private:
  bool _internal_has_role_type() const;
  public:
  void clear_role_type();
  ::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE role_type() const;
  void set_role_type(::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE value);
  private:
  ::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE _internal_role_type() const;
  void _internal_set_role_type(::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE value);
  public:

  // optional uint32 server_port = 3 [default = 8080];
  bool has_server_port() const;
  private:
  bool _internal_has_server_port() const;
  public:
  void clear_server_port();
  ::PROTOBUF_NAMESPACE_ID::uint32 server_port() const;
  void set_server_port(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_server_port() const;
  void _internal_set_server_port(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 run_thread_cnt = 4 [default = 4];
  bool has_run_thread_cnt() const;
  private:
  bool _internal_has_run_thread_cnt() const;
  public:
  void clear_run_thread_cnt();
  ::PROTOBUF_NAMESPACE_ID::uint32 run_thread_cnt() const;
  void set_run_thread_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_run_thread_cnt() const;
  void _internal_set_run_thread_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 send_thread_cnt = 5 [default = 4];
  bool has_send_thread_cnt() const;
  private:
  bool _internal_has_send_thread_cnt() const;
  public:
  void clear_send_thread_cnt();
  ::PROTOBUF_NAMESPACE_ID::uint32 send_thread_cnt() const;
  void set_send_thread_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_send_thread_cnt() const;
  void _internal_set_send_thread_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 server_client_timeout_ms = 6 [default = 20000];
  bool has_server_client_timeout_ms() const;
  private:
  bool _internal_has_server_client_timeout_ms() const;
  public:
  void clear_server_client_timeout_ms();
  ::PROTOBUF_NAMESPACE_ID::uint32 server_client_timeout_ms() const;
  void set_server_client_timeout_ms(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_server_client_timeout_ms() const;
  void _internal_set_server_client_timeout_ms(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 server_check_timeout_ms = 7 [default = 3000];
  bool has_server_check_timeout_ms() const;
  private:
  bool _internal_has_server_check_timeout_ms() const;
  public:
  void clear_server_check_timeout_ms();
  ::PROTOBUF_NAMESPACE_ID::uint32 server_check_timeout_ms() const;
  void set_server_check_timeout_ms(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_server_check_timeout_ms() const;
  void _internal_set_server_check_timeout_ms(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional .robosense.proto_compress_msgs.RS_POST_DATA_COMPRESSION_FORMAT compress_format = 8 [default = RS_POST_DATA_COMPRESSION_NOTHING];
  bool has_compress_format() const;
  private:
  bool _internal_has_compress_format() const;
  public:
  void clear_compress_format();
  ::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT compress_format() const;
  void set_compress_format(::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT value);
  private:
  ::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT _internal_compress_format() const;
  void _internal_set_compress_format(::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT value);
  public:

  // optional uint32 compress_level = 9 [default = 9];
  bool has_compress_level() const;
  private:
  bool _internal_has_compress_level() const;
  public:
  void clear_compress_level();
  ::PROTOBUF_NAMESPACE_ID::uint32 compress_level() const;
  void set_compress_level(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_compress_level() const;
  void _internal_set_compress_level(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:robosense.rs_hmi.config.WebsocketEndPointConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  static const ::PROTOBUF_NAMESPACE_ID::internal::LazyString _i_give_permission_to_break_this_code_default_server_ip_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr server_ip_;
  int role_type_;
  ::PROTOBUF_NAMESPACE_ID::uint32 server_port_;
  ::PROTOBUF_NAMESPACE_ID::uint32 run_thread_cnt_;
  ::PROTOBUF_NAMESPACE_ID::uint32 send_thread_cnt_;
  ::PROTOBUF_NAMESPACE_ID::uint32 server_client_timeout_ms_;
  ::PROTOBUF_NAMESPACE_ID::uint32 server_check_timeout_ms_;
  int compress_format_;
  ::PROTOBUF_NAMESPACE_ID::uint32 compress_level_;
  friend struct ::TableStruct_config_2eproto;
};
// -------------------------------------------------------------------

class UdpControlConfig PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.rs_hmi.config.UdpControlConfig) */ {
 public:
  inline UdpControlConfig() : UdpControlConfig(nullptr) {}
  virtual ~UdpControlConfig();

  UdpControlConfig(const UdpControlConfig& from);
  UdpControlConfig(UdpControlConfig&& from) noexcept
    : UdpControlConfig() {
    *this = ::std::move(from);
  }

  inline UdpControlConfig& operator=(const UdpControlConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline UdpControlConfig& operator=(UdpControlConfig&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const UdpControlConfig& default_instance();

  static inline const UdpControlConfig* internal_default_instance() {
    return reinterpret_cast<const UdpControlConfig*>(
               &_UdpControlConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(UdpControlConfig& a, UdpControlConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(UdpControlConfig* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UdpControlConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline UdpControlConfig* New() const final {
    return CreateMaybeMessage<UdpControlConfig>(nullptr);
  }

  UdpControlConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<UdpControlConfig>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const UdpControlConfig& from);
  void MergeFrom(const UdpControlConfig& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UdpControlConfig* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.rs_hmi.config.UdpControlConfig";
  }
  protected:
  explicit UdpControlConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_config_2eproto);
    return ::descriptor_table_config_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kUdpDataControlTimeMsFieldNumber = 5,
    kUdpControlTypeFieldNumber = 1,
    kUdpTotalControlTimeMsFieldNumber = 2,
    kUdpTotalControlSingleTimeMsFieldNumber = 3,
    kUdpDataControlSizeFieldNumber = 4,
  };
  // optional uint32 udp_data_control_time_ms = 5 [default = 2];
  bool has_udp_data_control_time_ms() const;
  private:
  bool _internal_has_udp_data_control_time_ms() const;
  public:
  void clear_udp_data_control_time_ms();
  ::PROTOBUF_NAMESPACE_ID::uint32 udp_data_control_time_ms() const;
  void set_udp_data_control_time_ms(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_udp_data_control_time_ms() const;
  void _internal_set_udp_data_control_time_ms(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional .robosense.rs_hmi.config.RS_UDP_CONTROL_MODE_TYPE udp_control_type = 1 [default = RS_UDP_CONTROL_TOTAL_CONTROL_TIME];
  bool has_udp_control_type() const;
  private:
  bool _internal_has_udp_control_type() const;
  public:
  void clear_udp_control_type();
  ::robosense::rs_hmi::config::RS_UDP_CONTROL_MODE_TYPE udp_control_type() const;
  void set_udp_control_type(::robosense::rs_hmi::config::RS_UDP_CONTROL_MODE_TYPE value);
  private:
  ::robosense::rs_hmi::config::RS_UDP_CONTROL_MODE_TYPE _internal_udp_control_type() const;
  void _internal_set_udp_control_type(::robosense::rs_hmi::config::RS_UDP_CONTROL_MODE_TYPE value);
  public:

  // optional uint32 udp_total_control_time_ms = 2 [default = 60];
  bool has_udp_total_control_time_ms() const;
  private:
  bool _internal_has_udp_total_control_time_ms() const;
  public:
  void clear_udp_total_control_time_ms();
  ::PROTOBUF_NAMESPACE_ID::uint32 udp_total_control_time_ms() const;
  void set_udp_total_control_time_ms(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_udp_total_control_time_ms() const;
  void _internal_set_udp_total_control_time_ms(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 udp_total_control_single_time_ms = 3 [default = 2];
  bool has_udp_total_control_single_time_ms() const;
  private:
  bool _internal_has_udp_total_control_single_time_ms() const;
  public:
  void clear_udp_total_control_single_time_ms();
  ::PROTOBUF_NAMESPACE_ID::uint32 udp_total_control_single_time_ms() const;
  void set_udp_total_control_single_time_ms(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_udp_total_control_single_time_ms() const;
  void _internal_set_udp_total_control_single_time_ms(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 udp_data_control_size = 4 [default = 262144];
  bool has_udp_data_control_size() const;
  private:
  bool _internal_has_udp_data_control_size() const;
  public:
  void clear_udp_data_control_size();
  ::PROTOBUF_NAMESPACE_ID::uint32 udp_data_control_size() const;
  void set_udp_data_control_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_udp_data_control_size() const;
  void _internal_set_udp_data_control_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:robosense.rs_hmi.config.UdpControlConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 udp_data_control_time_ms_;
  int udp_control_type_;
  ::PROTOBUF_NAMESPACE_ID::uint32 udp_total_control_time_ms_;
  ::PROTOBUF_NAMESPACE_ID::uint32 udp_total_control_single_time_ms_;
  ::PROTOBUF_NAMESPACE_ID::uint32 udp_data_control_size_;
  friend struct ::TableStruct_config_2eproto;
};
// -------------------------------------------------------------------

class UdpBufferControlConfig PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.rs_hmi.config.UdpBufferControlConfig) */ {
 public:
  inline UdpBufferControlConfig() : UdpBufferControlConfig(nullptr) {}
  virtual ~UdpBufferControlConfig();

  UdpBufferControlConfig(const UdpBufferControlConfig& from);
  UdpBufferControlConfig(UdpBufferControlConfig&& from) noexcept
    : UdpBufferControlConfig() {
    *this = ::std::move(from);
  }

  inline UdpBufferControlConfig& operator=(const UdpBufferControlConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline UdpBufferControlConfig& operator=(UdpBufferControlConfig&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const UdpBufferControlConfig& default_instance();

  static inline const UdpBufferControlConfig* internal_default_instance() {
    return reinterpret_cast<const UdpBufferControlConfig*>(
               &_UdpBufferControlConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(UdpBufferControlConfig& a, UdpBufferControlConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(UdpBufferControlConfig* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UdpBufferControlConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline UdpBufferControlConfig* New() const final {
    return CreateMaybeMessage<UdpBufferControlConfig>(nullptr);
  }

  UdpBufferControlConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<UdpBufferControlConfig>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const UdpBufferControlConfig& from);
  void MergeFrom(const UdpBufferControlConfig& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UdpBufferControlConfig* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.rs_hmi.config.UdpBufferControlConfig";
  }
  protected:
  explicit UdpBufferControlConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_config_2eproto);
    return ::descriptor_table_config_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kUdpBufferControlSendFrameGapFieldNumber = 5,
    kUdpBufferControlTypeFieldNumber = 1,
    kUdpDynamicSingleControlThFieldNumber = 2,
    kUdpStaticSingleControlThFieldNumber = 3,
    kUdpCombineControlThFieldNumber = 4,
  };
  // optional uint32 udp_buffer_control_send_frame_gap = 5 [default = 2];
  bool has_udp_buffer_control_send_frame_gap() const;
  private:
  bool _internal_has_udp_buffer_control_send_frame_gap() const;
  public:
  void clear_udp_buffer_control_send_frame_gap();
  ::PROTOBUF_NAMESPACE_ID::uint32 udp_buffer_control_send_frame_gap() const;
  void set_udp_buffer_control_send_frame_gap(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_udp_buffer_control_send_frame_gap() const;
  void _internal_set_udp_buffer_control_send_frame_gap(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional .robosense.rs_hmi.config.RS_UDP_BUFFER_CONTROL_MODE_TYPE udp_buffer_control_type = 1 [default = RS_UDP_BUFFER_CONTROL_ENABLE];
  bool has_udp_buffer_control_type() const;
  private:
  bool _internal_has_udp_buffer_control_type() const;
  public:
  void clear_udp_buffer_control_type();
  ::robosense::rs_hmi::config::RS_UDP_BUFFER_CONTROL_MODE_TYPE udp_buffer_control_type() const;
  void set_udp_buffer_control_type(::robosense::rs_hmi::config::RS_UDP_BUFFER_CONTROL_MODE_TYPE value);
  private:
  ::robosense::rs_hmi::config::RS_UDP_BUFFER_CONTROL_MODE_TYPE _internal_udp_buffer_control_type() const;
  void _internal_set_udp_buffer_control_type(::robosense::rs_hmi::config::RS_UDP_BUFFER_CONTROL_MODE_TYPE value);
  public:

  // optional uint32 udp_dynamic_single_control_th = 2 [default = 3];
  bool has_udp_dynamic_single_control_th() const;
  private:
  bool _internal_has_udp_dynamic_single_control_th() const;
  public:
  void clear_udp_dynamic_single_control_th();
  ::PROTOBUF_NAMESPACE_ID::uint32 udp_dynamic_single_control_th() const;
  void set_udp_dynamic_single_control_th(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_udp_dynamic_single_control_th() const;
  void _internal_set_udp_dynamic_single_control_th(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 udp_static_single_control_th = 3 [default = 3];
  bool has_udp_static_single_control_th() const;
  private:
  bool _internal_has_udp_static_single_control_th() const;
  public:
  void clear_udp_static_single_control_th();
  ::PROTOBUF_NAMESPACE_ID::uint32 udp_static_single_control_th() const;
  void set_udp_static_single_control_th(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_udp_static_single_control_th() const;
  void _internal_set_udp_static_single_control_th(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 udp_combine_control_th = 4 [default = 6];
  bool has_udp_combine_control_th() const;
  private:
  bool _internal_has_udp_combine_control_th() const;
  public:
  void clear_udp_combine_control_th();
  ::PROTOBUF_NAMESPACE_ID::uint32 udp_combine_control_th() const;
  void set_udp_combine_control_th(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_udp_combine_control_th() const;
  void _internal_set_udp_combine_control_th(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:robosense.rs_hmi.config.UdpBufferControlConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 udp_buffer_control_send_frame_gap_;
  int udp_buffer_control_type_;
  ::PROTOBUF_NAMESPACE_ID::uint32 udp_dynamic_single_control_th_;
  ::PROTOBUF_NAMESPACE_ID::uint32 udp_static_single_control_th_;
  ::PROTOBUF_NAMESPACE_ID::uint32 udp_combine_control_th_;
  friend struct ::TableStruct_config_2eproto;
};
// -------------------------------------------------------------------

class UdpMulticastEndPointConfig PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.rs_hmi.config.UdpMulticastEndPointConfig) */ {
 public:
  inline UdpMulticastEndPointConfig() : UdpMulticastEndPointConfig(nullptr) {}
  virtual ~UdpMulticastEndPointConfig();

  UdpMulticastEndPointConfig(const UdpMulticastEndPointConfig& from);
  UdpMulticastEndPointConfig(UdpMulticastEndPointConfig&& from) noexcept
    : UdpMulticastEndPointConfig() {
    *this = ::std::move(from);
  }

  inline UdpMulticastEndPointConfig& operator=(const UdpMulticastEndPointConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline UdpMulticastEndPointConfig& operator=(UdpMulticastEndPointConfig&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const UdpMulticastEndPointConfig& default_instance();

  static inline const UdpMulticastEndPointConfig* internal_default_instance() {
    return reinterpret_cast<const UdpMulticastEndPointConfig*>(
               &_UdpMulticastEndPointConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(UdpMulticastEndPointConfig& a, UdpMulticastEndPointConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(UdpMulticastEndPointConfig* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UdpMulticastEndPointConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline UdpMulticastEndPointConfig* New() const final {
    return CreateMaybeMessage<UdpMulticastEndPointConfig>(nullptr);
  }

  UdpMulticastEndPointConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<UdpMulticastEndPointConfig>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const UdpMulticastEndPointConfig& from);
  void MergeFrom(const UdpMulticastEndPointConfig& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UdpMulticastEndPointConfig* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.rs_hmi.config.UdpMulticastEndPointConfig";
  }
  protected:
  explicit UdpMulticastEndPointConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_config_2eproto);
    return ::descriptor_table_config_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMulticastIpFieldNumber = 2,
    kMulticastHostIpFieldNumber = 4,
    kUdpControlConfigFieldNumber = 13,
    kRoleTypeFieldNumber = 1,
    kMulticastPortFieldNumber = 3,
    kMaxMsgSizeFieldNumber = 5,
    kBufferCellSizeFieldNumber = 6,
    kAsioRunThreadCntFieldNumber = 7,
    kAsioSndThreadCntFieldNumber = 8,
    kAsioRecvThreadCntFieldNumber = 9,
    kMsgTimeoutThMsFieldNumber = 10,
    kAsioSndRetryCntFieldNumber = 11,
    kAsioSndRetryThMsFieldNumber = 12,
    kCompressFormatFieldNumber = 14,
    kCompressLevelFieldNumber = 15,
  };
  // optional string multicast_ip = 2 [default = "***********"];
  bool has_multicast_ip() const;
  private:
  bool _internal_has_multicast_ip() const;
  public:
  void clear_multicast_ip();
  const std::string& multicast_ip() const;
  void set_multicast_ip(const std::string& value);
  void set_multicast_ip(std::string&& value);
  void set_multicast_ip(const char* value);
  void set_multicast_ip(const char* value, size_t size);
  std::string* mutable_multicast_ip();
  std::string* release_multicast_ip();
  void set_allocated_multicast_ip(std::string* multicast_ip);
  private:
  const std::string& _internal_multicast_ip() const;
  void _internal_set_multicast_ip(const std::string& value);
  std::string* _internal_mutable_multicast_ip();
  public:

  // optional string multicast_host_ip = 4 [default = "***********"];
  bool has_multicast_host_ip() const;
  private:
  bool _internal_has_multicast_host_ip() const;
  public:
  void clear_multicast_host_ip();
  const std::string& multicast_host_ip() const;
  void set_multicast_host_ip(const std::string& value);
  void set_multicast_host_ip(std::string&& value);
  void set_multicast_host_ip(const char* value);
  void set_multicast_host_ip(const char* value, size_t size);
  std::string* mutable_multicast_host_ip();
  std::string* release_multicast_host_ip();
  void set_allocated_multicast_host_ip(std::string* multicast_host_ip);
  private:
  const std::string& _internal_multicast_host_ip() const;
  void _internal_set_multicast_host_ip(const std::string& value);
  std::string* _internal_mutable_multicast_host_ip();
  public:

  // optional .robosense.rs_hmi.config.UdpControlConfig udp_control_config = 13;
  bool has_udp_control_config() const;
  private:
  bool _internal_has_udp_control_config() const;
  public:
  void clear_udp_control_config();
  const ::robosense::rs_hmi::config::UdpControlConfig& udp_control_config() const;
  ::robosense::rs_hmi::config::UdpControlConfig* release_udp_control_config();
  ::robosense::rs_hmi::config::UdpControlConfig* mutable_udp_control_config();
  void set_allocated_udp_control_config(::robosense::rs_hmi::config::UdpControlConfig* udp_control_config);
  private:
  const ::robosense::rs_hmi::config::UdpControlConfig& _internal_udp_control_config() const;
  ::robosense::rs_hmi::config::UdpControlConfig* _internal_mutable_udp_control_config();
  public:
  void unsafe_arena_set_allocated_udp_control_config(
      ::robosense::rs_hmi::config::UdpControlConfig* udp_control_config);
  ::robosense::rs_hmi::config::UdpControlConfig* unsafe_arena_release_udp_control_config();

  // optional .robosense.rs_hmi.config.RS_SOCKET_ROLE_TYPE role_type = 1;
  bool has_role_type() const;
  private:
  bool _internal_has_role_type() const;
  public:
  void clear_role_type();
  ::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE role_type() const;
  void set_role_type(::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE value);
  private:
  ::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE _internal_role_type() const;
  void _internal_set_role_type(::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE value);
  public:

  // optional uint32 multicast_port = 3 [default = 9096];
  bool has_multicast_port() const;
  private:
  bool _internal_has_multicast_port() const;
  public:
  void clear_multicast_port();
  ::PROTOBUF_NAMESPACE_ID::uint32 multicast_port() const;
  void set_multicast_port(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_multicast_port() const;
  void _internal_set_multicast_port(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 max_msg_size = 5 [default = 4096];
  bool has_max_msg_size() const;
  private:
  bool _internal_has_max_msg_size() const;
  public:
  void clear_max_msg_size();
  ::PROTOBUF_NAMESPACE_ID::uint32 max_msg_size() const;
  void set_max_msg_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_max_msg_size() const;
  void _internal_set_max_msg_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 buffer_cell_size = 6 [default = 128];
  bool has_buffer_cell_size() const;
  private:
  bool _internal_has_buffer_cell_size() const;
  public:
  void clear_buffer_cell_size();
  ::PROTOBUF_NAMESPACE_ID::uint32 buffer_cell_size() const;
  void set_buffer_cell_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_buffer_cell_size() const;
  void _internal_set_buffer_cell_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 asio_run_thread_cnt = 7 [default = 1];
  bool has_asio_run_thread_cnt() const;
  private:
  bool _internal_has_asio_run_thread_cnt() const;
  public:
  void clear_asio_run_thread_cnt();
  ::PROTOBUF_NAMESPACE_ID::uint32 asio_run_thread_cnt() const;
  void set_asio_run_thread_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_asio_run_thread_cnt() const;
  void _internal_set_asio_run_thread_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 asio_snd_thread_cnt = 8 [default = 1];
  bool has_asio_snd_thread_cnt() const;
  private:
  bool _internal_has_asio_snd_thread_cnt() const;
  public:
  void clear_asio_snd_thread_cnt();
  ::PROTOBUF_NAMESPACE_ID::uint32 asio_snd_thread_cnt() const;
  void set_asio_snd_thread_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_asio_snd_thread_cnt() const;
  void _internal_set_asio_snd_thread_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 asio_recv_thread_cnt = 9 [default = 1];
  bool has_asio_recv_thread_cnt() const;
  private:
  bool _internal_has_asio_recv_thread_cnt() const;
  public:
  void clear_asio_recv_thread_cnt();
  ::PROTOBUF_NAMESPACE_ID::uint32 asio_recv_thread_cnt() const;
  void set_asio_recv_thread_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_asio_recv_thread_cnt() const;
  void _internal_set_asio_recv_thread_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 msg_timeout_th_ms = 10 [default = 100];
  bool has_msg_timeout_th_ms() const;
  private:
  bool _internal_has_msg_timeout_th_ms() const;
  public:
  void clear_msg_timeout_th_ms();
  ::PROTOBUF_NAMESPACE_ID::uint32 msg_timeout_th_ms() const;
  void set_msg_timeout_th_ms(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_msg_timeout_th_ms() const;
  void _internal_set_msg_timeout_th_ms(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 asio_snd_retry_cnt = 11 [default = 1];
  bool has_asio_snd_retry_cnt() const;
  private:
  bool _internal_has_asio_snd_retry_cnt() const;
  public:
  void clear_asio_snd_retry_cnt();
  ::PROTOBUF_NAMESPACE_ID::uint32 asio_snd_retry_cnt() const;
  void set_asio_snd_retry_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_asio_snd_retry_cnt() const;
  void _internal_set_asio_snd_retry_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 asio_snd_retry_th_ms = 12 [default = 5];
  bool has_asio_snd_retry_th_ms() const;
  private:
  bool _internal_has_asio_snd_retry_th_ms() const;
  public:
  void clear_asio_snd_retry_th_ms();
  ::PROTOBUF_NAMESPACE_ID::uint32 asio_snd_retry_th_ms() const;
  void set_asio_snd_retry_th_ms(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_asio_snd_retry_th_ms() const;
  void _internal_set_asio_snd_retry_th_ms(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional .robosense.proto_compress_msgs.RS_POST_DATA_COMPRESSION_FORMAT compress_format = 14 [default = RS_POST_DATA_COMPRESSION_NOTHING];
  bool has_compress_format() const;
  private:
  bool _internal_has_compress_format() const;
  public:
  void clear_compress_format();
  ::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT compress_format() const;
  void set_compress_format(::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT value);
  private:
  ::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT _internal_compress_format() const;
  void _internal_set_compress_format(::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT value);
  public:

  // optional uint32 compress_level = 15 [default = 9];
  bool has_compress_level() const;
  private:
  bool _internal_has_compress_level() const;
  public:
  void clear_compress_level();
  ::PROTOBUF_NAMESPACE_ID::uint32 compress_level() const;
  void set_compress_level(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_compress_level() const;
  void _internal_set_compress_level(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:robosense.rs_hmi.config.UdpMulticastEndPointConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  static const ::PROTOBUF_NAMESPACE_ID::internal::LazyString _i_give_permission_to_break_this_code_default_multicast_ip_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr multicast_ip_;
  static const ::PROTOBUF_NAMESPACE_ID::internal::LazyString _i_give_permission_to_break_this_code_default_multicast_host_ip_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr multicast_host_ip_;
  ::robosense::rs_hmi::config::UdpControlConfig* udp_control_config_;
  int role_type_;
  ::PROTOBUF_NAMESPACE_ID::uint32 multicast_port_;
  ::PROTOBUF_NAMESPACE_ID::uint32 max_msg_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 buffer_cell_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 asio_run_thread_cnt_;
  ::PROTOBUF_NAMESPACE_ID::uint32 asio_snd_thread_cnt_;
  ::PROTOBUF_NAMESPACE_ID::uint32 asio_recv_thread_cnt_;
  ::PROTOBUF_NAMESPACE_ID::uint32 msg_timeout_th_ms_;
  ::PROTOBUF_NAMESPACE_ID::uint32 asio_snd_retry_cnt_;
  ::PROTOBUF_NAMESPACE_ID::uint32 asio_snd_retry_th_ms_;
  int compress_format_;
  ::PROTOBUF_NAMESPACE_ID::uint32 compress_level_;
  friend struct ::TableStruct_config_2eproto;
};
// -------------------------------------------------------------------

class UdpP2PEndPointConfig PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.rs_hmi.config.UdpP2PEndPointConfig) */ {
 public:
  inline UdpP2PEndPointConfig() : UdpP2PEndPointConfig(nullptr) {}
  virtual ~UdpP2PEndPointConfig();

  UdpP2PEndPointConfig(const UdpP2PEndPointConfig& from);
  UdpP2PEndPointConfig(UdpP2PEndPointConfig&& from) noexcept
    : UdpP2PEndPointConfig() {
    *this = ::std::move(from);
  }

  inline UdpP2PEndPointConfig& operator=(const UdpP2PEndPointConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline UdpP2PEndPointConfig& operator=(UdpP2PEndPointConfig&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const UdpP2PEndPointConfig& default_instance();

  static inline const UdpP2PEndPointConfig* internal_default_instance() {
    return reinterpret_cast<const UdpP2PEndPointConfig*>(
               &_UdpP2PEndPointConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(UdpP2PEndPointConfig& a, UdpP2PEndPointConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(UdpP2PEndPointConfig* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UdpP2PEndPointConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline UdpP2PEndPointConfig* New() const final {
    return CreateMaybeMessage<UdpP2PEndPointConfig>(nullptr);
  }

  UdpP2PEndPointConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<UdpP2PEndPointConfig>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const UdpP2PEndPointConfig& from);
  void MergeFrom(const UdpP2PEndPointConfig& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UdpP2PEndPointConfig* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.rs_hmi.config.UdpP2PEndPointConfig";
  }
  protected:
  explicit UdpP2PEndPointConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_config_2eproto);
    return ::descriptor_table_config_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRemoteIpFieldNumber = 2,
    kUdpControlConfigFieldNumber = 14,
    kCompressLevelFieldNumber = 16,
    kRoleTypeFieldNumber = 1,
    kRemotePortFieldNumber = 3,
    kMaxMsgSizeFieldNumber = 5,
    kBufferCellSizeFieldNumber = 6,
    kAsioRunThreadCntFieldNumber = 7,
    kAsioSndThreadCntFieldNumber = 8,
    kAsioRecvThreadCntFieldNumber = 9,
    kMsgTimeoutThMsFieldNumber = 10,
    kAsioSndRetryCntFieldNumber = 11,
    kAsioSndRetryThMsFieldNumber = 12,
    kSocketBufferSizeFieldNumber = 13,
    kCompressFormatFieldNumber = 15,
  };
  // optional string remote_ip = 2;
  bool has_remote_ip() const;
  private:
  bool _internal_has_remote_ip() const;
  public:
  void clear_remote_ip();
  const std::string& remote_ip() const;
  void set_remote_ip(const std::string& value);
  void set_remote_ip(std::string&& value);
  void set_remote_ip(const char* value);
  void set_remote_ip(const char* value, size_t size);
  std::string* mutable_remote_ip();
  std::string* release_remote_ip();
  void set_allocated_remote_ip(std::string* remote_ip);
  private:
  const std::string& _internal_remote_ip() const;
  void _internal_set_remote_ip(const std::string& value);
  std::string* _internal_mutable_remote_ip();
  public:

  // optional .robosense.rs_hmi.config.UdpControlConfig udp_control_config = 14;
  bool has_udp_control_config() const;
  private:
  bool _internal_has_udp_control_config() const;
  public:
  void clear_udp_control_config();
  const ::robosense::rs_hmi::config::UdpControlConfig& udp_control_config() const;
  ::robosense::rs_hmi::config::UdpControlConfig* release_udp_control_config();
  ::robosense::rs_hmi::config::UdpControlConfig* mutable_udp_control_config();
  void set_allocated_udp_control_config(::robosense::rs_hmi::config::UdpControlConfig* udp_control_config);
  private:
  const ::robosense::rs_hmi::config::UdpControlConfig& _internal_udp_control_config() const;
  ::robosense::rs_hmi::config::UdpControlConfig* _internal_mutable_udp_control_config();
  public:
  void unsafe_arena_set_allocated_udp_control_config(
      ::robosense::rs_hmi::config::UdpControlConfig* udp_control_config);
  ::robosense::rs_hmi::config::UdpControlConfig* unsafe_arena_release_udp_control_config();

  // optional uint32 compress_level = 16 [default = 9];
  bool has_compress_level() const;
  private:
  bool _internal_has_compress_level() const;
  public:
  void clear_compress_level();
  ::PROTOBUF_NAMESPACE_ID::uint32 compress_level() const;
  void set_compress_level(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_compress_level() const;
  void _internal_set_compress_level(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional .robosense.rs_hmi.config.RS_SOCKET_ROLE_TYPE role_type = 1;
  bool has_role_type() const;
  private:
  bool _internal_has_role_type() const;
  public:
  void clear_role_type();
  ::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE role_type() const;
  void set_role_type(::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE value);
  private:
  ::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE _internal_role_type() const;
  void _internal_set_role_type(::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE value);
  public:

  // optional uint32 remote_port = 3 [default = 9097];
  bool has_remote_port() const;
  private:
  bool _internal_has_remote_port() const;
  public:
  void clear_remote_port();
  ::PROTOBUF_NAMESPACE_ID::uint32 remote_port() const;
  void set_remote_port(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_remote_port() const;
  void _internal_set_remote_port(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 max_msg_size = 5 [default = 4096];
  bool has_max_msg_size() const;
  private:
  bool _internal_has_max_msg_size() const;
  public:
  void clear_max_msg_size();
  ::PROTOBUF_NAMESPACE_ID::uint32 max_msg_size() const;
  void set_max_msg_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_max_msg_size() const;
  void _internal_set_max_msg_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 buffer_cell_size = 6 [default = 128];
  bool has_buffer_cell_size() const;
  private:
  bool _internal_has_buffer_cell_size() const;
  public:
  void clear_buffer_cell_size();
  ::PROTOBUF_NAMESPACE_ID::uint32 buffer_cell_size() const;
  void set_buffer_cell_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_buffer_cell_size() const;
  void _internal_set_buffer_cell_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 asio_run_thread_cnt = 7 [default = 1];
  bool has_asio_run_thread_cnt() const;
  private:
  bool _internal_has_asio_run_thread_cnt() const;
  public:
  void clear_asio_run_thread_cnt();
  ::PROTOBUF_NAMESPACE_ID::uint32 asio_run_thread_cnt() const;
  void set_asio_run_thread_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_asio_run_thread_cnt() const;
  void _internal_set_asio_run_thread_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 asio_snd_thread_cnt = 8 [default = 1];
  bool has_asio_snd_thread_cnt() const;
  private:
  bool _internal_has_asio_snd_thread_cnt() const;
  public:
  void clear_asio_snd_thread_cnt();
  ::PROTOBUF_NAMESPACE_ID::uint32 asio_snd_thread_cnt() const;
  void set_asio_snd_thread_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_asio_snd_thread_cnt() const;
  void _internal_set_asio_snd_thread_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 asio_recv_thread_cnt = 9 [default = 1];
  bool has_asio_recv_thread_cnt() const;
  private:
  bool _internal_has_asio_recv_thread_cnt() const;
  public:
  void clear_asio_recv_thread_cnt();
  ::PROTOBUF_NAMESPACE_ID::uint32 asio_recv_thread_cnt() const;
  void set_asio_recv_thread_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_asio_recv_thread_cnt() const;
  void _internal_set_asio_recv_thread_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 msg_timeout_th_ms = 10 [default = 100];
  bool has_msg_timeout_th_ms() const;
  private:
  bool _internal_has_msg_timeout_th_ms() const;
  public:
  void clear_msg_timeout_th_ms();
  ::PROTOBUF_NAMESPACE_ID::uint32 msg_timeout_th_ms() const;
  void set_msg_timeout_th_ms(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_msg_timeout_th_ms() const;
  void _internal_set_msg_timeout_th_ms(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 asio_snd_retry_cnt = 11 [default = 1];
  bool has_asio_snd_retry_cnt() const;
  private:
  bool _internal_has_asio_snd_retry_cnt() const;
  public:
  void clear_asio_snd_retry_cnt();
  ::PROTOBUF_NAMESPACE_ID::uint32 asio_snd_retry_cnt() const;
  void set_asio_snd_retry_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_asio_snd_retry_cnt() const;
  void _internal_set_asio_snd_retry_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 asio_snd_retry_th_ms = 12 [default = 5];
  bool has_asio_snd_retry_th_ms() const;
  private:
  bool _internal_has_asio_snd_retry_th_ms() const;
  public:
  void clear_asio_snd_retry_th_ms();
  ::PROTOBUF_NAMESPACE_ID::uint32 asio_snd_retry_th_ms() const;
  void set_asio_snd_retry_th_ms(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_asio_snd_retry_th_ms() const;
  void _internal_set_asio_snd_retry_th_ms(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 socket_buffer_size = 13 [default = 262144];
  bool has_socket_buffer_size() const;
  private:
  bool _internal_has_socket_buffer_size() const;
  public:
  void clear_socket_buffer_size();
  ::PROTOBUF_NAMESPACE_ID::uint32 socket_buffer_size() const;
  void set_socket_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_socket_buffer_size() const;
  void _internal_set_socket_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional .robosense.proto_compress_msgs.RS_POST_DATA_COMPRESSION_FORMAT compress_format = 15 [default = RS_POST_DATA_COMPRESSION_NOTHING];
  bool has_compress_format() const;
  private:
  bool _internal_has_compress_format() const;
  public:
  void clear_compress_format();
  ::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT compress_format() const;
  void set_compress_format(::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT value);
  private:
  ::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT _internal_compress_format() const;
  void _internal_set_compress_format(::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT value);
  public:

  // @@protoc_insertion_point(class_scope:robosense.rs_hmi.config.UdpP2PEndPointConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr remote_ip_;
  ::robosense::rs_hmi::config::UdpControlConfig* udp_control_config_;
  ::PROTOBUF_NAMESPACE_ID::uint32 compress_level_;
  int role_type_;
  ::PROTOBUF_NAMESPACE_ID::uint32 remote_port_;
  ::PROTOBUF_NAMESPACE_ID::uint32 max_msg_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 buffer_cell_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 asio_run_thread_cnt_;
  ::PROTOBUF_NAMESPACE_ID::uint32 asio_snd_thread_cnt_;
  ::PROTOBUF_NAMESPACE_ID::uint32 asio_recv_thread_cnt_;
  ::PROTOBUF_NAMESPACE_ID::uint32 msg_timeout_th_ms_;
  ::PROTOBUF_NAMESPACE_ID::uint32 asio_snd_retry_cnt_;
  ::PROTOBUF_NAMESPACE_ID::uint32 asio_snd_retry_th_ms_;
  ::PROTOBUF_NAMESPACE_ID::uint32 socket_buffer_size_;
  int compress_format_;
  friend struct ::TableStruct_config_2eproto;
};
// -------------------------------------------------------------------

class WebsocketSendControlConfig PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.rs_hmi.config.WebsocketSendControlConfig) */ {
 public:
  inline WebsocketSendControlConfig() : WebsocketSendControlConfig(nullptr) {}
  virtual ~WebsocketSendControlConfig();

  WebsocketSendControlConfig(const WebsocketSendControlConfig& from);
  WebsocketSendControlConfig(WebsocketSendControlConfig&& from) noexcept
    : WebsocketSendControlConfig() {
    *this = ::std::move(from);
  }

  inline WebsocketSendControlConfig& operator=(const WebsocketSendControlConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline WebsocketSendControlConfig& operator=(WebsocketSendControlConfig&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const WebsocketSendControlConfig& default_instance();

  static inline const WebsocketSendControlConfig* internal_default_instance() {
    return reinterpret_cast<const WebsocketSendControlConfig*>(
               &_WebsocketSendControlConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(WebsocketSendControlConfig& a, WebsocketSendControlConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(WebsocketSendControlConfig* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(WebsocketSendControlConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline WebsocketSendControlConfig* New() const final {
    return CreateMaybeMessage<WebsocketSendControlConfig>(nullptr);
  }

  WebsocketSendControlConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<WebsocketSendControlConfig>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const WebsocketSendControlConfig& from);
  void MergeFrom(const WebsocketSendControlConfig& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WebsocketSendControlConfig* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.rs_hmi.config.WebsocketSendControlConfig";
  }
  protected:
  explicit WebsocketSendControlConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_config_2eproto);
    return ::descriptor_table_config_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMessageTypeFieldNumber = 1,
    kLossBufferSizeFieldNumber = 3,
    kLossGapSizeFieldNumber = 4,
    kEnableLossFieldNumber = 2,
    kEnableLoss2FieldNumber = 5,
    kEnableClearFieldNumber = 8,
    kLossBufferSize2FieldNumber = 6,
    kLossGapSize2FieldNumber = 7,
    kClearBufferSizeFieldNumber = 9,
  };
  // optional string message_type = 1;
  bool has_message_type() const;
  private:
  bool _internal_has_message_type() const;
  public:
  void clear_message_type();
  const std::string& message_type() const;
  void set_message_type(const std::string& value);
  void set_message_type(std::string&& value);
  void set_message_type(const char* value);
  void set_message_type(const char* value, size_t size);
  std::string* mutable_message_type();
  std::string* release_message_type();
  void set_allocated_message_type(std::string* message_type);
  private:
  const std::string& _internal_message_type() const;
  void _internal_set_message_type(const std::string& value);
  std::string* _internal_mutable_message_type();
  public:

  // optional uint32 loss_buffer_size = 3 [default = 10];
  bool has_loss_buffer_size() const;
  private:
  bool _internal_has_loss_buffer_size() const;
  public:
  void clear_loss_buffer_size();
  ::PROTOBUF_NAMESPACE_ID::uint32 loss_buffer_size() const;
  void set_loss_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_loss_buffer_size() const;
  void _internal_set_loss_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 loss_gap_size = 4 [default = 2];
  bool has_loss_gap_size() const;
  private:
  bool _internal_has_loss_gap_size() const;
  public:
  void clear_loss_gap_size();
  ::PROTOBUF_NAMESPACE_ID::uint32 loss_gap_size() const;
  void set_loss_gap_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_loss_gap_size() const;
  void _internal_set_loss_gap_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional bool enable_loss = 2 [default = true];
  bool has_enable_loss() const;
  private:
  bool _internal_has_enable_loss() const;
  public:
  void clear_enable_loss();
  bool enable_loss() const;
  void set_enable_loss(bool value);
  private:
  bool _internal_enable_loss() const;
  void _internal_set_enable_loss(bool value);
  public:

  // optional bool enable_loss2 = 5 [default = true];
  bool has_enable_loss2() const;
  private:
  bool _internal_has_enable_loss2() const;
  public:
  void clear_enable_loss2();
  bool enable_loss2() const;
  void set_enable_loss2(bool value);
  private:
  bool _internal_enable_loss2() const;
  void _internal_set_enable_loss2(bool value);
  public:

  // optional bool enable_clear = 8 [default = true];
  bool has_enable_clear() const;
  private:
  bool _internal_has_enable_clear() const;
  public:
  void clear_enable_clear();
  bool enable_clear() const;
  void set_enable_clear(bool value);
  private:
  bool _internal_enable_clear() const;
  void _internal_set_enable_clear(bool value);
  public:

  // optional uint32 loss_buffer_size2 = 6 [default = 15];
  bool has_loss_buffer_size2() const;
  private:
  bool _internal_has_loss_buffer_size2() const;
  public:
  void clear_loss_buffer_size2();
  ::PROTOBUF_NAMESPACE_ID::uint32 loss_buffer_size2() const;
  void set_loss_buffer_size2(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_loss_buffer_size2() const;
  void _internal_set_loss_buffer_size2(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 loss_gap_size2 = 7 [default = 3];
  bool has_loss_gap_size2() const;
  private:
  bool _internal_has_loss_gap_size2() const;
  public:
  void clear_loss_gap_size2();
  ::PROTOBUF_NAMESPACE_ID::uint32 loss_gap_size2() const;
  void set_loss_gap_size2(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_loss_gap_size2() const;
  void _internal_set_loss_gap_size2(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 clear_buffer_size = 9 [default = 20];
  bool has_clear_buffer_size() const;
  private:
  bool _internal_has_clear_buffer_size() const;
  public:
  void clear_clear_buffer_size();
  ::PROTOBUF_NAMESPACE_ID::uint32 clear_buffer_size() const;
  void set_clear_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_clear_buffer_size() const;
  void _internal_set_clear_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:robosense.rs_hmi.config.WebsocketSendControlConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr message_type_;
  ::PROTOBUF_NAMESPACE_ID::uint32 loss_buffer_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 loss_gap_size_;
  bool enable_loss_;
  bool enable_loss2_;
  bool enable_clear_;
  ::PROTOBUF_NAMESPACE_ID::uint32 loss_buffer_size2_;
  ::PROTOBUF_NAMESPACE_ID::uint32 loss_gap_size2_;
  ::PROTOBUF_NAMESPACE_ID::uint32 clear_buffer_size_;
  friend struct ::TableStruct_config_2eproto;
};
// -------------------------------------------------------------------

class WebsocketTaskConfig PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.rs_hmi.config.WebsocketTaskConfig) */ {
 public:
  inline WebsocketTaskConfig() : WebsocketTaskConfig(nullptr) {}
  virtual ~WebsocketTaskConfig();

  WebsocketTaskConfig(const WebsocketTaskConfig& from);
  WebsocketTaskConfig(WebsocketTaskConfig&& from) noexcept
    : WebsocketTaskConfig() {
    *this = ::std::move(from);
  }

  inline WebsocketTaskConfig& operator=(const WebsocketTaskConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline WebsocketTaskConfig& operator=(WebsocketTaskConfig&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const WebsocketTaskConfig& default_instance();

  static inline const WebsocketTaskConfig* internal_default_instance() {
    return reinterpret_cast<const WebsocketTaskConfig*>(
               &_WebsocketTaskConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(WebsocketTaskConfig& a, WebsocketTaskConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(WebsocketTaskConfig* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(WebsocketTaskConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline WebsocketTaskConfig* New() const final {
    return CreateMaybeMessage<WebsocketTaskConfig>(nullptr);
  }

  WebsocketTaskConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<WebsocketTaskConfig>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const WebsocketTaskConfig& from);
  void MergeFrom(const WebsocketTaskConfig& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WebsocketTaskConfig* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.rs_hmi.config.WebsocketTaskConfig";
  }
  protected:
  explicit WebsocketTaskConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_config_2eproto);
    return ::descriptor_table_config_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kWebsocketSendControlsFieldNumber = 3,
    kTaskSocketKeyFieldNumber = 1,
    kWebsocketEndpointFieldNumber = 2,
  };
  // repeated .robosense.rs_hmi.config.WebsocketSendControlConfig websocket_send_controls = 3;
  int websocket_send_controls_size() const;
  private:
  int _internal_websocket_send_controls_size() const;
  public:
  void clear_websocket_send_controls();
  ::robosense::rs_hmi::config::WebsocketSendControlConfig* mutable_websocket_send_controls(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::robosense::rs_hmi::config::WebsocketSendControlConfig >*
      mutable_websocket_send_controls();
  private:
  const ::robosense::rs_hmi::config::WebsocketSendControlConfig& _internal_websocket_send_controls(int index) const;
  ::robosense::rs_hmi::config::WebsocketSendControlConfig* _internal_add_websocket_send_controls();
  public:
  const ::robosense::rs_hmi::config::WebsocketSendControlConfig& websocket_send_controls(int index) const;
  ::robosense::rs_hmi::config::WebsocketSendControlConfig* add_websocket_send_controls();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::robosense::rs_hmi::config::WebsocketSendControlConfig >&
      websocket_send_controls() const;

  // optional string task_socket_key = 1;
  bool has_task_socket_key() const;
  private:
  bool _internal_has_task_socket_key() const;
  public:
  void clear_task_socket_key();
  const std::string& task_socket_key() const;
  void set_task_socket_key(const std::string& value);
  void set_task_socket_key(std::string&& value);
  void set_task_socket_key(const char* value);
  void set_task_socket_key(const char* value, size_t size);
  std::string* mutable_task_socket_key();
  std::string* release_task_socket_key();
  void set_allocated_task_socket_key(std::string* task_socket_key);
  private:
  const std::string& _internal_task_socket_key() const;
  void _internal_set_task_socket_key(const std::string& value);
  std::string* _internal_mutable_task_socket_key();
  public:

  // optional .robosense.rs_hmi.config.WebsocketEndPointConfig websocket_endpoint = 2;
  bool has_websocket_endpoint() const;
  private:
  bool _internal_has_websocket_endpoint() const;
  public:
  void clear_websocket_endpoint();
  const ::robosense::rs_hmi::config::WebsocketEndPointConfig& websocket_endpoint() const;
  ::robosense::rs_hmi::config::WebsocketEndPointConfig* release_websocket_endpoint();
  ::robosense::rs_hmi::config::WebsocketEndPointConfig* mutable_websocket_endpoint();
  void set_allocated_websocket_endpoint(::robosense::rs_hmi::config::WebsocketEndPointConfig* websocket_endpoint);
  private:
  const ::robosense::rs_hmi::config::WebsocketEndPointConfig& _internal_websocket_endpoint() const;
  ::robosense::rs_hmi::config::WebsocketEndPointConfig* _internal_mutable_websocket_endpoint();
  public:
  void unsafe_arena_set_allocated_websocket_endpoint(
      ::robosense::rs_hmi::config::WebsocketEndPointConfig* websocket_endpoint);
  ::robosense::rs_hmi::config::WebsocketEndPointConfig* unsafe_arena_release_websocket_endpoint();

  // @@protoc_insertion_point(class_scope:robosense.rs_hmi.config.WebsocketTaskConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::robosense::rs_hmi::config::WebsocketSendControlConfig > websocket_send_controls_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr task_socket_key_;
  ::robosense::rs_hmi::config::WebsocketEndPointConfig* websocket_endpoint_;
  friend struct ::TableStruct_config_2eproto;
};
// -------------------------------------------------------------------

class UdpMulticastTaskConfig PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.rs_hmi.config.UdpMulticastTaskConfig) */ {
 public:
  inline UdpMulticastTaskConfig() : UdpMulticastTaskConfig(nullptr) {}
  virtual ~UdpMulticastTaskConfig();

  UdpMulticastTaskConfig(const UdpMulticastTaskConfig& from);
  UdpMulticastTaskConfig(UdpMulticastTaskConfig&& from) noexcept
    : UdpMulticastTaskConfig() {
    *this = ::std::move(from);
  }

  inline UdpMulticastTaskConfig& operator=(const UdpMulticastTaskConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline UdpMulticastTaskConfig& operator=(UdpMulticastTaskConfig&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const UdpMulticastTaskConfig& default_instance();

  static inline const UdpMulticastTaskConfig* internal_default_instance() {
    return reinterpret_cast<const UdpMulticastTaskConfig*>(
               &_UdpMulticastTaskConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(UdpMulticastTaskConfig& a, UdpMulticastTaskConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(UdpMulticastTaskConfig* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UdpMulticastTaskConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline UdpMulticastTaskConfig* New() const final {
    return CreateMaybeMessage<UdpMulticastTaskConfig>(nullptr);
  }

  UdpMulticastTaskConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<UdpMulticastTaskConfig>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const UdpMulticastTaskConfig& from);
  void MergeFrom(const UdpMulticastTaskConfig& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UdpMulticastTaskConfig* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.rs_hmi.config.UdpMulticastTaskConfig";
  }
  protected:
  explicit UdpMulticastTaskConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_config_2eproto);
    return ::descriptor_table_config_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTaskSocketKeyFieldNumber = 1,
    kUdpMulticastEndpointFieldNumber = 2,
  };
  // optional string task_socket_key = 1;
  bool has_task_socket_key() const;
  private:
  bool _internal_has_task_socket_key() const;
  public:
  void clear_task_socket_key();
  const std::string& task_socket_key() const;
  void set_task_socket_key(const std::string& value);
  void set_task_socket_key(std::string&& value);
  void set_task_socket_key(const char* value);
  void set_task_socket_key(const char* value, size_t size);
  std::string* mutable_task_socket_key();
  std::string* release_task_socket_key();
  void set_allocated_task_socket_key(std::string* task_socket_key);
  private:
  const std::string& _internal_task_socket_key() const;
  void _internal_set_task_socket_key(const std::string& value);
  std::string* _internal_mutable_task_socket_key();
  public:

  // optional .robosense.rs_hmi.config.UdpMulticastEndPointConfig udp_multicast_endpoint = 2;
  bool has_udp_multicast_endpoint() const;
  private:
  bool _internal_has_udp_multicast_endpoint() const;
  public:
  void clear_udp_multicast_endpoint();
  const ::robosense::rs_hmi::config::UdpMulticastEndPointConfig& udp_multicast_endpoint() const;
  ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* release_udp_multicast_endpoint();
  ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* mutable_udp_multicast_endpoint();
  void set_allocated_udp_multicast_endpoint(::robosense::rs_hmi::config::UdpMulticastEndPointConfig* udp_multicast_endpoint);
  private:
  const ::robosense::rs_hmi::config::UdpMulticastEndPointConfig& _internal_udp_multicast_endpoint() const;
  ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* _internal_mutable_udp_multicast_endpoint();
  public:
  void unsafe_arena_set_allocated_udp_multicast_endpoint(
      ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* udp_multicast_endpoint);
  ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* unsafe_arena_release_udp_multicast_endpoint();

  // @@protoc_insertion_point(class_scope:robosense.rs_hmi.config.UdpMulticastTaskConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr task_socket_key_;
  ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* udp_multicast_endpoint_;
  friend struct ::TableStruct_config_2eproto;
};
// -------------------------------------------------------------------

class UdpP2PTaskConfig PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.rs_hmi.config.UdpP2PTaskConfig) */ {
 public:
  inline UdpP2PTaskConfig() : UdpP2PTaskConfig(nullptr) {}
  virtual ~UdpP2PTaskConfig();

  UdpP2PTaskConfig(const UdpP2PTaskConfig& from);
  UdpP2PTaskConfig(UdpP2PTaskConfig&& from) noexcept
    : UdpP2PTaskConfig() {
    *this = ::std::move(from);
  }

  inline UdpP2PTaskConfig& operator=(const UdpP2PTaskConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline UdpP2PTaskConfig& operator=(UdpP2PTaskConfig&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const UdpP2PTaskConfig& default_instance();

  static inline const UdpP2PTaskConfig* internal_default_instance() {
    return reinterpret_cast<const UdpP2PTaskConfig*>(
               &_UdpP2PTaskConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(UdpP2PTaskConfig& a, UdpP2PTaskConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(UdpP2PTaskConfig* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UdpP2PTaskConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline UdpP2PTaskConfig* New() const final {
    return CreateMaybeMessage<UdpP2PTaskConfig>(nullptr);
  }

  UdpP2PTaskConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<UdpP2PTaskConfig>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const UdpP2PTaskConfig& from);
  void MergeFrom(const UdpP2PTaskConfig& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UdpP2PTaskConfig* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.rs_hmi.config.UdpP2PTaskConfig";
  }
  protected:
  explicit UdpP2PTaskConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_config_2eproto);
    return ::descriptor_table_config_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTaskSocketKeyFieldNumber = 1,
    kUdpP2PEndpointFieldNumber = 2,
  };
  // optional string task_socket_key = 1;
  bool has_task_socket_key() const;
  private:
  bool _internal_has_task_socket_key() const;
  public:
  void clear_task_socket_key();
  const std::string& task_socket_key() const;
  void set_task_socket_key(const std::string& value);
  void set_task_socket_key(std::string&& value);
  void set_task_socket_key(const char* value);
  void set_task_socket_key(const char* value, size_t size);
  std::string* mutable_task_socket_key();
  std::string* release_task_socket_key();
  void set_allocated_task_socket_key(std::string* task_socket_key);
  private:
  const std::string& _internal_task_socket_key() const;
  void _internal_set_task_socket_key(const std::string& value);
  std::string* _internal_mutable_task_socket_key();
  public:

  // optional .robosense.rs_hmi.config.UdpP2PEndPointConfig udp_p2p_endpoint = 2;
  bool has_udp_p2p_endpoint() const;
  private:
  bool _internal_has_udp_p2p_endpoint() const;
  public:
  void clear_udp_p2p_endpoint();
  const ::robosense::rs_hmi::config::UdpP2PEndPointConfig& udp_p2p_endpoint() const;
  ::robosense::rs_hmi::config::UdpP2PEndPointConfig* release_udp_p2p_endpoint();
  ::robosense::rs_hmi::config::UdpP2PEndPointConfig* mutable_udp_p2p_endpoint();
  void set_allocated_udp_p2p_endpoint(::robosense::rs_hmi::config::UdpP2PEndPointConfig* udp_p2p_endpoint);
  private:
  const ::robosense::rs_hmi::config::UdpP2PEndPointConfig& _internal_udp_p2p_endpoint() const;
  ::robosense::rs_hmi::config::UdpP2PEndPointConfig* _internal_mutable_udp_p2p_endpoint();
  public:
  void unsafe_arena_set_allocated_udp_p2p_endpoint(
      ::robosense::rs_hmi::config::UdpP2PEndPointConfig* udp_p2p_endpoint);
  ::robosense::rs_hmi::config::UdpP2PEndPointConfig* unsafe_arena_release_udp_p2p_endpoint();

  // @@protoc_insertion_point(class_scope:robosense.rs_hmi.config.UdpP2PTaskConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr task_socket_key_;
  ::robosense::rs_hmi::config::UdpP2PEndPointConfig* udp_p2p_endpoint_;
  friend struct ::TableStruct_config_2eproto;
};
// -------------------------------------------------------------------

class SocketTaskConfig PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.rs_hmi.config.SocketTaskConfig) */ {
 public:
  inline SocketTaskConfig() : SocketTaskConfig(nullptr) {}
  virtual ~SocketTaskConfig();

  SocketTaskConfig(const SocketTaskConfig& from);
  SocketTaskConfig(SocketTaskConfig&& from) noexcept
    : SocketTaskConfig() {
    *this = ::std::move(from);
  }

  inline SocketTaskConfig& operator=(const SocketTaskConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline SocketTaskConfig& operator=(SocketTaskConfig&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SocketTaskConfig& default_instance();

  enum SocketEndpointConfigCase {
    kWebsocketTask = 1,
    kUdpMulticastTask = 2,
    kUdpP2PTask = 3,
    SOCKET_ENDPOINT_CONFIG_NOT_SET = 0,
  };

  static inline const SocketTaskConfig* internal_default_instance() {
    return reinterpret_cast<const SocketTaskConfig*>(
               &_SocketTaskConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(SocketTaskConfig& a, SocketTaskConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(SocketTaskConfig* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SocketTaskConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SocketTaskConfig* New() const final {
    return CreateMaybeMessage<SocketTaskConfig>(nullptr);
  }

  SocketTaskConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SocketTaskConfig>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SocketTaskConfig& from);
  void MergeFrom(const SocketTaskConfig& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SocketTaskConfig* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.rs_hmi.config.SocketTaskConfig";
  }
  protected:
  explicit SocketTaskConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_config_2eproto);
    return ::descriptor_table_config_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kWebsocketTaskFieldNumber = 1,
    kUdpMulticastTaskFieldNumber = 2,
    kUdpP2PTaskFieldNumber = 3,
  };
  // .robosense.rs_hmi.config.WebsocketTaskConfig websocket_task = 1;
  bool has_websocket_task() const;
  private:
  bool _internal_has_websocket_task() const;
  public:
  void clear_websocket_task();
  const ::robosense::rs_hmi::config::WebsocketTaskConfig& websocket_task() const;
  ::robosense::rs_hmi::config::WebsocketTaskConfig* release_websocket_task();
  ::robosense::rs_hmi::config::WebsocketTaskConfig* mutable_websocket_task();
  void set_allocated_websocket_task(::robosense::rs_hmi::config::WebsocketTaskConfig* websocket_task);
  private:
  const ::robosense::rs_hmi::config::WebsocketTaskConfig& _internal_websocket_task() const;
  ::robosense::rs_hmi::config::WebsocketTaskConfig* _internal_mutable_websocket_task();
  public:
  void unsafe_arena_set_allocated_websocket_task(
      ::robosense::rs_hmi::config::WebsocketTaskConfig* websocket_task);
  ::robosense::rs_hmi::config::WebsocketTaskConfig* unsafe_arena_release_websocket_task();

  // .robosense.rs_hmi.config.UdpMulticastTaskConfig udp_multicast_task = 2;
  bool has_udp_multicast_task() const;
  private:
  bool _internal_has_udp_multicast_task() const;
  public:
  void clear_udp_multicast_task();
  const ::robosense::rs_hmi::config::UdpMulticastTaskConfig& udp_multicast_task() const;
  ::robosense::rs_hmi::config::UdpMulticastTaskConfig* release_udp_multicast_task();
  ::robosense::rs_hmi::config::UdpMulticastTaskConfig* mutable_udp_multicast_task();
  void set_allocated_udp_multicast_task(::robosense::rs_hmi::config::UdpMulticastTaskConfig* udp_multicast_task);
  private:
  const ::robosense::rs_hmi::config::UdpMulticastTaskConfig& _internal_udp_multicast_task() const;
  ::robosense::rs_hmi::config::UdpMulticastTaskConfig* _internal_mutable_udp_multicast_task();
  public:
  void unsafe_arena_set_allocated_udp_multicast_task(
      ::robosense::rs_hmi::config::UdpMulticastTaskConfig* udp_multicast_task);
  ::robosense::rs_hmi::config::UdpMulticastTaskConfig* unsafe_arena_release_udp_multicast_task();

  // .robosense.rs_hmi.config.UdpP2PTaskConfig udp_p2p_task = 3;
  bool has_udp_p2p_task() const;
  private:
  bool _internal_has_udp_p2p_task() const;
  public:
  void clear_udp_p2p_task();
  const ::robosense::rs_hmi::config::UdpP2PTaskConfig& udp_p2p_task() const;
  ::robosense::rs_hmi::config::UdpP2PTaskConfig* release_udp_p2p_task();
  ::robosense::rs_hmi::config::UdpP2PTaskConfig* mutable_udp_p2p_task();
  void set_allocated_udp_p2p_task(::robosense::rs_hmi::config::UdpP2PTaskConfig* udp_p2p_task);
  private:
  const ::robosense::rs_hmi::config::UdpP2PTaskConfig& _internal_udp_p2p_task() const;
  ::robosense::rs_hmi::config::UdpP2PTaskConfig* _internal_mutable_udp_p2p_task();
  public:
  void unsafe_arena_set_allocated_udp_p2p_task(
      ::robosense::rs_hmi::config::UdpP2PTaskConfig* udp_p2p_task);
  ::robosense::rs_hmi::config::UdpP2PTaskConfig* unsafe_arena_release_udp_p2p_task();

  void clear_socket_endpoint_config();
  SocketEndpointConfigCase socket_endpoint_config_case() const;
  // @@protoc_insertion_point(class_scope:robosense.rs_hmi.config.SocketTaskConfig)
 private:
  class _Internal;
  void set_has_websocket_task();
  void set_has_udp_multicast_task();
  void set_has_udp_p2p_task();

  inline bool has_socket_endpoint_config() const;
  inline void clear_has_socket_endpoint_config();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  union SocketEndpointConfigUnion {
    SocketEndpointConfigUnion() {}
    ::robosense::rs_hmi::config::WebsocketTaskConfig* websocket_task_;
    ::robosense::rs_hmi::config::UdpMulticastTaskConfig* udp_multicast_task_;
    ::robosense::rs_hmi::config::UdpP2PTaskConfig* udp_p2p_task_;
  } socket_endpoint_config_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[1];

  friend struct ::TableStruct_config_2eproto;
};
// -------------------------------------------------------------------

class HmiSocketTaskConfig PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.rs_hmi.config.HmiSocketTaskConfig) */ {
 public:
  inline HmiSocketTaskConfig() : HmiSocketTaskConfig(nullptr) {}
  virtual ~HmiSocketTaskConfig();

  HmiSocketTaskConfig(const HmiSocketTaskConfig& from);
  HmiSocketTaskConfig(HmiSocketTaskConfig&& from) noexcept
    : HmiSocketTaskConfig() {
    *this = ::std::move(from);
  }

  inline HmiSocketTaskConfig& operator=(const HmiSocketTaskConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline HmiSocketTaskConfig& operator=(HmiSocketTaskConfig&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const HmiSocketTaskConfig& default_instance();

  static inline const HmiSocketTaskConfig* internal_default_instance() {
    return reinterpret_cast<const HmiSocketTaskConfig*>(
               &_HmiSocketTaskConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(HmiSocketTaskConfig& a, HmiSocketTaskConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(HmiSocketTaskConfig* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HmiSocketTaskConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline HmiSocketTaskConfig* New() const final {
    return CreateMaybeMessage<HmiSocketTaskConfig>(nullptr);
  }

  HmiSocketTaskConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<HmiSocketTaskConfig>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const HmiSocketTaskConfig& from);
  void MergeFrom(const HmiSocketTaskConfig& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HmiSocketTaskConfig* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.rs_hmi.config.HmiSocketTaskConfig";
  }
  protected:
  explicit HmiSocketTaskConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_config_2eproto);
    return ::descriptor_table_config_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSocketTaskConfigsFieldNumber = 1,
  };
  // repeated .robosense.rs_hmi.config.SocketTaskConfig socket_task_configs = 1;
  int socket_task_configs_size() const;
  private:
  int _internal_socket_task_configs_size() const;
  public:
  void clear_socket_task_configs();
  ::robosense::rs_hmi::config::SocketTaskConfig* mutable_socket_task_configs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::robosense::rs_hmi::config::SocketTaskConfig >*
      mutable_socket_task_configs();
  private:
  const ::robosense::rs_hmi::config::SocketTaskConfig& _internal_socket_task_configs(int index) const;
  ::robosense::rs_hmi::config::SocketTaskConfig* _internal_add_socket_task_configs();
  public:
  const ::robosense::rs_hmi::config::SocketTaskConfig& socket_task_configs(int index) const;
  ::robosense::rs_hmi::config::SocketTaskConfig* add_socket_task_configs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::robosense::rs_hmi::config::SocketTaskConfig >&
      socket_task_configs() const;

  // @@protoc_insertion_point(class_scope:robosense.rs_hmi.config.HmiSocketTaskConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::robosense::rs_hmi::config::SocketTaskConfig > socket_task_configs_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_config_2eproto;
};
// -------------------------------------------------------------------

class HmiSocketTopicTaskConfig PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.rs_hmi.config.HmiSocketTopicTaskConfig) */ {
 public:
  inline HmiSocketTopicTaskConfig() : HmiSocketTopicTaskConfig(nullptr) {}
  virtual ~HmiSocketTopicTaskConfig();

  HmiSocketTopicTaskConfig(const HmiSocketTopicTaskConfig& from);
  HmiSocketTopicTaskConfig(HmiSocketTopicTaskConfig&& from) noexcept
    : HmiSocketTopicTaskConfig() {
    *this = ::std::move(from);
  }

  inline HmiSocketTopicTaskConfig& operator=(const HmiSocketTopicTaskConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline HmiSocketTopicTaskConfig& operator=(HmiSocketTopicTaskConfig&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const HmiSocketTopicTaskConfig& default_instance();

  static inline const HmiSocketTopicTaskConfig* internal_default_instance() {
    return reinterpret_cast<const HmiSocketTopicTaskConfig*>(
               &_HmiSocketTopicTaskConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(HmiSocketTopicTaskConfig& a, HmiSocketTopicTaskConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(HmiSocketTopicTaskConfig* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HmiSocketTopicTaskConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline HmiSocketTopicTaskConfig* New() const final {
    return CreateMaybeMessage<HmiSocketTopicTaskConfig>(nullptr);
  }

  HmiSocketTopicTaskConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<HmiSocketTopicTaskConfig>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const HmiSocketTopicTaskConfig& from);
  void MergeFrom(const HmiSocketTopicTaskConfig& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HmiSocketTopicTaskConfig* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.rs_hmi.config.HmiSocketTopicTaskConfig";
  }
  protected:
  explicit HmiSocketTopicTaskConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_config_2eproto);
    return ::descriptor_table_config_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSupportMessageTopicsFieldNumber = 1,
    kTaskSocketKeyFieldNumber = 2,
  };
  // repeated string support_message_topics = 1;
  int support_message_topics_size() const;
  private:
  int _internal_support_message_topics_size() const;
  public:
  void clear_support_message_topics();
  const std::string& support_message_topics(int index) const;
  std::string* mutable_support_message_topics(int index);
  void set_support_message_topics(int index, const std::string& value);
  void set_support_message_topics(int index, std::string&& value);
  void set_support_message_topics(int index, const char* value);
  void set_support_message_topics(int index, const char* value, size_t size);
  std::string* add_support_message_topics();
  void add_support_message_topics(const std::string& value);
  void add_support_message_topics(std::string&& value);
  void add_support_message_topics(const char* value);
  void add_support_message_topics(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& support_message_topics() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_support_message_topics();
  private:
  const std::string& _internal_support_message_topics(int index) const;
  std::string* _internal_add_support_message_topics();
  public:

  // optional string task_socket_key = 2;
  bool has_task_socket_key() const;
  private:
  bool _internal_has_task_socket_key() const;
  public:
  void clear_task_socket_key();
  const std::string& task_socket_key() const;
  void set_task_socket_key(const std::string& value);
  void set_task_socket_key(std::string&& value);
  void set_task_socket_key(const char* value);
  void set_task_socket_key(const char* value, size_t size);
  std::string* mutable_task_socket_key();
  std::string* release_task_socket_key();
  void set_allocated_task_socket_key(std::string* task_socket_key);
  private:
  const std::string& _internal_task_socket_key() const;
  void _internal_set_task_socket_key(const std::string& value);
  std::string* _internal_mutable_task_socket_key();
  public:

  // @@protoc_insertion_point(class_scope:robosense.rs_hmi.config.HmiSocketTopicTaskConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> support_message_topics_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr task_socket_key_;
  friend struct ::TableStruct_config_2eproto;
};
// -------------------------------------------------------------------

class HmiTaskSocketConfig PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.rs_hmi.config.HmiTaskSocketConfig) */ {
 public:
  inline HmiTaskSocketConfig() : HmiTaskSocketConfig(nullptr) {}
  virtual ~HmiTaskSocketConfig();

  HmiTaskSocketConfig(const HmiTaskSocketConfig& from);
  HmiTaskSocketConfig(HmiTaskSocketConfig&& from) noexcept
    : HmiTaskSocketConfig() {
    *this = ::std::move(from);
  }

  inline HmiTaskSocketConfig& operator=(const HmiTaskSocketConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline HmiTaskSocketConfig& operator=(HmiTaskSocketConfig&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const HmiTaskSocketConfig& default_instance();

  static inline const HmiTaskSocketConfig* internal_default_instance() {
    return reinterpret_cast<const HmiTaskSocketConfig*>(
               &_HmiTaskSocketConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(HmiTaskSocketConfig& a, HmiTaskSocketConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(HmiTaskSocketConfig* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HmiTaskSocketConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline HmiTaskSocketConfig* New() const final {
    return CreateMaybeMessage<HmiTaskSocketConfig>(nullptr);
  }

  HmiTaskSocketConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<HmiTaskSocketConfig>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const HmiTaskSocketConfig& from);
  void MergeFrom(const HmiTaskSocketConfig& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HmiTaskSocketConfig* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.rs_hmi.config.HmiTaskSocketConfig";
  }
  protected:
  explicit HmiTaskSocketConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_config_2eproto);
    return ::descriptor_table_config_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTaskSocketConfigsFieldNumber = 2,
    kEnableTaskFieldNumber = 1,
  };
  // repeated .robosense.rs_hmi.config.HmiSocketTopicTaskConfig task_socket_configs = 2;
  int task_socket_configs_size() const;
  private:
  int _internal_task_socket_configs_size() const;
  public:
  void clear_task_socket_configs();
  ::robosense::rs_hmi::config::HmiSocketTopicTaskConfig* mutable_task_socket_configs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::robosense::rs_hmi::config::HmiSocketTopicTaskConfig >*
      mutable_task_socket_configs();
  private:
  const ::robosense::rs_hmi::config::HmiSocketTopicTaskConfig& _internal_task_socket_configs(int index) const;
  ::robosense::rs_hmi::config::HmiSocketTopicTaskConfig* _internal_add_task_socket_configs();
  public:
  const ::robosense::rs_hmi::config::HmiSocketTopicTaskConfig& task_socket_configs(int index) const;
  ::robosense::rs_hmi::config::HmiSocketTopicTaskConfig* add_task_socket_configs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::robosense::rs_hmi::config::HmiSocketTopicTaskConfig >&
      task_socket_configs() const;

  // optional bool enable_task = 1 [default = false];
  bool has_enable_task() const;
  private:
  bool _internal_has_enable_task() const;
  public:
  void clear_enable_task();
  bool enable_task() const;
  void set_enable_task(bool value);
  private:
  bool _internal_enable_task() const;
  void _internal_set_enable_task(bool value);
  public:

  // @@protoc_insertion_point(class_scope:robosense.rs_hmi.config.HmiTaskSocketConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::robosense::rs_hmi::config::HmiSocketTopicTaskConfig > task_socket_configs_;
  bool enable_task_;
  friend struct ::TableStruct_config_2eproto;
};
// -------------------------------------------------------------------

class WebsocketRenderTaskConfig PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.rs_hmi.config.WebsocketRenderTaskConfig) */ {
 public:
  inline WebsocketRenderTaskConfig() : WebsocketRenderTaskConfig(nullptr) {}
  virtual ~WebsocketRenderTaskConfig();

  WebsocketRenderTaskConfig(const WebsocketRenderTaskConfig& from);
  WebsocketRenderTaskConfig(WebsocketRenderTaskConfig&& from) noexcept
    : WebsocketRenderTaskConfig() {
    *this = ::std::move(from);
  }

  inline WebsocketRenderTaskConfig& operator=(const WebsocketRenderTaskConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline WebsocketRenderTaskConfig& operator=(WebsocketRenderTaskConfig&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const WebsocketRenderTaskConfig& default_instance();

  static inline const WebsocketRenderTaskConfig* internal_default_instance() {
    return reinterpret_cast<const WebsocketRenderTaskConfig*>(
               &_WebsocketRenderTaskConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(WebsocketRenderTaskConfig& a, WebsocketRenderTaskConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(WebsocketRenderTaskConfig* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(WebsocketRenderTaskConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline WebsocketRenderTaskConfig* New() const final {
    return CreateMaybeMessage<WebsocketRenderTaskConfig>(nullptr);
  }

  WebsocketRenderTaskConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<WebsocketRenderTaskConfig>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const WebsocketRenderTaskConfig& from);
  void MergeFrom(const WebsocketRenderTaskConfig& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WebsocketRenderTaskConfig* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.rs_hmi.config.WebsocketRenderTaskConfig";
  }
  protected:
  explicit WebsocketRenderTaskConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_config_2eproto);
    return ::descriptor_table_config_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kWebsocketCmdSocketKeyFieldNumber = 5,
    kWebsocketSocketKeyFieldNumber = 6,
    kUdpMulticastSocketKeyFieldNumber = 7,
    kUdpDoubleMulticastSocketKeyFieldNumber = 8,
    kUdpP2PSocketKeyFieldNumber = 9,
    kWebsocketTaskFieldNumber = 1,
    kUdpMulticastRenderConfigFieldNumber = 14,
    kUdpP2PRenderConfigFieldNumber = 15,
    kDoubleUdpMulticastRenderConfigFieldNumber = 16,
    kMaxSendRenderBufferSizeFieldNumber = 13,
    kEnableWebsocketBufferFieldNumber = 2,
    kWebsocketBufferSizeFieldNumber = 3,
    kLowWebsocketBufferSizeFieldNumber = 4,
    kUdp2Sample1BufferSizeFieldNumber = 10,
    kUdp3Sample1BufferSizeFieldNumber = 11,
    kUdpLossAllBufferSizeFieldNumber = 12,
  };
  // optional string websocket_cmd_socket_key = 5;
  bool has_websocket_cmd_socket_key() const;
  private:
  bool _internal_has_websocket_cmd_socket_key() const;
  public:
  void clear_websocket_cmd_socket_key();
  const std::string& websocket_cmd_socket_key() const;
  void set_websocket_cmd_socket_key(const std::string& value);
  void set_websocket_cmd_socket_key(std::string&& value);
  void set_websocket_cmd_socket_key(const char* value);
  void set_websocket_cmd_socket_key(const char* value, size_t size);
  std::string* mutable_websocket_cmd_socket_key();
  std::string* release_websocket_cmd_socket_key();
  void set_allocated_websocket_cmd_socket_key(std::string* websocket_cmd_socket_key);
  private:
  const std::string& _internal_websocket_cmd_socket_key() const;
  void _internal_set_websocket_cmd_socket_key(const std::string& value);
  std::string* _internal_mutable_websocket_cmd_socket_key();
  public:

  // optional string websocket_socket_key = 6;
  bool has_websocket_socket_key() const;
  private:
  bool _internal_has_websocket_socket_key() const;
  public:
  void clear_websocket_socket_key();
  const std::string& websocket_socket_key() const;
  void set_websocket_socket_key(const std::string& value);
  void set_websocket_socket_key(std::string&& value);
  void set_websocket_socket_key(const char* value);
  void set_websocket_socket_key(const char* value, size_t size);
  std::string* mutable_websocket_socket_key();
  std::string* release_websocket_socket_key();
  void set_allocated_websocket_socket_key(std::string* websocket_socket_key);
  private:
  const std::string& _internal_websocket_socket_key() const;
  void _internal_set_websocket_socket_key(const std::string& value);
  std::string* _internal_mutable_websocket_socket_key();
  public:

  // optional string udp_multicast_socket_key = 7;
  bool has_udp_multicast_socket_key() const;
  private:
  bool _internal_has_udp_multicast_socket_key() const;
  public:
  void clear_udp_multicast_socket_key();
  const std::string& udp_multicast_socket_key() const;
  void set_udp_multicast_socket_key(const std::string& value);
  void set_udp_multicast_socket_key(std::string&& value);
  void set_udp_multicast_socket_key(const char* value);
  void set_udp_multicast_socket_key(const char* value, size_t size);
  std::string* mutable_udp_multicast_socket_key();
  std::string* release_udp_multicast_socket_key();
  void set_allocated_udp_multicast_socket_key(std::string* udp_multicast_socket_key);
  private:
  const std::string& _internal_udp_multicast_socket_key() const;
  void _internal_set_udp_multicast_socket_key(const std::string& value);
  std::string* _internal_mutable_udp_multicast_socket_key();
  public:

  // optional string udp_double_multicast_socket_key = 8;
  bool has_udp_double_multicast_socket_key() const;
  private:
  bool _internal_has_udp_double_multicast_socket_key() const;
  public:
  void clear_udp_double_multicast_socket_key();
  const std::string& udp_double_multicast_socket_key() const;
  void set_udp_double_multicast_socket_key(const std::string& value);
  void set_udp_double_multicast_socket_key(std::string&& value);
  void set_udp_double_multicast_socket_key(const char* value);
  void set_udp_double_multicast_socket_key(const char* value, size_t size);
  std::string* mutable_udp_double_multicast_socket_key();
  std::string* release_udp_double_multicast_socket_key();
  void set_allocated_udp_double_multicast_socket_key(std::string* udp_double_multicast_socket_key);
  private:
  const std::string& _internal_udp_double_multicast_socket_key() const;
  void _internal_set_udp_double_multicast_socket_key(const std::string& value);
  std::string* _internal_mutable_udp_double_multicast_socket_key();
  public:

  // optional string udp_p2p_socket_key = 9;
  bool has_udp_p2p_socket_key() const;
  private:
  bool _internal_has_udp_p2p_socket_key() const;
  public:
  void clear_udp_p2p_socket_key();
  const std::string& udp_p2p_socket_key() const;
  void set_udp_p2p_socket_key(const std::string& value);
  void set_udp_p2p_socket_key(std::string&& value);
  void set_udp_p2p_socket_key(const char* value);
  void set_udp_p2p_socket_key(const char* value, size_t size);
  std::string* mutable_udp_p2p_socket_key();
  std::string* release_udp_p2p_socket_key();
  void set_allocated_udp_p2p_socket_key(std::string* udp_p2p_socket_key);
  private:
  const std::string& _internal_udp_p2p_socket_key() const;
  void _internal_set_udp_p2p_socket_key(const std::string& value);
  std::string* _internal_mutable_udp_p2p_socket_key();
  public:

  // optional .robosense.rs_hmi.config.HmiTaskSocketConfig websocket_task = 1;
  bool has_websocket_task() const;
  private:
  bool _internal_has_websocket_task() const;
  public:
  void clear_websocket_task();
  const ::robosense::rs_hmi::config::HmiTaskSocketConfig& websocket_task() const;
  ::robosense::rs_hmi::config::HmiTaskSocketConfig* release_websocket_task();
  ::robosense::rs_hmi::config::HmiTaskSocketConfig* mutable_websocket_task();
  void set_allocated_websocket_task(::robosense::rs_hmi::config::HmiTaskSocketConfig* websocket_task);
  private:
  const ::robosense::rs_hmi::config::HmiTaskSocketConfig& _internal_websocket_task() const;
  ::robosense::rs_hmi::config::HmiTaskSocketConfig* _internal_mutable_websocket_task();
  public:
  void unsafe_arena_set_allocated_websocket_task(
      ::robosense::rs_hmi::config::HmiTaskSocketConfig* websocket_task);
  ::robosense::rs_hmi::config::HmiTaskSocketConfig* unsafe_arena_release_websocket_task();

  // optional .robosense.rs_hmi.config.UdpMulticastEndPointConfig udp_multicast_render_config = 14;
  bool has_udp_multicast_render_config() const;
  private:
  bool _internal_has_udp_multicast_render_config() const;
  public:
  void clear_udp_multicast_render_config();
  const ::robosense::rs_hmi::config::UdpMulticastEndPointConfig& udp_multicast_render_config() const;
  ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* release_udp_multicast_render_config();
  ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* mutable_udp_multicast_render_config();
  void set_allocated_udp_multicast_render_config(::robosense::rs_hmi::config::UdpMulticastEndPointConfig* udp_multicast_render_config);
  private:
  const ::robosense::rs_hmi::config::UdpMulticastEndPointConfig& _internal_udp_multicast_render_config() const;
  ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* _internal_mutable_udp_multicast_render_config();
  public:
  void unsafe_arena_set_allocated_udp_multicast_render_config(
      ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* udp_multicast_render_config);
  ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* unsafe_arena_release_udp_multicast_render_config();

  // optional .robosense.rs_hmi.config.UdpP2PEndPointConfig udp_p2p_render_config = 15;
  bool has_udp_p2p_render_config() const;
  private:
  bool _internal_has_udp_p2p_render_config() const;
  public:
  void clear_udp_p2p_render_config();
  const ::robosense::rs_hmi::config::UdpP2PEndPointConfig& udp_p2p_render_config() const;
  ::robosense::rs_hmi::config::UdpP2PEndPointConfig* release_udp_p2p_render_config();
  ::robosense::rs_hmi::config::UdpP2PEndPointConfig* mutable_udp_p2p_render_config();
  void set_allocated_udp_p2p_render_config(::robosense::rs_hmi::config::UdpP2PEndPointConfig* udp_p2p_render_config);
  private:
  const ::robosense::rs_hmi::config::UdpP2PEndPointConfig& _internal_udp_p2p_render_config() const;
  ::robosense::rs_hmi::config::UdpP2PEndPointConfig* _internal_mutable_udp_p2p_render_config();
  public:
  void unsafe_arena_set_allocated_udp_p2p_render_config(
      ::robosense::rs_hmi::config::UdpP2PEndPointConfig* udp_p2p_render_config);
  ::robosense::rs_hmi::config::UdpP2PEndPointConfig* unsafe_arena_release_udp_p2p_render_config();

  // optional .robosense.rs_hmi.config.UdpMulticastEndPointConfig double_udp_multicast_render_config = 16;
  bool has_double_udp_multicast_render_config() const;
  private:
  bool _internal_has_double_udp_multicast_render_config() const;
  public:
  void clear_double_udp_multicast_render_config();
  const ::robosense::rs_hmi::config::UdpMulticastEndPointConfig& double_udp_multicast_render_config() const;
  ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* release_double_udp_multicast_render_config();
  ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* mutable_double_udp_multicast_render_config();
  void set_allocated_double_udp_multicast_render_config(::robosense::rs_hmi::config::UdpMulticastEndPointConfig* double_udp_multicast_render_config);
  private:
  const ::robosense::rs_hmi::config::UdpMulticastEndPointConfig& _internal_double_udp_multicast_render_config() const;
  ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* _internal_mutable_double_udp_multicast_render_config();
  public:
  void unsafe_arena_set_allocated_double_udp_multicast_render_config(
      ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* double_udp_multicast_render_config);
  ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* unsafe_arena_release_double_udp_multicast_render_config();

  // optional uint32 max_send_render_buffer_size = 13 [default = 15];
  bool has_max_send_render_buffer_size() const;
  private:
  bool _internal_has_max_send_render_buffer_size() const;
  public:
  void clear_max_send_render_buffer_size();
  ::PROTOBUF_NAMESPACE_ID::uint32 max_send_render_buffer_size() const;
  void set_max_send_render_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_max_send_render_buffer_size() const;
  void _internal_set_max_send_render_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional bool enable_websocket_buffer = 2 [default = true];
  bool has_enable_websocket_buffer() const;
  private:
  bool _internal_has_enable_websocket_buffer() const;
  public:
  void clear_enable_websocket_buffer();
  bool enable_websocket_buffer() const;
  void set_enable_websocket_buffer(bool value);
  private:
  bool _internal_enable_websocket_buffer() const;
  void _internal_set_enable_websocket_buffer(bool value);
  public:

  // optional uint32 websocket_buffer_size = 3 [default = 2097152];
  bool has_websocket_buffer_size() const;
  private:
  bool _internal_has_websocket_buffer_size() const;
  public:
  void clear_websocket_buffer_size();
  ::PROTOBUF_NAMESPACE_ID::uint32 websocket_buffer_size() const;
  void set_websocket_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_websocket_buffer_size() const;
  void _internal_set_websocket_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 low_websocket_buffer_size = 4 [default = 524288];
  bool has_low_websocket_buffer_size() const;
  private:
  bool _internal_has_low_websocket_buffer_size() const;
  public:
  void clear_low_websocket_buffer_size();
  ::PROTOBUF_NAMESPACE_ID::uint32 low_websocket_buffer_size() const;
  void set_low_websocket_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_low_websocket_buffer_size() const;
  void _internal_set_low_websocket_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 udp_2_sample_1_buffer_size = 10 [default = 3];
  bool has_udp_2_sample_1_buffer_size() const;
  private:
  bool _internal_has_udp_2_sample_1_buffer_size() const;
  public:
  void clear_udp_2_sample_1_buffer_size();
  ::PROTOBUF_NAMESPACE_ID::uint32 udp_2_sample_1_buffer_size() const;
  void set_udp_2_sample_1_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_udp_2_sample_1_buffer_size() const;
  void _internal_set_udp_2_sample_1_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 udp_3_sample_1_buffer_size = 11 [default = 5];
  bool has_udp_3_sample_1_buffer_size() const;
  private:
  bool _internal_has_udp_3_sample_1_buffer_size() const;
  public:
  void clear_udp_3_sample_1_buffer_size();
  ::PROTOBUF_NAMESPACE_ID::uint32 udp_3_sample_1_buffer_size() const;
  void set_udp_3_sample_1_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_udp_3_sample_1_buffer_size() const;
  void _internal_set_udp_3_sample_1_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 udp_loss_all_buffer_size = 12 [default = 10];
  bool has_udp_loss_all_buffer_size() const;
  private:
  bool _internal_has_udp_loss_all_buffer_size() const;
  public:
  void clear_udp_loss_all_buffer_size();
  ::PROTOBUF_NAMESPACE_ID::uint32 udp_loss_all_buffer_size() const;
  void set_udp_loss_all_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_udp_loss_all_buffer_size() const;
  void _internal_set_udp_loss_all_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:robosense.rs_hmi.config.WebsocketRenderTaskConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr websocket_cmd_socket_key_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr websocket_socket_key_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr udp_multicast_socket_key_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr udp_double_multicast_socket_key_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr udp_p2p_socket_key_;
  ::robosense::rs_hmi::config::HmiTaskSocketConfig* websocket_task_;
  ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* udp_multicast_render_config_;
  ::robosense::rs_hmi::config::UdpP2PEndPointConfig* udp_p2p_render_config_;
  ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* double_udp_multicast_render_config_;
  ::PROTOBUF_NAMESPACE_ID::uint32 max_send_render_buffer_size_;
  bool enable_websocket_buffer_;
  ::PROTOBUF_NAMESPACE_ID::uint32 websocket_buffer_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 low_websocket_buffer_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 udp_2_sample_1_buffer_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 udp_3_sample_1_buffer_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 udp_loss_all_buffer_size_;
  friend struct ::TableStruct_config_2eproto;
};
// -------------------------------------------------------------------

class Config PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.rs_hmi.config.Config) */ {
 public:
  inline Config() : Config(nullptr) {}
  virtual ~Config();

  Config(const Config& from);
  Config(Config&& from) noexcept
    : Config() {
    *this = ::std::move(from);
  }

  inline Config& operator=(const Config& from) {
    CopyFrom(from);
    return *this;
  }
  inline Config& operator=(Config&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Config& default_instance();

  static inline const Config* internal_default_instance() {
    return reinterpret_cast<const Config*>(
               &_Config_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(Config& a, Config& b) {
    a.Swap(&b);
  }
  inline void Swap(Config* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Config* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Config* New() const final {
    return CreateMaybeMessage<Config>(nullptr);
  }

  Config* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Config>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Config& from);
  void MergeFrom(const Config& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Config* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.rs_hmi.config.Config";
  }
  protected:
  explicit Config(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_config_2eproto);
    return ::descriptor_table_config_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRenderConfigFieldNumber = 4,
    kHmiSocketTaskConfigFieldNumber = 17,
    kEnableWebsocketBufferCheckFieldNumber = 1,
    kWebsocketBufferCheckTimeoutThMsFieldNumber = 2,
  };
  // optional .robosense.rs_hmi.config.WebsocketRenderTaskConfig render_config = 4;
  bool has_render_config() const;
  private:
  bool _internal_has_render_config() const;
  public:
  void clear_render_config();
  const ::robosense::rs_hmi::config::WebsocketRenderTaskConfig& render_config() const;
  ::robosense::rs_hmi::config::WebsocketRenderTaskConfig* release_render_config();
  ::robosense::rs_hmi::config::WebsocketRenderTaskConfig* mutable_render_config();
  void set_allocated_render_config(::robosense::rs_hmi::config::WebsocketRenderTaskConfig* render_config);
  private:
  const ::robosense::rs_hmi::config::WebsocketRenderTaskConfig& _internal_render_config() const;
  ::robosense::rs_hmi::config::WebsocketRenderTaskConfig* _internal_mutable_render_config();
  public:
  void unsafe_arena_set_allocated_render_config(
      ::robosense::rs_hmi::config::WebsocketRenderTaskConfig* render_config);
  ::robosense::rs_hmi::config::WebsocketRenderTaskConfig* unsafe_arena_release_render_config();

  // optional .robosense.rs_hmi.config.HmiSocketTaskConfig hmi_socket_task_config = 17;
  bool has_hmi_socket_task_config() const;
  private:
  bool _internal_has_hmi_socket_task_config() const;
  public:
  void clear_hmi_socket_task_config();
  const ::robosense::rs_hmi::config::HmiSocketTaskConfig& hmi_socket_task_config() const;
  ::robosense::rs_hmi::config::HmiSocketTaskConfig* release_hmi_socket_task_config();
  ::robosense::rs_hmi::config::HmiSocketTaskConfig* mutable_hmi_socket_task_config();
  void set_allocated_hmi_socket_task_config(::robosense::rs_hmi::config::HmiSocketTaskConfig* hmi_socket_task_config);
  private:
  const ::robosense::rs_hmi::config::HmiSocketTaskConfig& _internal_hmi_socket_task_config() const;
  ::robosense::rs_hmi::config::HmiSocketTaskConfig* _internal_mutable_hmi_socket_task_config();
  public:
  void unsafe_arena_set_allocated_hmi_socket_task_config(
      ::robosense::rs_hmi::config::HmiSocketTaskConfig* hmi_socket_task_config);
  ::robosense::rs_hmi::config::HmiSocketTaskConfig* unsafe_arena_release_hmi_socket_task_config();

  // optional bool enable_websocket_buffer_check = 1 [default = true];
  bool has_enable_websocket_buffer_check() const;
  private:
  bool _internal_has_enable_websocket_buffer_check() const;
  public:
  void clear_enable_websocket_buffer_check();
  bool enable_websocket_buffer_check() const;
  void set_enable_websocket_buffer_check(bool value);
  private:
  bool _internal_enable_websocket_buffer_check() const;
  void _internal_set_enable_websocket_buffer_check(bool value);
  public:

  // optional uint32 websocket_buffer_check_timeout_th_ms = 2 [default = 2000];
  bool has_websocket_buffer_check_timeout_th_ms() const;
  private:
  bool _internal_has_websocket_buffer_check_timeout_th_ms() const;
  public:
  void clear_websocket_buffer_check_timeout_th_ms();
  ::PROTOBUF_NAMESPACE_ID::uint32 websocket_buffer_check_timeout_th_ms() const;
  void set_websocket_buffer_check_timeout_th_ms(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_websocket_buffer_check_timeout_th_ms() const;
  void _internal_set_websocket_buffer_check_timeout_th_ms(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:robosense.rs_hmi.config.Config)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::robosense::rs_hmi::config::WebsocketRenderTaskConfig* render_config_;
  ::robosense::rs_hmi::config::HmiSocketTaskConfig* hmi_socket_task_config_;
  bool enable_websocket_buffer_check_;
  ::PROTOBUF_NAMESPACE_ID::uint32 websocket_buffer_check_timeout_th_ms_;
  friend struct ::TableStruct_config_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// WebsocketEndPointConfig

// optional .robosense.rs_hmi.config.RS_SOCKET_ROLE_TYPE role_type = 1;
inline bool WebsocketEndPointConfig::_internal_has_role_type() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool WebsocketEndPointConfig::has_role_type() const {
  return _internal_has_role_type();
}
inline void WebsocketEndPointConfig::clear_role_type() {
  role_type_ = 1;
  _has_bits_[0] &= ~0x00000002u;
}
inline ::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE WebsocketEndPointConfig::_internal_role_type() const {
  return static_cast< ::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE >(role_type_);
}
inline ::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE WebsocketEndPointConfig::role_type() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketEndPointConfig.role_type)
  return _internal_role_type();
}
inline void WebsocketEndPointConfig::_internal_set_role_type(::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE value) {
  assert(::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE_IsValid(value));
  _has_bits_[0] |= 0x00000002u;
  role_type_ = value;
}
inline void WebsocketEndPointConfig::set_role_type(::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE value) {
  _internal_set_role_type(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.WebsocketEndPointConfig.role_type)
}

// optional string server_ip = 2 [default = "0.0.0.0"];
inline bool WebsocketEndPointConfig::_internal_has_server_ip() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool WebsocketEndPointConfig::has_server_ip() const {
  return _internal_has_server_ip();
}
inline void WebsocketEndPointConfig::clear_server_ip() {
  server_ip_.ClearToDefault(::robosense::rs_hmi::config::WebsocketEndPointConfig::_i_give_permission_to_break_this_code_default_server_ip_, GetArena());
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& WebsocketEndPointConfig::server_ip() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketEndPointConfig.server_ip)
  if (server_ip_.IsDefault(nullptr)) return _i_give_permission_to_break_this_code_default_server_ip_.get();
  return _internal_server_ip();
}
inline void WebsocketEndPointConfig::set_server_ip(const std::string& value) {
  _internal_set_server_ip(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.WebsocketEndPointConfig.server_ip)
}
inline std::string* WebsocketEndPointConfig::mutable_server_ip() {
  // @@protoc_insertion_point(field_mutable:robosense.rs_hmi.config.WebsocketEndPointConfig.server_ip)
  return _internal_mutable_server_ip();
}
inline const std::string& WebsocketEndPointConfig::_internal_server_ip() const {
  return server_ip_.Get();
}
inline void WebsocketEndPointConfig::_internal_set_server_ip(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  server_ip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::NonEmptyDefault{}, value, GetArena());
}
inline void WebsocketEndPointConfig::set_server_ip(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  server_ip_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::NonEmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.rs_hmi.config.WebsocketEndPointConfig.server_ip)
}
inline void WebsocketEndPointConfig::set_server_ip(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  server_ip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::NonEmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.rs_hmi.config.WebsocketEndPointConfig.server_ip)
}
inline void WebsocketEndPointConfig::set_server_ip(const char* value,
    size_t size) {
  _has_bits_[0] |= 0x00000001u;
  server_ip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::NonEmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.rs_hmi.config.WebsocketEndPointConfig.server_ip)
}
inline std::string* WebsocketEndPointConfig::_internal_mutable_server_ip() {
  _has_bits_[0] |= 0x00000001u;
  return server_ip_.Mutable(::robosense::rs_hmi::config::WebsocketEndPointConfig::_i_give_permission_to_break_this_code_default_server_ip_, GetArena());
}
inline std::string* WebsocketEndPointConfig::release_server_ip() {
  // @@protoc_insertion_point(field_release:robosense.rs_hmi.config.WebsocketEndPointConfig.server_ip)
  if (!_internal_has_server_ip()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return server_ip_.ReleaseNonDefault(nullptr, GetArena());
}
inline void WebsocketEndPointConfig::set_allocated_server_ip(std::string* server_ip) {
  if (server_ip != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  server_ip_.SetAllocated(nullptr, server_ip,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.rs_hmi.config.WebsocketEndPointConfig.server_ip)
}

// optional uint32 server_port = 3 [default = 8080];
inline bool WebsocketEndPointConfig::_internal_has_server_port() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool WebsocketEndPointConfig::has_server_port() const {
  return _internal_has_server_port();
}
inline void WebsocketEndPointConfig::clear_server_port() {
  server_port_ = 8080u;
  _has_bits_[0] &= ~0x00000004u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketEndPointConfig::_internal_server_port() const {
  return server_port_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketEndPointConfig::server_port() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketEndPointConfig.server_port)
  return _internal_server_port();
}
inline void WebsocketEndPointConfig::_internal_set_server_port(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000004u;
  server_port_ = value;
}
inline void WebsocketEndPointConfig::set_server_port(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_server_port(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.WebsocketEndPointConfig.server_port)
}

// optional uint32 run_thread_cnt = 4 [default = 4];
inline bool WebsocketEndPointConfig::_internal_has_run_thread_cnt() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool WebsocketEndPointConfig::has_run_thread_cnt() const {
  return _internal_has_run_thread_cnt();
}
inline void WebsocketEndPointConfig::clear_run_thread_cnt() {
  run_thread_cnt_ = 4u;
  _has_bits_[0] &= ~0x00000008u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketEndPointConfig::_internal_run_thread_cnt() const {
  return run_thread_cnt_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketEndPointConfig::run_thread_cnt() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketEndPointConfig.run_thread_cnt)
  return _internal_run_thread_cnt();
}
inline void WebsocketEndPointConfig::_internal_set_run_thread_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000008u;
  run_thread_cnt_ = value;
}
inline void WebsocketEndPointConfig::set_run_thread_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_run_thread_cnt(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.WebsocketEndPointConfig.run_thread_cnt)
}

// optional uint32 send_thread_cnt = 5 [default = 4];
inline bool WebsocketEndPointConfig::_internal_has_send_thread_cnt() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool WebsocketEndPointConfig::has_send_thread_cnt() const {
  return _internal_has_send_thread_cnt();
}
inline void WebsocketEndPointConfig::clear_send_thread_cnt() {
  send_thread_cnt_ = 4u;
  _has_bits_[0] &= ~0x00000010u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketEndPointConfig::_internal_send_thread_cnt() const {
  return send_thread_cnt_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketEndPointConfig::send_thread_cnt() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketEndPointConfig.send_thread_cnt)
  return _internal_send_thread_cnt();
}
inline void WebsocketEndPointConfig::_internal_set_send_thread_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000010u;
  send_thread_cnt_ = value;
}
inline void WebsocketEndPointConfig::set_send_thread_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_send_thread_cnt(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.WebsocketEndPointConfig.send_thread_cnt)
}

// optional uint32 server_client_timeout_ms = 6 [default = 20000];
inline bool WebsocketEndPointConfig::_internal_has_server_client_timeout_ms() const {
  bool value = (_has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool WebsocketEndPointConfig::has_server_client_timeout_ms() const {
  return _internal_has_server_client_timeout_ms();
}
inline void WebsocketEndPointConfig::clear_server_client_timeout_ms() {
  server_client_timeout_ms_ = 20000u;
  _has_bits_[0] &= ~0x00000020u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketEndPointConfig::_internal_server_client_timeout_ms() const {
  return server_client_timeout_ms_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketEndPointConfig::server_client_timeout_ms() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketEndPointConfig.server_client_timeout_ms)
  return _internal_server_client_timeout_ms();
}
inline void WebsocketEndPointConfig::_internal_set_server_client_timeout_ms(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000020u;
  server_client_timeout_ms_ = value;
}
inline void WebsocketEndPointConfig::set_server_client_timeout_ms(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_server_client_timeout_ms(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.WebsocketEndPointConfig.server_client_timeout_ms)
}

// optional uint32 server_check_timeout_ms = 7 [default = 3000];
inline bool WebsocketEndPointConfig::_internal_has_server_check_timeout_ms() const {
  bool value = (_has_bits_[0] & 0x00000040u) != 0;
  return value;
}
inline bool WebsocketEndPointConfig::has_server_check_timeout_ms() const {
  return _internal_has_server_check_timeout_ms();
}
inline void WebsocketEndPointConfig::clear_server_check_timeout_ms() {
  server_check_timeout_ms_ = 3000u;
  _has_bits_[0] &= ~0x00000040u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketEndPointConfig::_internal_server_check_timeout_ms() const {
  return server_check_timeout_ms_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketEndPointConfig::server_check_timeout_ms() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketEndPointConfig.server_check_timeout_ms)
  return _internal_server_check_timeout_ms();
}
inline void WebsocketEndPointConfig::_internal_set_server_check_timeout_ms(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000040u;
  server_check_timeout_ms_ = value;
}
inline void WebsocketEndPointConfig::set_server_check_timeout_ms(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_server_check_timeout_ms(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.WebsocketEndPointConfig.server_check_timeout_ms)
}

// optional .robosense.proto_compress_msgs.RS_POST_DATA_COMPRESSION_FORMAT compress_format = 8 [default = RS_POST_DATA_COMPRESSION_NOTHING];
inline bool WebsocketEndPointConfig::_internal_has_compress_format() const {
  bool value = (_has_bits_[0] & 0x00000080u) != 0;
  return value;
}
inline bool WebsocketEndPointConfig::has_compress_format() const {
  return _internal_has_compress_format();
}
inline void WebsocketEndPointConfig::clear_compress_format() {
  compress_format_ = 1;
  _has_bits_[0] &= ~0x00000080u;
}
inline ::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT WebsocketEndPointConfig::_internal_compress_format() const {
  return static_cast< ::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT >(compress_format_);
}
inline ::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT WebsocketEndPointConfig::compress_format() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketEndPointConfig.compress_format)
  return _internal_compress_format();
}
inline void WebsocketEndPointConfig::_internal_set_compress_format(::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT value) {
  assert(::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT_IsValid(value));
  _has_bits_[0] |= 0x00000080u;
  compress_format_ = value;
}
inline void WebsocketEndPointConfig::set_compress_format(::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT value) {
  _internal_set_compress_format(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.WebsocketEndPointConfig.compress_format)
}

// optional uint32 compress_level = 9 [default = 9];
inline bool WebsocketEndPointConfig::_internal_has_compress_level() const {
  bool value = (_has_bits_[0] & 0x00000100u) != 0;
  return value;
}
inline bool WebsocketEndPointConfig::has_compress_level() const {
  return _internal_has_compress_level();
}
inline void WebsocketEndPointConfig::clear_compress_level() {
  compress_level_ = 9u;
  _has_bits_[0] &= ~0x00000100u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketEndPointConfig::_internal_compress_level() const {
  return compress_level_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketEndPointConfig::compress_level() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketEndPointConfig.compress_level)
  return _internal_compress_level();
}
inline void WebsocketEndPointConfig::_internal_set_compress_level(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000100u;
  compress_level_ = value;
}
inline void WebsocketEndPointConfig::set_compress_level(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_compress_level(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.WebsocketEndPointConfig.compress_level)
}

// -------------------------------------------------------------------

// UdpControlConfig

// optional .robosense.rs_hmi.config.RS_UDP_CONTROL_MODE_TYPE udp_control_type = 1 [default = RS_UDP_CONTROL_TOTAL_CONTROL_TIME];
inline bool UdpControlConfig::_internal_has_udp_control_type() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool UdpControlConfig::has_udp_control_type() const {
  return _internal_has_udp_control_type();
}
inline void UdpControlConfig::clear_udp_control_type() {
  udp_control_type_ = 2;
  _has_bits_[0] &= ~0x00000002u;
}
inline ::robosense::rs_hmi::config::RS_UDP_CONTROL_MODE_TYPE UdpControlConfig::_internal_udp_control_type() const {
  return static_cast< ::robosense::rs_hmi::config::RS_UDP_CONTROL_MODE_TYPE >(udp_control_type_);
}
inline ::robosense::rs_hmi::config::RS_UDP_CONTROL_MODE_TYPE UdpControlConfig::udp_control_type() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpControlConfig.udp_control_type)
  return _internal_udp_control_type();
}
inline void UdpControlConfig::_internal_set_udp_control_type(::robosense::rs_hmi::config::RS_UDP_CONTROL_MODE_TYPE value) {
  assert(::robosense::rs_hmi::config::RS_UDP_CONTROL_MODE_TYPE_IsValid(value));
  _has_bits_[0] |= 0x00000002u;
  udp_control_type_ = value;
}
inline void UdpControlConfig::set_udp_control_type(::robosense::rs_hmi::config::RS_UDP_CONTROL_MODE_TYPE value) {
  _internal_set_udp_control_type(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpControlConfig.udp_control_type)
}

// optional uint32 udp_total_control_time_ms = 2 [default = 60];
inline bool UdpControlConfig::_internal_has_udp_total_control_time_ms() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool UdpControlConfig::has_udp_total_control_time_ms() const {
  return _internal_has_udp_total_control_time_ms();
}
inline void UdpControlConfig::clear_udp_total_control_time_ms() {
  udp_total_control_time_ms_ = 60u;
  _has_bits_[0] &= ~0x00000004u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpControlConfig::_internal_udp_total_control_time_ms() const {
  return udp_total_control_time_ms_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpControlConfig::udp_total_control_time_ms() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpControlConfig.udp_total_control_time_ms)
  return _internal_udp_total_control_time_ms();
}
inline void UdpControlConfig::_internal_set_udp_total_control_time_ms(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000004u;
  udp_total_control_time_ms_ = value;
}
inline void UdpControlConfig::set_udp_total_control_time_ms(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_udp_total_control_time_ms(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpControlConfig.udp_total_control_time_ms)
}

// optional uint32 udp_total_control_single_time_ms = 3 [default = 2];
inline bool UdpControlConfig::_internal_has_udp_total_control_single_time_ms() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool UdpControlConfig::has_udp_total_control_single_time_ms() const {
  return _internal_has_udp_total_control_single_time_ms();
}
inline void UdpControlConfig::clear_udp_total_control_single_time_ms() {
  udp_total_control_single_time_ms_ = 2u;
  _has_bits_[0] &= ~0x00000008u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpControlConfig::_internal_udp_total_control_single_time_ms() const {
  return udp_total_control_single_time_ms_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpControlConfig::udp_total_control_single_time_ms() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpControlConfig.udp_total_control_single_time_ms)
  return _internal_udp_total_control_single_time_ms();
}
inline void UdpControlConfig::_internal_set_udp_total_control_single_time_ms(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000008u;
  udp_total_control_single_time_ms_ = value;
}
inline void UdpControlConfig::set_udp_total_control_single_time_ms(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_udp_total_control_single_time_ms(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpControlConfig.udp_total_control_single_time_ms)
}

// optional uint32 udp_data_control_size = 4 [default = 262144];
inline bool UdpControlConfig::_internal_has_udp_data_control_size() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool UdpControlConfig::has_udp_data_control_size() const {
  return _internal_has_udp_data_control_size();
}
inline void UdpControlConfig::clear_udp_data_control_size() {
  udp_data_control_size_ = 262144u;
  _has_bits_[0] &= ~0x00000010u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpControlConfig::_internal_udp_data_control_size() const {
  return udp_data_control_size_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpControlConfig::udp_data_control_size() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpControlConfig.udp_data_control_size)
  return _internal_udp_data_control_size();
}
inline void UdpControlConfig::_internal_set_udp_data_control_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000010u;
  udp_data_control_size_ = value;
}
inline void UdpControlConfig::set_udp_data_control_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_udp_data_control_size(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpControlConfig.udp_data_control_size)
}

// optional uint32 udp_data_control_time_ms = 5 [default = 2];
inline bool UdpControlConfig::_internal_has_udp_data_control_time_ms() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool UdpControlConfig::has_udp_data_control_time_ms() const {
  return _internal_has_udp_data_control_time_ms();
}
inline void UdpControlConfig::clear_udp_data_control_time_ms() {
  udp_data_control_time_ms_ = 2u;
  _has_bits_[0] &= ~0x00000001u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpControlConfig::_internal_udp_data_control_time_ms() const {
  return udp_data_control_time_ms_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpControlConfig::udp_data_control_time_ms() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpControlConfig.udp_data_control_time_ms)
  return _internal_udp_data_control_time_ms();
}
inline void UdpControlConfig::_internal_set_udp_data_control_time_ms(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000001u;
  udp_data_control_time_ms_ = value;
}
inline void UdpControlConfig::set_udp_data_control_time_ms(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_udp_data_control_time_ms(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpControlConfig.udp_data_control_time_ms)
}

// -------------------------------------------------------------------

// UdpBufferControlConfig

// optional .robosense.rs_hmi.config.RS_UDP_BUFFER_CONTROL_MODE_TYPE udp_buffer_control_type = 1 [default = RS_UDP_BUFFER_CONTROL_ENABLE];
inline bool UdpBufferControlConfig::_internal_has_udp_buffer_control_type() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool UdpBufferControlConfig::has_udp_buffer_control_type() const {
  return _internal_has_udp_buffer_control_type();
}
inline void UdpBufferControlConfig::clear_udp_buffer_control_type() {
  udp_buffer_control_type_ = 2;
  _has_bits_[0] &= ~0x00000002u;
}
inline ::robosense::rs_hmi::config::RS_UDP_BUFFER_CONTROL_MODE_TYPE UdpBufferControlConfig::_internal_udp_buffer_control_type() const {
  return static_cast< ::robosense::rs_hmi::config::RS_UDP_BUFFER_CONTROL_MODE_TYPE >(udp_buffer_control_type_);
}
inline ::robosense::rs_hmi::config::RS_UDP_BUFFER_CONTROL_MODE_TYPE UdpBufferControlConfig::udp_buffer_control_type() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpBufferControlConfig.udp_buffer_control_type)
  return _internal_udp_buffer_control_type();
}
inline void UdpBufferControlConfig::_internal_set_udp_buffer_control_type(::robosense::rs_hmi::config::RS_UDP_BUFFER_CONTROL_MODE_TYPE value) {
  assert(::robosense::rs_hmi::config::RS_UDP_BUFFER_CONTROL_MODE_TYPE_IsValid(value));
  _has_bits_[0] |= 0x00000002u;
  udp_buffer_control_type_ = value;
}
inline void UdpBufferControlConfig::set_udp_buffer_control_type(::robosense::rs_hmi::config::RS_UDP_BUFFER_CONTROL_MODE_TYPE value) {
  _internal_set_udp_buffer_control_type(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpBufferControlConfig.udp_buffer_control_type)
}

// optional uint32 udp_dynamic_single_control_th = 2 [default = 3];
inline bool UdpBufferControlConfig::_internal_has_udp_dynamic_single_control_th() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool UdpBufferControlConfig::has_udp_dynamic_single_control_th() const {
  return _internal_has_udp_dynamic_single_control_th();
}
inline void UdpBufferControlConfig::clear_udp_dynamic_single_control_th() {
  udp_dynamic_single_control_th_ = 3u;
  _has_bits_[0] &= ~0x00000004u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpBufferControlConfig::_internal_udp_dynamic_single_control_th() const {
  return udp_dynamic_single_control_th_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpBufferControlConfig::udp_dynamic_single_control_th() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpBufferControlConfig.udp_dynamic_single_control_th)
  return _internal_udp_dynamic_single_control_th();
}
inline void UdpBufferControlConfig::_internal_set_udp_dynamic_single_control_th(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000004u;
  udp_dynamic_single_control_th_ = value;
}
inline void UdpBufferControlConfig::set_udp_dynamic_single_control_th(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_udp_dynamic_single_control_th(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpBufferControlConfig.udp_dynamic_single_control_th)
}

// optional uint32 udp_static_single_control_th = 3 [default = 3];
inline bool UdpBufferControlConfig::_internal_has_udp_static_single_control_th() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool UdpBufferControlConfig::has_udp_static_single_control_th() const {
  return _internal_has_udp_static_single_control_th();
}
inline void UdpBufferControlConfig::clear_udp_static_single_control_th() {
  udp_static_single_control_th_ = 3u;
  _has_bits_[0] &= ~0x00000008u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpBufferControlConfig::_internal_udp_static_single_control_th() const {
  return udp_static_single_control_th_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpBufferControlConfig::udp_static_single_control_th() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpBufferControlConfig.udp_static_single_control_th)
  return _internal_udp_static_single_control_th();
}
inline void UdpBufferControlConfig::_internal_set_udp_static_single_control_th(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000008u;
  udp_static_single_control_th_ = value;
}
inline void UdpBufferControlConfig::set_udp_static_single_control_th(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_udp_static_single_control_th(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpBufferControlConfig.udp_static_single_control_th)
}

// optional uint32 udp_combine_control_th = 4 [default = 6];
inline bool UdpBufferControlConfig::_internal_has_udp_combine_control_th() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool UdpBufferControlConfig::has_udp_combine_control_th() const {
  return _internal_has_udp_combine_control_th();
}
inline void UdpBufferControlConfig::clear_udp_combine_control_th() {
  udp_combine_control_th_ = 6u;
  _has_bits_[0] &= ~0x00000010u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpBufferControlConfig::_internal_udp_combine_control_th() const {
  return udp_combine_control_th_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpBufferControlConfig::udp_combine_control_th() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpBufferControlConfig.udp_combine_control_th)
  return _internal_udp_combine_control_th();
}
inline void UdpBufferControlConfig::_internal_set_udp_combine_control_th(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000010u;
  udp_combine_control_th_ = value;
}
inline void UdpBufferControlConfig::set_udp_combine_control_th(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_udp_combine_control_th(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpBufferControlConfig.udp_combine_control_th)
}

// optional uint32 udp_buffer_control_send_frame_gap = 5 [default = 2];
inline bool UdpBufferControlConfig::_internal_has_udp_buffer_control_send_frame_gap() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool UdpBufferControlConfig::has_udp_buffer_control_send_frame_gap() const {
  return _internal_has_udp_buffer_control_send_frame_gap();
}
inline void UdpBufferControlConfig::clear_udp_buffer_control_send_frame_gap() {
  udp_buffer_control_send_frame_gap_ = 2u;
  _has_bits_[0] &= ~0x00000001u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpBufferControlConfig::_internal_udp_buffer_control_send_frame_gap() const {
  return udp_buffer_control_send_frame_gap_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpBufferControlConfig::udp_buffer_control_send_frame_gap() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpBufferControlConfig.udp_buffer_control_send_frame_gap)
  return _internal_udp_buffer_control_send_frame_gap();
}
inline void UdpBufferControlConfig::_internal_set_udp_buffer_control_send_frame_gap(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000001u;
  udp_buffer_control_send_frame_gap_ = value;
}
inline void UdpBufferControlConfig::set_udp_buffer_control_send_frame_gap(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_udp_buffer_control_send_frame_gap(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpBufferControlConfig.udp_buffer_control_send_frame_gap)
}

// -------------------------------------------------------------------

// UdpMulticastEndPointConfig

// optional .robosense.rs_hmi.config.RS_SOCKET_ROLE_TYPE role_type = 1;
inline bool UdpMulticastEndPointConfig::_internal_has_role_type() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool UdpMulticastEndPointConfig::has_role_type() const {
  return _internal_has_role_type();
}
inline void UdpMulticastEndPointConfig::clear_role_type() {
  role_type_ = 1;
  _has_bits_[0] &= ~0x00000008u;
}
inline ::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE UdpMulticastEndPointConfig::_internal_role_type() const {
  return static_cast< ::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE >(role_type_);
}
inline ::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE UdpMulticastEndPointConfig::role_type() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpMulticastEndPointConfig.role_type)
  return _internal_role_type();
}
inline void UdpMulticastEndPointConfig::_internal_set_role_type(::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE value) {
  assert(::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE_IsValid(value));
  _has_bits_[0] |= 0x00000008u;
  role_type_ = value;
}
inline void UdpMulticastEndPointConfig::set_role_type(::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE value) {
  _internal_set_role_type(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpMulticastEndPointConfig.role_type)
}

// optional string multicast_ip = 2 [default = "***********"];
inline bool UdpMulticastEndPointConfig::_internal_has_multicast_ip() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool UdpMulticastEndPointConfig::has_multicast_ip() const {
  return _internal_has_multicast_ip();
}
inline void UdpMulticastEndPointConfig::clear_multicast_ip() {
  multicast_ip_.ClearToDefault(::robosense::rs_hmi::config::UdpMulticastEndPointConfig::_i_give_permission_to_break_this_code_default_multicast_ip_, GetArena());
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& UdpMulticastEndPointConfig::multicast_ip() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpMulticastEndPointConfig.multicast_ip)
  if (multicast_ip_.IsDefault(nullptr)) return _i_give_permission_to_break_this_code_default_multicast_ip_.get();
  return _internal_multicast_ip();
}
inline void UdpMulticastEndPointConfig::set_multicast_ip(const std::string& value) {
  _internal_set_multicast_ip(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpMulticastEndPointConfig.multicast_ip)
}
inline std::string* UdpMulticastEndPointConfig::mutable_multicast_ip() {
  // @@protoc_insertion_point(field_mutable:robosense.rs_hmi.config.UdpMulticastEndPointConfig.multicast_ip)
  return _internal_mutable_multicast_ip();
}
inline const std::string& UdpMulticastEndPointConfig::_internal_multicast_ip() const {
  return multicast_ip_.Get();
}
inline void UdpMulticastEndPointConfig::_internal_set_multicast_ip(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  multicast_ip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::NonEmptyDefault{}, value, GetArena());
}
inline void UdpMulticastEndPointConfig::set_multicast_ip(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  multicast_ip_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::NonEmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.rs_hmi.config.UdpMulticastEndPointConfig.multicast_ip)
}
inline void UdpMulticastEndPointConfig::set_multicast_ip(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  multicast_ip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::NonEmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.rs_hmi.config.UdpMulticastEndPointConfig.multicast_ip)
}
inline void UdpMulticastEndPointConfig::set_multicast_ip(const char* value,
    size_t size) {
  _has_bits_[0] |= 0x00000001u;
  multicast_ip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::NonEmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.rs_hmi.config.UdpMulticastEndPointConfig.multicast_ip)
}
inline std::string* UdpMulticastEndPointConfig::_internal_mutable_multicast_ip() {
  _has_bits_[0] |= 0x00000001u;
  return multicast_ip_.Mutable(::robosense::rs_hmi::config::UdpMulticastEndPointConfig::_i_give_permission_to_break_this_code_default_multicast_ip_, GetArena());
}
inline std::string* UdpMulticastEndPointConfig::release_multicast_ip() {
  // @@protoc_insertion_point(field_release:robosense.rs_hmi.config.UdpMulticastEndPointConfig.multicast_ip)
  if (!_internal_has_multicast_ip()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return multicast_ip_.ReleaseNonDefault(nullptr, GetArena());
}
inline void UdpMulticastEndPointConfig::set_allocated_multicast_ip(std::string* multicast_ip) {
  if (multicast_ip != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  multicast_ip_.SetAllocated(nullptr, multicast_ip,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.rs_hmi.config.UdpMulticastEndPointConfig.multicast_ip)
}

// optional uint32 multicast_port = 3 [default = 9096];
inline bool UdpMulticastEndPointConfig::_internal_has_multicast_port() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool UdpMulticastEndPointConfig::has_multicast_port() const {
  return _internal_has_multicast_port();
}
inline void UdpMulticastEndPointConfig::clear_multicast_port() {
  multicast_port_ = 9096u;
  _has_bits_[0] &= ~0x00000010u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpMulticastEndPointConfig::_internal_multicast_port() const {
  return multicast_port_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpMulticastEndPointConfig::multicast_port() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpMulticastEndPointConfig.multicast_port)
  return _internal_multicast_port();
}
inline void UdpMulticastEndPointConfig::_internal_set_multicast_port(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000010u;
  multicast_port_ = value;
}
inline void UdpMulticastEndPointConfig::set_multicast_port(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_multicast_port(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpMulticastEndPointConfig.multicast_port)
}

// optional string multicast_host_ip = 4 [default = "***********"];
inline bool UdpMulticastEndPointConfig::_internal_has_multicast_host_ip() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool UdpMulticastEndPointConfig::has_multicast_host_ip() const {
  return _internal_has_multicast_host_ip();
}
inline void UdpMulticastEndPointConfig::clear_multicast_host_ip() {
  multicast_host_ip_.ClearToDefault(::robosense::rs_hmi::config::UdpMulticastEndPointConfig::_i_give_permission_to_break_this_code_default_multicast_host_ip_, GetArena());
  _has_bits_[0] &= ~0x00000002u;
}
inline const std::string& UdpMulticastEndPointConfig::multicast_host_ip() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpMulticastEndPointConfig.multicast_host_ip)
  if (multicast_host_ip_.IsDefault(nullptr)) return _i_give_permission_to_break_this_code_default_multicast_host_ip_.get();
  return _internal_multicast_host_ip();
}
inline void UdpMulticastEndPointConfig::set_multicast_host_ip(const std::string& value) {
  _internal_set_multicast_host_ip(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpMulticastEndPointConfig.multicast_host_ip)
}
inline std::string* UdpMulticastEndPointConfig::mutable_multicast_host_ip() {
  // @@protoc_insertion_point(field_mutable:robosense.rs_hmi.config.UdpMulticastEndPointConfig.multicast_host_ip)
  return _internal_mutable_multicast_host_ip();
}
inline const std::string& UdpMulticastEndPointConfig::_internal_multicast_host_ip() const {
  return multicast_host_ip_.Get();
}
inline void UdpMulticastEndPointConfig::_internal_set_multicast_host_ip(const std::string& value) {
  _has_bits_[0] |= 0x00000002u;
  multicast_host_ip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::NonEmptyDefault{}, value, GetArena());
}
inline void UdpMulticastEndPointConfig::set_multicast_host_ip(std::string&& value) {
  _has_bits_[0] |= 0x00000002u;
  multicast_host_ip_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::NonEmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.rs_hmi.config.UdpMulticastEndPointConfig.multicast_host_ip)
}
inline void UdpMulticastEndPointConfig::set_multicast_host_ip(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000002u;
  multicast_host_ip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::NonEmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.rs_hmi.config.UdpMulticastEndPointConfig.multicast_host_ip)
}
inline void UdpMulticastEndPointConfig::set_multicast_host_ip(const char* value,
    size_t size) {
  _has_bits_[0] |= 0x00000002u;
  multicast_host_ip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::NonEmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.rs_hmi.config.UdpMulticastEndPointConfig.multicast_host_ip)
}
inline std::string* UdpMulticastEndPointConfig::_internal_mutable_multicast_host_ip() {
  _has_bits_[0] |= 0x00000002u;
  return multicast_host_ip_.Mutable(::robosense::rs_hmi::config::UdpMulticastEndPointConfig::_i_give_permission_to_break_this_code_default_multicast_host_ip_, GetArena());
}
inline std::string* UdpMulticastEndPointConfig::release_multicast_host_ip() {
  // @@protoc_insertion_point(field_release:robosense.rs_hmi.config.UdpMulticastEndPointConfig.multicast_host_ip)
  if (!_internal_has_multicast_host_ip()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000002u;
  return multicast_host_ip_.ReleaseNonDefault(nullptr, GetArena());
}
inline void UdpMulticastEndPointConfig::set_allocated_multicast_host_ip(std::string* multicast_host_ip) {
  if (multicast_host_ip != nullptr) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  multicast_host_ip_.SetAllocated(nullptr, multicast_host_ip,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.rs_hmi.config.UdpMulticastEndPointConfig.multicast_host_ip)
}

// optional uint32 max_msg_size = 5 [default = 4096];
inline bool UdpMulticastEndPointConfig::_internal_has_max_msg_size() const {
  bool value = (_has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool UdpMulticastEndPointConfig::has_max_msg_size() const {
  return _internal_has_max_msg_size();
}
inline void UdpMulticastEndPointConfig::clear_max_msg_size() {
  max_msg_size_ = 4096u;
  _has_bits_[0] &= ~0x00000020u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpMulticastEndPointConfig::_internal_max_msg_size() const {
  return max_msg_size_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpMulticastEndPointConfig::max_msg_size() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpMulticastEndPointConfig.max_msg_size)
  return _internal_max_msg_size();
}
inline void UdpMulticastEndPointConfig::_internal_set_max_msg_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000020u;
  max_msg_size_ = value;
}
inline void UdpMulticastEndPointConfig::set_max_msg_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_max_msg_size(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpMulticastEndPointConfig.max_msg_size)
}

// optional uint32 buffer_cell_size = 6 [default = 128];
inline bool UdpMulticastEndPointConfig::_internal_has_buffer_cell_size() const {
  bool value = (_has_bits_[0] & 0x00000040u) != 0;
  return value;
}
inline bool UdpMulticastEndPointConfig::has_buffer_cell_size() const {
  return _internal_has_buffer_cell_size();
}
inline void UdpMulticastEndPointConfig::clear_buffer_cell_size() {
  buffer_cell_size_ = 128u;
  _has_bits_[0] &= ~0x00000040u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpMulticastEndPointConfig::_internal_buffer_cell_size() const {
  return buffer_cell_size_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpMulticastEndPointConfig::buffer_cell_size() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpMulticastEndPointConfig.buffer_cell_size)
  return _internal_buffer_cell_size();
}
inline void UdpMulticastEndPointConfig::_internal_set_buffer_cell_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000040u;
  buffer_cell_size_ = value;
}
inline void UdpMulticastEndPointConfig::set_buffer_cell_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_buffer_cell_size(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpMulticastEndPointConfig.buffer_cell_size)
}

// optional uint32 asio_run_thread_cnt = 7 [default = 1];
inline bool UdpMulticastEndPointConfig::_internal_has_asio_run_thread_cnt() const {
  bool value = (_has_bits_[0] & 0x00000080u) != 0;
  return value;
}
inline bool UdpMulticastEndPointConfig::has_asio_run_thread_cnt() const {
  return _internal_has_asio_run_thread_cnt();
}
inline void UdpMulticastEndPointConfig::clear_asio_run_thread_cnt() {
  asio_run_thread_cnt_ = 1u;
  _has_bits_[0] &= ~0x00000080u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpMulticastEndPointConfig::_internal_asio_run_thread_cnt() const {
  return asio_run_thread_cnt_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpMulticastEndPointConfig::asio_run_thread_cnt() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpMulticastEndPointConfig.asio_run_thread_cnt)
  return _internal_asio_run_thread_cnt();
}
inline void UdpMulticastEndPointConfig::_internal_set_asio_run_thread_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000080u;
  asio_run_thread_cnt_ = value;
}
inline void UdpMulticastEndPointConfig::set_asio_run_thread_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_asio_run_thread_cnt(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpMulticastEndPointConfig.asio_run_thread_cnt)
}

// optional uint32 asio_snd_thread_cnt = 8 [default = 1];
inline bool UdpMulticastEndPointConfig::_internal_has_asio_snd_thread_cnt() const {
  bool value = (_has_bits_[0] & 0x00000100u) != 0;
  return value;
}
inline bool UdpMulticastEndPointConfig::has_asio_snd_thread_cnt() const {
  return _internal_has_asio_snd_thread_cnt();
}
inline void UdpMulticastEndPointConfig::clear_asio_snd_thread_cnt() {
  asio_snd_thread_cnt_ = 1u;
  _has_bits_[0] &= ~0x00000100u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpMulticastEndPointConfig::_internal_asio_snd_thread_cnt() const {
  return asio_snd_thread_cnt_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpMulticastEndPointConfig::asio_snd_thread_cnt() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpMulticastEndPointConfig.asio_snd_thread_cnt)
  return _internal_asio_snd_thread_cnt();
}
inline void UdpMulticastEndPointConfig::_internal_set_asio_snd_thread_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000100u;
  asio_snd_thread_cnt_ = value;
}
inline void UdpMulticastEndPointConfig::set_asio_snd_thread_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_asio_snd_thread_cnt(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpMulticastEndPointConfig.asio_snd_thread_cnt)
}

// optional uint32 asio_recv_thread_cnt = 9 [default = 1];
inline bool UdpMulticastEndPointConfig::_internal_has_asio_recv_thread_cnt() const {
  bool value = (_has_bits_[0] & 0x00000200u) != 0;
  return value;
}
inline bool UdpMulticastEndPointConfig::has_asio_recv_thread_cnt() const {
  return _internal_has_asio_recv_thread_cnt();
}
inline void UdpMulticastEndPointConfig::clear_asio_recv_thread_cnt() {
  asio_recv_thread_cnt_ = 1u;
  _has_bits_[0] &= ~0x00000200u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpMulticastEndPointConfig::_internal_asio_recv_thread_cnt() const {
  return asio_recv_thread_cnt_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpMulticastEndPointConfig::asio_recv_thread_cnt() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpMulticastEndPointConfig.asio_recv_thread_cnt)
  return _internal_asio_recv_thread_cnt();
}
inline void UdpMulticastEndPointConfig::_internal_set_asio_recv_thread_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000200u;
  asio_recv_thread_cnt_ = value;
}
inline void UdpMulticastEndPointConfig::set_asio_recv_thread_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_asio_recv_thread_cnt(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpMulticastEndPointConfig.asio_recv_thread_cnt)
}

// optional uint32 msg_timeout_th_ms = 10 [default = 100];
inline bool UdpMulticastEndPointConfig::_internal_has_msg_timeout_th_ms() const {
  bool value = (_has_bits_[0] & 0x00000400u) != 0;
  return value;
}
inline bool UdpMulticastEndPointConfig::has_msg_timeout_th_ms() const {
  return _internal_has_msg_timeout_th_ms();
}
inline void UdpMulticastEndPointConfig::clear_msg_timeout_th_ms() {
  msg_timeout_th_ms_ = 100u;
  _has_bits_[0] &= ~0x00000400u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpMulticastEndPointConfig::_internal_msg_timeout_th_ms() const {
  return msg_timeout_th_ms_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpMulticastEndPointConfig::msg_timeout_th_ms() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpMulticastEndPointConfig.msg_timeout_th_ms)
  return _internal_msg_timeout_th_ms();
}
inline void UdpMulticastEndPointConfig::_internal_set_msg_timeout_th_ms(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000400u;
  msg_timeout_th_ms_ = value;
}
inline void UdpMulticastEndPointConfig::set_msg_timeout_th_ms(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_msg_timeout_th_ms(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpMulticastEndPointConfig.msg_timeout_th_ms)
}

// optional uint32 asio_snd_retry_cnt = 11 [default = 1];
inline bool UdpMulticastEndPointConfig::_internal_has_asio_snd_retry_cnt() const {
  bool value = (_has_bits_[0] & 0x00000800u) != 0;
  return value;
}
inline bool UdpMulticastEndPointConfig::has_asio_snd_retry_cnt() const {
  return _internal_has_asio_snd_retry_cnt();
}
inline void UdpMulticastEndPointConfig::clear_asio_snd_retry_cnt() {
  asio_snd_retry_cnt_ = 1u;
  _has_bits_[0] &= ~0x00000800u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpMulticastEndPointConfig::_internal_asio_snd_retry_cnt() const {
  return asio_snd_retry_cnt_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpMulticastEndPointConfig::asio_snd_retry_cnt() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpMulticastEndPointConfig.asio_snd_retry_cnt)
  return _internal_asio_snd_retry_cnt();
}
inline void UdpMulticastEndPointConfig::_internal_set_asio_snd_retry_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000800u;
  asio_snd_retry_cnt_ = value;
}
inline void UdpMulticastEndPointConfig::set_asio_snd_retry_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_asio_snd_retry_cnt(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpMulticastEndPointConfig.asio_snd_retry_cnt)
}

// optional uint32 asio_snd_retry_th_ms = 12 [default = 5];
inline bool UdpMulticastEndPointConfig::_internal_has_asio_snd_retry_th_ms() const {
  bool value = (_has_bits_[0] & 0x00001000u) != 0;
  return value;
}
inline bool UdpMulticastEndPointConfig::has_asio_snd_retry_th_ms() const {
  return _internal_has_asio_snd_retry_th_ms();
}
inline void UdpMulticastEndPointConfig::clear_asio_snd_retry_th_ms() {
  asio_snd_retry_th_ms_ = 5u;
  _has_bits_[0] &= ~0x00001000u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpMulticastEndPointConfig::_internal_asio_snd_retry_th_ms() const {
  return asio_snd_retry_th_ms_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpMulticastEndPointConfig::asio_snd_retry_th_ms() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpMulticastEndPointConfig.asio_snd_retry_th_ms)
  return _internal_asio_snd_retry_th_ms();
}
inline void UdpMulticastEndPointConfig::_internal_set_asio_snd_retry_th_ms(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00001000u;
  asio_snd_retry_th_ms_ = value;
}
inline void UdpMulticastEndPointConfig::set_asio_snd_retry_th_ms(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_asio_snd_retry_th_ms(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpMulticastEndPointConfig.asio_snd_retry_th_ms)
}

// optional .robosense.rs_hmi.config.UdpControlConfig udp_control_config = 13;
inline bool UdpMulticastEndPointConfig::_internal_has_udp_control_config() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  PROTOBUF_ASSUME(!value || udp_control_config_ != nullptr);
  return value;
}
inline bool UdpMulticastEndPointConfig::has_udp_control_config() const {
  return _internal_has_udp_control_config();
}
inline void UdpMulticastEndPointConfig::clear_udp_control_config() {
  if (udp_control_config_ != nullptr) udp_control_config_->Clear();
  _has_bits_[0] &= ~0x00000004u;
}
inline const ::robosense::rs_hmi::config::UdpControlConfig& UdpMulticastEndPointConfig::_internal_udp_control_config() const {
  const ::robosense::rs_hmi::config::UdpControlConfig* p = udp_control_config_;
  return p != nullptr ? *p : reinterpret_cast<const ::robosense::rs_hmi::config::UdpControlConfig&>(
      ::robosense::rs_hmi::config::_UdpControlConfig_default_instance_);
}
inline const ::robosense::rs_hmi::config::UdpControlConfig& UdpMulticastEndPointConfig::udp_control_config() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpMulticastEndPointConfig.udp_control_config)
  return _internal_udp_control_config();
}
inline void UdpMulticastEndPointConfig::unsafe_arena_set_allocated_udp_control_config(
    ::robosense::rs_hmi::config::UdpControlConfig* udp_control_config) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(udp_control_config_);
  }
  udp_control_config_ = udp_control_config;
  if (udp_control_config) {
    _has_bits_[0] |= 0x00000004u;
  } else {
    _has_bits_[0] &= ~0x00000004u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:robosense.rs_hmi.config.UdpMulticastEndPointConfig.udp_control_config)
}
inline ::robosense::rs_hmi::config::UdpControlConfig* UdpMulticastEndPointConfig::release_udp_control_config() {
  _has_bits_[0] &= ~0x00000004u;
  ::robosense::rs_hmi::config::UdpControlConfig* temp = udp_control_config_;
  udp_control_config_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::robosense::rs_hmi::config::UdpControlConfig* UdpMulticastEndPointConfig::unsafe_arena_release_udp_control_config() {
  // @@protoc_insertion_point(field_release:robosense.rs_hmi.config.UdpMulticastEndPointConfig.udp_control_config)
  _has_bits_[0] &= ~0x00000004u;
  ::robosense::rs_hmi::config::UdpControlConfig* temp = udp_control_config_;
  udp_control_config_ = nullptr;
  return temp;
}
inline ::robosense::rs_hmi::config::UdpControlConfig* UdpMulticastEndPointConfig::_internal_mutable_udp_control_config() {
  _has_bits_[0] |= 0x00000004u;
  if (udp_control_config_ == nullptr) {
    auto* p = CreateMaybeMessage<::robosense::rs_hmi::config::UdpControlConfig>(GetArena());
    udp_control_config_ = p;
  }
  return udp_control_config_;
}
inline ::robosense::rs_hmi::config::UdpControlConfig* UdpMulticastEndPointConfig::mutable_udp_control_config() {
  // @@protoc_insertion_point(field_mutable:robosense.rs_hmi.config.UdpMulticastEndPointConfig.udp_control_config)
  return _internal_mutable_udp_control_config();
}
inline void UdpMulticastEndPointConfig::set_allocated_udp_control_config(::robosense::rs_hmi::config::UdpControlConfig* udp_control_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete udp_control_config_;
  }
  if (udp_control_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(udp_control_config);
    if (message_arena != submessage_arena) {
      udp_control_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, udp_control_config, submessage_arena);
    }
    _has_bits_[0] |= 0x00000004u;
  } else {
    _has_bits_[0] &= ~0x00000004u;
  }
  udp_control_config_ = udp_control_config;
  // @@protoc_insertion_point(field_set_allocated:robosense.rs_hmi.config.UdpMulticastEndPointConfig.udp_control_config)
}

// optional .robosense.proto_compress_msgs.RS_POST_DATA_COMPRESSION_FORMAT compress_format = 14 [default = RS_POST_DATA_COMPRESSION_NOTHING];
inline bool UdpMulticastEndPointConfig::_internal_has_compress_format() const {
  bool value = (_has_bits_[0] & 0x00002000u) != 0;
  return value;
}
inline bool UdpMulticastEndPointConfig::has_compress_format() const {
  return _internal_has_compress_format();
}
inline void UdpMulticastEndPointConfig::clear_compress_format() {
  compress_format_ = 1;
  _has_bits_[0] &= ~0x00002000u;
}
inline ::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT UdpMulticastEndPointConfig::_internal_compress_format() const {
  return static_cast< ::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT >(compress_format_);
}
inline ::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT UdpMulticastEndPointConfig::compress_format() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpMulticastEndPointConfig.compress_format)
  return _internal_compress_format();
}
inline void UdpMulticastEndPointConfig::_internal_set_compress_format(::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT value) {
  assert(::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT_IsValid(value));
  _has_bits_[0] |= 0x00002000u;
  compress_format_ = value;
}
inline void UdpMulticastEndPointConfig::set_compress_format(::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT value) {
  _internal_set_compress_format(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpMulticastEndPointConfig.compress_format)
}

// optional uint32 compress_level = 15 [default = 9];
inline bool UdpMulticastEndPointConfig::_internal_has_compress_level() const {
  bool value = (_has_bits_[0] & 0x00004000u) != 0;
  return value;
}
inline bool UdpMulticastEndPointConfig::has_compress_level() const {
  return _internal_has_compress_level();
}
inline void UdpMulticastEndPointConfig::clear_compress_level() {
  compress_level_ = 9u;
  _has_bits_[0] &= ~0x00004000u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpMulticastEndPointConfig::_internal_compress_level() const {
  return compress_level_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpMulticastEndPointConfig::compress_level() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpMulticastEndPointConfig.compress_level)
  return _internal_compress_level();
}
inline void UdpMulticastEndPointConfig::_internal_set_compress_level(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00004000u;
  compress_level_ = value;
}
inline void UdpMulticastEndPointConfig::set_compress_level(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_compress_level(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpMulticastEndPointConfig.compress_level)
}

// -------------------------------------------------------------------

// UdpP2PEndPointConfig

// optional .robosense.rs_hmi.config.RS_SOCKET_ROLE_TYPE role_type = 1;
inline bool UdpP2PEndPointConfig::_internal_has_role_type() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool UdpP2PEndPointConfig::has_role_type() const {
  return _internal_has_role_type();
}
inline void UdpP2PEndPointConfig::clear_role_type() {
  role_type_ = 1;
  _has_bits_[0] &= ~0x00000008u;
}
inline ::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE UdpP2PEndPointConfig::_internal_role_type() const {
  return static_cast< ::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE >(role_type_);
}
inline ::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE UdpP2PEndPointConfig::role_type() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpP2PEndPointConfig.role_type)
  return _internal_role_type();
}
inline void UdpP2PEndPointConfig::_internal_set_role_type(::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE value) {
  assert(::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE_IsValid(value));
  _has_bits_[0] |= 0x00000008u;
  role_type_ = value;
}
inline void UdpP2PEndPointConfig::set_role_type(::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE value) {
  _internal_set_role_type(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpP2PEndPointConfig.role_type)
}

// optional string remote_ip = 2;
inline bool UdpP2PEndPointConfig::_internal_has_remote_ip() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool UdpP2PEndPointConfig::has_remote_ip() const {
  return _internal_has_remote_ip();
}
inline void UdpP2PEndPointConfig::clear_remote_ip() {
  remote_ip_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& UdpP2PEndPointConfig::remote_ip() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpP2PEndPointConfig.remote_ip)
  return _internal_remote_ip();
}
inline void UdpP2PEndPointConfig::set_remote_ip(const std::string& value) {
  _internal_set_remote_ip(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpP2PEndPointConfig.remote_ip)
}
inline std::string* UdpP2PEndPointConfig::mutable_remote_ip() {
  // @@protoc_insertion_point(field_mutable:robosense.rs_hmi.config.UdpP2PEndPointConfig.remote_ip)
  return _internal_mutable_remote_ip();
}
inline const std::string& UdpP2PEndPointConfig::_internal_remote_ip() const {
  return remote_ip_.Get();
}
inline void UdpP2PEndPointConfig::_internal_set_remote_ip(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  remote_ip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void UdpP2PEndPointConfig::set_remote_ip(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  remote_ip_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.rs_hmi.config.UdpP2PEndPointConfig.remote_ip)
}
inline void UdpP2PEndPointConfig::set_remote_ip(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  remote_ip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.rs_hmi.config.UdpP2PEndPointConfig.remote_ip)
}
inline void UdpP2PEndPointConfig::set_remote_ip(const char* value,
    size_t size) {
  _has_bits_[0] |= 0x00000001u;
  remote_ip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.rs_hmi.config.UdpP2PEndPointConfig.remote_ip)
}
inline std::string* UdpP2PEndPointConfig::_internal_mutable_remote_ip() {
  _has_bits_[0] |= 0x00000001u;
  return remote_ip_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* UdpP2PEndPointConfig::release_remote_ip() {
  // @@protoc_insertion_point(field_release:robosense.rs_hmi.config.UdpP2PEndPointConfig.remote_ip)
  if (!_internal_has_remote_ip()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return remote_ip_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void UdpP2PEndPointConfig::set_allocated_remote_ip(std::string* remote_ip) {
  if (remote_ip != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  remote_ip_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), remote_ip,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.rs_hmi.config.UdpP2PEndPointConfig.remote_ip)
}

// optional uint32 remote_port = 3 [default = 9097];
inline bool UdpP2PEndPointConfig::_internal_has_remote_port() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool UdpP2PEndPointConfig::has_remote_port() const {
  return _internal_has_remote_port();
}
inline void UdpP2PEndPointConfig::clear_remote_port() {
  remote_port_ = 9097u;
  _has_bits_[0] &= ~0x00000010u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpP2PEndPointConfig::_internal_remote_port() const {
  return remote_port_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpP2PEndPointConfig::remote_port() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpP2PEndPointConfig.remote_port)
  return _internal_remote_port();
}
inline void UdpP2PEndPointConfig::_internal_set_remote_port(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000010u;
  remote_port_ = value;
}
inline void UdpP2PEndPointConfig::set_remote_port(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_remote_port(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpP2PEndPointConfig.remote_port)
}

// optional uint32 max_msg_size = 5 [default = 4096];
inline bool UdpP2PEndPointConfig::_internal_has_max_msg_size() const {
  bool value = (_has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool UdpP2PEndPointConfig::has_max_msg_size() const {
  return _internal_has_max_msg_size();
}
inline void UdpP2PEndPointConfig::clear_max_msg_size() {
  max_msg_size_ = 4096u;
  _has_bits_[0] &= ~0x00000020u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpP2PEndPointConfig::_internal_max_msg_size() const {
  return max_msg_size_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpP2PEndPointConfig::max_msg_size() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpP2PEndPointConfig.max_msg_size)
  return _internal_max_msg_size();
}
inline void UdpP2PEndPointConfig::_internal_set_max_msg_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000020u;
  max_msg_size_ = value;
}
inline void UdpP2PEndPointConfig::set_max_msg_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_max_msg_size(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpP2PEndPointConfig.max_msg_size)
}

// optional uint32 buffer_cell_size = 6 [default = 128];
inline bool UdpP2PEndPointConfig::_internal_has_buffer_cell_size() const {
  bool value = (_has_bits_[0] & 0x00000040u) != 0;
  return value;
}
inline bool UdpP2PEndPointConfig::has_buffer_cell_size() const {
  return _internal_has_buffer_cell_size();
}
inline void UdpP2PEndPointConfig::clear_buffer_cell_size() {
  buffer_cell_size_ = 128u;
  _has_bits_[0] &= ~0x00000040u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpP2PEndPointConfig::_internal_buffer_cell_size() const {
  return buffer_cell_size_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpP2PEndPointConfig::buffer_cell_size() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpP2PEndPointConfig.buffer_cell_size)
  return _internal_buffer_cell_size();
}
inline void UdpP2PEndPointConfig::_internal_set_buffer_cell_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000040u;
  buffer_cell_size_ = value;
}
inline void UdpP2PEndPointConfig::set_buffer_cell_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_buffer_cell_size(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpP2PEndPointConfig.buffer_cell_size)
}

// optional uint32 asio_run_thread_cnt = 7 [default = 1];
inline bool UdpP2PEndPointConfig::_internal_has_asio_run_thread_cnt() const {
  bool value = (_has_bits_[0] & 0x00000080u) != 0;
  return value;
}
inline bool UdpP2PEndPointConfig::has_asio_run_thread_cnt() const {
  return _internal_has_asio_run_thread_cnt();
}
inline void UdpP2PEndPointConfig::clear_asio_run_thread_cnt() {
  asio_run_thread_cnt_ = 1u;
  _has_bits_[0] &= ~0x00000080u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpP2PEndPointConfig::_internal_asio_run_thread_cnt() const {
  return asio_run_thread_cnt_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpP2PEndPointConfig::asio_run_thread_cnt() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpP2PEndPointConfig.asio_run_thread_cnt)
  return _internal_asio_run_thread_cnt();
}
inline void UdpP2PEndPointConfig::_internal_set_asio_run_thread_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000080u;
  asio_run_thread_cnt_ = value;
}
inline void UdpP2PEndPointConfig::set_asio_run_thread_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_asio_run_thread_cnt(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpP2PEndPointConfig.asio_run_thread_cnt)
}

// optional uint32 asio_snd_thread_cnt = 8 [default = 1];
inline bool UdpP2PEndPointConfig::_internal_has_asio_snd_thread_cnt() const {
  bool value = (_has_bits_[0] & 0x00000100u) != 0;
  return value;
}
inline bool UdpP2PEndPointConfig::has_asio_snd_thread_cnt() const {
  return _internal_has_asio_snd_thread_cnt();
}
inline void UdpP2PEndPointConfig::clear_asio_snd_thread_cnt() {
  asio_snd_thread_cnt_ = 1u;
  _has_bits_[0] &= ~0x00000100u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpP2PEndPointConfig::_internal_asio_snd_thread_cnt() const {
  return asio_snd_thread_cnt_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpP2PEndPointConfig::asio_snd_thread_cnt() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpP2PEndPointConfig.asio_snd_thread_cnt)
  return _internal_asio_snd_thread_cnt();
}
inline void UdpP2PEndPointConfig::_internal_set_asio_snd_thread_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000100u;
  asio_snd_thread_cnt_ = value;
}
inline void UdpP2PEndPointConfig::set_asio_snd_thread_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_asio_snd_thread_cnt(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpP2PEndPointConfig.asio_snd_thread_cnt)
}

// optional uint32 asio_recv_thread_cnt = 9 [default = 1];
inline bool UdpP2PEndPointConfig::_internal_has_asio_recv_thread_cnt() const {
  bool value = (_has_bits_[0] & 0x00000200u) != 0;
  return value;
}
inline bool UdpP2PEndPointConfig::has_asio_recv_thread_cnt() const {
  return _internal_has_asio_recv_thread_cnt();
}
inline void UdpP2PEndPointConfig::clear_asio_recv_thread_cnt() {
  asio_recv_thread_cnt_ = 1u;
  _has_bits_[0] &= ~0x00000200u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpP2PEndPointConfig::_internal_asio_recv_thread_cnt() const {
  return asio_recv_thread_cnt_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpP2PEndPointConfig::asio_recv_thread_cnt() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpP2PEndPointConfig.asio_recv_thread_cnt)
  return _internal_asio_recv_thread_cnt();
}
inline void UdpP2PEndPointConfig::_internal_set_asio_recv_thread_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000200u;
  asio_recv_thread_cnt_ = value;
}
inline void UdpP2PEndPointConfig::set_asio_recv_thread_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_asio_recv_thread_cnt(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpP2PEndPointConfig.asio_recv_thread_cnt)
}

// optional uint32 msg_timeout_th_ms = 10 [default = 100];
inline bool UdpP2PEndPointConfig::_internal_has_msg_timeout_th_ms() const {
  bool value = (_has_bits_[0] & 0x00000400u) != 0;
  return value;
}
inline bool UdpP2PEndPointConfig::has_msg_timeout_th_ms() const {
  return _internal_has_msg_timeout_th_ms();
}
inline void UdpP2PEndPointConfig::clear_msg_timeout_th_ms() {
  msg_timeout_th_ms_ = 100u;
  _has_bits_[0] &= ~0x00000400u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpP2PEndPointConfig::_internal_msg_timeout_th_ms() const {
  return msg_timeout_th_ms_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpP2PEndPointConfig::msg_timeout_th_ms() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpP2PEndPointConfig.msg_timeout_th_ms)
  return _internal_msg_timeout_th_ms();
}
inline void UdpP2PEndPointConfig::_internal_set_msg_timeout_th_ms(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000400u;
  msg_timeout_th_ms_ = value;
}
inline void UdpP2PEndPointConfig::set_msg_timeout_th_ms(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_msg_timeout_th_ms(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpP2PEndPointConfig.msg_timeout_th_ms)
}

// optional uint32 asio_snd_retry_cnt = 11 [default = 1];
inline bool UdpP2PEndPointConfig::_internal_has_asio_snd_retry_cnt() const {
  bool value = (_has_bits_[0] & 0x00000800u) != 0;
  return value;
}
inline bool UdpP2PEndPointConfig::has_asio_snd_retry_cnt() const {
  return _internal_has_asio_snd_retry_cnt();
}
inline void UdpP2PEndPointConfig::clear_asio_snd_retry_cnt() {
  asio_snd_retry_cnt_ = 1u;
  _has_bits_[0] &= ~0x00000800u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpP2PEndPointConfig::_internal_asio_snd_retry_cnt() const {
  return asio_snd_retry_cnt_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpP2PEndPointConfig::asio_snd_retry_cnt() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpP2PEndPointConfig.asio_snd_retry_cnt)
  return _internal_asio_snd_retry_cnt();
}
inline void UdpP2PEndPointConfig::_internal_set_asio_snd_retry_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000800u;
  asio_snd_retry_cnt_ = value;
}
inline void UdpP2PEndPointConfig::set_asio_snd_retry_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_asio_snd_retry_cnt(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpP2PEndPointConfig.asio_snd_retry_cnt)
}

// optional uint32 asio_snd_retry_th_ms = 12 [default = 5];
inline bool UdpP2PEndPointConfig::_internal_has_asio_snd_retry_th_ms() const {
  bool value = (_has_bits_[0] & 0x00001000u) != 0;
  return value;
}
inline bool UdpP2PEndPointConfig::has_asio_snd_retry_th_ms() const {
  return _internal_has_asio_snd_retry_th_ms();
}
inline void UdpP2PEndPointConfig::clear_asio_snd_retry_th_ms() {
  asio_snd_retry_th_ms_ = 5u;
  _has_bits_[0] &= ~0x00001000u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpP2PEndPointConfig::_internal_asio_snd_retry_th_ms() const {
  return asio_snd_retry_th_ms_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpP2PEndPointConfig::asio_snd_retry_th_ms() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpP2PEndPointConfig.asio_snd_retry_th_ms)
  return _internal_asio_snd_retry_th_ms();
}
inline void UdpP2PEndPointConfig::_internal_set_asio_snd_retry_th_ms(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00001000u;
  asio_snd_retry_th_ms_ = value;
}
inline void UdpP2PEndPointConfig::set_asio_snd_retry_th_ms(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_asio_snd_retry_th_ms(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpP2PEndPointConfig.asio_snd_retry_th_ms)
}

// optional uint32 socket_buffer_size = 13 [default = 262144];
inline bool UdpP2PEndPointConfig::_internal_has_socket_buffer_size() const {
  bool value = (_has_bits_[0] & 0x00002000u) != 0;
  return value;
}
inline bool UdpP2PEndPointConfig::has_socket_buffer_size() const {
  return _internal_has_socket_buffer_size();
}
inline void UdpP2PEndPointConfig::clear_socket_buffer_size() {
  socket_buffer_size_ = 262144u;
  _has_bits_[0] &= ~0x00002000u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpP2PEndPointConfig::_internal_socket_buffer_size() const {
  return socket_buffer_size_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpP2PEndPointConfig::socket_buffer_size() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpP2PEndPointConfig.socket_buffer_size)
  return _internal_socket_buffer_size();
}
inline void UdpP2PEndPointConfig::_internal_set_socket_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00002000u;
  socket_buffer_size_ = value;
}
inline void UdpP2PEndPointConfig::set_socket_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_socket_buffer_size(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpP2PEndPointConfig.socket_buffer_size)
}

// optional .robosense.rs_hmi.config.UdpControlConfig udp_control_config = 14;
inline bool UdpP2PEndPointConfig::_internal_has_udp_control_config() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  PROTOBUF_ASSUME(!value || udp_control_config_ != nullptr);
  return value;
}
inline bool UdpP2PEndPointConfig::has_udp_control_config() const {
  return _internal_has_udp_control_config();
}
inline void UdpP2PEndPointConfig::clear_udp_control_config() {
  if (udp_control_config_ != nullptr) udp_control_config_->Clear();
  _has_bits_[0] &= ~0x00000002u;
}
inline const ::robosense::rs_hmi::config::UdpControlConfig& UdpP2PEndPointConfig::_internal_udp_control_config() const {
  const ::robosense::rs_hmi::config::UdpControlConfig* p = udp_control_config_;
  return p != nullptr ? *p : reinterpret_cast<const ::robosense::rs_hmi::config::UdpControlConfig&>(
      ::robosense::rs_hmi::config::_UdpControlConfig_default_instance_);
}
inline const ::robosense::rs_hmi::config::UdpControlConfig& UdpP2PEndPointConfig::udp_control_config() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpP2PEndPointConfig.udp_control_config)
  return _internal_udp_control_config();
}
inline void UdpP2PEndPointConfig::unsafe_arena_set_allocated_udp_control_config(
    ::robosense::rs_hmi::config::UdpControlConfig* udp_control_config) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(udp_control_config_);
  }
  udp_control_config_ = udp_control_config;
  if (udp_control_config) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:robosense.rs_hmi.config.UdpP2PEndPointConfig.udp_control_config)
}
inline ::robosense::rs_hmi::config::UdpControlConfig* UdpP2PEndPointConfig::release_udp_control_config() {
  _has_bits_[0] &= ~0x00000002u;
  ::robosense::rs_hmi::config::UdpControlConfig* temp = udp_control_config_;
  udp_control_config_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::robosense::rs_hmi::config::UdpControlConfig* UdpP2PEndPointConfig::unsafe_arena_release_udp_control_config() {
  // @@protoc_insertion_point(field_release:robosense.rs_hmi.config.UdpP2PEndPointConfig.udp_control_config)
  _has_bits_[0] &= ~0x00000002u;
  ::robosense::rs_hmi::config::UdpControlConfig* temp = udp_control_config_;
  udp_control_config_ = nullptr;
  return temp;
}
inline ::robosense::rs_hmi::config::UdpControlConfig* UdpP2PEndPointConfig::_internal_mutable_udp_control_config() {
  _has_bits_[0] |= 0x00000002u;
  if (udp_control_config_ == nullptr) {
    auto* p = CreateMaybeMessage<::robosense::rs_hmi::config::UdpControlConfig>(GetArena());
    udp_control_config_ = p;
  }
  return udp_control_config_;
}
inline ::robosense::rs_hmi::config::UdpControlConfig* UdpP2PEndPointConfig::mutable_udp_control_config() {
  // @@protoc_insertion_point(field_mutable:robosense.rs_hmi.config.UdpP2PEndPointConfig.udp_control_config)
  return _internal_mutable_udp_control_config();
}
inline void UdpP2PEndPointConfig::set_allocated_udp_control_config(::robosense::rs_hmi::config::UdpControlConfig* udp_control_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete udp_control_config_;
  }
  if (udp_control_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(udp_control_config);
    if (message_arena != submessage_arena) {
      udp_control_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, udp_control_config, submessage_arena);
    }
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  udp_control_config_ = udp_control_config;
  // @@protoc_insertion_point(field_set_allocated:robosense.rs_hmi.config.UdpP2PEndPointConfig.udp_control_config)
}

// optional .robosense.proto_compress_msgs.RS_POST_DATA_COMPRESSION_FORMAT compress_format = 15 [default = RS_POST_DATA_COMPRESSION_NOTHING];
inline bool UdpP2PEndPointConfig::_internal_has_compress_format() const {
  bool value = (_has_bits_[0] & 0x00004000u) != 0;
  return value;
}
inline bool UdpP2PEndPointConfig::has_compress_format() const {
  return _internal_has_compress_format();
}
inline void UdpP2PEndPointConfig::clear_compress_format() {
  compress_format_ = 1;
  _has_bits_[0] &= ~0x00004000u;
}
inline ::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT UdpP2PEndPointConfig::_internal_compress_format() const {
  return static_cast< ::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT >(compress_format_);
}
inline ::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT UdpP2PEndPointConfig::compress_format() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpP2PEndPointConfig.compress_format)
  return _internal_compress_format();
}
inline void UdpP2PEndPointConfig::_internal_set_compress_format(::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT value) {
  assert(::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT_IsValid(value));
  _has_bits_[0] |= 0x00004000u;
  compress_format_ = value;
}
inline void UdpP2PEndPointConfig::set_compress_format(::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT value) {
  _internal_set_compress_format(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpP2PEndPointConfig.compress_format)
}

// optional uint32 compress_level = 16 [default = 9];
inline bool UdpP2PEndPointConfig::_internal_has_compress_level() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool UdpP2PEndPointConfig::has_compress_level() const {
  return _internal_has_compress_level();
}
inline void UdpP2PEndPointConfig::clear_compress_level() {
  compress_level_ = 9u;
  _has_bits_[0] &= ~0x00000004u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpP2PEndPointConfig::_internal_compress_level() const {
  return compress_level_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpP2PEndPointConfig::compress_level() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpP2PEndPointConfig.compress_level)
  return _internal_compress_level();
}
inline void UdpP2PEndPointConfig::_internal_set_compress_level(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000004u;
  compress_level_ = value;
}
inline void UdpP2PEndPointConfig::set_compress_level(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_compress_level(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpP2PEndPointConfig.compress_level)
}

// -------------------------------------------------------------------

// WebsocketSendControlConfig

// optional string message_type = 1;
inline bool WebsocketSendControlConfig::_internal_has_message_type() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool WebsocketSendControlConfig::has_message_type() const {
  return _internal_has_message_type();
}
inline void WebsocketSendControlConfig::clear_message_type() {
  message_type_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& WebsocketSendControlConfig::message_type() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketSendControlConfig.message_type)
  return _internal_message_type();
}
inline void WebsocketSendControlConfig::set_message_type(const std::string& value) {
  _internal_set_message_type(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.WebsocketSendControlConfig.message_type)
}
inline std::string* WebsocketSendControlConfig::mutable_message_type() {
  // @@protoc_insertion_point(field_mutable:robosense.rs_hmi.config.WebsocketSendControlConfig.message_type)
  return _internal_mutable_message_type();
}
inline const std::string& WebsocketSendControlConfig::_internal_message_type() const {
  return message_type_.Get();
}
inline void WebsocketSendControlConfig::_internal_set_message_type(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  message_type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void WebsocketSendControlConfig::set_message_type(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  message_type_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.rs_hmi.config.WebsocketSendControlConfig.message_type)
}
inline void WebsocketSendControlConfig::set_message_type(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  message_type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.rs_hmi.config.WebsocketSendControlConfig.message_type)
}
inline void WebsocketSendControlConfig::set_message_type(const char* value,
    size_t size) {
  _has_bits_[0] |= 0x00000001u;
  message_type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.rs_hmi.config.WebsocketSendControlConfig.message_type)
}
inline std::string* WebsocketSendControlConfig::_internal_mutable_message_type() {
  _has_bits_[0] |= 0x00000001u;
  return message_type_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* WebsocketSendControlConfig::release_message_type() {
  // @@protoc_insertion_point(field_release:robosense.rs_hmi.config.WebsocketSendControlConfig.message_type)
  if (!_internal_has_message_type()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return message_type_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void WebsocketSendControlConfig::set_allocated_message_type(std::string* message_type) {
  if (message_type != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  message_type_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), message_type,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.rs_hmi.config.WebsocketSendControlConfig.message_type)
}

// optional bool enable_loss = 2 [default = true];
inline bool WebsocketSendControlConfig::_internal_has_enable_loss() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool WebsocketSendControlConfig::has_enable_loss() const {
  return _internal_has_enable_loss();
}
inline void WebsocketSendControlConfig::clear_enable_loss() {
  enable_loss_ = true;
  _has_bits_[0] &= ~0x00000008u;
}
inline bool WebsocketSendControlConfig::_internal_enable_loss() const {
  return enable_loss_;
}
inline bool WebsocketSendControlConfig::enable_loss() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketSendControlConfig.enable_loss)
  return _internal_enable_loss();
}
inline void WebsocketSendControlConfig::_internal_set_enable_loss(bool value) {
  _has_bits_[0] |= 0x00000008u;
  enable_loss_ = value;
}
inline void WebsocketSendControlConfig::set_enable_loss(bool value) {
  _internal_set_enable_loss(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.WebsocketSendControlConfig.enable_loss)
}

// optional uint32 loss_buffer_size = 3 [default = 10];
inline bool WebsocketSendControlConfig::_internal_has_loss_buffer_size() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool WebsocketSendControlConfig::has_loss_buffer_size() const {
  return _internal_has_loss_buffer_size();
}
inline void WebsocketSendControlConfig::clear_loss_buffer_size() {
  loss_buffer_size_ = 10u;
  _has_bits_[0] &= ~0x00000002u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketSendControlConfig::_internal_loss_buffer_size() const {
  return loss_buffer_size_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketSendControlConfig::loss_buffer_size() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketSendControlConfig.loss_buffer_size)
  return _internal_loss_buffer_size();
}
inline void WebsocketSendControlConfig::_internal_set_loss_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000002u;
  loss_buffer_size_ = value;
}
inline void WebsocketSendControlConfig::set_loss_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_loss_buffer_size(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.WebsocketSendControlConfig.loss_buffer_size)
}

// optional uint32 loss_gap_size = 4 [default = 2];
inline bool WebsocketSendControlConfig::_internal_has_loss_gap_size() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool WebsocketSendControlConfig::has_loss_gap_size() const {
  return _internal_has_loss_gap_size();
}
inline void WebsocketSendControlConfig::clear_loss_gap_size() {
  loss_gap_size_ = 2u;
  _has_bits_[0] &= ~0x00000004u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketSendControlConfig::_internal_loss_gap_size() const {
  return loss_gap_size_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketSendControlConfig::loss_gap_size() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketSendControlConfig.loss_gap_size)
  return _internal_loss_gap_size();
}
inline void WebsocketSendControlConfig::_internal_set_loss_gap_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000004u;
  loss_gap_size_ = value;
}
inline void WebsocketSendControlConfig::set_loss_gap_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_loss_gap_size(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.WebsocketSendControlConfig.loss_gap_size)
}

// optional bool enable_loss2 = 5 [default = true];
inline bool WebsocketSendControlConfig::_internal_has_enable_loss2() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool WebsocketSendControlConfig::has_enable_loss2() const {
  return _internal_has_enable_loss2();
}
inline void WebsocketSendControlConfig::clear_enable_loss2() {
  enable_loss2_ = true;
  _has_bits_[0] &= ~0x00000010u;
}
inline bool WebsocketSendControlConfig::_internal_enable_loss2() const {
  return enable_loss2_;
}
inline bool WebsocketSendControlConfig::enable_loss2() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketSendControlConfig.enable_loss2)
  return _internal_enable_loss2();
}
inline void WebsocketSendControlConfig::_internal_set_enable_loss2(bool value) {
  _has_bits_[0] |= 0x00000010u;
  enable_loss2_ = value;
}
inline void WebsocketSendControlConfig::set_enable_loss2(bool value) {
  _internal_set_enable_loss2(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.WebsocketSendControlConfig.enable_loss2)
}

// optional uint32 loss_buffer_size2 = 6 [default = 15];
inline bool WebsocketSendControlConfig::_internal_has_loss_buffer_size2() const {
  bool value = (_has_bits_[0] & 0x00000040u) != 0;
  return value;
}
inline bool WebsocketSendControlConfig::has_loss_buffer_size2() const {
  return _internal_has_loss_buffer_size2();
}
inline void WebsocketSendControlConfig::clear_loss_buffer_size2() {
  loss_buffer_size2_ = 15u;
  _has_bits_[0] &= ~0x00000040u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketSendControlConfig::_internal_loss_buffer_size2() const {
  return loss_buffer_size2_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketSendControlConfig::loss_buffer_size2() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketSendControlConfig.loss_buffer_size2)
  return _internal_loss_buffer_size2();
}
inline void WebsocketSendControlConfig::_internal_set_loss_buffer_size2(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000040u;
  loss_buffer_size2_ = value;
}
inline void WebsocketSendControlConfig::set_loss_buffer_size2(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_loss_buffer_size2(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.WebsocketSendControlConfig.loss_buffer_size2)
}

// optional uint32 loss_gap_size2 = 7 [default = 3];
inline bool WebsocketSendControlConfig::_internal_has_loss_gap_size2() const {
  bool value = (_has_bits_[0] & 0x00000080u) != 0;
  return value;
}
inline bool WebsocketSendControlConfig::has_loss_gap_size2() const {
  return _internal_has_loss_gap_size2();
}
inline void WebsocketSendControlConfig::clear_loss_gap_size2() {
  loss_gap_size2_ = 3u;
  _has_bits_[0] &= ~0x00000080u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketSendControlConfig::_internal_loss_gap_size2() const {
  return loss_gap_size2_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketSendControlConfig::loss_gap_size2() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketSendControlConfig.loss_gap_size2)
  return _internal_loss_gap_size2();
}
inline void WebsocketSendControlConfig::_internal_set_loss_gap_size2(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000080u;
  loss_gap_size2_ = value;
}
inline void WebsocketSendControlConfig::set_loss_gap_size2(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_loss_gap_size2(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.WebsocketSendControlConfig.loss_gap_size2)
}

// optional bool enable_clear = 8 [default = true];
inline bool WebsocketSendControlConfig::_internal_has_enable_clear() const {
  bool value = (_has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool WebsocketSendControlConfig::has_enable_clear() const {
  return _internal_has_enable_clear();
}
inline void WebsocketSendControlConfig::clear_enable_clear() {
  enable_clear_ = true;
  _has_bits_[0] &= ~0x00000020u;
}
inline bool WebsocketSendControlConfig::_internal_enable_clear() const {
  return enable_clear_;
}
inline bool WebsocketSendControlConfig::enable_clear() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketSendControlConfig.enable_clear)
  return _internal_enable_clear();
}
inline void WebsocketSendControlConfig::_internal_set_enable_clear(bool value) {
  _has_bits_[0] |= 0x00000020u;
  enable_clear_ = value;
}
inline void WebsocketSendControlConfig::set_enable_clear(bool value) {
  _internal_set_enable_clear(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.WebsocketSendControlConfig.enable_clear)
}

// optional uint32 clear_buffer_size = 9 [default = 20];
inline bool WebsocketSendControlConfig::_internal_has_clear_buffer_size() const {
  bool value = (_has_bits_[0] & 0x00000100u) != 0;
  return value;
}
inline bool WebsocketSendControlConfig::has_clear_buffer_size() const {
  return _internal_has_clear_buffer_size();
}
inline void WebsocketSendControlConfig::clear_clear_buffer_size() {
  clear_buffer_size_ = 20u;
  _has_bits_[0] &= ~0x00000100u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketSendControlConfig::_internal_clear_buffer_size() const {
  return clear_buffer_size_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketSendControlConfig::clear_buffer_size() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketSendControlConfig.clear_buffer_size)
  return _internal_clear_buffer_size();
}
inline void WebsocketSendControlConfig::_internal_set_clear_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000100u;
  clear_buffer_size_ = value;
}
inline void WebsocketSendControlConfig::set_clear_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_clear_buffer_size(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.WebsocketSendControlConfig.clear_buffer_size)
}

// -------------------------------------------------------------------

// WebsocketTaskConfig

// optional string task_socket_key = 1;
inline bool WebsocketTaskConfig::_internal_has_task_socket_key() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool WebsocketTaskConfig::has_task_socket_key() const {
  return _internal_has_task_socket_key();
}
inline void WebsocketTaskConfig::clear_task_socket_key() {
  task_socket_key_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& WebsocketTaskConfig::task_socket_key() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketTaskConfig.task_socket_key)
  return _internal_task_socket_key();
}
inline void WebsocketTaskConfig::set_task_socket_key(const std::string& value) {
  _internal_set_task_socket_key(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.WebsocketTaskConfig.task_socket_key)
}
inline std::string* WebsocketTaskConfig::mutable_task_socket_key() {
  // @@protoc_insertion_point(field_mutable:robosense.rs_hmi.config.WebsocketTaskConfig.task_socket_key)
  return _internal_mutable_task_socket_key();
}
inline const std::string& WebsocketTaskConfig::_internal_task_socket_key() const {
  return task_socket_key_.Get();
}
inline void WebsocketTaskConfig::_internal_set_task_socket_key(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  task_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void WebsocketTaskConfig::set_task_socket_key(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  task_socket_key_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.rs_hmi.config.WebsocketTaskConfig.task_socket_key)
}
inline void WebsocketTaskConfig::set_task_socket_key(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  task_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.rs_hmi.config.WebsocketTaskConfig.task_socket_key)
}
inline void WebsocketTaskConfig::set_task_socket_key(const char* value,
    size_t size) {
  _has_bits_[0] |= 0x00000001u;
  task_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.rs_hmi.config.WebsocketTaskConfig.task_socket_key)
}
inline std::string* WebsocketTaskConfig::_internal_mutable_task_socket_key() {
  _has_bits_[0] |= 0x00000001u;
  return task_socket_key_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* WebsocketTaskConfig::release_task_socket_key() {
  // @@protoc_insertion_point(field_release:robosense.rs_hmi.config.WebsocketTaskConfig.task_socket_key)
  if (!_internal_has_task_socket_key()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return task_socket_key_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void WebsocketTaskConfig::set_allocated_task_socket_key(std::string* task_socket_key) {
  if (task_socket_key != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  task_socket_key_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), task_socket_key,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.rs_hmi.config.WebsocketTaskConfig.task_socket_key)
}

// optional .robosense.rs_hmi.config.WebsocketEndPointConfig websocket_endpoint = 2;
inline bool WebsocketTaskConfig::_internal_has_websocket_endpoint() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  PROTOBUF_ASSUME(!value || websocket_endpoint_ != nullptr);
  return value;
}
inline bool WebsocketTaskConfig::has_websocket_endpoint() const {
  return _internal_has_websocket_endpoint();
}
inline void WebsocketTaskConfig::clear_websocket_endpoint() {
  if (websocket_endpoint_ != nullptr) websocket_endpoint_->Clear();
  _has_bits_[0] &= ~0x00000002u;
}
inline const ::robosense::rs_hmi::config::WebsocketEndPointConfig& WebsocketTaskConfig::_internal_websocket_endpoint() const {
  const ::robosense::rs_hmi::config::WebsocketEndPointConfig* p = websocket_endpoint_;
  return p != nullptr ? *p : reinterpret_cast<const ::robosense::rs_hmi::config::WebsocketEndPointConfig&>(
      ::robosense::rs_hmi::config::_WebsocketEndPointConfig_default_instance_);
}
inline const ::robosense::rs_hmi::config::WebsocketEndPointConfig& WebsocketTaskConfig::websocket_endpoint() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketTaskConfig.websocket_endpoint)
  return _internal_websocket_endpoint();
}
inline void WebsocketTaskConfig::unsafe_arena_set_allocated_websocket_endpoint(
    ::robosense::rs_hmi::config::WebsocketEndPointConfig* websocket_endpoint) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(websocket_endpoint_);
  }
  websocket_endpoint_ = websocket_endpoint;
  if (websocket_endpoint) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:robosense.rs_hmi.config.WebsocketTaskConfig.websocket_endpoint)
}
inline ::robosense::rs_hmi::config::WebsocketEndPointConfig* WebsocketTaskConfig::release_websocket_endpoint() {
  _has_bits_[0] &= ~0x00000002u;
  ::robosense::rs_hmi::config::WebsocketEndPointConfig* temp = websocket_endpoint_;
  websocket_endpoint_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::robosense::rs_hmi::config::WebsocketEndPointConfig* WebsocketTaskConfig::unsafe_arena_release_websocket_endpoint() {
  // @@protoc_insertion_point(field_release:robosense.rs_hmi.config.WebsocketTaskConfig.websocket_endpoint)
  _has_bits_[0] &= ~0x00000002u;
  ::robosense::rs_hmi::config::WebsocketEndPointConfig* temp = websocket_endpoint_;
  websocket_endpoint_ = nullptr;
  return temp;
}
inline ::robosense::rs_hmi::config::WebsocketEndPointConfig* WebsocketTaskConfig::_internal_mutable_websocket_endpoint() {
  _has_bits_[0] |= 0x00000002u;
  if (websocket_endpoint_ == nullptr) {
    auto* p = CreateMaybeMessage<::robosense::rs_hmi::config::WebsocketEndPointConfig>(GetArena());
    websocket_endpoint_ = p;
  }
  return websocket_endpoint_;
}
inline ::robosense::rs_hmi::config::WebsocketEndPointConfig* WebsocketTaskConfig::mutable_websocket_endpoint() {
  // @@protoc_insertion_point(field_mutable:robosense.rs_hmi.config.WebsocketTaskConfig.websocket_endpoint)
  return _internal_mutable_websocket_endpoint();
}
inline void WebsocketTaskConfig::set_allocated_websocket_endpoint(::robosense::rs_hmi::config::WebsocketEndPointConfig* websocket_endpoint) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete websocket_endpoint_;
  }
  if (websocket_endpoint) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(websocket_endpoint);
    if (message_arena != submessage_arena) {
      websocket_endpoint = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, websocket_endpoint, submessage_arena);
    }
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  websocket_endpoint_ = websocket_endpoint;
  // @@protoc_insertion_point(field_set_allocated:robosense.rs_hmi.config.WebsocketTaskConfig.websocket_endpoint)
}

// repeated .robosense.rs_hmi.config.WebsocketSendControlConfig websocket_send_controls = 3;
inline int WebsocketTaskConfig::_internal_websocket_send_controls_size() const {
  return websocket_send_controls_.size();
}
inline int WebsocketTaskConfig::websocket_send_controls_size() const {
  return _internal_websocket_send_controls_size();
}
inline void WebsocketTaskConfig::clear_websocket_send_controls() {
  websocket_send_controls_.Clear();
}
inline ::robosense::rs_hmi::config::WebsocketSendControlConfig* WebsocketTaskConfig::mutable_websocket_send_controls(int index) {
  // @@protoc_insertion_point(field_mutable:robosense.rs_hmi.config.WebsocketTaskConfig.websocket_send_controls)
  return websocket_send_controls_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::robosense::rs_hmi::config::WebsocketSendControlConfig >*
WebsocketTaskConfig::mutable_websocket_send_controls() {
  // @@protoc_insertion_point(field_mutable_list:robosense.rs_hmi.config.WebsocketTaskConfig.websocket_send_controls)
  return &websocket_send_controls_;
}
inline const ::robosense::rs_hmi::config::WebsocketSendControlConfig& WebsocketTaskConfig::_internal_websocket_send_controls(int index) const {
  return websocket_send_controls_.Get(index);
}
inline const ::robosense::rs_hmi::config::WebsocketSendControlConfig& WebsocketTaskConfig::websocket_send_controls(int index) const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketTaskConfig.websocket_send_controls)
  return _internal_websocket_send_controls(index);
}
inline ::robosense::rs_hmi::config::WebsocketSendControlConfig* WebsocketTaskConfig::_internal_add_websocket_send_controls() {
  return websocket_send_controls_.Add();
}
inline ::robosense::rs_hmi::config::WebsocketSendControlConfig* WebsocketTaskConfig::add_websocket_send_controls() {
  // @@protoc_insertion_point(field_add:robosense.rs_hmi.config.WebsocketTaskConfig.websocket_send_controls)
  return _internal_add_websocket_send_controls();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::robosense::rs_hmi::config::WebsocketSendControlConfig >&
WebsocketTaskConfig::websocket_send_controls() const {
  // @@protoc_insertion_point(field_list:robosense.rs_hmi.config.WebsocketTaskConfig.websocket_send_controls)
  return websocket_send_controls_;
}

// -------------------------------------------------------------------

// UdpMulticastTaskConfig

// optional string task_socket_key = 1;
inline bool UdpMulticastTaskConfig::_internal_has_task_socket_key() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool UdpMulticastTaskConfig::has_task_socket_key() const {
  return _internal_has_task_socket_key();
}
inline void UdpMulticastTaskConfig::clear_task_socket_key() {
  task_socket_key_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& UdpMulticastTaskConfig::task_socket_key() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpMulticastTaskConfig.task_socket_key)
  return _internal_task_socket_key();
}
inline void UdpMulticastTaskConfig::set_task_socket_key(const std::string& value) {
  _internal_set_task_socket_key(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpMulticastTaskConfig.task_socket_key)
}
inline std::string* UdpMulticastTaskConfig::mutable_task_socket_key() {
  // @@protoc_insertion_point(field_mutable:robosense.rs_hmi.config.UdpMulticastTaskConfig.task_socket_key)
  return _internal_mutable_task_socket_key();
}
inline const std::string& UdpMulticastTaskConfig::_internal_task_socket_key() const {
  return task_socket_key_.Get();
}
inline void UdpMulticastTaskConfig::_internal_set_task_socket_key(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  task_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void UdpMulticastTaskConfig::set_task_socket_key(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  task_socket_key_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.rs_hmi.config.UdpMulticastTaskConfig.task_socket_key)
}
inline void UdpMulticastTaskConfig::set_task_socket_key(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  task_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.rs_hmi.config.UdpMulticastTaskConfig.task_socket_key)
}
inline void UdpMulticastTaskConfig::set_task_socket_key(const char* value,
    size_t size) {
  _has_bits_[0] |= 0x00000001u;
  task_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.rs_hmi.config.UdpMulticastTaskConfig.task_socket_key)
}
inline std::string* UdpMulticastTaskConfig::_internal_mutable_task_socket_key() {
  _has_bits_[0] |= 0x00000001u;
  return task_socket_key_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* UdpMulticastTaskConfig::release_task_socket_key() {
  // @@protoc_insertion_point(field_release:robosense.rs_hmi.config.UdpMulticastTaskConfig.task_socket_key)
  if (!_internal_has_task_socket_key()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return task_socket_key_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void UdpMulticastTaskConfig::set_allocated_task_socket_key(std::string* task_socket_key) {
  if (task_socket_key != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  task_socket_key_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), task_socket_key,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.rs_hmi.config.UdpMulticastTaskConfig.task_socket_key)
}

// optional .robosense.rs_hmi.config.UdpMulticastEndPointConfig udp_multicast_endpoint = 2;
inline bool UdpMulticastTaskConfig::_internal_has_udp_multicast_endpoint() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  PROTOBUF_ASSUME(!value || udp_multicast_endpoint_ != nullptr);
  return value;
}
inline bool UdpMulticastTaskConfig::has_udp_multicast_endpoint() const {
  return _internal_has_udp_multicast_endpoint();
}
inline void UdpMulticastTaskConfig::clear_udp_multicast_endpoint() {
  if (udp_multicast_endpoint_ != nullptr) udp_multicast_endpoint_->Clear();
  _has_bits_[0] &= ~0x00000002u;
}
inline const ::robosense::rs_hmi::config::UdpMulticastEndPointConfig& UdpMulticastTaskConfig::_internal_udp_multicast_endpoint() const {
  const ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* p = udp_multicast_endpoint_;
  return p != nullptr ? *p : reinterpret_cast<const ::robosense::rs_hmi::config::UdpMulticastEndPointConfig&>(
      ::robosense::rs_hmi::config::_UdpMulticastEndPointConfig_default_instance_);
}
inline const ::robosense::rs_hmi::config::UdpMulticastEndPointConfig& UdpMulticastTaskConfig::udp_multicast_endpoint() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpMulticastTaskConfig.udp_multicast_endpoint)
  return _internal_udp_multicast_endpoint();
}
inline void UdpMulticastTaskConfig::unsafe_arena_set_allocated_udp_multicast_endpoint(
    ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* udp_multicast_endpoint) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(udp_multicast_endpoint_);
  }
  udp_multicast_endpoint_ = udp_multicast_endpoint;
  if (udp_multicast_endpoint) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:robosense.rs_hmi.config.UdpMulticastTaskConfig.udp_multicast_endpoint)
}
inline ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* UdpMulticastTaskConfig::release_udp_multicast_endpoint() {
  _has_bits_[0] &= ~0x00000002u;
  ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* temp = udp_multicast_endpoint_;
  udp_multicast_endpoint_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* UdpMulticastTaskConfig::unsafe_arena_release_udp_multicast_endpoint() {
  // @@protoc_insertion_point(field_release:robosense.rs_hmi.config.UdpMulticastTaskConfig.udp_multicast_endpoint)
  _has_bits_[0] &= ~0x00000002u;
  ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* temp = udp_multicast_endpoint_;
  udp_multicast_endpoint_ = nullptr;
  return temp;
}
inline ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* UdpMulticastTaskConfig::_internal_mutable_udp_multicast_endpoint() {
  _has_bits_[0] |= 0x00000002u;
  if (udp_multicast_endpoint_ == nullptr) {
    auto* p = CreateMaybeMessage<::robosense::rs_hmi::config::UdpMulticastEndPointConfig>(GetArena());
    udp_multicast_endpoint_ = p;
  }
  return udp_multicast_endpoint_;
}
inline ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* UdpMulticastTaskConfig::mutable_udp_multicast_endpoint() {
  // @@protoc_insertion_point(field_mutable:robosense.rs_hmi.config.UdpMulticastTaskConfig.udp_multicast_endpoint)
  return _internal_mutable_udp_multicast_endpoint();
}
inline void UdpMulticastTaskConfig::set_allocated_udp_multicast_endpoint(::robosense::rs_hmi::config::UdpMulticastEndPointConfig* udp_multicast_endpoint) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete udp_multicast_endpoint_;
  }
  if (udp_multicast_endpoint) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(udp_multicast_endpoint);
    if (message_arena != submessage_arena) {
      udp_multicast_endpoint = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, udp_multicast_endpoint, submessage_arena);
    }
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  udp_multicast_endpoint_ = udp_multicast_endpoint;
  // @@protoc_insertion_point(field_set_allocated:robosense.rs_hmi.config.UdpMulticastTaskConfig.udp_multicast_endpoint)
}

// -------------------------------------------------------------------

// UdpP2PTaskConfig

// optional string task_socket_key = 1;
inline bool UdpP2PTaskConfig::_internal_has_task_socket_key() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool UdpP2PTaskConfig::has_task_socket_key() const {
  return _internal_has_task_socket_key();
}
inline void UdpP2PTaskConfig::clear_task_socket_key() {
  task_socket_key_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& UdpP2PTaskConfig::task_socket_key() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpP2PTaskConfig.task_socket_key)
  return _internal_task_socket_key();
}
inline void UdpP2PTaskConfig::set_task_socket_key(const std::string& value) {
  _internal_set_task_socket_key(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.UdpP2PTaskConfig.task_socket_key)
}
inline std::string* UdpP2PTaskConfig::mutable_task_socket_key() {
  // @@protoc_insertion_point(field_mutable:robosense.rs_hmi.config.UdpP2PTaskConfig.task_socket_key)
  return _internal_mutable_task_socket_key();
}
inline const std::string& UdpP2PTaskConfig::_internal_task_socket_key() const {
  return task_socket_key_.Get();
}
inline void UdpP2PTaskConfig::_internal_set_task_socket_key(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  task_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void UdpP2PTaskConfig::set_task_socket_key(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  task_socket_key_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.rs_hmi.config.UdpP2PTaskConfig.task_socket_key)
}
inline void UdpP2PTaskConfig::set_task_socket_key(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  task_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.rs_hmi.config.UdpP2PTaskConfig.task_socket_key)
}
inline void UdpP2PTaskConfig::set_task_socket_key(const char* value,
    size_t size) {
  _has_bits_[0] |= 0x00000001u;
  task_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.rs_hmi.config.UdpP2PTaskConfig.task_socket_key)
}
inline std::string* UdpP2PTaskConfig::_internal_mutable_task_socket_key() {
  _has_bits_[0] |= 0x00000001u;
  return task_socket_key_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* UdpP2PTaskConfig::release_task_socket_key() {
  // @@protoc_insertion_point(field_release:robosense.rs_hmi.config.UdpP2PTaskConfig.task_socket_key)
  if (!_internal_has_task_socket_key()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return task_socket_key_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void UdpP2PTaskConfig::set_allocated_task_socket_key(std::string* task_socket_key) {
  if (task_socket_key != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  task_socket_key_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), task_socket_key,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.rs_hmi.config.UdpP2PTaskConfig.task_socket_key)
}

// optional .robosense.rs_hmi.config.UdpP2PEndPointConfig udp_p2p_endpoint = 2;
inline bool UdpP2PTaskConfig::_internal_has_udp_p2p_endpoint() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  PROTOBUF_ASSUME(!value || udp_p2p_endpoint_ != nullptr);
  return value;
}
inline bool UdpP2PTaskConfig::has_udp_p2p_endpoint() const {
  return _internal_has_udp_p2p_endpoint();
}
inline void UdpP2PTaskConfig::clear_udp_p2p_endpoint() {
  if (udp_p2p_endpoint_ != nullptr) udp_p2p_endpoint_->Clear();
  _has_bits_[0] &= ~0x00000002u;
}
inline const ::robosense::rs_hmi::config::UdpP2PEndPointConfig& UdpP2PTaskConfig::_internal_udp_p2p_endpoint() const {
  const ::robosense::rs_hmi::config::UdpP2PEndPointConfig* p = udp_p2p_endpoint_;
  return p != nullptr ? *p : reinterpret_cast<const ::robosense::rs_hmi::config::UdpP2PEndPointConfig&>(
      ::robosense::rs_hmi::config::_UdpP2PEndPointConfig_default_instance_);
}
inline const ::robosense::rs_hmi::config::UdpP2PEndPointConfig& UdpP2PTaskConfig::udp_p2p_endpoint() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.UdpP2PTaskConfig.udp_p2p_endpoint)
  return _internal_udp_p2p_endpoint();
}
inline void UdpP2PTaskConfig::unsafe_arena_set_allocated_udp_p2p_endpoint(
    ::robosense::rs_hmi::config::UdpP2PEndPointConfig* udp_p2p_endpoint) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(udp_p2p_endpoint_);
  }
  udp_p2p_endpoint_ = udp_p2p_endpoint;
  if (udp_p2p_endpoint) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:robosense.rs_hmi.config.UdpP2PTaskConfig.udp_p2p_endpoint)
}
inline ::robosense::rs_hmi::config::UdpP2PEndPointConfig* UdpP2PTaskConfig::release_udp_p2p_endpoint() {
  _has_bits_[0] &= ~0x00000002u;
  ::robosense::rs_hmi::config::UdpP2PEndPointConfig* temp = udp_p2p_endpoint_;
  udp_p2p_endpoint_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::robosense::rs_hmi::config::UdpP2PEndPointConfig* UdpP2PTaskConfig::unsafe_arena_release_udp_p2p_endpoint() {
  // @@protoc_insertion_point(field_release:robosense.rs_hmi.config.UdpP2PTaskConfig.udp_p2p_endpoint)
  _has_bits_[0] &= ~0x00000002u;
  ::robosense::rs_hmi::config::UdpP2PEndPointConfig* temp = udp_p2p_endpoint_;
  udp_p2p_endpoint_ = nullptr;
  return temp;
}
inline ::robosense::rs_hmi::config::UdpP2PEndPointConfig* UdpP2PTaskConfig::_internal_mutable_udp_p2p_endpoint() {
  _has_bits_[0] |= 0x00000002u;
  if (udp_p2p_endpoint_ == nullptr) {
    auto* p = CreateMaybeMessage<::robosense::rs_hmi::config::UdpP2PEndPointConfig>(GetArena());
    udp_p2p_endpoint_ = p;
  }
  return udp_p2p_endpoint_;
}
inline ::robosense::rs_hmi::config::UdpP2PEndPointConfig* UdpP2PTaskConfig::mutable_udp_p2p_endpoint() {
  // @@protoc_insertion_point(field_mutable:robosense.rs_hmi.config.UdpP2PTaskConfig.udp_p2p_endpoint)
  return _internal_mutable_udp_p2p_endpoint();
}
inline void UdpP2PTaskConfig::set_allocated_udp_p2p_endpoint(::robosense::rs_hmi::config::UdpP2PEndPointConfig* udp_p2p_endpoint) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete udp_p2p_endpoint_;
  }
  if (udp_p2p_endpoint) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(udp_p2p_endpoint);
    if (message_arena != submessage_arena) {
      udp_p2p_endpoint = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, udp_p2p_endpoint, submessage_arena);
    }
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  udp_p2p_endpoint_ = udp_p2p_endpoint;
  // @@protoc_insertion_point(field_set_allocated:robosense.rs_hmi.config.UdpP2PTaskConfig.udp_p2p_endpoint)
}

// -------------------------------------------------------------------

// SocketTaskConfig

// .robosense.rs_hmi.config.WebsocketTaskConfig websocket_task = 1;
inline bool SocketTaskConfig::_internal_has_websocket_task() const {
  return socket_endpoint_config_case() == kWebsocketTask;
}
inline bool SocketTaskConfig::has_websocket_task() const {
  return _internal_has_websocket_task();
}
inline void SocketTaskConfig::set_has_websocket_task() {
  _oneof_case_[0] = kWebsocketTask;
}
inline void SocketTaskConfig::clear_websocket_task() {
  if (_internal_has_websocket_task()) {
    if (GetArena() == nullptr) {
      delete socket_endpoint_config_.websocket_task_;
    }
    clear_has_socket_endpoint_config();
  }
}
inline ::robosense::rs_hmi::config::WebsocketTaskConfig* SocketTaskConfig::release_websocket_task() {
  // @@protoc_insertion_point(field_release:robosense.rs_hmi.config.SocketTaskConfig.websocket_task)
  if (_internal_has_websocket_task()) {
    clear_has_socket_endpoint_config();
      ::robosense::rs_hmi::config::WebsocketTaskConfig* temp = socket_endpoint_config_.websocket_task_;
    if (GetArena() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    socket_endpoint_config_.websocket_task_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::robosense::rs_hmi::config::WebsocketTaskConfig& SocketTaskConfig::_internal_websocket_task() const {
  return _internal_has_websocket_task()
      ? *socket_endpoint_config_.websocket_task_
      : reinterpret_cast< ::robosense::rs_hmi::config::WebsocketTaskConfig&>(::robosense::rs_hmi::config::_WebsocketTaskConfig_default_instance_);
}
inline const ::robosense::rs_hmi::config::WebsocketTaskConfig& SocketTaskConfig::websocket_task() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.SocketTaskConfig.websocket_task)
  return _internal_websocket_task();
}
inline ::robosense::rs_hmi::config::WebsocketTaskConfig* SocketTaskConfig::unsafe_arena_release_websocket_task() {
  // @@protoc_insertion_point(field_unsafe_arena_release:robosense.rs_hmi.config.SocketTaskConfig.websocket_task)
  if (_internal_has_websocket_task()) {
    clear_has_socket_endpoint_config();
    ::robosense::rs_hmi::config::WebsocketTaskConfig* temp = socket_endpoint_config_.websocket_task_;
    socket_endpoint_config_.websocket_task_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void SocketTaskConfig::unsafe_arena_set_allocated_websocket_task(::robosense::rs_hmi::config::WebsocketTaskConfig* websocket_task) {
  clear_socket_endpoint_config();
  if (websocket_task) {
    set_has_websocket_task();
    socket_endpoint_config_.websocket_task_ = websocket_task;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:robosense.rs_hmi.config.SocketTaskConfig.websocket_task)
}
inline ::robosense::rs_hmi::config::WebsocketTaskConfig* SocketTaskConfig::_internal_mutable_websocket_task() {
  if (!_internal_has_websocket_task()) {
    clear_socket_endpoint_config();
    set_has_websocket_task();
    socket_endpoint_config_.websocket_task_ = CreateMaybeMessage< ::robosense::rs_hmi::config::WebsocketTaskConfig >(GetArena());
  }
  return socket_endpoint_config_.websocket_task_;
}
inline ::robosense::rs_hmi::config::WebsocketTaskConfig* SocketTaskConfig::mutable_websocket_task() {
  // @@protoc_insertion_point(field_mutable:robosense.rs_hmi.config.SocketTaskConfig.websocket_task)
  return _internal_mutable_websocket_task();
}

// .robosense.rs_hmi.config.UdpMulticastTaskConfig udp_multicast_task = 2;
inline bool SocketTaskConfig::_internal_has_udp_multicast_task() const {
  return socket_endpoint_config_case() == kUdpMulticastTask;
}
inline bool SocketTaskConfig::has_udp_multicast_task() const {
  return _internal_has_udp_multicast_task();
}
inline void SocketTaskConfig::set_has_udp_multicast_task() {
  _oneof_case_[0] = kUdpMulticastTask;
}
inline void SocketTaskConfig::clear_udp_multicast_task() {
  if (_internal_has_udp_multicast_task()) {
    if (GetArena() == nullptr) {
      delete socket_endpoint_config_.udp_multicast_task_;
    }
    clear_has_socket_endpoint_config();
  }
}
inline ::robosense::rs_hmi::config::UdpMulticastTaskConfig* SocketTaskConfig::release_udp_multicast_task() {
  // @@protoc_insertion_point(field_release:robosense.rs_hmi.config.SocketTaskConfig.udp_multicast_task)
  if (_internal_has_udp_multicast_task()) {
    clear_has_socket_endpoint_config();
      ::robosense::rs_hmi::config::UdpMulticastTaskConfig* temp = socket_endpoint_config_.udp_multicast_task_;
    if (GetArena() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    socket_endpoint_config_.udp_multicast_task_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::robosense::rs_hmi::config::UdpMulticastTaskConfig& SocketTaskConfig::_internal_udp_multicast_task() const {
  return _internal_has_udp_multicast_task()
      ? *socket_endpoint_config_.udp_multicast_task_
      : reinterpret_cast< ::robosense::rs_hmi::config::UdpMulticastTaskConfig&>(::robosense::rs_hmi::config::_UdpMulticastTaskConfig_default_instance_);
}
inline const ::robosense::rs_hmi::config::UdpMulticastTaskConfig& SocketTaskConfig::udp_multicast_task() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.SocketTaskConfig.udp_multicast_task)
  return _internal_udp_multicast_task();
}
inline ::robosense::rs_hmi::config::UdpMulticastTaskConfig* SocketTaskConfig::unsafe_arena_release_udp_multicast_task() {
  // @@protoc_insertion_point(field_unsafe_arena_release:robosense.rs_hmi.config.SocketTaskConfig.udp_multicast_task)
  if (_internal_has_udp_multicast_task()) {
    clear_has_socket_endpoint_config();
    ::robosense::rs_hmi::config::UdpMulticastTaskConfig* temp = socket_endpoint_config_.udp_multicast_task_;
    socket_endpoint_config_.udp_multicast_task_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void SocketTaskConfig::unsafe_arena_set_allocated_udp_multicast_task(::robosense::rs_hmi::config::UdpMulticastTaskConfig* udp_multicast_task) {
  clear_socket_endpoint_config();
  if (udp_multicast_task) {
    set_has_udp_multicast_task();
    socket_endpoint_config_.udp_multicast_task_ = udp_multicast_task;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:robosense.rs_hmi.config.SocketTaskConfig.udp_multicast_task)
}
inline ::robosense::rs_hmi::config::UdpMulticastTaskConfig* SocketTaskConfig::_internal_mutable_udp_multicast_task() {
  if (!_internal_has_udp_multicast_task()) {
    clear_socket_endpoint_config();
    set_has_udp_multicast_task();
    socket_endpoint_config_.udp_multicast_task_ = CreateMaybeMessage< ::robosense::rs_hmi::config::UdpMulticastTaskConfig >(GetArena());
  }
  return socket_endpoint_config_.udp_multicast_task_;
}
inline ::robosense::rs_hmi::config::UdpMulticastTaskConfig* SocketTaskConfig::mutable_udp_multicast_task() {
  // @@protoc_insertion_point(field_mutable:robosense.rs_hmi.config.SocketTaskConfig.udp_multicast_task)
  return _internal_mutable_udp_multicast_task();
}

// .robosense.rs_hmi.config.UdpP2PTaskConfig udp_p2p_task = 3;
inline bool SocketTaskConfig::_internal_has_udp_p2p_task() const {
  return socket_endpoint_config_case() == kUdpP2PTask;
}
inline bool SocketTaskConfig::has_udp_p2p_task() const {
  return _internal_has_udp_p2p_task();
}
inline void SocketTaskConfig::set_has_udp_p2p_task() {
  _oneof_case_[0] = kUdpP2PTask;
}
inline void SocketTaskConfig::clear_udp_p2p_task() {
  if (_internal_has_udp_p2p_task()) {
    if (GetArena() == nullptr) {
      delete socket_endpoint_config_.udp_p2p_task_;
    }
    clear_has_socket_endpoint_config();
  }
}
inline ::robosense::rs_hmi::config::UdpP2PTaskConfig* SocketTaskConfig::release_udp_p2p_task() {
  // @@protoc_insertion_point(field_release:robosense.rs_hmi.config.SocketTaskConfig.udp_p2p_task)
  if (_internal_has_udp_p2p_task()) {
    clear_has_socket_endpoint_config();
      ::robosense::rs_hmi::config::UdpP2PTaskConfig* temp = socket_endpoint_config_.udp_p2p_task_;
    if (GetArena() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    socket_endpoint_config_.udp_p2p_task_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::robosense::rs_hmi::config::UdpP2PTaskConfig& SocketTaskConfig::_internal_udp_p2p_task() const {
  return _internal_has_udp_p2p_task()
      ? *socket_endpoint_config_.udp_p2p_task_
      : reinterpret_cast< ::robosense::rs_hmi::config::UdpP2PTaskConfig&>(::robosense::rs_hmi::config::_UdpP2PTaskConfig_default_instance_);
}
inline const ::robosense::rs_hmi::config::UdpP2PTaskConfig& SocketTaskConfig::udp_p2p_task() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.SocketTaskConfig.udp_p2p_task)
  return _internal_udp_p2p_task();
}
inline ::robosense::rs_hmi::config::UdpP2PTaskConfig* SocketTaskConfig::unsafe_arena_release_udp_p2p_task() {
  // @@protoc_insertion_point(field_unsafe_arena_release:robosense.rs_hmi.config.SocketTaskConfig.udp_p2p_task)
  if (_internal_has_udp_p2p_task()) {
    clear_has_socket_endpoint_config();
    ::robosense::rs_hmi::config::UdpP2PTaskConfig* temp = socket_endpoint_config_.udp_p2p_task_;
    socket_endpoint_config_.udp_p2p_task_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void SocketTaskConfig::unsafe_arena_set_allocated_udp_p2p_task(::robosense::rs_hmi::config::UdpP2PTaskConfig* udp_p2p_task) {
  clear_socket_endpoint_config();
  if (udp_p2p_task) {
    set_has_udp_p2p_task();
    socket_endpoint_config_.udp_p2p_task_ = udp_p2p_task;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:robosense.rs_hmi.config.SocketTaskConfig.udp_p2p_task)
}
inline ::robosense::rs_hmi::config::UdpP2PTaskConfig* SocketTaskConfig::_internal_mutable_udp_p2p_task() {
  if (!_internal_has_udp_p2p_task()) {
    clear_socket_endpoint_config();
    set_has_udp_p2p_task();
    socket_endpoint_config_.udp_p2p_task_ = CreateMaybeMessage< ::robosense::rs_hmi::config::UdpP2PTaskConfig >(GetArena());
  }
  return socket_endpoint_config_.udp_p2p_task_;
}
inline ::robosense::rs_hmi::config::UdpP2PTaskConfig* SocketTaskConfig::mutable_udp_p2p_task() {
  // @@protoc_insertion_point(field_mutable:robosense.rs_hmi.config.SocketTaskConfig.udp_p2p_task)
  return _internal_mutable_udp_p2p_task();
}

inline bool SocketTaskConfig::has_socket_endpoint_config() const {
  return socket_endpoint_config_case() != SOCKET_ENDPOINT_CONFIG_NOT_SET;
}
inline void SocketTaskConfig::clear_has_socket_endpoint_config() {
  _oneof_case_[0] = SOCKET_ENDPOINT_CONFIG_NOT_SET;
}
inline SocketTaskConfig::SocketEndpointConfigCase SocketTaskConfig::socket_endpoint_config_case() const {
  return SocketTaskConfig::SocketEndpointConfigCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// HmiSocketTaskConfig

// repeated .robosense.rs_hmi.config.SocketTaskConfig socket_task_configs = 1;
inline int HmiSocketTaskConfig::_internal_socket_task_configs_size() const {
  return socket_task_configs_.size();
}
inline int HmiSocketTaskConfig::socket_task_configs_size() const {
  return _internal_socket_task_configs_size();
}
inline void HmiSocketTaskConfig::clear_socket_task_configs() {
  socket_task_configs_.Clear();
}
inline ::robosense::rs_hmi::config::SocketTaskConfig* HmiSocketTaskConfig::mutable_socket_task_configs(int index) {
  // @@protoc_insertion_point(field_mutable:robosense.rs_hmi.config.HmiSocketTaskConfig.socket_task_configs)
  return socket_task_configs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::robosense::rs_hmi::config::SocketTaskConfig >*
HmiSocketTaskConfig::mutable_socket_task_configs() {
  // @@protoc_insertion_point(field_mutable_list:robosense.rs_hmi.config.HmiSocketTaskConfig.socket_task_configs)
  return &socket_task_configs_;
}
inline const ::robosense::rs_hmi::config::SocketTaskConfig& HmiSocketTaskConfig::_internal_socket_task_configs(int index) const {
  return socket_task_configs_.Get(index);
}
inline const ::robosense::rs_hmi::config::SocketTaskConfig& HmiSocketTaskConfig::socket_task_configs(int index) const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.HmiSocketTaskConfig.socket_task_configs)
  return _internal_socket_task_configs(index);
}
inline ::robosense::rs_hmi::config::SocketTaskConfig* HmiSocketTaskConfig::_internal_add_socket_task_configs() {
  return socket_task_configs_.Add();
}
inline ::robosense::rs_hmi::config::SocketTaskConfig* HmiSocketTaskConfig::add_socket_task_configs() {
  // @@protoc_insertion_point(field_add:robosense.rs_hmi.config.HmiSocketTaskConfig.socket_task_configs)
  return _internal_add_socket_task_configs();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::robosense::rs_hmi::config::SocketTaskConfig >&
HmiSocketTaskConfig::socket_task_configs() const {
  // @@protoc_insertion_point(field_list:robosense.rs_hmi.config.HmiSocketTaskConfig.socket_task_configs)
  return socket_task_configs_;
}

// -------------------------------------------------------------------

// HmiSocketTopicTaskConfig

// repeated string support_message_topics = 1;
inline int HmiSocketTopicTaskConfig::_internal_support_message_topics_size() const {
  return support_message_topics_.size();
}
inline int HmiSocketTopicTaskConfig::support_message_topics_size() const {
  return _internal_support_message_topics_size();
}
inline void HmiSocketTopicTaskConfig::clear_support_message_topics() {
  support_message_topics_.Clear();
}
inline std::string* HmiSocketTopicTaskConfig::add_support_message_topics() {
  // @@protoc_insertion_point(field_add_mutable:robosense.rs_hmi.config.HmiSocketTopicTaskConfig.support_message_topics)
  return _internal_add_support_message_topics();
}
inline const std::string& HmiSocketTopicTaskConfig::_internal_support_message_topics(int index) const {
  return support_message_topics_.Get(index);
}
inline const std::string& HmiSocketTopicTaskConfig::support_message_topics(int index) const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.HmiSocketTopicTaskConfig.support_message_topics)
  return _internal_support_message_topics(index);
}
inline std::string* HmiSocketTopicTaskConfig::mutable_support_message_topics(int index) {
  // @@protoc_insertion_point(field_mutable:robosense.rs_hmi.config.HmiSocketTopicTaskConfig.support_message_topics)
  return support_message_topics_.Mutable(index);
}
inline void HmiSocketTopicTaskConfig::set_support_message_topics(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.HmiSocketTopicTaskConfig.support_message_topics)
  support_message_topics_.Mutable(index)->assign(value);
}
inline void HmiSocketTopicTaskConfig::set_support_message_topics(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.HmiSocketTopicTaskConfig.support_message_topics)
  support_message_topics_.Mutable(index)->assign(std::move(value));
}
inline void HmiSocketTopicTaskConfig::set_support_message_topics(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  support_message_topics_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:robosense.rs_hmi.config.HmiSocketTopicTaskConfig.support_message_topics)
}
inline void HmiSocketTopicTaskConfig::set_support_message_topics(int index, const char* value, size_t size) {
  support_message_topics_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:robosense.rs_hmi.config.HmiSocketTopicTaskConfig.support_message_topics)
}
inline std::string* HmiSocketTopicTaskConfig::_internal_add_support_message_topics() {
  return support_message_topics_.Add();
}
inline void HmiSocketTopicTaskConfig::add_support_message_topics(const std::string& value) {
  support_message_topics_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:robosense.rs_hmi.config.HmiSocketTopicTaskConfig.support_message_topics)
}
inline void HmiSocketTopicTaskConfig::add_support_message_topics(std::string&& value) {
  support_message_topics_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:robosense.rs_hmi.config.HmiSocketTopicTaskConfig.support_message_topics)
}
inline void HmiSocketTopicTaskConfig::add_support_message_topics(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  support_message_topics_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:robosense.rs_hmi.config.HmiSocketTopicTaskConfig.support_message_topics)
}
inline void HmiSocketTopicTaskConfig::add_support_message_topics(const char* value, size_t size) {
  support_message_topics_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:robosense.rs_hmi.config.HmiSocketTopicTaskConfig.support_message_topics)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
HmiSocketTopicTaskConfig::support_message_topics() const {
  // @@protoc_insertion_point(field_list:robosense.rs_hmi.config.HmiSocketTopicTaskConfig.support_message_topics)
  return support_message_topics_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
HmiSocketTopicTaskConfig::mutable_support_message_topics() {
  // @@protoc_insertion_point(field_mutable_list:robosense.rs_hmi.config.HmiSocketTopicTaskConfig.support_message_topics)
  return &support_message_topics_;
}

// optional string task_socket_key = 2;
inline bool HmiSocketTopicTaskConfig::_internal_has_task_socket_key() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool HmiSocketTopicTaskConfig::has_task_socket_key() const {
  return _internal_has_task_socket_key();
}
inline void HmiSocketTopicTaskConfig::clear_task_socket_key() {
  task_socket_key_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& HmiSocketTopicTaskConfig::task_socket_key() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.HmiSocketTopicTaskConfig.task_socket_key)
  return _internal_task_socket_key();
}
inline void HmiSocketTopicTaskConfig::set_task_socket_key(const std::string& value) {
  _internal_set_task_socket_key(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.HmiSocketTopicTaskConfig.task_socket_key)
}
inline std::string* HmiSocketTopicTaskConfig::mutable_task_socket_key() {
  // @@protoc_insertion_point(field_mutable:robosense.rs_hmi.config.HmiSocketTopicTaskConfig.task_socket_key)
  return _internal_mutable_task_socket_key();
}
inline const std::string& HmiSocketTopicTaskConfig::_internal_task_socket_key() const {
  return task_socket_key_.Get();
}
inline void HmiSocketTopicTaskConfig::_internal_set_task_socket_key(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  task_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void HmiSocketTopicTaskConfig::set_task_socket_key(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  task_socket_key_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.rs_hmi.config.HmiSocketTopicTaskConfig.task_socket_key)
}
inline void HmiSocketTopicTaskConfig::set_task_socket_key(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  task_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.rs_hmi.config.HmiSocketTopicTaskConfig.task_socket_key)
}
inline void HmiSocketTopicTaskConfig::set_task_socket_key(const char* value,
    size_t size) {
  _has_bits_[0] |= 0x00000001u;
  task_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.rs_hmi.config.HmiSocketTopicTaskConfig.task_socket_key)
}
inline std::string* HmiSocketTopicTaskConfig::_internal_mutable_task_socket_key() {
  _has_bits_[0] |= 0x00000001u;
  return task_socket_key_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* HmiSocketTopicTaskConfig::release_task_socket_key() {
  // @@protoc_insertion_point(field_release:robosense.rs_hmi.config.HmiSocketTopicTaskConfig.task_socket_key)
  if (!_internal_has_task_socket_key()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return task_socket_key_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void HmiSocketTopicTaskConfig::set_allocated_task_socket_key(std::string* task_socket_key) {
  if (task_socket_key != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  task_socket_key_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), task_socket_key,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.rs_hmi.config.HmiSocketTopicTaskConfig.task_socket_key)
}

// -------------------------------------------------------------------

// HmiTaskSocketConfig

// optional bool enable_task = 1 [default = false];
inline bool HmiTaskSocketConfig::_internal_has_enable_task() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool HmiTaskSocketConfig::has_enable_task() const {
  return _internal_has_enable_task();
}
inline void HmiTaskSocketConfig::clear_enable_task() {
  enable_task_ = false;
  _has_bits_[0] &= ~0x00000001u;
}
inline bool HmiTaskSocketConfig::_internal_enable_task() const {
  return enable_task_;
}
inline bool HmiTaskSocketConfig::enable_task() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.HmiTaskSocketConfig.enable_task)
  return _internal_enable_task();
}
inline void HmiTaskSocketConfig::_internal_set_enable_task(bool value) {
  _has_bits_[0] |= 0x00000001u;
  enable_task_ = value;
}
inline void HmiTaskSocketConfig::set_enable_task(bool value) {
  _internal_set_enable_task(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.HmiTaskSocketConfig.enable_task)
}

// repeated .robosense.rs_hmi.config.HmiSocketTopicTaskConfig task_socket_configs = 2;
inline int HmiTaskSocketConfig::_internal_task_socket_configs_size() const {
  return task_socket_configs_.size();
}
inline int HmiTaskSocketConfig::task_socket_configs_size() const {
  return _internal_task_socket_configs_size();
}
inline void HmiTaskSocketConfig::clear_task_socket_configs() {
  task_socket_configs_.Clear();
}
inline ::robosense::rs_hmi::config::HmiSocketTopicTaskConfig* HmiTaskSocketConfig::mutable_task_socket_configs(int index) {
  // @@protoc_insertion_point(field_mutable:robosense.rs_hmi.config.HmiTaskSocketConfig.task_socket_configs)
  return task_socket_configs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::robosense::rs_hmi::config::HmiSocketTopicTaskConfig >*
HmiTaskSocketConfig::mutable_task_socket_configs() {
  // @@protoc_insertion_point(field_mutable_list:robosense.rs_hmi.config.HmiTaskSocketConfig.task_socket_configs)
  return &task_socket_configs_;
}
inline const ::robosense::rs_hmi::config::HmiSocketTopicTaskConfig& HmiTaskSocketConfig::_internal_task_socket_configs(int index) const {
  return task_socket_configs_.Get(index);
}
inline const ::robosense::rs_hmi::config::HmiSocketTopicTaskConfig& HmiTaskSocketConfig::task_socket_configs(int index) const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.HmiTaskSocketConfig.task_socket_configs)
  return _internal_task_socket_configs(index);
}
inline ::robosense::rs_hmi::config::HmiSocketTopicTaskConfig* HmiTaskSocketConfig::_internal_add_task_socket_configs() {
  return task_socket_configs_.Add();
}
inline ::robosense::rs_hmi::config::HmiSocketTopicTaskConfig* HmiTaskSocketConfig::add_task_socket_configs() {
  // @@protoc_insertion_point(field_add:robosense.rs_hmi.config.HmiTaskSocketConfig.task_socket_configs)
  return _internal_add_task_socket_configs();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::robosense::rs_hmi::config::HmiSocketTopicTaskConfig >&
HmiTaskSocketConfig::task_socket_configs() const {
  // @@protoc_insertion_point(field_list:robosense.rs_hmi.config.HmiTaskSocketConfig.task_socket_configs)
  return task_socket_configs_;
}

// -------------------------------------------------------------------

// WebsocketRenderTaskConfig

// optional .robosense.rs_hmi.config.HmiTaskSocketConfig websocket_task = 1;
inline bool WebsocketRenderTaskConfig::_internal_has_websocket_task() const {
  bool value = (_has_bits_[0] & 0x00000020u) != 0;
  PROTOBUF_ASSUME(!value || websocket_task_ != nullptr);
  return value;
}
inline bool WebsocketRenderTaskConfig::has_websocket_task() const {
  return _internal_has_websocket_task();
}
inline void WebsocketRenderTaskConfig::clear_websocket_task() {
  if (websocket_task_ != nullptr) websocket_task_->Clear();
  _has_bits_[0] &= ~0x00000020u;
}
inline const ::robosense::rs_hmi::config::HmiTaskSocketConfig& WebsocketRenderTaskConfig::_internal_websocket_task() const {
  const ::robosense::rs_hmi::config::HmiTaskSocketConfig* p = websocket_task_;
  return p != nullptr ? *p : reinterpret_cast<const ::robosense::rs_hmi::config::HmiTaskSocketConfig&>(
      ::robosense::rs_hmi::config::_HmiTaskSocketConfig_default_instance_);
}
inline const ::robosense::rs_hmi::config::HmiTaskSocketConfig& WebsocketRenderTaskConfig::websocket_task() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketRenderTaskConfig.websocket_task)
  return _internal_websocket_task();
}
inline void WebsocketRenderTaskConfig::unsafe_arena_set_allocated_websocket_task(
    ::robosense::rs_hmi::config::HmiTaskSocketConfig* websocket_task) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(websocket_task_);
  }
  websocket_task_ = websocket_task;
  if (websocket_task) {
    _has_bits_[0] |= 0x00000020u;
  } else {
    _has_bits_[0] &= ~0x00000020u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:robosense.rs_hmi.config.WebsocketRenderTaskConfig.websocket_task)
}
inline ::robosense::rs_hmi::config::HmiTaskSocketConfig* WebsocketRenderTaskConfig::release_websocket_task() {
  _has_bits_[0] &= ~0x00000020u;
  ::robosense::rs_hmi::config::HmiTaskSocketConfig* temp = websocket_task_;
  websocket_task_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::robosense::rs_hmi::config::HmiTaskSocketConfig* WebsocketRenderTaskConfig::unsafe_arena_release_websocket_task() {
  // @@protoc_insertion_point(field_release:robosense.rs_hmi.config.WebsocketRenderTaskConfig.websocket_task)
  _has_bits_[0] &= ~0x00000020u;
  ::robosense::rs_hmi::config::HmiTaskSocketConfig* temp = websocket_task_;
  websocket_task_ = nullptr;
  return temp;
}
inline ::robosense::rs_hmi::config::HmiTaskSocketConfig* WebsocketRenderTaskConfig::_internal_mutable_websocket_task() {
  _has_bits_[0] |= 0x00000020u;
  if (websocket_task_ == nullptr) {
    auto* p = CreateMaybeMessage<::robosense::rs_hmi::config::HmiTaskSocketConfig>(GetArena());
    websocket_task_ = p;
  }
  return websocket_task_;
}
inline ::robosense::rs_hmi::config::HmiTaskSocketConfig* WebsocketRenderTaskConfig::mutable_websocket_task() {
  // @@protoc_insertion_point(field_mutable:robosense.rs_hmi.config.WebsocketRenderTaskConfig.websocket_task)
  return _internal_mutable_websocket_task();
}
inline void WebsocketRenderTaskConfig::set_allocated_websocket_task(::robosense::rs_hmi::config::HmiTaskSocketConfig* websocket_task) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete websocket_task_;
  }
  if (websocket_task) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(websocket_task);
    if (message_arena != submessage_arena) {
      websocket_task = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, websocket_task, submessage_arena);
    }
    _has_bits_[0] |= 0x00000020u;
  } else {
    _has_bits_[0] &= ~0x00000020u;
  }
  websocket_task_ = websocket_task;
  // @@protoc_insertion_point(field_set_allocated:robosense.rs_hmi.config.WebsocketRenderTaskConfig.websocket_task)
}

// optional bool enable_websocket_buffer = 2 [default = true];
inline bool WebsocketRenderTaskConfig::_internal_has_enable_websocket_buffer() const {
  bool value = (_has_bits_[0] & 0x00000400u) != 0;
  return value;
}
inline bool WebsocketRenderTaskConfig::has_enable_websocket_buffer() const {
  return _internal_has_enable_websocket_buffer();
}
inline void WebsocketRenderTaskConfig::clear_enable_websocket_buffer() {
  enable_websocket_buffer_ = true;
  _has_bits_[0] &= ~0x00000400u;
}
inline bool WebsocketRenderTaskConfig::_internal_enable_websocket_buffer() const {
  return enable_websocket_buffer_;
}
inline bool WebsocketRenderTaskConfig::enable_websocket_buffer() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketRenderTaskConfig.enable_websocket_buffer)
  return _internal_enable_websocket_buffer();
}
inline void WebsocketRenderTaskConfig::_internal_set_enable_websocket_buffer(bool value) {
  _has_bits_[0] |= 0x00000400u;
  enable_websocket_buffer_ = value;
}
inline void WebsocketRenderTaskConfig::set_enable_websocket_buffer(bool value) {
  _internal_set_enable_websocket_buffer(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.WebsocketRenderTaskConfig.enable_websocket_buffer)
}

// optional uint32 websocket_buffer_size = 3 [default = 2097152];
inline bool WebsocketRenderTaskConfig::_internal_has_websocket_buffer_size() const {
  bool value = (_has_bits_[0] & 0x00000800u) != 0;
  return value;
}
inline bool WebsocketRenderTaskConfig::has_websocket_buffer_size() const {
  return _internal_has_websocket_buffer_size();
}
inline void WebsocketRenderTaskConfig::clear_websocket_buffer_size() {
  websocket_buffer_size_ = 2097152u;
  _has_bits_[0] &= ~0x00000800u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketRenderTaskConfig::_internal_websocket_buffer_size() const {
  return websocket_buffer_size_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketRenderTaskConfig::websocket_buffer_size() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketRenderTaskConfig.websocket_buffer_size)
  return _internal_websocket_buffer_size();
}
inline void WebsocketRenderTaskConfig::_internal_set_websocket_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000800u;
  websocket_buffer_size_ = value;
}
inline void WebsocketRenderTaskConfig::set_websocket_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_websocket_buffer_size(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.WebsocketRenderTaskConfig.websocket_buffer_size)
}

// optional uint32 low_websocket_buffer_size = 4 [default = 524288];
inline bool WebsocketRenderTaskConfig::_internal_has_low_websocket_buffer_size() const {
  bool value = (_has_bits_[0] & 0x00001000u) != 0;
  return value;
}
inline bool WebsocketRenderTaskConfig::has_low_websocket_buffer_size() const {
  return _internal_has_low_websocket_buffer_size();
}
inline void WebsocketRenderTaskConfig::clear_low_websocket_buffer_size() {
  low_websocket_buffer_size_ = 524288u;
  _has_bits_[0] &= ~0x00001000u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketRenderTaskConfig::_internal_low_websocket_buffer_size() const {
  return low_websocket_buffer_size_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketRenderTaskConfig::low_websocket_buffer_size() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketRenderTaskConfig.low_websocket_buffer_size)
  return _internal_low_websocket_buffer_size();
}
inline void WebsocketRenderTaskConfig::_internal_set_low_websocket_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00001000u;
  low_websocket_buffer_size_ = value;
}
inline void WebsocketRenderTaskConfig::set_low_websocket_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_low_websocket_buffer_size(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.WebsocketRenderTaskConfig.low_websocket_buffer_size)
}

// optional string websocket_cmd_socket_key = 5;
inline bool WebsocketRenderTaskConfig::_internal_has_websocket_cmd_socket_key() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool WebsocketRenderTaskConfig::has_websocket_cmd_socket_key() const {
  return _internal_has_websocket_cmd_socket_key();
}
inline void WebsocketRenderTaskConfig::clear_websocket_cmd_socket_key() {
  websocket_cmd_socket_key_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& WebsocketRenderTaskConfig::websocket_cmd_socket_key() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketRenderTaskConfig.websocket_cmd_socket_key)
  return _internal_websocket_cmd_socket_key();
}
inline void WebsocketRenderTaskConfig::set_websocket_cmd_socket_key(const std::string& value) {
  _internal_set_websocket_cmd_socket_key(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.WebsocketRenderTaskConfig.websocket_cmd_socket_key)
}
inline std::string* WebsocketRenderTaskConfig::mutable_websocket_cmd_socket_key() {
  // @@protoc_insertion_point(field_mutable:robosense.rs_hmi.config.WebsocketRenderTaskConfig.websocket_cmd_socket_key)
  return _internal_mutable_websocket_cmd_socket_key();
}
inline const std::string& WebsocketRenderTaskConfig::_internal_websocket_cmd_socket_key() const {
  return websocket_cmd_socket_key_.Get();
}
inline void WebsocketRenderTaskConfig::_internal_set_websocket_cmd_socket_key(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  websocket_cmd_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void WebsocketRenderTaskConfig::set_websocket_cmd_socket_key(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  websocket_cmd_socket_key_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.rs_hmi.config.WebsocketRenderTaskConfig.websocket_cmd_socket_key)
}
inline void WebsocketRenderTaskConfig::set_websocket_cmd_socket_key(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  websocket_cmd_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.rs_hmi.config.WebsocketRenderTaskConfig.websocket_cmd_socket_key)
}
inline void WebsocketRenderTaskConfig::set_websocket_cmd_socket_key(const char* value,
    size_t size) {
  _has_bits_[0] |= 0x00000001u;
  websocket_cmd_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.rs_hmi.config.WebsocketRenderTaskConfig.websocket_cmd_socket_key)
}
inline std::string* WebsocketRenderTaskConfig::_internal_mutable_websocket_cmd_socket_key() {
  _has_bits_[0] |= 0x00000001u;
  return websocket_cmd_socket_key_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* WebsocketRenderTaskConfig::release_websocket_cmd_socket_key() {
  // @@protoc_insertion_point(field_release:robosense.rs_hmi.config.WebsocketRenderTaskConfig.websocket_cmd_socket_key)
  if (!_internal_has_websocket_cmd_socket_key()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return websocket_cmd_socket_key_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void WebsocketRenderTaskConfig::set_allocated_websocket_cmd_socket_key(std::string* websocket_cmd_socket_key) {
  if (websocket_cmd_socket_key != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  websocket_cmd_socket_key_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), websocket_cmd_socket_key,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.rs_hmi.config.WebsocketRenderTaskConfig.websocket_cmd_socket_key)
}

// optional string websocket_socket_key = 6;
inline bool WebsocketRenderTaskConfig::_internal_has_websocket_socket_key() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool WebsocketRenderTaskConfig::has_websocket_socket_key() const {
  return _internal_has_websocket_socket_key();
}
inline void WebsocketRenderTaskConfig::clear_websocket_socket_key() {
  websocket_socket_key_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000002u;
}
inline const std::string& WebsocketRenderTaskConfig::websocket_socket_key() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketRenderTaskConfig.websocket_socket_key)
  return _internal_websocket_socket_key();
}
inline void WebsocketRenderTaskConfig::set_websocket_socket_key(const std::string& value) {
  _internal_set_websocket_socket_key(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.WebsocketRenderTaskConfig.websocket_socket_key)
}
inline std::string* WebsocketRenderTaskConfig::mutable_websocket_socket_key() {
  // @@protoc_insertion_point(field_mutable:robosense.rs_hmi.config.WebsocketRenderTaskConfig.websocket_socket_key)
  return _internal_mutable_websocket_socket_key();
}
inline const std::string& WebsocketRenderTaskConfig::_internal_websocket_socket_key() const {
  return websocket_socket_key_.Get();
}
inline void WebsocketRenderTaskConfig::_internal_set_websocket_socket_key(const std::string& value) {
  _has_bits_[0] |= 0x00000002u;
  websocket_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void WebsocketRenderTaskConfig::set_websocket_socket_key(std::string&& value) {
  _has_bits_[0] |= 0x00000002u;
  websocket_socket_key_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.rs_hmi.config.WebsocketRenderTaskConfig.websocket_socket_key)
}
inline void WebsocketRenderTaskConfig::set_websocket_socket_key(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000002u;
  websocket_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.rs_hmi.config.WebsocketRenderTaskConfig.websocket_socket_key)
}
inline void WebsocketRenderTaskConfig::set_websocket_socket_key(const char* value,
    size_t size) {
  _has_bits_[0] |= 0x00000002u;
  websocket_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.rs_hmi.config.WebsocketRenderTaskConfig.websocket_socket_key)
}
inline std::string* WebsocketRenderTaskConfig::_internal_mutable_websocket_socket_key() {
  _has_bits_[0] |= 0x00000002u;
  return websocket_socket_key_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* WebsocketRenderTaskConfig::release_websocket_socket_key() {
  // @@protoc_insertion_point(field_release:robosense.rs_hmi.config.WebsocketRenderTaskConfig.websocket_socket_key)
  if (!_internal_has_websocket_socket_key()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000002u;
  return websocket_socket_key_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void WebsocketRenderTaskConfig::set_allocated_websocket_socket_key(std::string* websocket_socket_key) {
  if (websocket_socket_key != nullptr) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  websocket_socket_key_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), websocket_socket_key,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.rs_hmi.config.WebsocketRenderTaskConfig.websocket_socket_key)
}

// optional string udp_multicast_socket_key = 7;
inline bool WebsocketRenderTaskConfig::_internal_has_udp_multicast_socket_key() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool WebsocketRenderTaskConfig::has_udp_multicast_socket_key() const {
  return _internal_has_udp_multicast_socket_key();
}
inline void WebsocketRenderTaskConfig::clear_udp_multicast_socket_key() {
  udp_multicast_socket_key_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000004u;
}
inline const std::string& WebsocketRenderTaskConfig::udp_multicast_socket_key() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_multicast_socket_key)
  return _internal_udp_multicast_socket_key();
}
inline void WebsocketRenderTaskConfig::set_udp_multicast_socket_key(const std::string& value) {
  _internal_set_udp_multicast_socket_key(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_multicast_socket_key)
}
inline std::string* WebsocketRenderTaskConfig::mutable_udp_multicast_socket_key() {
  // @@protoc_insertion_point(field_mutable:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_multicast_socket_key)
  return _internal_mutable_udp_multicast_socket_key();
}
inline const std::string& WebsocketRenderTaskConfig::_internal_udp_multicast_socket_key() const {
  return udp_multicast_socket_key_.Get();
}
inline void WebsocketRenderTaskConfig::_internal_set_udp_multicast_socket_key(const std::string& value) {
  _has_bits_[0] |= 0x00000004u;
  udp_multicast_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void WebsocketRenderTaskConfig::set_udp_multicast_socket_key(std::string&& value) {
  _has_bits_[0] |= 0x00000004u;
  udp_multicast_socket_key_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_multicast_socket_key)
}
inline void WebsocketRenderTaskConfig::set_udp_multicast_socket_key(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000004u;
  udp_multicast_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_multicast_socket_key)
}
inline void WebsocketRenderTaskConfig::set_udp_multicast_socket_key(const char* value,
    size_t size) {
  _has_bits_[0] |= 0x00000004u;
  udp_multicast_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_multicast_socket_key)
}
inline std::string* WebsocketRenderTaskConfig::_internal_mutable_udp_multicast_socket_key() {
  _has_bits_[0] |= 0x00000004u;
  return udp_multicast_socket_key_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* WebsocketRenderTaskConfig::release_udp_multicast_socket_key() {
  // @@protoc_insertion_point(field_release:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_multicast_socket_key)
  if (!_internal_has_udp_multicast_socket_key()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000004u;
  return udp_multicast_socket_key_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void WebsocketRenderTaskConfig::set_allocated_udp_multicast_socket_key(std::string* udp_multicast_socket_key) {
  if (udp_multicast_socket_key != nullptr) {
    _has_bits_[0] |= 0x00000004u;
  } else {
    _has_bits_[0] &= ~0x00000004u;
  }
  udp_multicast_socket_key_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), udp_multicast_socket_key,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_multicast_socket_key)
}

// optional string udp_double_multicast_socket_key = 8;
inline bool WebsocketRenderTaskConfig::_internal_has_udp_double_multicast_socket_key() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool WebsocketRenderTaskConfig::has_udp_double_multicast_socket_key() const {
  return _internal_has_udp_double_multicast_socket_key();
}
inline void WebsocketRenderTaskConfig::clear_udp_double_multicast_socket_key() {
  udp_double_multicast_socket_key_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000008u;
}
inline const std::string& WebsocketRenderTaskConfig::udp_double_multicast_socket_key() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_double_multicast_socket_key)
  return _internal_udp_double_multicast_socket_key();
}
inline void WebsocketRenderTaskConfig::set_udp_double_multicast_socket_key(const std::string& value) {
  _internal_set_udp_double_multicast_socket_key(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_double_multicast_socket_key)
}
inline std::string* WebsocketRenderTaskConfig::mutable_udp_double_multicast_socket_key() {
  // @@protoc_insertion_point(field_mutable:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_double_multicast_socket_key)
  return _internal_mutable_udp_double_multicast_socket_key();
}
inline const std::string& WebsocketRenderTaskConfig::_internal_udp_double_multicast_socket_key() const {
  return udp_double_multicast_socket_key_.Get();
}
inline void WebsocketRenderTaskConfig::_internal_set_udp_double_multicast_socket_key(const std::string& value) {
  _has_bits_[0] |= 0x00000008u;
  udp_double_multicast_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void WebsocketRenderTaskConfig::set_udp_double_multicast_socket_key(std::string&& value) {
  _has_bits_[0] |= 0x00000008u;
  udp_double_multicast_socket_key_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_double_multicast_socket_key)
}
inline void WebsocketRenderTaskConfig::set_udp_double_multicast_socket_key(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000008u;
  udp_double_multicast_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_double_multicast_socket_key)
}
inline void WebsocketRenderTaskConfig::set_udp_double_multicast_socket_key(const char* value,
    size_t size) {
  _has_bits_[0] |= 0x00000008u;
  udp_double_multicast_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_double_multicast_socket_key)
}
inline std::string* WebsocketRenderTaskConfig::_internal_mutable_udp_double_multicast_socket_key() {
  _has_bits_[0] |= 0x00000008u;
  return udp_double_multicast_socket_key_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* WebsocketRenderTaskConfig::release_udp_double_multicast_socket_key() {
  // @@protoc_insertion_point(field_release:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_double_multicast_socket_key)
  if (!_internal_has_udp_double_multicast_socket_key()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000008u;
  return udp_double_multicast_socket_key_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void WebsocketRenderTaskConfig::set_allocated_udp_double_multicast_socket_key(std::string* udp_double_multicast_socket_key) {
  if (udp_double_multicast_socket_key != nullptr) {
    _has_bits_[0] |= 0x00000008u;
  } else {
    _has_bits_[0] &= ~0x00000008u;
  }
  udp_double_multicast_socket_key_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), udp_double_multicast_socket_key,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_double_multicast_socket_key)
}

// optional string udp_p2p_socket_key = 9;
inline bool WebsocketRenderTaskConfig::_internal_has_udp_p2p_socket_key() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool WebsocketRenderTaskConfig::has_udp_p2p_socket_key() const {
  return _internal_has_udp_p2p_socket_key();
}
inline void WebsocketRenderTaskConfig::clear_udp_p2p_socket_key() {
  udp_p2p_socket_key_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000010u;
}
inline const std::string& WebsocketRenderTaskConfig::udp_p2p_socket_key() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_p2p_socket_key)
  return _internal_udp_p2p_socket_key();
}
inline void WebsocketRenderTaskConfig::set_udp_p2p_socket_key(const std::string& value) {
  _internal_set_udp_p2p_socket_key(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_p2p_socket_key)
}
inline std::string* WebsocketRenderTaskConfig::mutable_udp_p2p_socket_key() {
  // @@protoc_insertion_point(field_mutable:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_p2p_socket_key)
  return _internal_mutable_udp_p2p_socket_key();
}
inline const std::string& WebsocketRenderTaskConfig::_internal_udp_p2p_socket_key() const {
  return udp_p2p_socket_key_.Get();
}
inline void WebsocketRenderTaskConfig::_internal_set_udp_p2p_socket_key(const std::string& value) {
  _has_bits_[0] |= 0x00000010u;
  udp_p2p_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void WebsocketRenderTaskConfig::set_udp_p2p_socket_key(std::string&& value) {
  _has_bits_[0] |= 0x00000010u;
  udp_p2p_socket_key_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_p2p_socket_key)
}
inline void WebsocketRenderTaskConfig::set_udp_p2p_socket_key(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000010u;
  udp_p2p_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_p2p_socket_key)
}
inline void WebsocketRenderTaskConfig::set_udp_p2p_socket_key(const char* value,
    size_t size) {
  _has_bits_[0] |= 0x00000010u;
  udp_p2p_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_p2p_socket_key)
}
inline std::string* WebsocketRenderTaskConfig::_internal_mutable_udp_p2p_socket_key() {
  _has_bits_[0] |= 0x00000010u;
  return udp_p2p_socket_key_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* WebsocketRenderTaskConfig::release_udp_p2p_socket_key() {
  // @@protoc_insertion_point(field_release:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_p2p_socket_key)
  if (!_internal_has_udp_p2p_socket_key()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000010u;
  return udp_p2p_socket_key_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void WebsocketRenderTaskConfig::set_allocated_udp_p2p_socket_key(std::string* udp_p2p_socket_key) {
  if (udp_p2p_socket_key != nullptr) {
    _has_bits_[0] |= 0x00000010u;
  } else {
    _has_bits_[0] &= ~0x00000010u;
  }
  udp_p2p_socket_key_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), udp_p2p_socket_key,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_p2p_socket_key)
}

// optional uint32 udp_2_sample_1_buffer_size = 10 [default = 3];
inline bool WebsocketRenderTaskConfig::_internal_has_udp_2_sample_1_buffer_size() const {
  bool value = (_has_bits_[0] & 0x00002000u) != 0;
  return value;
}
inline bool WebsocketRenderTaskConfig::has_udp_2_sample_1_buffer_size() const {
  return _internal_has_udp_2_sample_1_buffer_size();
}
inline void WebsocketRenderTaskConfig::clear_udp_2_sample_1_buffer_size() {
  udp_2_sample_1_buffer_size_ = 3u;
  _has_bits_[0] &= ~0x00002000u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketRenderTaskConfig::_internal_udp_2_sample_1_buffer_size() const {
  return udp_2_sample_1_buffer_size_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketRenderTaskConfig::udp_2_sample_1_buffer_size() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_2_sample_1_buffer_size)
  return _internal_udp_2_sample_1_buffer_size();
}
inline void WebsocketRenderTaskConfig::_internal_set_udp_2_sample_1_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00002000u;
  udp_2_sample_1_buffer_size_ = value;
}
inline void WebsocketRenderTaskConfig::set_udp_2_sample_1_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_udp_2_sample_1_buffer_size(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_2_sample_1_buffer_size)
}

// optional uint32 udp_3_sample_1_buffer_size = 11 [default = 5];
inline bool WebsocketRenderTaskConfig::_internal_has_udp_3_sample_1_buffer_size() const {
  bool value = (_has_bits_[0] & 0x00004000u) != 0;
  return value;
}
inline bool WebsocketRenderTaskConfig::has_udp_3_sample_1_buffer_size() const {
  return _internal_has_udp_3_sample_1_buffer_size();
}
inline void WebsocketRenderTaskConfig::clear_udp_3_sample_1_buffer_size() {
  udp_3_sample_1_buffer_size_ = 5u;
  _has_bits_[0] &= ~0x00004000u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketRenderTaskConfig::_internal_udp_3_sample_1_buffer_size() const {
  return udp_3_sample_1_buffer_size_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketRenderTaskConfig::udp_3_sample_1_buffer_size() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_3_sample_1_buffer_size)
  return _internal_udp_3_sample_1_buffer_size();
}
inline void WebsocketRenderTaskConfig::_internal_set_udp_3_sample_1_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00004000u;
  udp_3_sample_1_buffer_size_ = value;
}
inline void WebsocketRenderTaskConfig::set_udp_3_sample_1_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_udp_3_sample_1_buffer_size(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_3_sample_1_buffer_size)
}

// optional uint32 udp_loss_all_buffer_size = 12 [default = 10];
inline bool WebsocketRenderTaskConfig::_internal_has_udp_loss_all_buffer_size() const {
  bool value = (_has_bits_[0] & 0x00008000u) != 0;
  return value;
}
inline bool WebsocketRenderTaskConfig::has_udp_loss_all_buffer_size() const {
  return _internal_has_udp_loss_all_buffer_size();
}
inline void WebsocketRenderTaskConfig::clear_udp_loss_all_buffer_size() {
  udp_loss_all_buffer_size_ = 10u;
  _has_bits_[0] &= ~0x00008000u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketRenderTaskConfig::_internal_udp_loss_all_buffer_size() const {
  return udp_loss_all_buffer_size_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketRenderTaskConfig::udp_loss_all_buffer_size() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_loss_all_buffer_size)
  return _internal_udp_loss_all_buffer_size();
}
inline void WebsocketRenderTaskConfig::_internal_set_udp_loss_all_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00008000u;
  udp_loss_all_buffer_size_ = value;
}
inline void WebsocketRenderTaskConfig::set_udp_loss_all_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_udp_loss_all_buffer_size(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_loss_all_buffer_size)
}

// optional uint32 max_send_render_buffer_size = 13 [default = 15];
inline bool WebsocketRenderTaskConfig::_internal_has_max_send_render_buffer_size() const {
  bool value = (_has_bits_[0] & 0x00000200u) != 0;
  return value;
}
inline bool WebsocketRenderTaskConfig::has_max_send_render_buffer_size() const {
  return _internal_has_max_send_render_buffer_size();
}
inline void WebsocketRenderTaskConfig::clear_max_send_render_buffer_size() {
  max_send_render_buffer_size_ = 15u;
  _has_bits_[0] &= ~0x00000200u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketRenderTaskConfig::_internal_max_send_render_buffer_size() const {
  return max_send_render_buffer_size_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 WebsocketRenderTaskConfig::max_send_render_buffer_size() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketRenderTaskConfig.max_send_render_buffer_size)
  return _internal_max_send_render_buffer_size();
}
inline void WebsocketRenderTaskConfig::_internal_set_max_send_render_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000200u;
  max_send_render_buffer_size_ = value;
}
inline void WebsocketRenderTaskConfig::set_max_send_render_buffer_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_max_send_render_buffer_size(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.WebsocketRenderTaskConfig.max_send_render_buffer_size)
}

// optional .robosense.rs_hmi.config.UdpMulticastEndPointConfig udp_multicast_render_config = 14;
inline bool WebsocketRenderTaskConfig::_internal_has_udp_multicast_render_config() const {
  bool value = (_has_bits_[0] & 0x00000040u) != 0;
  PROTOBUF_ASSUME(!value || udp_multicast_render_config_ != nullptr);
  return value;
}
inline bool WebsocketRenderTaskConfig::has_udp_multicast_render_config() const {
  return _internal_has_udp_multicast_render_config();
}
inline void WebsocketRenderTaskConfig::clear_udp_multicast_render_config() {
  if (udp_multicast_render_config_ != nullptr) udp_multicast_render_config_->Clear();
  _has_bits_[0] &= ~0x00000040u;
}
inline const ::robosense::rs_hmi::config::UdpMulticastEndPointConfig& WebsocketRenderTaskConfig::_internal_udp_multicast_render_config() const {
  const ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* p = udp_multicast_render_config_;
  return p != nullptr ? *p : reinterpret_cast<const ::robosense::rs_hmi::config::UdpMulticastEndPointConfig&>(
      ::robosense::rs_hmi::config::_UdpMulticastEndPointConfig_default_instance_);
}
inline const ::robosense::rs_hmi::config::UdpMulticastEndPointConfig& WebsocketRenderTaskConfig::udp_multicast_render_config() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_multicast_render_config)
  return _internal_udp_multicast_render_config();
}
inline void WebsocketRenderTaskConfig::unsafe_arena_set_allocated_udp_multicast_render_config(
    ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* udp_multicast_render_config) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(udp_multicast_render_config_);
  }
  udp_multicast_render_config_ = udp_multicast_render_config;
  if (udp_multicast_render_config) {
    _has_bits_[0] |= 0x00000040u;
  } else {
    _has_bits_[0] &= ~0x00000040u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_multicast_render_config)
}
inline ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* WebsocketRenderTaskConfig::release_udp_multicast_render_config() {
  _has_bits_[0] &= ~0x00000040u;
  ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* temp = udp_multicast_render_config_;
  udp_multicast_render_config_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* WebsocketRenderTaskConfig::unsafe_arena_release_udp_multicast_render_config() {
  // @@protoc_insertion_point(field_release:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_multicast_render_config)
  _has_bits_[0] &= ~0x00000040u;
  ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* temp = udp_multicast_render_config_;
  udp_multicast_render_config_ = nullptr;
  return temp;
}
inline ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* WebsocketRenderTaskConfig::_internal_mutable_udp_multicast_render_config() {
  _has_bits_[0] |= 0x00000040u;
  if (udp_multicast_render_config_ == nullptr) {
    auto* p = CreateMaybeMessage<::robosense::rs_hmi::config::UdpMulticastEndPointConfig>(GetArena());
    udp_multicast_render_config_ = p;
  }
  return udp_multicast_render_config_;
}
inline ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* WebsocketRenderTaskConfig::mutable_udp_multicast_render_config() {
  // @@protoc_insertion_point(field_mutable:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_multicast_render_config)
  return _internal_mutable_udp_multicast_render_config();
}
inline void WebsocketRenderTaskConfig::set_allocated_udp_multicast_render_config(::robosense::rs_hmi::config::UdpMulticastEndPointConfig* udp_multicast_render_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete udp_multicast_render_config_;
  }
  if (udp_multicast_render_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(udp_multicast_render_config);
    if (message_arena != submessage_arena) {
      udp_multicast_render_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, udp_multicast_render_config, submessage_arena);
    }
    _has_bits_[0] |= 0x00000040u;
  } else {
    _has_bits_[0] &= ~0x00000040u;
  }
  udp_multicast_render_config_ = udp_multicast_render_config;
  // @@protoc_insertion_point(field_set_allocated:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_multicast_render_config)
}

// optional .robosense.rs_hmi.config.UdpP2PEndPointConfig udp_p2p_render_config = 15;
inline bool WebsocketRenderTaskConfig::_internal_has_udp_p2p_render_config() const {
  bool value = (_has_bits_[0] & 0x00000080u) != 0;
  PROTOBUF_ASSUME(!value || udp_p2p_render_config_ != nullptr);
  return value;
}
inline bool WebsocketRenderTaskConfig::has_udp_p2p_render_config() const {
  return _internal_has_udp_p2p_render_config();
}
inline void WebsocketRenderTaskConfig::clear_udp_p2p_render_config() {
  if (udp_p2p_render_config_ != nullptr) udp_p2p_render_config_->Clear();
  _has_bits_[0] &= ~0x00000080u;
}
inline const ::robosense::rs_hmi::config::UdpP2PEndPointConfig& WebsocketRenderTaskConfig::_internal_udp_p2p_render_config() const {
  const ::robosense::rs_hmi::config::UdpP2PEndPointConfig* p = udp_p2p_render_config_;
  return p != nullptr ? *p : reinterpret_cast<const ::robosense::rs_hmi::config::UdpP2PEndPointConfig&>(
      ::robosense::rs_hmi::config::_UdpP2PEndPointConfig_default_instance_);
}
inline const ::robosense::rs_hmi::config::UdpP2PEndPointConfig& WebsocketRenderTaskConfig::udp_p2p_render_config() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_p2p_render_config)
  return _internal_udp_p2p_render_config();
}
inline void WebsocketRenderTaskConfig::unsafe_arena_set_allocated_udp_p2p_render_config(
    ::robosense::rs_hmi::config::UdpP2PEndPointConfig* udp_p2p_render_config) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(udp_p2p_render_config_);
  }
  udp_p2p_render_config_ = udp_p2p_render_config;
  if (udp_p2p_render_config) {
    _has_bits_[0] |= 0x00000080u;
  } else {
    _has_bits_[0] &= ~0x00000080u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_p2p_render_config)
}
inline ::robosense::rs_hmi::config::UdpP2PEndPointConfig* WebsocketRenderTaskConfig::release_udp_p2p_render_config() {
  _has_bits_[0] &= ~0x00000080u;
  ::robosense::rs_hmi::config::UdpP2PEndPointConfig* temp = udp_p2p_render_config_;
  udp_p2p_render_config_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::robosense::rs_hmi::config::UdpP2PEndPointConfig* WebsocketRenderTaskConfig::unsafe_arena_release_udp_p2p_render_config() {
  // @@protoc_insertion_point(field_release:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_p2p_render_config)
  _has_bits_[0] &= ~0x00000080u;
  ::robosense::rs_hmi::config::UdpP2PEndPointConfig* temp = udp_p2p_render_config_;
  udp_p2p_render_config_ = nullptr;
  return temp;
}
inline ::robosense::rs_hmi::config::UdpP2PEndPointConfig* WebsocketRenderTaskConfig::_internal_mutable_udp_p2p_render_config() {
  _has_bits_[0] |= 0x00000080u;
  if (udp_p2p_render_config_ == nullptr) {
    auto* p = CreateMaybeMessage<::robosense::rs_hmi::config::UdpP2PEndPointConfig>(GetArena());
    udp_p2p_render_config_ = p;
  }
  return udp_p2p_render_config_;
}
inline ::robosense::rs_hmi::config::UdpP2PEndPointConfig* WebsocketRenderTaskConfig::mutable_udp_p2p_render_config() {
  // @@protoc_insertion_point(field_mutable:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_p2p_render_config)
  return _internal_mutable_udp_p2p_render_config();
}
inline void WebsocketRenderTaskConfig::set_allocated_udp_p2p_render_config(::robosense::rs_hmi::config::UdpP2PEndPointConfig* udp_p2p_render_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete udp_p2p_render_config_;
  }
  if (udp_p2p_render_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(udp_p2p_render_config);
    if (message_arena != submessage_arena) {
      udp_p2p_render_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, udp_p2p_render_config, submessage_arena);
    }
    _has_bits_[0] |= 0x00000080u;
  } else {
    _has_bits_[0] &= ~0x00000080u;
  }
  udp_p2p_render_config_ = udp_p2p_render_config;
  // @@protoc_insertion_point(field_set_allocated:robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_p2p_render_config)
}

// optional .robosense.rs_hmi.config.UdpMulticastEndPointConfig double_udp_multicast_render_config = 16;
inline bool WebsocketRenderTaskConfig::_internal_has_double_udp_multicast_render_config() const {
  bool value = (_has_bits_[0] & 0x00000100u) != 0;
  PROTOBUF_ASSUME(!value || double_udp_multicast_render_config_ != nullptr);
  return value;
}
inline bool WebsocketRenderTaskConfig::has_double_udp_multicast_render_config() const {
  return _internal_has_double_udp_multicast_render_config();
}
inline void WebsocketRenderTaskConfig::clear_double_udp_multicast_render_config() {
  if (double_udp_multicast_render_config_ != nullptr) double_udp_multicast_render_config_->Clear();
  _has_bits_[0] &= ~0x00000100u;
}
inline const ::robosense::rs_hmi::config::UdpMulticastEndPointConfig& WebsocketRenderTaskConfig::_internal_double_udp_multicast_render_config() const {
  const ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* p = double_udp_multicast_render_config_;
  return p != nullptr ? *p : reinterpret_cast<const ::robosense::rs_hmi::config::UdpMulticastEndPointConfig&>(
      ::robosense::rs_hmi::config::_UdpMulticastEndPointConfig_default_instance_);
}
inline const ::robosense::rs_hmi::config::UdpMulticastEndPointConfig& WebsocketRenderTaskConfig::double_udp_multicast_render_config() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.WebsocketRenderTaskConfig.double_udp_multicast_render_config)
  return _internal_double_udp_multicast_render_config();
}
inline void WebsocketRenderTaskConfig::unsafe_arena_set_allocated_double_udp_multicast_render_config(
    ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* double_udp_multicast_render_config) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(double_udp_multicast_render_config_);
  }
  double_udp_multicast_render_config_ = double_udp_multicast_render_config;
  if (double_udp_multicast_render_config) {
    _has_bits_[0] |= 0x00000100u;
  } else {
    _has_bits_[0] &= ~0x00000100u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:robosense.rs_hmi.config.WebsocketRenderTaskConfig.double_udp_multicast_render_config)
}
inline ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* WebsocketRenderTaskConfig::release_double_udp_multicast_render_config() {
  _has_bits_[0] &= ~0x00000100u;
  ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* temp = double_udp_multicast_render_config_;
  double_udp_multicast_render_config_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* WebsocketRenderTaskConfig::unsafe_arena_release_double_udp_multicast_render_config() {
  // @@protoc_insertion_point(field_release:robosense.rs_hmi.config.WebsocketRenderTaskConfig.double_udp_multicast_render_config)
  _has_bits_[0] &= ~0x00000100u;
  ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* temp = double_udp_multicast_render_config_;
  double_udp_multicast_render_config_ = nullptr;
  return temp;
}
inline ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* WebsocketRenderTaskConfig::_internal_mutable_double_udp_multicast_render_config() {
  _has_bits_[0] |= 0x00000100u;
  if (double_udp_multicast_render_config_ == nullptr) {
    auto* p = CreateMaybeMessage<::robosense::rs_hmi::config::UdpMulticastEndPointConfig>(GetArena());
    double_udp_multicast_render_config_ = p;
  }
  return double_udp_multicast_render_config_;
}
inline ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* WebsocketRenderTaskConfig::mutable_double_udp_multicast_render_config() {
  // @@protoc_insertion_point(field_mutable:robosense.rs_hmi.config.WebsocketRenderTaskConfig.double_udp_multicast_render_config)
  return _internal_mutable_double_udp_multicast_render_config();
}
inline void WebsocketRenderTaskConfig::set_allocated_double_udp_multicast_render_config(::robosense::rs_hmi::config::UdpMulticastEndPointConfig* double_udp_multicast_render_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete double_udp_multicast_render_config_;
  }
  if (double_udp_multicast_render_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(double_udp_multicast_render_config);
    if (message_arena != submessage_arena) {
      double_udp_multicast_render_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, double_udp_multicast_render_config, submessage_arena);
    }
    _has_bits_[0] |= 0x00000100u;
  } else {
    _has_bits_[0] &= ~0x00000100u;
  }
  double_udp_multicast_render_config_ = double_udp_multicast_render_config;
  // @@protoc_insertion_point(field_set_allocated:robosense.rs_hmi.config.WebsocketRenderTaskConfig.double_udp_multicast_render_config)
}

// -------------------------------------------------------------------

// Config

// optional bool enable_websocket_buffer_check = 1 [default = true];
inline bool Config::_internal_has_enable_websocket_buffer_check() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool Config::has_enable_websocket_buffer_check() const {
  return _internal_has_enable_websocket_buffer_check();
}
inline void Config::clear_enable_websocket_buffer_check() {
  enable_websocket_buffer_check_ = true;
  _has_bits_[0] &= ~0x00000004u;
}
inline bool Config::_internal_enable_websocket_buffer_check() const {
  return enable_websocket_buffer_check_;
}
inline bool Config::enable_websocket_buffer_check() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.Config.enable_websocket_buffer_check)
  return _internal_enable_websocket_buffer_check();
}
inline void Config::_internal_set_enable_websocket_buffer_check(bool value) {
  _has_bits_[0] |= 0x00000004u;
  enable_websocket_buffer_check_ = value;
}
inline void Config::set_enable_websocket_buffer_check(bool value) {
  _internal_set_enable_websocket_buffer_check(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.Config.enable_websocket_buffer_check)
}

// optional uint32 websocket_buffer_check_timeout_th_ms = 2 [default = 2000];
inline bool Config::_internal_has_websocket_buffer_check_timeout_th_ms() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool Config::has_websocket_buffer_check_timeout_th_ms() const {
  return _internal_has_websocket_buffer_check_timeout_th_ms();
}
inline void Config::clear_websocket_buffer_check_timeout_th_ms() {
  websocket_buffer_check_timeout_th_ms_ = 2000u;
  _has_bits_[0] &= ~0x00000008u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 Config::_internal_websocket_buffer_check_timeout_th_ms() const {
  return websocket_buffer_check_timeout_th_ms_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 Config::websocket_buffer_check_timeout_th_ms() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.Config.websocket_buffer_check_timeout_th_ms)
  return _internal_websocket_buffer_check_timeout_th_ms();
}
inline void Config::_internal_set_websocket_buffer_check_timeout_th_ms(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000008u;
  websocket_buffer_check_timeout_th_ms_ = value;
}
inline void Config::set_websocket_buffer_check_timeout_th_ms(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_websocket_buffer_check_timeout_th_ms(value);
  // @@protoc_insertion_point(field_set:robosense.rs_hmi.config.Config.websocket_buffer_check_timeout_th_ms)
}

// optional .robosense.rs_hmi.config.WebsocketRenderTaskConfig render_config = 4;
inline bool Config::_internal_has_render_config() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || render_config_ != nullptr);
  return value;
}
inline bool Config::has_render_config() const {
  return _internal_has_render_config();
}
inline void Config::clear_render_config() {
  if (render_config_ != nullptr) render_config_->Clear();
  _has_bits_[0] &= ~0x00000001u;
}
inline const ::robosense::rs_hmi::config::WebsocketRenderTaskConfig& Config::_internal_render_config() const {
  const ::robosense::rs_hmi::config::WebsocketRenderTaskConfig* p = render_config_;
  return p != nullptr ? *p : reinterpret_cast<const ::robosense::rs_hmi::config::WebsocketRenderTaskConfig&>(
      ::robosense::rs_hmi::config::_WebsocketRenderTaskConfig_default_instance_);
}
inline const ::robosense::rs_hmi::config::WebsocketRenderTaskConfig& Config::render_config() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.Config.render_config)
  return _internal_render_config();
}
inline void Config::unsafe_arena_set_allocated_render_config(
    ::robosense::rs_hmi::config::WebsocketRenderTaskConfig* render_config) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(render_config_);
  }
  render_config_ = render_config;
  if (render_config) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:robosense.rs_hmi.config.Config.render_config)
}
inline ::robosense::rs_hmi::config::WebsocketRenderTaskConfig* Config::release_render_config() {
  _has_bits_[0] &= ~0x00000001u;
  ::robosense::rs_hmi::config::WebsocketRenderTaskConfig* temp = render_config_;
  render_config_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::robosense::rs_hmi::config::WebsocketRenderTaskConfig* Config::unsafe_arena_release_render_config() {
  // @@protoc_insertion_point(field_release:robosense.rs_hmi.config.Config.render_config)
  _has_bits_[0] &= ~0x00000001u;
  ::robosense::rs_hmi::config::WebsocketRenderTaskConfig* temp = render_config_;
  render_config_ = nullptr;
  return temp;
}
inline ::robosense::rs_hmi::config::WebsocketRenderTaskConfig* Config::_internal_mutable_render_config() {
  _has_bits_[0] |= 0x00000001u;
  if (render_config_ == nullptr) {
    auto* p = CreateMaybeMessage<::robosense::rs_hmi::config::WebsocketRenderTaskConfig>(GetArena());
    render_config_ = p;
  }
  return render_config_;
}
inline ::robosense::rs_hmi::config::WebsocketRenderTaskConfig* Config::mutable_render_config() {
  // @@protoc_insertion_point(field_mutable:robosense.rs_hmi.config.Config.render_config)
  return _internal_mutable_render_config();
}
inline void Config::set_allocated_render_config(::robosense::rs_hmi::config::WebsocketRenderTaskConfig* render_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete render_config_;
  }
  if (render_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(render_config);
    if (message_arena != submessage_arena) {
      render_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, render_config, submessage_arena);
    }
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  render_config_ = render_config;
  // @@protoc_insertion_point(field_set_allocated:robosense.rs_hmi.config.Config.render_config)
}

// optional .robosense.rs_hmi.config.HmiSocketTaskConfig hmi_socket_task_config = 17;
inline bool Config::_internal_has_hmi_socket_task_config() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  PROTOBUF_ASSUME(!value || hmi_socket_task_config_ != nullptr);
  return value;
}
inline bool Config::has_hmi_socket_task_config() const {
  return _internal_has_hmi_socket_task_config();
}
inline void Config::clear_hmi_socket_task_config() {
  if (hmi_socket_task_config_ != nullptr) hmi_socket_task_config_->Clear();
  _has_bits_[0] &= ~0x00000002u;
}
inline const ::robosense::rs_hmi::config::HmiSocketTaskConfig& Config::_internal_hmi_socket_task_config() const {
  const ::robosense::rs_hmi::config::HmiSocketTaskConfig* p = hmi_socket_task_config_;
  return p != nullptr ? *p : reinterpret_cast<const ::robosense::rs_hmi::config::HmiSocketTaskConfig&>(
      ::robosense::rs_hmi::config::_HmiSocketTaskConfig_default_instance_);
}
inline const ::robosense::rs_hmi::config::HmiSocketTaskConfig& Config::hmi_socket_task_config() const {
  // @@protoc_insertion_point(field_get:robosense.rs_hmi.config.Config.hmi_socket_task_config)
  return _internal_hmi_socket_task_config();
}
inline void Config::unsafe_arena_set_allocated_hmi_socket_task_config(
    ::robosense::rs_hmi::config::HmiSocketTaskConfig* hmi_socket_task_config) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(hmi_socket_task_config_);
  }
  hmi_socket_task_config_ = hmi_socket_task_config;
  if (hmi_socket_task_config) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:robosense.rs_hmi.config.Config.hmi_socket_task_config)
}
inline ::robosense::rs_hmi::config::HmiSocketTaskConfig* Config::release_hmi_socket_task_config() {
  _has_bits_[0] &= ~0x00000002u;
  ::robosense::rs_hmi::config::HmiSocketTaskConfig* temp = hmi_socket_task_config_;
  hmi_socket_task_config_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::robosense::rs_hmi::config::HmiSocketTaskConfig* Config::unsafe_arena_release_hmi_socket_task_config() {
  // @@protoc_insertion_point(field_release:robosense.rs_hmi.config.Config.hmi_socket_task_config)
  _has_bits_[0] &= ~0x00000002u;
  ::robosense::rs_hmi::config::HmiSocketTaskConfig* temp = hmi_socket_task_config_;
  hmi_socket_task_config_ = nullptr;
  return temp;
}
inline ::robosense::rs_hmi::config::HmiSocketTaskConfig* Config::_internal_mutable_hmi_socket_task_config() {
  _has_bits_[0] |= 0x00000002u;
  if (hmi_socket_task_config_ == nullptr) {
    auto* p = CreateMaybeMessage<::robosense::rs_hmi::config::HmiSocketTaskConfig>(GetArena());
    hmi_socket_task_config_ = p;
  }
  return hmi_socket_task_config_;
}
inline ::robosense::rs_hmi::config::HmiSocketTaskConfig* Config::mutable_hmi_socket_task_config() {
  // @@protoc_insertion_point(field_mutable:robosense.rs_hmi.config.Config.hmi_socket_task_config)
  return _internal_mutable_hmi_socket_task_config();
}
inline void Config::set_allocated_hmi_socket_task_config(::robosense::rs_hmi::config::HmiSocketTaskConfig* hmi_socket_task_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete hmi_socket_task_config_;
  }
  if (hmi_socket_task_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(hmi_socket_task_config);
    if (message_arena != submessage_arena) {
      hmi_socket_task_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, hmi_socket_task_config, submessage_arena);
    }
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  hmi_socket_task_config_ = hmi_socket_task_config;
  // @@protoc_insertion_point(field_set_allocated:robosense.rs_hmi.config.Config.hmi_socket_task_config)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace config
}  // namespace rs_hmi
}  // namespace robosense

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE>() {
  return ::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE_descriptor();
}
template <> struct is_proto_enum< ::robosense::rs_hmi::config::RS_UDP_CONTROL_MODE_TYPE> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::robosense::rs_hmi::config::RS_UDP_CONTROL_MODE_TYPE>() {
  return ::robosense::rs_hmi::config::RS_UDP_CONTROL_MODE_TYPE_descriptor();
}
template <> struct is_proto_enum< ::robosense::rs_hmi::config::RS_UDP_BUFFER_CONTROL_MODE_TYPE> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::robosense::rs_hmi::config::RS_UDP_BUFFER_CONTROL_MODE_TYPE>() {
  return ::robosense::rs_hmi::config::RS_UDP_BUFFER_CONTROL_MODE_TYPE_descriptor();
}
template <> struct is_proto_enum< ::robosense::rs_hmi::config::RS_HMI_TASK_TYPE> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::robosense::rs_hmi::config::RS_HMI_TASK_TYPE>() {
  return ::robosense::rs_hmi::config::RS_HMI_TASK_TYPE_descriptor();
}
template <> struct is_proto_enum< ::robosense::rs_hmi::config::RS_HD_MAP_TRANSFORM_LEVEL> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::robosense::rs_hmi::config::RS_HD_MAP_TRANSFORM_LEVEL>() {
  return ::robosense::rs_hmi::config::RS_HD_MAP_TRANSFORM_LEVEL_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_config_2eproto
