syntax = "proto2";

package robosense.acviewer_msgs;  

enum RS_CMD_TYPE { 
    RS_HEART_BEAT = 0; 
    RS_SET_RUN_MODE = 1; 
    RS_SET_SHOW_CONFIG = 2;
    RS_GET_DEVICE_INFO = 3; 
    RS_OPERATOR_DEVICE = 4;
    RS_GET_WRITER_CONFIG = 5;
    RS_START_WRITER = 6;
    RS_STOP_WRITER = 7;
    RS_GET_FILE_LIST = 8;
    RS_GET_FILE_TOPICS = 9;
    RS_START_READER = 10;
    RS_LOADING_PROGRESS = 11;
    RS_GET_READER_PROGRESS = 12;
    RS_SKIP_FRAME = 13;
    RS_PLAY = 14;
    RS_PAUSE = 15;
    RS_STOP_READER = 16;
}

// ==================================================================
// ========================== Request ===============================
// ================================================================== 
message RSRequest 
{
    required RS_CMD_TYPE       cmd_type = 1;  // 请求指令
    required uint64          request_Id = 2;  // 请求唯一标识 64位整形时间戳
    optional RSRequestData request_data = 3;  // 请求数据
}

message RSShowConfigData 
{
    optional bool show_pos     = 1;
    optional bool show_pc      = 2;
    optional bool show_pc_slam = 3;
    optional bool show_tri     = 4;
    optional bool show_rgb     = 5;
    optional bool show_depth   = 6;
}

message RSOperatorDeviceData 
{
    required string uuid          = 1;
    required uint32 operator_type = 2; // 0关闭 1打开
}

message RSWriterSetting 
{
    optional string file_name                  = 1; // 只传递文件命名 后端数据保存目录可以指定在固定目录下
    optional string file_format                = 2; // 文件格式
    repeated RSTopicSetting topic_setting_list = 3; // 需要写入的topic列表
}

message RSTopicSetting 
{
    optional string topic = 1;
    optional string compressed_type= 2; // 压缩类型
}

message RSReaderSetting 
{
    optional string file_name = 1;               // 文件名称
    optional string main_sync_topic = 2;         // 主控制帧话题
    optional uint32 pre_load_mains_sync_cnt = 3; // 预加载数量
    repeated string read_topic_names = 4;        // 播放的话题名称, 为空时则播放全部
    optional int64  read_start_timestamp_ns = 5[default = -1];
    optional int64  read_end_timestamp_ns = 6[default = -1]; 
}

message RSRequestData 
{
    optional uint32 runMode = 1;
    optional RSShowConfigData show_config_data = 2;
    optional RSOperatorDeviceData operator_device_data= 3; 
    optional RSWriterSetting writer_setting = 4; 
    optional RSReaderSetting reader_setting = 5; 
    optional string          topic_file_path = 6; 
    optional int32 skip_frame = 7; 
}        

// ==================================================================
// ========================== Response ==============================
// ==================================================================  
message RSResponse 
{
    required RS_CMD_TYPE         cmd_type = 1; // 响应指令
    required uint64            request_Id = 2; // 响应对应的请求唯一标识 64位整形时间戳
    required uint64           response_Id = 3; // 响应唯一标识 64位整形时间戳
    required uint32         response_code = 4; // 0->成功 其他->失败
    optional string         response_info = 5; // 响应信息，失败时可填入失败原因
    optional RSResponseData response_data = 6; // 响应数据
}

message RSDeviceInfo 
{
    optional string       uuid = 1;
    optional uint32 event_type = 2; // 挂载类型，目前都是1
}

message RSWriterConfig 
{
    repeated RSTopicConfig topic_config_list = 1; // 目前后端配置的topic列表
    repeated string file_format = 2;              // 当前系统支持的文件格式
}

message RSReaderConfig 
{
    repeated string               file_topics        = 1; // 获取指定文件包含的topic
    optional int64                start_timestamp_ns = 2; // 开始时间
    optional int64                end_timestamp_ns   = 3; // 结束时间
}

message RSTopicConfig 
{
    optional string         topic = 1;
    optional string          type = 2; // 话题分类
    repeated string process_types = 3; // 支持的压缩类型
}

//播放数据过程中的进度数据
message RSReaderProgress 
{
    optional uint32  total_frame_count = 1;
    optional bool           is_playing = 2;
    optional int32 current_frame_index = 3;
    optional uint64  current_timestamp = 4;
}

message RSResponseData 
{
    repeated RSDeviceInfo    device_info_list = 1;
    optional RSWriterConfig     writer_config = 2;
    repeated string                 file_list = 3;              // 获取后端所有可以播放的文件
    optional RSReaderConfig     reader_config = 4; 
    optional uint32          loading_progress = 5;
    optional RSReaderProgress reader_progress = 6;
}

// ==================================================================
// ========================== Render ================================
// ==================================================================  
enum RS_RENDER_COMMUNICATION_TYPE {
    RS_RENDER_COMM_WEBSOCKET            = 1; 
    RS_RENDER_COMM_UDP_MULTICAST        = 2; 
    RS_RENDER_COMM_DOUBLE_UDP_MULTICAST = 3; 
    RS_RENDER_COMM_UDP_P2P              = 4; 
}

enum RS_UDP_CONTROL_MODE_TYPE {
    RS_UDP_CONTROL_NOTHING            = 1; 
    RS_UDP_CONTROL_TOTAL_CONTROL_TIME = 2; 
    RS_UDP_CONTROL_DATA_CONTROL_TIME  = 3; 
}


message UdpControlConfig {
    optional RS_UDP_CONTROL_MODE_TYPE udp_control_type                 = 1[default = RS_UDP_CONTROL_TOTAL_CONTROL_TIME]; 
    optional uint32                   udp_total_control_time_ms        = 2[default = 60]; 
    optional uint32                   udp_total_control_single_time_ms = 3[default = 2]; 
    optional uint32                   udp_data_control_size            = 4[default = 262144]; 
    optional uint32                   udp_data_control_time_ms         = 5[default = 2]; 
}

enum RS_UDP_BUFFER_CONTROL_MODE_TYPE {
    RS_UDP_BUFFER_CONTROL_NOTHING = 1; 
    RS_UDP_BUFFER_CONTROL_ENABLE  = 2; 
}

message RenderSwitch 
{
    optional bool                          enable_render     = 1[default = true]; 
    optional RS_RENDER_COMMUNICATION_TYPE  render_comm_type  = 2[default = RS_RENDER_COMM_WEBSOCKET]; 
    optional string                        udp_p2p_carapp_ip = 3; 
    optional UdpControlConfig             udp_control_config = 4; 
}

message RSPositon 
{
    optional float          x = 1;
    optional float          y = 2;
    optional float          z = 3;
    optional uint64 timestamp = 4;
}

message RSPointCloud 
{
    optional bytes  data = 1; 
    optional uint32 size = 2[default = 0]; 
}

message RSTrangleFacet
{
    optional bytes  data = 1; 
    optional uint32 size = 2[default = 0]; 
}

message RSRgbJpeg 
{
    optional bool   is_jpeg_compress = 1[default = false];
    optional uint32            width = 2;  
    optional uint32           height = 3; 
    optional bytes              data = 4;
    optional uint64        timestamp = 5;
}

message RSDepthImage 
{
    optional uint32     width = 1; 
    optional uint32    height = 2; 
    optional uint64 timestamp = 3;
    optional bytes       data = 4; 
}

message RSRender 
{
    optional RSPositon      position         = 1;
    optional RSPointCloud   point_cloud      = 2;
    optional RSPointCloud   point_cloud_slam = 3;
    optional RSTrangleFacet triangle_list    = 4; 
    optional RSRgbJpeg      rgb_jpeg         = 5; 
    optional RSDepthImage   depth_image      = 6; 
}



