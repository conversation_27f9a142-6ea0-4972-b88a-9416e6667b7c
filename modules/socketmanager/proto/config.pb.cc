// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: config.proto

#include "config.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
extern PROTOBUF_INTERNAL_EXPORT_config_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_HmiSocketTaskConfig_config_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_config_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_HmiSocketTopicTaskConfig_config_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_config_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_HmiTaskSocketConfig_config_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_config_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<3> scc_info_SocketTaskConfig_config_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_config_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_UdpControlConfig_config_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_config_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_UdpMulticastEndPointConfig_config_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_config_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_UdpMulticastTaskConfig_config_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_config_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_UdpP2PEndPointConfig_config_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_config_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_UdpP2PTaskConfig_config_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_config_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_WebsocketEndPointConfig_config_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_config_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<3> scc_info_WebsocketRenderTaskConfig_config_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_config_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_WebsocketSendControlConfig_config_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_config_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_WebsocketTaskConfig_config_2eproto;
namespace robosense {
namespace rs_hmi {
namespace config {
class WebsocketEndPointConfigDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<WebsocketEndPointConfig> _instance;
} _WebsocketEndPointConfig_default_instance_;
class UdpControlConfigDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<UdpControlConfig> _instance;
} _UdpControlConfig_default_instance_;
class UdpBufferControlConfigDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<UdpBufferControlConfig> _instance;
} _UdpBufferControlConfig_default_instance_;
class UdpMulticastEndPointConfigDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<UdpMulticastEndPointConfig> _instance;
} _UdpMulticastEndPointConfig_default_instance_;
class UdpP2PEndPointConfigDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<UdpP2PEndPointConfig> _instance;
} _UdpP2PEndPointConfig_default_instance_;
class WebsocketSendControlConfigDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<WebsocketSendControlConfig> _instance;
} _WebsocketSendControlConfig_default_instance_;
class WebsocketTaskConfigDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<WebsocketTaskConfig> _instance;
} _WebsocketTaskConfig_default_instance_;
class UdpMulticastTaskConfigDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<UdpMulticastTaskConfig> _instance;
} _UdpMulticastTaskConfig_default_instance_;
class UdpP2PTaskConfigDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<UdpP2PTaskConfig> _instance;
} _UdpP2PTaskConfig_default_instance_;
class SocketTaskConfigDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<SocketTaskConfig> _instance;
} _SocketTaskConfig_default_instance_;
class HmiSocketTaskConfigDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<HmiSocketTaskConfig> _instance;
} _HmiSocketTaskConfig_default_instance_;
class HmiSocketTopicTaskConfigDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<HmiSocketTopicTaskConfig> _instance;
} _HmiSocketTopicTaskConfig_default_instance_;
class HmiTaskSocketConfigDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<HmiTaskSocketConfig> _instance;
} _HmiTaskSocketConfig_default_instance_;
class WebsocketRenderTaskConfigDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<WebsocketRenderTaskConfig> _instance;
} _WebsocketRenderTaskConfig_default_instance_;
class ConfigDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<Config> _instance;
} _Config_default_instance_;
}  // namespace config
}  // namespace rs_hmi
}  // namespace robosense
static void InitDefaultsscc_info_Config_config_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::rs_hmi::config::_Config_default_instance_;
    new (ptr) ::robosense::rs_hmi::config::Config();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_Config_config_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 2, 0, InitDefaultsscc_info_Config_config_2eproto}, {
      &scc_info_WebsocketRenderTaskConfig_config_2eproto.base,
      &scc_info_HmiSocketTaskConfig_config_2eproto.base,}};

static void InitDefaultsscc_info_HmiSocketTaskConfig_config_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::rs_hmi::config::_HmiSocketTaskConfig_default_instance_;
    new (ptr) ::robosense::rs_hmi::config::HmiSocketTaskConfig();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_HmiSocketTaskConfig_config_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_HmiSocketTaskConfig_config_2eproto}, {
      &scc_info_SocketTaskConfig_config_2eproto.base,}};

static void InitDefaultsscc_info_HmiSocketTopicTaskConfig_config_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::rs_hmi::config::_HmiSocketTopicTaskConfig_default_instance_;
    new (ptr) ::robosense::rs_hmi::config::HmiSocketTopicTaskConfig();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_HmiSocketTopicTaskConfig_config_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_HmiSocketTopicTaskConfig_config_2eproto}, {}};

static void InitDefaultsscc_info_HmiTaskSocketConfig_config_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::rs_hmi::config::_HmiTaskSocketConfig_default_instance_;
    new (ptr) ::robosense::rs_hmi::config::HmiTaskSocketConfig();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_HmiTaskSocketConfig_config_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_HmiTaskSocketConfig_config_2eproto}, {
      &scc_info_HmiSocketTopicTaskConfig_config_2eproto.base,}};

static void InitDefaultsscc_info_SocketTaskConfig_config_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::rs_hmi::config::_SocketTaskConfig_default_instance_;
    new (ptr) ::robosense::rs_hmi::config::SocketTaskConfig();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<3> scc_info_SocketTaskConfig_config_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 3, 0, InitDefaultsscc_info_SocketTaskConfig_config_2eproto}, {
      &scc_info_WebsocketTaskConfig_config_2eproto.base,
      &scc_info_UdpMulticastTaskConfig_config_2eproto.base,
      &scc_info_UdpP2PTaskConfig_config_2eproto.base,}};

static void InitDefaultsscc_info_UdpBufferControlConfig_config_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::rs_hmi::config::_UdpBufferControlConfig_default_instance_;
    new (ptr) ::robosense::rs_hmi::config::UdpBufferControlConfig();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_UdpBufferControlConfig_config_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_UdpBufferControlConfig_config_2eproto}, {}};

static void InitDefaultsscc_info_UdpControlConfig_config_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::rs_hmi::config::_UdpControlConfig_default_instance_;
    new (ptr) ::robosense::rs_hmi::config::UdpControlConfig();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_UdpControlConfig_config_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_UdpControlConfig_config_2eproto}, {}};

static void InitDefaultsscc_info_UdpMulticastEndPointConfig_config_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::rs_hmi::config::_UdpMulticastEndPointConfig_default_instance_;
    new (ptr) ::robosense::rs_hmi::config::UdpMulticastEndPointConfig();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_UdpMulticastEndPointConfig_config_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_UdpMulticastEndPointConfig_config_2eproto}, {
      &scc_info_UdpControlConfig_config_2eproto.base,}};

static void InitDefaultsscc_info_UdpMulticastTaskConfig_config_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::rs_hmi::config::_UdpMulticastTaskConfig_default_instance_;
    new (ptr) ::robosense::rs_hmi::config::UdpMulticastTaskConfig();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_UdpMulticastTaskConfig_config_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_UdpMulticastTaskConfig_config_2eproto}, {
      &scc_info_UdpMulticastEndPointConfig_config_2eproto.base,}};

static void InitDefaultsscc_info_UdpP2PEndPointConfig_config_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::rs_hmi::config::_UdpP2PEndPointConfig_default_instance_;
    new (ptr) ::robosense::rs_hmi::config::UdpP2PEndPointConfig();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_UdpP2PEndPointConfig_config_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_UdpP2PEndPointConfig_config_2eproto}, {
      &scc_info_UdpControlConfig_config_2eproto.base,}};

static void InitDefaultsscc_info_UdpP2PTaskConfig_config_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::rs_hmi::config::_UdpP2PTaskConfig_default_instance_;
    new (ptr) ::robosense::rs_hmi::config::UdpP2PTaskConfig();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_UdpP2PTaskConfig_config_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_UdpP2PTaskConfig_config_2eproto}, {
      &scc_info_UdpP2PEndPointConfig_config_2eproto.base,}};

static void InitDefaultsscc_info_WebsocketEndPointConfig_config_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::rs_hmi::config::_WebsocketEndPointConfig_default_instance_;
    new (ptr) ::robosense::rs_hmi::config::WebsocketEndPointConfig();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_WebsocketEndPointConfig_config_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_WebsocketEndPointConfig_config_2eproto}, {}};

static void InitDefaultsscc_info_WebsocketRenderTaskConfig_config_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::rs_hmi::config::_WebsocketRenderTaskConfig_default_instance_;
    new (ptr) ::robosense::rs_hmi::config::WebsocketRenderTaskConfig();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<3> scc_info_WebsocketRenderTaskConfig_config_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 3, 0, InitDefaultsscc_info_WebsocketRenderTaskConfig_config_2eproto}, {
      &scc_info_HmiTaskSocketConfig_config_2eproto.base,
      &scc_info_UdpMulticastEndPointConfig_config_2eproto.base,
      &scc_info_UdpP2PEndPointConfig_config_2eproto.base,}};

static void InitDefaultsscc_info_WebsocketSendControlConfig_config_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::rs_hmi::config::_WebsocketSendControlConfig_default_instance_;
    new (ptr) ::robosense::rs_hmi::config::WebsocketSendControlConfig();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_WebsocketSendControlConfig_config_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_WebsocketSendControlConfig_config_2eproto}, {}};

static void InitDefaultsscc_info_WebsocketTaskConfig_config_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::rs_hmi::config::_WebsocketTaskConfig_default_instance_;
    new (ptr) ::robosense::rs_hmi::config::WebsocketTaskConfig();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_WebsocketTaskConfig_config_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 2, 0, InitDefaultsscc_info_WebsocketTaskConfig_config_2eproto}, {
      &scc_info_WebsocketEndPointConfig_config_2eproto.base,
      &scc_info_WebsocketSendControlConfig_config_2eproto.base,}};

static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_config_2eproto[15];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_config_2eproto[5];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_config_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_config_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketEndPointConfig, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketEndPointConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketEndPointConfig, role_type_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketEndPointConfig, server_ip_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketEndPointConfig, server_port_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketEndPointConfig, run_thread_cnt_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketEndPointConfig, send_thread_cnt_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketEndPointConfig, server_client_timeout_ms_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketEndPointConfig, server_check_timeout_ms_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketEndPointConfig, compress_format_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketEndPointConfig, compress_level_),
  1,
  0,
  2,
  3,
  4,
  5,
  6,
  7,
  8,
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpControlConfig, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpControlConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpControlConfig, udp_control_type_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpControlConfig, udp_total_control_time_ms_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpControlConfig, udp_total_control_single_time_ms_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpControlConfig, udp_data_control_size_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpControlConfig, udp_data_control_time_ms_),
  1,
  2,
  3,
  4,
  0,
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpBufferControlConfig, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpBufferControlConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpBufferControlConfig, udp_buffer_control_type_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpBufferControlConfig, udp_dynamic_single_control_th_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpBufferControlConfig, udp_static_single_control_th_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpBufferControlConfig, udp_combine_control_th_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpBufferControlConfig, udp_buffer_control_send_frame_gap_),
  1,
  2,
  3,
  4,
  0,
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpMulticastEndPointConfig, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpMulticastEndPointConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpMulticastEndPointConfig, role_type_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpMulticastEndPointConfig, multicast_ip_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpMulticastEndPointConfig, multicast_port_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpMulticastEndPointConfig, multicast_host_ip_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpMulticastEndPointConfig, max_msg_size_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpMulticastEndPointConfig, buffer_cell_size_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpMulticastEndPointConfig, asio_run_thread_cnt_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpMulticastEndPointConfig, asio_snd_thread_cnt_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpMulticastEndPointConfig, asio_recv_thread_cnt_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpMulticastEndPointConfig, msg_timeout_th_ms_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpMulticastEndPointConfig, asio_snd_retry_cnt_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpMulticastEndPointConfig, asio_snd_retry_th_ms_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpMulticastEndPointConfig, udp_control_config_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpMulticastEndPointConfig, compress_format_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpMulticastEndPointConfig, compress_level_),
  3,
  0,
  4,
  1,
  5,
  6,
  7,
  8,
  9,
  10,
  11,
  12,
  2,
  13,
  14,
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpP2PEndPointConfig, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpP2PEndPointConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpP2PEndPointConfig, role_type_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpP2PEndPointConfig, remote_ip_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpP2PEndPointConfig, remote_port_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpP2PEndPointConfig, max_msg_size_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpP2PEndPointConfig, buffer_cell_size_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpP2PEndPointConfig, asio_run_thread_cnt_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpP2PEndPointConfig, asio_snd_thread_cnt_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpP2PEndPointConfig, asio_recv_thread_cnt_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpP2PEndPointConfig, msg_timeout_th_ms_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpP2PEndPointConfig, asio_snd_retry_cnt_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpP2PEndPointConfig, asio_snd_retry_th_ms_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpP2PEndPointConfig, socket_buffer_size_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpP2PEndPointConfig, udp_control_config_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpP2PEndPointConfig, compress_format_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpP2PEndPointConfig, compress_level_),
  3,
  0,
  4,
  5,
  6,
  7,
  8,
  9,
  10,
  11,
  12,
  13,
  1,
  14,
  2,
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketSendControlConfig, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketSendControlConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketSendControlConfig, message_type_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketSendControlConfig, enable_loss_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketSendControlConfig, loss_buffer_size_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketSendControlConfig, loss_gap_size_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketSendControlConfig, enable_loss2_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketSendControlConfig, loss_buffer_size2_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketSendControlConfig, loss_gap_size2_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketSendControlConfig, enable_clear_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketSendControlConfig, clear_buffer_size_),
  0,
  3,
  1,
  2,
  4,
  6,
  7,
  5,
  8,
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketTaskConfig, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketTaskConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketTaskConfig, task_socket_key_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketTaskConfig, websocket_endpoint_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketTaskConfig, websocket_send_controls_),
  0,
  1,
  ~0u,
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpMulticastTaskConfig, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpMulticastTaskConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpMulticastTaskConfig, task_socket_key_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpMulticastTaskConfig, udp_multicast_endpoint_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpP2PTaskConfig, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpP2PTaskConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpP2PTaskConfig, task_socket_key_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::UdpP2PTaskConfig, udp_p2p_endpoint_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::SocketTaskConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::SocketTaskConfig, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::SocketTaskConfig, socket_endpoint_config_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::HmiSocketTaskConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::HmiSocketTaskConfig, socket_task_configs_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::HmiSocketTopicTaskConfig, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::HmiSocketTopicTaskConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::HmiSocketTopicTaskConfig, support_message_topics_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::HmiSocketTopicTaskConfig, task_socket_key_),
  ~0u,
  0,
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::HmiTaskSocketConfig, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::HmiTaskSocketConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::HmiTaskSocketConfig, enable_task_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::HmiTaskSocketConfig, task_socket_configs_),
  0,
  ~0u,
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketRenderTaskConfig, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketRenderTaskConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketRenderTaskConfig, websocket_task_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketRenderTaskConfig, enable_websocket_buffer_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketRenderTaskConfig, websocket_buffer_size_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketRenderTaskConfig, low_websocket_buffer_size_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketRenderTaskConfig, websocket_cmd_socket_key_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketRenderTaskConfig, websocket_socket_key_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketRenderTaskConfig, udp_multicast_socket_key_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketRenderTaskConfig, udp_double_multicast_socket_key_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketRenderTaskConfig, udp_p2p_socket_key_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketRenderTaskConfig, udp_2_sample_1_buffer_size_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketRenderTaskConfig, udp_3_sample_1_buffer_size_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketRenderTaskConfig, udp_loss_all_buffer_size_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketRenderTaskConfig, max_send_render_buffer_size_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketRenderTaskConfig, udp_multicast_render_config_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketRenderTaskConfig, udp_p2p_render_config_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::WebsocketRenderTaskConfig, double_udp_multicast_render_config_),
  5,
  10,
  11,
  12,
  0,
  1,
  2,
  3,
  4,
  13,
  14,
  15,
  9,
  6,
  7,
  8,
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::Config, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::Config, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::Config, enable_websocket_buffer_check_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::Config, websocket_buffer_check_timeout_th_ms_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::Config, render_config_),
  PROTOBUF_FIELD_OFFSET(::robosense::rs_hmi::config::Config, hmi_socket_task_config_),
  2,
  3,
  0,
  1,
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, 14, sizeof(::robosense::rs_hmi::config::WebsocketEndPointConfig)},
  { 23, 33, sizeof(::robosense::rs_hmi::config::UdpControlConfig)},
  { 38, 48, sizeof(::robosense::rs_hmi::config::UdpBufferControlConfig)},
  { 53, 73, sizeof(::robosense::rs_hmi::config::UdpMulticastEndPointConfig)},
  { 88, 108, sizeof(::robosense::rs_hmi::config::UdpP2PEndPointConfig)},
  { 123, 137, sizeof(::robosense::rs_hmi::config::WebsocketSendControlConfig)},
  { 146, 154, sizeof(::robosense::rs_hmi::config::WebsocketTaskConfig)},
  { 157, 164, sizeof(::robosense::rs_hmi::config::UdpMulticastTaskConfig)},
  { 166, 173, sizeof(::robosense::rs_hmi::config::UdpP2PTaskConfig)},
  { 175, -1, sizeof(::robosense::rs_hmi::config::SocketTaskConfig)},
  { 184, -1, sizeof(::robosense::rs_hmi::config::HmiSocketTaskConfig)},
  { 190, 197, sizeof(::robosense::rs_hmi::config::HmiSocketTopicTaskConfig)},
  { 199, 206, sizeof(::robosense::rs_hmi::config::HmiTaskSocketConfig)},
  { 208, 229, sizeof(::robosense::rs_hmi::config::WebsocketRenderTaskConfig)},
  { 245, 254, sizeof(::robosense::rs_hmi::config::Config)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::rs_hmi::config::_WebsocketEndPointConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::rs_hmi::config::_UdpControlConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::rs_hmi::config::_UdpBufferControlConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::rs_hmi::config::_UdpMulticastEndPointConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::rs_hmi::config::_UdpP2PEndPointConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::rs_hmi::config::_WebsocketSendControlConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::rs_hmi::config::_WebsocketTaskConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::rs_hmi::config::_UdpMulticastTaskConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::rs_hmi::config::_UdpP2PTaskConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::rs_hmi::config::_SocketTaskConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::rs_hmi::config::_HmiSocketTaskConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::rs_hmi::config::_HmiSocketTopicTaskConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::rs_hmi::config::_HmiTaskSocketConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::rs_hmi::config::_WebsocketRenderTaskConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::rs_hmi::config::_Config_default_instance_),
};

const char descriptor_table_protodef_config_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\014config.proto\022\027robosense.rs_hmi.config\032"
  "\024proto_compress.proto\"\256\003\n\027WebsocketEndPo"
  "intConfig\022\?\n\trole_type\030\001 \001(\0162,.robosense"
  ".rs_hmi.config.RS_SOCKET_ROLE_TYPE\022\032\n\tse"
  "rver_ip\030\002 \001(\t:\0070.0.0.0\022\031\n\013server_port\030\003 "
  "\001(\r:\0048080\022\031\n\016run_thread_cnt\030\004 \001(\r:\0014\022\032\n\017"
  "send_thread_cnt\030\005 \001(\r:\0014\022\'\n\030server_clien"
  "t_timeout_ms\030\006 \001(\r:\00520000\022%\n\027server_chec"
  "k_timeout_ms\030\007 \001(\r:\0043000\022y\n\017compress_for"
  "mat\030\010 \001(\0162>.robosense.proto_compress_msg"
  "s.RS_POST_DATA_COMPRESSION_FORMAT: RS_PO"
  "ST_DATA_COMPRESSION_NOTHING\022\031\n\016compress_"
  "level\030\t \001(\r:\0019\"\242\002\n\020UdpControlConfig\022n\n\020u"
  "dp_control_type\030\001 \001(\01621.robosense.rs_hmi"
  ".config.RS_UDP_CONTROL_MODE_TYPE:!RS_UDP"
  "_CONTROL_TOTAL_CONTROL_TIME\022%\n\031udp_total"
  "_control_time_ms\030\002 \001(\r:\00260\022+\n udp_total_"
  "control_single_time_ms\030\003 \001(\r:\0012\022%\n\025udp_d"
  "ata_control_size\030\004 \001(\r:\006262144\022#\n\030udp_da"
  "ta_control_time_ms\030\005 \001(\r:\0012\"\265\002\n\026UdpBuffe"
  "rControlConfig\022w\n\027udp_buffer_control_typ"
  "e\030\001 \001(\01628.robosense.rs_hmi.config.RS_UDP"
  "_BUFFER_CONTROL_MODE_TYPE:\034RS_UDP_BUFFER"
  "_CONTROL_ENABLE\022(\n\035udp_dynamic_single_co"
  "ntrol_th\030\002 \001(\r:\0013\022\'\n\034udp_static_single_c"
  "ontrol_th\030\003 \001(\r:\0013\022!\n\026udp_combine_contro"
  "l_th\030\004 \001(\r:\0016\022,\n!udp_buffer_control_send"
  "_frame_gap\030\005 \001(\r:\0012\"\237\005\n\032UdpMulticastEndP"
  "ointConfig\022\?\n\trole_type\030\001 \001(\0162,.robosens"
  "e.rs_hmi.config.RS_SOCKET_ROLE_TYPE\022!\n\014m"
  "ulticast_ip\030\002 \001(\t:\013***********\022\034\n\016multic"
  "ast_port\030\003 \001(\r:\0049096\022&\n\021multicast_host_i"
  "p\030\004 \001(\t:\013***********\022\032\n\014max_msg_size\030\005 \001"
  "(\r:\0044096\022\035\n\020buffer_cell_size\030\006 \001(\r:\003128\022"
  "\036\n\023asio_run_thread_cnt\030\007 \001(\r:\0011\022\036\n\023asio_"
  "snd_thread_cnt\030\010 \001(\r:\0011\022\037\n\024asio_recv_thr"
  "ead_cnt\030\t \001(\r:\0011\022\036\n\021msg_timeout_th_ms\030\n "
  "\001(\r:\003100\022\035\n\022asio_snd_retry_cnt\030\013 \001(\r:\0011\022"
  "\037\n\024asio_snd_retry_th_ms\030\014 \001(\r:\0015\022E\n\022udp_"
  "control_config\030\r \001(\0132).robosense.rs_hmi."
  "config.UdpControlConfig\022y\n\017compress_form"
  "at\030\016 \001(\0162>.robosense.proto_compress_msgs"
  ".RS_POST_DATA_COMPRESSION_FORMAT: RS_POS"
  "T_DATA_COMPRESSION_NOTHING\022\031\n\016compress_l"
  "evel\030\017 \001(\r:\0019\"\202\005\n\024UdpP2PEndPointConfig\022\?"
  "\n\trole_type\030\001 \001(\0162,.robosense.rs_hmi.con"
  "fig.RS_SOCKET_ROLE_TYPE\022\021\n\tremote_ip\030\002 \001"
  "(\t\022\031\n\013remote_port\030\003 \001(\r:\0049097\022\032\n\014max_msg"
  "_size\030\005 \001(\r:\0044096\022\035\n\020buffer_cell_size\030\006 "
  "\001(\r:\003128\022\036\n\023asio_run_thread_cnt\030\007 \001(\r:\0011"
  "\022\036\n\023asio_snd_thread_cnt\030\010 \001(\r:\0011\022\037\n\024asio"
  "_recv_thread_cnt\030\t \001(\r:\0011\022\036\n\021msg_timeout"
  "_th_ms\030\n \001(\r:\003100\022\035\n\022asio_snd_retry_cnt\030"
  "\013 \001(\r:\0011\022\037\n\024asio_snd_retry_th_ms\030\014 \001(\r:\001"
  "5\022\"\n\022socket_buffer_size\030\r \001(\r:\006262144\022E\n"
  "\022udp_control_config\030\016 \001(\0132).robosense.rs"
  "_hmi.config.UdpControlConfig\022y\n\017compress"
  "_format\030\017 \001(\0162>.robosense.proto_compress"
  "_msgs.RS_POST_DATA_COMPRESSION_FORMAT: R"
  "S_POST_DATA_COMPRESSION_NOTHING\022\031\n\016compr"
  "ess_level\030\020 \001(\r:\0019\"\226\002\n\032WebsocketSendCont"
  "rolConfig\022\024\n\014message_type\030\001 \001(\t\022\031\n\013enabl"
  "e_loss\030\002 \001(\010:\004true\022\034\n\020loss_buffer_size\030\003"
  " \001(\r:\00210\022\030\n\rloss_gap_size\030\004 \001(\r:\0012\022\032\n\014en"
  "able_loss2\030\005 \001(\010:\004true\022\035\n\021loss_buffer_si"
  "ze2\030\006 \001(\r:\00215\022\031\n\016loss_gap_size2\030\007 \001(\r:\0013"
  "\022\032\n\014enable_clear\030\010 \001(\010:\004true\022\035\n\021clear_bu"
  "ffer_size\030\t \001(\r:\00220\"\322\001\n\023WebsocketTaskCon"
  "fig\022\027\n\017task_socket_key\030\001 \001(\t\022L\n\022websocke"
  "t_endpoint\030\002 \001(\01320.robosense.rs_hmi.conf"
  "ig.WebsocketEndPointConfig\022T\n\027websocket_"
  "send_controls\030\003 \003(\01323.robosense.rs_hmi.c"
  "onfig.WebsocketSendControlConfig\"\206\001\n\026Udp"
  "MulticastTaskConfig\022\027\n\017task_socket_key\030\001"
  " \001(\t\022S\n\026udp_multicast_endpoint\030\002 \001(\01323.r"
  "obosense.rs_hmi.config.UdpMulticastEndPo"
  "intConfig\"t\n\020UdpP2PTaskConfig\022\027\n\017task_so"
  "cket_key\030\001 \001(\t\022G\n\020udp_p2p_endpoint\030\002 \001(\013"
  "2-.robosense.rs_hmi.config.UdpP2PEndPoin"
  "tConfig\"\206\002\n\020SocketTaskConfig\022F\n\016websocke"
  "t_task\030\001 \001(\0132,.robosense.rs_hmi.config.W"
  "ebsocketTaskConfigH\000\022M\n\022udp_multicast_ta"
  "sk\030\002 \001(\0132/.robosense.rs_hmi.config.UdpMu"
  "lticastTaskConfigH\000\022A\n\014udp_p2p_task\030\003 \001("
  "\0132).robosense.rs_hmi.config.UdpP2PTaskCo"
  "nfigH\000B\030\n\026socket_endpoint_config\"]\n\023HmiS"
  "ocketTaskConfig\022F\n\023socket_task_configs\030\001"
  " \003(\0132).robosense.rs_hmi.config.SocketTas"
  "kConfig\"S\n\030HmiSocketTopicTaskConfig\022\036\n\026s"
  "upport_message_topics\030\001 \003(\t\022\027\n\017task_sock"
  "et_key\030\002 \001(\t\"\201\001\n\023HmiTaskSocketConfig\022\032\n\013"
  "enable_task\030\001 \001(\010:\005false\022N\n\023task_socket_"
  "configs\030\002 \003(\01321.robosense.rs_hmi.config."
  "HmiSocketTopicTaskConfig\"\250\006\n\031WebsocketRe"
  "nderTaskConfig\022D\n\016websocket_task\030\001 \001(\0132,"
  ".robosense.rs_hmi.config.HmiTaskSocketCo"
  "nfig\022%\n\027enable_websocket_buffer\030\002 \001(\010:\004t"
  "rue\022&\n\025websocket_buffer_size\030\003 \001(\r:\0072097"
  "152\022)\n\031low_websocket_buffer_size\030\004 \001(\r:\006"
  "524288\022 \n\030websocket_cmd_socket_key\030\005 \001(\t"
  "\022\034\n\024websocket_socket_key\030\006 \001(\t\022 \n\030udp_mu"
  "lticast_socket_key\030\007 \001(\t\022\'\n\037udp_double_m"
  "ulticast_socket_key\030\010 \001(\t\022\032\n\022udp_p2p_soc"
  "ket_key\030\t \001(\t\022%\n\032udp_2_sample_1_buffer_s"
  "ize\030\n \001(\r:\0013\022%\n\032udp_3_sample_1_buffer_si"
  "ze\030\013 \001(\r:\0015\022$\n\030udp_loss_all_buffer_size\030"
  "\014 \001(\r:\00210\022\'\n\033max_send_render_buffer_size"
  "\030\r \001(\r:\00215\022X\n\033udp_multicast_render_confi"
  "g\030\016 \001(\01323.robosense.rs_hmi.config.UdpMul"
  "ticastEndPointConfig\022L\n\025udp_p2p_render_c"
  "onfig\030\017 \001(\0132-.robosense.rs_hmi.config.Ud"
  "pP2PEndPointConfig\022_\n\"double_udp_multica"
  "st_render_config\030\020 \001(\01323.robosense.rs_hm"
  "i.config.UdpMulticastEndPointConfig\"\202\002\n\006"
  "Config\022+\n\035enable_websocket_buffer_check\030"
  "\001 \001(\010:\004true\0222\n$websocket_buffer_check_ti"
  "meout_th_ms\030\002 \001(\r:\0042000\022I\n\rrender_config"
  "\030\004 \001(\01322.robosense.rs_hmi.config.Websock"
  "etRenderTaskConfig\022L\n\026hmi_socket_task_co"
  "nfig\030\021 \001(\0132,.robosense.rs_hmi.config.Hmi"
  "SocketTaskConfig*\353\001\n\023RS_SOCKET_ROLE_TYPE"
  "\022\034\n\030RS_WEBSOCKET_SERVER_ROLE\020\001\022\034\n\030RS_WEB"
  "SOCKET_CLIENT_ROLE\020\002\022 \n\034RS_UDP_MULTICAST"
  "_SENDER_ROLE\020\003\022\"\n\036RS_UDP_MULTICAST_RECEI"
  "VER_ROLE\020\004\022\032\n\026RS_UDP_P2P_SENDER_ROLE\020\005\022\034"
  "\n\030RS_UDP_P2P_RECEIVER_ROLE\020\006\022\030\n\024RS_UDP_P"
  "2P_BOTH_ROLE\020\007*\203\001\n\030RS_UDP_CONTROL_MODE_T"
  "YPE\022\032\n\026RS_UDP_CONTROL_NOTHING\020\001\022%\n!RS_UD"
  "P_CONTROL_TOTAL_CONTROL_TIME\020\002\022$\n RS_UDP"
  "_CONTROL_DATA_CONTROL_TIME\020\003*f\n\037RS_UDP_B"
  "UFFER_CONTROL_MODE_TYPE\022!\n\035RS_UDP_BUFFER"
  "_CONTROL_NOTHING\020\001\022 \n\034RS_UDP_BUFFER_CONT"
  "ROL_ENABLE\020\002*\235\003\n\020RS_HMI_TASK_TYPE\022\026\n\022RS_"
  "HMI_TASK_CARAPP\020\001\022\026\n\022RS_HMI_TASK_RENDER\020"
  "\002\022\034\n\030RS_HMI_TASK_SYSTEMSTATUS\020\003\022\026\n\022RS_HM"
  "I_TASK_PNCAPP\020\004\022\034\n\030RS_HMI_TASK_EGOCARSTA"
  "TUS\020\005\022\035\n\031RS_HMI_TASK_FAULTREPORTER\020\006\022\027\n\023"
  "RS_HMI_TASK_VERSION\020\007\022\031\n\025RS_HMI_TASK_FRE"
  "QUENCE\020\010\022\025\n\021RS_HMI_TASK_AUDIO\020\t\022\035\n\031RS_HM"
  "I_TASK_RTK_TIEM_SYNC\020\n\022\"\n\036RS_HMI_TASK_PR"
  "EFABRICATEDROUTE\020\013\022\034\n\030RS_HMI_TASK_LOCALI"
  "ZATION\020\014\022\036\n\032RS_HMI_TASK_REMOTE_CONTROL\020\r"
  "\022\032\n\026RS_HMI_TASK_QUICK_DATA\020\016*X\n\031RS_HD_MA"
  "P_TRANSFORM_LEVEL\022\035\n\031RS_HD_MAP_TRANSFORM"
  "_BASIC\020\001\022\034\n\030RS_HD_MAP_TRANSFORM_FULL\020\002"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_config_2eproto_deps[1] = {
  &::descriptor_table_proto_5fcompress_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase*const descriptor_table_config_2eproto_sccs[15] = {
  &scc_info_Config_config_2eproto.base,
  &scc_info_HmiSocketTaskConfig_config_2eproto.base,
  &scc_info_HmiSocketTopicTaskConfig_config_2eproto.base,
  &scc_info_HmiTaskSocketConfig_config_2eproto.base,
  &scc_info_SocketTaskConfig_config_2eproto.base,
  &scc_info_UdpBufferControlConfig_config_2eproto.base,
  &scc_info_UdpControlConfig_config_2eproto.base,
  &scc_info_UdpMulticastEndPointConfig_config_2eproto.base,
  &scc_info_UdpMulticastTaskConfig_config_2eproto.base,
  &scc_info_UdpP2PEndPointConfig_config_2eproto.base,
  &scc_info_UdpP2PTaskConfig_config_2eproto.base,
  &scc_info_WebsocketEndPointConfig_config_2eproto.base,
  &scc_info_WebsocketRenderTaskConfig_config_2eproto.base,
  &scc_info_WebsocketSendControlConfig_config_2eproto.base,
  &scc_info_WebsocketTaskConfig_config_2eproto.base,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_config_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_config_2eproto = {
  false, false, descriptor_table_protodef_config_2eproto, "config.proto", 5798,
  &descriptor_table_config_2eproto_once, descriptor_table_config_2eproto_sccs, descriptor_table_config_2eproto_deps, 15, 1,
  schemas, file_default_instances, TableStruct_config_2eproto::offsets,
  file_level_metadata_config_2eproto, 15, file_level_enum_descriptors_config_2eproto, file_level_service_descriptors_config_2eproto,
};

// Force running AddDescriptors() at dynamic initialization time.
static bool dynamic_init_dummy_config_2eproto = (static_cast<void>(::PROTOBUF_NAMESPACE_ID::internal::AddDescriptors(&descriptor_table_config_2eproto)), true);
namespace robosense {
namespace rs_hmi {
namespace config {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RS_SOCKET_ROLE_TYPE_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_config_2eproto);
  return file_level_enum_descriptors_config_2eproto[0];
}
bool RS_SOCKET_ROLE_TYPE_IsValid(int value) {
  switch (value) {
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RS_UDP_CONTROL_MODE_TYPE_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_config_2eproto);
  return file_level_enum_descriptors_config_2eproto[1];
}
bool RS_UDP_CONTROL_MODE_TYPE_IsValid(int value) {
  switch (value) {
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RS_UDP_BUFFER_CONTROL_MODE_TYPE_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_config_2eproto);
  return file_level_enum_descriptors_config_2eproto[2];
}
bool RS_UDP_BUFFER_CONTROL_MODE_TYPE_IsValid(int value) {
  switch (value) {
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RS_HMI_TASK_TYPE_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_config_2eproto);
  return file_level_enum_descriptors_config_2eproto[3];
}
bool RS_HMI_TASK_TYPE_IsValid(int value) {
  switch (value) {
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
    case 10:
    case 11:
    case 12:
    case 13:
    case 14:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RS_HD_MAP_TRANSFORM_LEVEL_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_config_2eproto);
  return file_level_enum_descriptors_config_2eproto[4];
}
bool RS_HD_MAP_TRANSFORM_LEVEL_IsValid(int value) {
  switch (value) {
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class WebsocketEndPointConfig::_Internal {
 public:
  using HasBits = decltype(std::declval<WebsocketEndPointConfig>()._has_bits_);
  static void set_has_role_type(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_server_ip(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_server_port(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_run_thread_cnt(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_send_thread_cnt(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static void set_has_server_client_timeout_ms(HasBits* has_bits) {
    (*has_bits)[0] |= 32u;
  }
  static void set_has_server_check_timeout_ms(HasBits* has_bits) {
    (*has_bits)[0] |= 64u;
  }
  static void set_has_compress_format(HasBits* has_bits) {
    (*has_bits)[0] |= 128u;
  }
  static void set_has_compress_level(HasBits* has_bits) {
    (*has_bits)[0] |= 256u;
  }
};

const ::PROTOBUF_NAMESPACE_ID::internal::LazyString WebsocketEndPointConfig::_i_give_permission_to_break_this_code_default_server_ip_{{{"0.0.0.0", 7}}, {nullptr}};
WebsocketEndPointConfig::WebsocketEndPointConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.rs_hmi.config.WebsocketEndPointConfig)
}
WebsocketEndPointConfig::WebsocketEndPointConfig(const WebsocketEndPointConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  server_ip_.UnsafeSetDefault(nullptr);
  if (from._internal_has_server_ip()) {
    server_ip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::NonEmptyDefault{}, from._internal_server_ip(), 
      GetArena());
  }
  ::memcpy(&role_type_, &from.role_type_,
    static_cast<size_t>(reinterpret_cast<char*>(&compress_level_) -
    reinterpret_cast<char*>(&role_type_)) + sizeof(compress_level_));
  // @@protoc_insertion_point(copy_constructor:robosense.rs_hmi.config.WebsocketEndPointConfig)
}

void WebsocketEndPointConfig::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_WebsocketEndPointConfig_config_2eproto.base);
  server_ip_.UnsafeSetDefault(nullptr);
  role_type_ = 1;
  server_port_ = 8080u;
  run_thread_cnt_ = 4u;
  send_thread_cnt_ = 4u;
  server_client_timeout_ms_ = 20000u;
  server_check_timeout_ms_ = 3000u;
  compress_format_ = 1;
  compress_level_ = 9u;
}

WebsocketEndPointConfig::~WebsocketEndPointConfig() {
  // @@protoc_insertion_point(destructor:robosense.rs_hmi.config.WebsocketEndPointConfig)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void WebsocketEndPointConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  server_ip_.DestroyNoArena(nullptr);
}

void WebsocketEndPointConfig::ArenaDtor(void* object) {
  WebsocketEndPointConfig* _this = reinterpret_cast< WebsocketEndPointConfig* >(object);
  (void)_this;
}
void WebsocketEndPointConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void WebsocketEndPointConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const WebsocketEndPointConfig& WebsocketEndPointConfig::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_WebsocketEndPointConfig_config_2eproto.base);
  return *internal_default_instance();
}


void WebsocketEndPointConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.rs_hmi.config.WebsocketEndPointConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    if (cached_has_bits & 0x00000001u) {
      server_ip_.ClearToDefault(::robosense::rs_hmi::config::WebsocketEndPointConfig::_i_give_permission_to_break_this_code_default_server_ip_, GetArena());
       }
    role_type_ = 1;
    server_port_ = 8080u;
    run_thread_cnt_ = 4u;
    send_thread_cnt_ = 4u;
    server_client_timeout_ms_ = 20000u;
    server_check_timeout_ms_ = 3000u;
    compress_format_ = 1;
  }
  compress_level_ = 9u;
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* WebsocketEndPointConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional .robosense.rs_hmi.config.RS_SOCKET_ROLE_TYPE role_type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE_IsValid(val))) {
            _internal_set_role_type(static_cast<::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(1, val, mutable_unknown_fields());
          }
        } else goto handle_unusual;
        continue;
      // optional string server_ip = 2 [default = "0.0.0.0"];
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_server_ip();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.rs_hmi.config.WebsocketEndPointConfig.server_ip");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 server_port = 3 [default = 8080];
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          _Internal::set_has_server_port(&has_bits);
          server_port_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 run_thread_cnt = 4 [default = 4];
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          _Internal::set_has_run_thread_cnt(&has_bits);
          run_thread_cnt_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 send_thread_cnt = 5 [default = 4];
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          _Internal::set_has_send_thread_cnt(&has_bits);
          send_thread_cnt_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 server_client_timeout_ms = 6 [default = 20000];
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          _Internal::set_has_server_client_timeout_ms(&has_bits);
          server_client_timeout_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 server_check_timeout_ms = 7 [default = 3000];
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          _Internal::set_has_server_check_timeout_ms(&has_bits);
          server_check_timeout_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .robosense.proto_compress_msgs.RS_POST_DATA_COMPRESSION_FORMAT compress_format = 8 [default = RS_POST_DATA_COMPRESSION_NOTHING];
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT_IsValid(val))) {
            _internal_set_compress_format(static_cast<::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(8, val, mutable_unknown_fields());
          }
        } else goto handle_unusual;
        continue;
      // optional uint32 compress_level = 9 [default = 9];
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 72)) {
          _Internal::set_has_compress_level(&has_bits);
          compress_level_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* WebsocketEndPointConfig::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.rs_hmi.config.WebsocketEndPointConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional .robosense.rs_hmi.config.RS_SOCKET_ROLE_TYPE role_type = 1;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_role_type(), target);
  }

  // optional string server_ip = 2 [default = "0.0.0.0"];
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_server_ip().data(), static_cast<int>(this->_internal_server_ip().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.rs_hmi.config.WebsocketEndPointConfig.server_ip");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_server_ip(), target);
  }

  // optional uint32 server_port = 3 [default = 8080];
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_server_port(), target);
  }

  // optional uint32 run_thread_cnt = 4 [default = 4];
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_run_thread_cnt(), target);
  }

  // optional uint32 send_thread_cnt = 5 [default = 4];
  if (cached_has_bits & 0x00000010u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_send_thread_cnt(), target);
  }

  // optional uint32 server_client_timeout_ms = 6 [default = 20000];
  if (cached_has_bits & 0x00000020u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_server_client_timeout_ms(), target);
  }

  // optional uint32 server_check_timeout_ms = 7 [default = 3000];
  if (cached_has_bits & 0x00000040u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(7, this->_internal_server_check_timeout_ms(), target);
  }

  // optional .robosense.proto_compress_msgs.RS_POST_DATA_COMPRESSION_FORMAT compress_format = 8 [default = RS_POST_DATA_COMPRESSION_NOTHING];
  if (cached_has_bits & 0x00000080u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      8, this->_internal_compress_format(), target);
  }

  // optional uint32 compress_level = 9 [default = 9];
  if (cached_has_bits & 0x00000100u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(9, this->_internal_compress_level(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.rs_hmi.config.WebsocketEndPointConfig)
  return target;
}

size_t WebsocketEndPointConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.rs_hmi.config.WebsocketEndPointConfig)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    // optional string server_ip = 2 [default = "0.0.0.0"];
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_server_ip());
    }

    // optional .robosense.rs_hmi.config.RS_SOCKET_ROLE_TYPE role_type = 1;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_role_type());
    }

    // optional uint32 server_port = 3 [default = 8080];
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_server_port());
    }

    // optional uint32 run_thread_cnt = 4 [default = 4];
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_run_thread_cnt());
    }

    // optional uint32 send_thread_cnt = 5 [default = 4];
    if (cached_has_bits & 0x00000010u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_send_thread_cnt());
    }

    // optional uint32 server_client_timeout_ms = 6 [default = 20000];
    if (cached_has_bits & 0x00000020u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_server_client_timeout_ms());
    }

    // optional uint32 server_check_timeout_ms = 7 [default = 3000];
    if (cached_has_bits & 0x00000040u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_server_check_timeout_ms());
    }

    // optional .robosense.proto_compress_msgs.RS_POST_DATA_COMPRESSION_FORMAT compress_format = 8 [default = RS_POST_DATA_COMPRESSION_NOTHING];
    if (cached_has_bits & 0x00000080u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_compress_format());
    }

  }
  // optional uint32 compress_level = 9 [default = 9];
  if (cached_has_bits & 0x00000100u) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_compress_level());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void WebsocketEndPointConfig::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.rs_hmi.config.WebsocketEndPointConfig)
  GOOGLE_DCHECK_NE(&from, this);
  const WebsocketEndPointConfig* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<WebsocketEndPointConfig>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.rs_hmi.config.WebsocketEndPointConfig)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.rs_hmi.config.WebsocketEndPointConfig)
    MergeFrom(*source);
  }
}

void WebsocketEndPointConfig::MergeFrom(const WebsocketEndPointConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.rs_hmi.config.WebsocketEndPointConfig)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_server_ip(from._internal_server_ip());
    }
    if (cached_has_bits & 0x00000002u) {
      role_type_ = from.role_type_;
    }
    if (cached_has_bits & 0x00000004u) {
      server_port_ = from.server_port_;
    }
    if (cached_has_bits & 0x00000008u) {
      run_thread_cnt_ = from.run_thread_cnt_;
    }
    if (cached_has_bits & 0x00000010u) {
      send_thread_cnt_ = from.send_thread_cnt_;
    }
    if (cached_has_bits & 0x00000020u) {
      server_client_timeout_ms_ = from.server_client_timeout_ms_;
    }
    if (cached_has_bits & 0x00000040u) {
      server_check_timeout_ms_ = from.server_check_timeout_ms_;
    }
    if (cached_has_bits & 0x00000080u) {
      compress_format_ = from.compress_format_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  if (cached_has_bits & 0x00000100u) {
    _internal_set_compress_level(from._internal_compress_level());
  }
}

void WebsocketEndPointConfig::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.rs_hmi.config.WebsocketEndPointConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void WebsocketEndPointConfig::CopyFrom(const WebsocketEndPointConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.rs_hmi.config.WebsocketEndPointConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool WebsocketEndPointConfig::IsInitialized() const {
  return true;
}

void WebsocketEndPointConfig::InternalSwap(WebsocketEndPointConfig* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  server_ip_.Swap(&other->server_ip_, nullptr, GetArena());
  swap(role_type_, other->role_type_);
  swap(server_port_, other->server_port_);
  swap(run_thread_cnt_, other->run_thread_cnt_);
  swap(send_thread_cnt_, other->send_thread_cnt_);
  swap(server_client_timeout_ms_, other->server_client_timeout_ms_);
  swap(server_check_timeout_ms_, other->server_check_timeout_ms_);
  swap(compress_format_, other->compress_format_);
  swap(compress_level_, other->compress_level_);
}

::PROTOBUF_NAMESPACE_ID::Metadata WebsocketEndPointConfig::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class UdpControlConfig::_Internal {
 public:
  using HasBits = decltype(std::declval<UdpControlConfig>()._has_bits_);
  static void set_has_udp_control_type(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_udp_total_control_time_ms(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_udp_total_control_single_time_ms(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_udp_data_control_size(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static void set_has_udp_data_control_time_ms(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

UdpControlConfig::UdpControlConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.rs_hmi.config.UdpControlConfig)
}
UdpControlConfig::UdpControlConfig(const UdpControlConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&udp_data_control_time_ms_, &from.udp_data_control_time_ms_,
    static_cast<size_t>(reinterpret_cast<char*>(&udp_data_control_size_) -
    reinterpret_cast<char*>(&udp_data_control_time_ms_)) + sizeof(udp_data_control_size_));
  // @@protoc_insertion_point(copy_constructor:robosense.rs_hmi.config.UdpControlConfig)
}

void UdpControlConfig::SharedCtor() {
  udp_data_control_time_ms_ = 2u;
  udp_control_type_ = 2;
  udp_total_control_time_ms_ = 60u;
  udp_total_control_single_time_ms_ = 2u;
  udp_data_control_size_ = 262144u;
}

UdpControlConfig::~UdpControlConfig() {
  // @@protoc_insertion_point(destructor:robosense.rs_hmi.config.UdpControlConfig)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void UdpControlConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void UdpControlConfig::ArenaDtor(void* object) {
  UdpControlConfig* _this = reinterpret_cast< UdpControlConfig* >(object);
  (void)_this;
}
void UdpControlConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UdpControlConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const UdpControlConfig& UdpControlConfig::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_UdpControlConfig_config_2eproto.base);
  return *internal_default_instance();
}


void UdpControlConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.rs_hmi.config.UdpControlConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000001fu) {
    udp_data_control_time_ms_ = 2u;
    udp_control_type_ = 2;
    udp_total_control_time_ms_ = 60u;
    udp_total_control_single_time_ms_ = 2u;
    udp_data_control_size_ = 262144u;
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* UdpControlConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional .robosense.rs_hmi.config.RS_UDP_CONTROL_MODE_TYPE udp_control_type = 1 [default = RS_UDP_CONTROL_TOTAL_CONTROL_TIME];
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::robosense::rs_hmi::config::RS_UDP_CONTROL_MODE_TYPE_IsValid(val))) {
            _internal_set_udp_control_type(static_cast<::robosense::rs_hmi::config::RS_UDP_CONTROL_MODE_TYPE>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(1, val, mutable_unknown_fields());
          }
        } else goto handle_unusual;
        continue;
      // optional uint32 udp_total_control_time_ms = 2 [default = 60];
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          _Internal::set_has_udp_total_control_time_ms(&has_bits);
          udp_total_control_time_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 udp_total_control_single_time_ms = 3 [default = 2];
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          _Internal::set_has_udp_total_control_single_time_ms(&has_bits);
          udp_total_control_single_time_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 udp_data_control_size = 4 [default = 262144];
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          _Internal::set_has_udp_data_control_size(&has_bits);
          udp_data_control_size_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 udp_data_control_time_ms = 5 [default = 2];
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          _Internal::set_has_udp_data_control_time_ms(&has_bits);
          udp_data_control_time_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* UdpControlConfig::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.rs_hmi.config.UdpControlConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional .robosense.rs_hmi.config.RS_UDP_CONTROL_MODE_TYPE udp_control_type = 1 [default = RS_UDP_CONTROL_TOTAL_CONTROL_TIME];
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_udp_control_type(), target);
  }

  // optional uint32 udp_total_control_time_ms = 2 [default = 60];
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_udp_total_control_time_ms(), target);
  }

  // optional uint32 udp_total_control_single_time_ms = 3 [default = 2];
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_udp_total_control_single_time_ms(), target);
  }

  // optional uint32 udp_data_control_size = 4 [default = 262144];
  if (cached_has_bits & 0x00000010u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_udp_data_control_size(), target);
  }

  // optional uint32 udp_data_control_time_ms = 5 [default = 2];
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_udp_data_control_time_ms(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.rs_hmi.config.UdpControlConfig)
  return target;
}

size_t UdpControlConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.rs_hmi.config.UdpControlConfig)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000001fu) {
    // optional uint32 udp_data_control_time_ms = 5 [default = 2];
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_udp_data_control_time_ms());
    }

    // optional .robosense.rs_hmi.config.RS_UDP_CONTROL_MODE_TYPE udp_control_type = 1 [default = RS_UDP_CONTROL_TOTAL_CONTROL_TIME];
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_udp_control_type());
    }

    // optional uint32 udp_total_control_time_ms = 2 [default = 60];
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_udp_total_control_time_ms());
    }

    // optional uint32 udp_total_control_single_time_ms = 3 [default = 2];
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_udp_total_control_single_time_ms());
    }

    // optional uint32 udp_data_control_size = 4 [default = 262144];
    if (cached_has_bits & 0x00000010u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_udp_data_control_size());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void UdpControlConfig::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.rs_hmi.config.UdpControlConfig)
  GOOGLE_DCHECK_NE(&from, this);
  const UdpControlConfig* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<UdpControlConfig>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.rs_hmi.config.UdpControlConfig)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.rs_hmi.config.UdpControlConfig)
    MergeFrom(*source);
  }
}

void UdpControlConfig::MergeFrom(const UdpControlConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.rs_hmi.config.UdpControlConfig)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000001fu) {
    if (cached_has_bits & 0x00000001u) {
      udp_data_control_time_ms_ = from.udp_data_control_time_ms_;
    }
    if (cached_has_bits & 0x00000002u) {
      udp_control_type_ = from.udp_control_type_;
    }
    if (cached_has_bits & 0x00000004u) {
      udp_total_control_time_ms_ = from.udp_total_control_time_ms_;
    }
    if (cached_has_bits & 0x00000008u) {
      udp_total_control_single_time_ms_ = from.udp_total_control_single_time_ms_;
    }
    if (cached_has_bits & 0x00000010u) {
      udp_data_control_size_ = from.udp_data_control_size_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void UdpControlConfig::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.rs_hmi.config.UdpControlConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void UdpControlConfig::CopyFrom(const UdpControlConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.rs_hmi.config.UdpControlConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UdpControlConfig::IsInitialized() const {
  return true;
}

void UdpControlConfig::InternalSwap(UdpControlConfig* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  swap(udp_data_control_time_ms_, other->udp_data_control_time_ms_);
  swap(udp_control_type_, other->udp_control_type_);
  swap(udp_total_control_time_ms_, other->udp_total_control_time_ms_);
  swap(udp_total_control_single_time_ms_, other->udp_total_control_single_time_ms_);
  swap(udp_data_control_size_, other->udp_data_control_size_);
}

::PROTOBUF_NAMESPACE_ID::Metadata UdpControlConfig::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class UdpBufferControlConfig::_Internal {
 public:
  using HasBits = decltype(std::declval<UdpBufferControlConfig>()._has_bits_);
  static void set_has_udp_buffer_control_type(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_udp_dynamic_single_control_th(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_udp_static_single_control_th(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_udp_combine_control_th(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static void set_has_udp_buffer_control_send_frame_gap(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

UdpBufferControlConfig::UdpBufferControlConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.rs_hmi.config.UdpBufferControlConfig)
}
UdpBufferControlConfig::UdpBufferControlConfig(const UdpBufferControlConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&udp_buffer_control_send_frame_gap_, &from.udp_buffer_control_send_frame_gap_,
    static_cast<size_t>(reinterpret_cast<char*>(&udp_combine_control_th_) -
    reinterpret_cast<char*>(&udp_buffer_control_send_frame_gap_)) + sizeof(udp_combine_control_th_));
  // @@protoc_insertion_point(copy_constructor:robosense.rs_hmi.config.UdpBufferControlConfig)
}

void UdpBufferControlConfig::SharedCtor() {
  udp_buffer_control_send_frame_gap_ = 2u;
  udp_buffer_control_type_ = 2;
  udp_dynamic_single_control_th_ = 3u;
  udp_static_single_control_th_ = 3u;
  udp_combine_control_th_ = 6u;
}

UdpBufferControlConfig::~UdpBufferControlConfig() {
  // @@protoc_insertion_point(destructor:robosense.rs_hmi.config.UdpBufferControlConfig)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void UdpBufferControlConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void UdpBufferControlConfig::ArenaDtor(void* object) {
  UdpBufferControlConfig* _this = reinterpret_cast< UdpBufferControlConfig* >(object);
  (void)_this;
}
void UdpBufferControlConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UdpBufferControlConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const UdpBufferControlConfig& UdpBufferControlConfig::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_UdpBufferControlConfig_config_2eproto.base);
  return *internal_default_instance();
}


void UdpBufferControlConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.rs_hmi.config.UdpBufferControlConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000001fu) {
    udp_buffer_control_send_frame_gap_ = 2u;
    udp_buffer_control_type_ = 2;
    udp_dynamic_single_control_th_ = 3u;
    udp_static_single_control_th_ = 3u;
    udp_combine_control_th_ = 6u;
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* UdpBufferControlConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional .robosense.rs_hmi.config.RS_UDP_BUFFER_CONTROL_MODE_TYPE udp_buffer_control_type = 1 [default = RS_UDP_BUFFER_CONTROL_ENABLE];
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::robosense::rs_hmi::config::RS_UDP_BUFFER_CONTROL_MODE_TYPE_IsValid(val))) {
            _internal_set_udp_buffer_control_type(static_cast<::robosense::rs_hmi::config::RS_UDP_BUFFER_CONTROL_MODE_TYPE>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(1, val, mutable_unknown_fields());
          }
        } else goto handle_unusual;
        continue;
      // optional uint32 udp_dynamic_single_control_th = 2 [default = 3];
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          _Internal::set_has_udp_dynamic_single_control_th(&has_bits);
          udp_dynamic_single_control_th_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 udp_static_single_control_th = 3 [default = 3];
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          _Internal::set_has_udp_static_single_control_th(&has_bits);
          udp_static_single_control_th_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 udp_combine_control_th = 4 [default = 6];
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          _Internal::set_has_udp_combine_control_th(&has_bits);
          udp_combine_control_th_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 udp_buffer_control_send_frame_gap = 5 [default = 2];
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          _Internal::set_has_udp_buffer_control_send_frame_gap(&has_bits);
          udp_buffer_control_send_frame_gap_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* UdpBufferControlConfig::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.rs_hmi.config.UdpBufferControlConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional .robosense.rs_hmi.config.RS_UDP_BUFFER_CONTROL_MODE_TYPE udp_buffer_control_type = 1 [default = RS_UDP_BUFFER_CONTROL_ENABLE];
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_udp_buffer_control_type(), target);
  }

  // optional uint32 udp_dynamic_single_control_th = 2 [default = 3];
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_udp_dynamic_single_control_th(), target);
  }

  // optional uint32 udp_static_single_control_th = 3 [default = 3];
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_udp_static_single_control_th(), target);
  }

  // optional uint32 udp_combine_control_th = 4 [default = 6];
  if (cached_has_bits & 0x00000010u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_udp_combine_control_th(), target);
  }

  // optional uint32 udp_buffer_control_send_frame_gap = 5 [default = 2];
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_udp_buffer_control_send_frame_gap(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.rs_hmi.config.UdpBufferControlConfig)
  return target;
}

size_t UdpBufferControlConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.rs_hmi.config.UdpBufferControlConfig)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000001fu) {
    // optional uint32 udp_buffer_control_send_frame_gap = 5 [default = 2];
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_udp_buffer_control_send_frame_gap());
    }

    // optional .robosense.rs_hmi.config.RS_UDP_BUFFER_CONTROL_MODE_TYPE udp_buffer_control_type = 1 [default = RS_UDP_BUFFER_CONTROL_ENABLE];
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_udp_buffer_control_type());
    }

    // optional uint32 udp_dynamic_single_control_th = 2 [default = 3];
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_udp_dynamic_single_control_th());
    }

    // optional uint32 udp_static_single_control_th = 3 [default = 3];
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_udp_static_single_control_th());
    }

    // optional uint32 udp_combine_control_th = 4 [default = 6];
    if (cached_has_bits & 0x00000010u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_udp_combine_control_th());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void UdpBufferControlConfig::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.rs_hmi.config.UdpBufferControlConfig)
  GOOGLE_DCHECK_NE(&from, this);
  const UdpBufferControlConfig* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<UdpBufferControlConfig>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.rs_hmi.config.UdpBufferControlConfig)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.rs_hmi.config.UdpBufferControlConfig)
    MergeFrom(*source);
  }
}

void UdpBufferControlConfig::MergeFrom(const UdpBufferControlConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.rs_hmi.config.UdpBufferControlConfig)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000001fu) {
    if (cached_has_bits & 0x00000001u) {
      udp_buffer_control_send_frame_gap_ = from.udp_buffer_control_send_frame_gap_;
    }
    if (cached_has_bits & 0x00000002u) {
      udp_buffer_control_type_ = from.udp_buffer_control_type_;
    }
    if (cached_has_bits & 0x00000004u) {
      udp_dynamic_single_control_th_ = from.udp_dynamic_single_control_th_;
    }
    if (cached_has_bits & 0x00000008u) {
      udp_static_single_control_th_ = from.udp_static_single_control_th_;
    }
    if (cached_has_bits & 0x00000010u) {
      udp_combine_control_th_ = from.udp_combine_control_th_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void UdpBufferControlConfig::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.rs_hmi.config.UdpBufferControlConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void UdpBufferControlConfig::CopyFrom(const UdpBufferControlConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.rs_hmi.config.UdpBufferControlConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UdpBufferControlConfig::IsInitialized() const {
  return true;
}

void UdpBufferControlConfig::InternalSwap(UdpBufferControlConfig* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  swap(udp_buffer_control_send_frame_gap_, other->udp_buffer_control_send_frame_gap_);
  swap(udp_buffer_control_type_, other->udp_buffer_control_type_);
  swap(udp_dynamic_single_control_th_, other->udp_dynamic_single_control_th_);
  swap(udp_static_single_control_th_, other->udp_static_single_control_th_);
  swap(udp_combine_control_th_, other->udp_combine_control_th_);
}

::PROTOBUF_NAMESPACE_ID::Metadata UdpBufferControlConfig::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class UdpMulticastEndPointConfig::_Internal {
 public:
  using HasBits = decltype(std::declval<UdpMulticastEndPointConfig>()._has_bits_);
  static void set_has_role_type(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_multicast_ip(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_multicast_port(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static void set_has_multicast_host_ip(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_max_msg_size(HasBits* has_bits) {
    (*has_bits)[0] |= 32u;
  }
  static void set_has_buffer_cell_size(HasBits* has_bits) {
    (*has_bits)[0] |= 64u;
  }
  static void set_has_asio_run_thread_cnt(HasBits* has_bits) {
    (*has_bits)[0] |= 128u;
  }
  static void set_has_asio_snd_thread_cnt(HasBits* has_bits) {
    (*has_bits)[0] |= 256u;
  }
  static void set_has_asio_recv_thread_cnt(HasBits* has_bits) {
    (*has_bits)[0] |= 512u;
  }
  static void set_has_msg_timeout_th_ms(HasBits* has_bits) {
    (*has_bits)[0] |= 1024u;
  }
  static void set_has_asio_snd_retry_cnt(HasBits* has_bits) {
    (*has_bits)[0] |= 2048u;
  }
  static void set_has_asio_snd_retry_th_ms(HasBits* has_bits) {
    (*has_bits)[0] |= 4096u;
  }
  static const ::robosense::rs_hmi::config::UdpControlConfig& udp_control_config(const UdpMulticastEndPointConfig* msg);
  static void set_has_udp_control_config(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_compress_format(HasBits* has_bits) {
    (*has_bits)[0] |= 8192u;
  }
  static void set_has_compress_level(HasBits* has_bits) {
    (*has_bits)[0] |= 16384u;
  }
};

const ::robosense::rs_hmi::config::UdpControlConfig&
UdpMulticastEndPointConfig::_Internal::udp_control_config(const UdpMulticastEndPointConfig* msg) {
  return *msg->udp_control_config_;
}
const ::PROTOBUF_NAMESPACE_ID::internal::LazyString UdpMulticastEndPointConfig::_i_give_permission_to_break_this_code_default_multicast_ip_{{{"***********", 11}}, {nullptr}};
const ::PROTOBUF_NAMESPACE_ID::internal::LazyString UdpMulticastEndPointConfig::_i_give_permission_to_break_this_code_default_multicast_host_ip_{{{"***********", 11}}, {nullptr}};
UdpMulticastEndPointConfig::UdpMulticastEndPointConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.rs_hmi.config.UdpMulticastEndPointConfig)
}
UdpMulticastEndPointConfig::UdpMulticastEndPointConfig(const UdpMulticastEndPointConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  multicast_ip_.UnsafeSetDefault(nullptr);
  if (from._internal_has_multicast_ip()) {
    multicast_ip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::NonEmptyDefault{}, from._internal_multicast_ip(), 
      GetArena());
  }
  multicast_host_ip_.UnsafeSetDefault(nullptr);
  if (from._internal_has_multicast_host_ip()) {
    multicast_host_ip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::NonEmptyDefault{}, from._internal_multicast_host_ip(), 
      GetArena());
  }
  if (from._internal_has_udp_control_config()) {
    udp_control_config_ = new ::robosense::rs_hmi::config::UdpControlConfig(*from.udp_control_config_);
  } else {
    udp_control_config_ = nullptr;
  }
  ::memcpy(&role_type_, &from.role_type_,
    static_cast<size_t>(reinterpret_cast<char*>(&compress_level_) -
    reinterpret_cast<char*>(&role_type_)) + sizeof(compress_level_));
  // @@protoc_insertion_point(copy_constructor:robosense.rs_hmi.config.UdpMulticastEndPointConfig)
}

void UdpMulticastEndPointConfig::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_UdpMulticastEndPointConfig_config_2eproto.base);
  multicast_ip_.UnsafeSetDefault(nullptr);
  multicast_host_ip_.UnsafeSetDefault(nullptr);
  udp_control_config_ = nullptr;
  role_type_ = 1;
  multicast_port_ = 9096u;
  max_msg_size_ = 4096u;
  buffer_cell_size_ = 128u;
  asio_run_thread_cnt_ = 1u;
  asio_snd_thread_cnt_ = 1u;
  asio_recv_thread_cnt_ = 1u;
  msg_timeout_th_ms_ = 100u;
  asio_snd_retry_cnt_ = 1u;
  asio_snd_retry_th_ms_ = 5u;
  compress_format_ = 1;
  compress_level_ = 9u;
}

UdpMulticastEndPointConfig::~UdpMulticastEndPointConfig() {
  // @@protoc_insertion_point(destructor:robosense.rs_hmi.config.UdpMulticastEndPointConfig)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void UdpMulticastEndPointConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  multicast_ip_.DestroyNoArena(nullptr);
  multicast_host_ip_.DestroyNoArena(nullptr);
  if (this != internal_default_instance()) delete udp_control_config_;
}

void UdpMulticastEndPointConfig::ArenaDtor(void* object) {
  UdpMulticastEndPointConfig* _this = reinterpret_cast< UdpMulticastEndPointConfig* >(object);
  (void)_this;
}
void UdpMulticastEndPointConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UdpMulticastEndPointConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const UdpMulticastEndPointConfig& UdpMulticastEndPointConfig::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_UdpMulticastEndPointConfig_config_2eproto.base);
  return *internal_default_instance();
}


void UdpMulticastEndPointConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.rs_hmi.config.UdpMulticastEndPointConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    if (cached_has_bits & 0x00000001u) {
      multicast_ip_.ClearToDefault(::robosense::rs_hmi::config::UdpMulticastEndPointConfig::_i_give_permission_to_break_this_code_default_multicast_ip_, GetArena());
       }
    if (cached_has_bits & 0x00000002u) {
      multicast_host_ip_.ClearToDefault(::robosense::rs_hmi::config::UdpMulticastEndPointConfig::_i_give_permission_to_break_this_code_default_multicast_host_ip_, GetArena());
       }
    if (cached_has_bits & 0x00000004u) {
      GOOGLE_DCHECK(udp_control_config_ != nullptr);
      udp_control_config_->Clear();
    }
    role_type_ = 1;
    multicast_port_ = 9096u;
    max_msg_size_ = 4096u;
    buffer_cell_size_ = 128u;
    asio_run_thread_cnt_ = 1u;
  }
  if (cached_has_bits & 0x00007f00u) {
    asio_snd_thread_cnt_ = 1u;
    asio_recv_thread_cnt_ = 1u;
    msg_timeout_th_ms_ = 100u;
    asio_snd_retry_cnt_ = 1u;
    asio_snd_retry_th_ms_ = 5u;
    compress_format_ = 1;
    compress_level_ = 9u;
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* UdpMulticastEndPointConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional .robosense.rs_hmi.config.RS_SOCKET_ROLE_TYPE role_type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE_IsValid(val))) {
            _internal_set_role_type(static_cast<::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(1, val, mutable_unknown_fields());
          }
        } else goto handle_unusual;
        continue;
      // optional string multicast_ip = 2 [default = "***********"];
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_multicast_ip();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.rs_hmi.config.UdpMulticastEndPointConfig.multicast_ip");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 multicast_port = 3 [default = 9096];
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          _Internal::set_has_multicast_port(&has_bits);
          multicast_port_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string multicast_host_ip = 4 [default = "***********"];
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          auto str = _internal_mutable_multicast_host_ip();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.rs_hmi.config.UdpMulticastEndPointConfig.multicast_host_ip");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 max_msg_size = 5 [default = 4096];
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          _Internal::set_has_max_msg_size(&has_bits);
          max_msg_size_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 buffer_cell_size = 6 [default = 128];
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          _Internal::set_has_buffer_cell_size(&has_bits);
          buffer_cell_size_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 asio_run_thread_cnt = 7 [default = 1];
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          _Internal::set_has_asio_run_thread_cnt(&has_bits);
          asio_run_thread_cnt_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 asio_snd_thread_cnt = 8 [default = 1];
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64)) {
          _Internal::set_has_asio_snd_thread_cnt(&has_bits);
          asio_snd_thread_cnt_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 asio_recv_thread_cnt = 9 [default = 1];
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 72)) {
          _Internal::set_has_asio_recv_thread_cnt(&has_bits);
          asio_recv_thread_cnt_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 msg_timeout_th_ms = 10 [default = 100];
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 80)) {
          _Internal::set_has_msg_timeout_th_ms(&has_bits);
          msg_timeout_th_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 asio_snd_retry_cnt = 11 [default = 1];
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 88)) {
          _Internal::set_has_asio_snd_retry_cnt(&has_bits);
          asio_snd_retry_cnt_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 asio_snd_retry_th_ms = 12 [default = 5];
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 96)) {
          _Internal::set_has_asio_snd_retry_th_ms(&has_bits);
          asio_snd_retry_th_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .robosense.rs_hmi.config.UdpControlConfig udp_control_config = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 106)) {
          ptr = ctx->ParseMessage(_internal_mutable_udp_control_config(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .robosense.proto_compress_msgs.RS_POST_DATA_COMPRESSION_FORMAT compress_format = 14 [default = RS_POST_DATA_COMPRESSION_NOTHING];
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 112)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT_IsValid(val))) {
            _internal_set_compress_format(static_cast<::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(14, val, mutable_unknown_fields());
          }
        } else goto handle_unusual;
        continue;
      // optional uint32 compress_level = 15 [default = 9];
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 120)) {
          _Internal::set_has_compress_level(&has_bits);
          compress_level_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* UdpMulticastEndPointConfig::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.rs_hmi.config.UdpMulticastEndPointConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional .robosense.rs_hmi.config.RS_SOCKET_ROLE_TYPE role_type = 1;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_role_type(), target);
  }

  // optional string multicast_ip = 2 [default = "***********"];
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_multicast_ip().data(), static_cast<int>(this->_internal_multicast_ip().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.rs_hmi.config.UdpMulticastEndPointConfig.multicast_ip");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_multicast_ip(), target);
  }

  // optional uint32 multicast_port = 3 [default = 9096];
  if (cached_has_bits & 0x00000010u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_multicast_port(), target);
  }

  // optional string multicast_host_ip = 4 [default = "***********"];
  if (cached_has_bits & 0x00000002u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_multicast_host_ip().data(), static_cast<int>(this->_internal_multicast_host_ip().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.rs_hmi.config.UdpMulticastEndPointConfig.multicast_host_ip");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_multicast_host_ip(), target);
  }

  // optional uint32 max_msg_size = 5 [default = 4096];
  if (cached_has_bits & 0x00000020u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_max_msg_size(), target);
  }

  // optional uint32 buffer_cell_size = 6 [default = 128];
  if (cached_has_bits & 0x00000040u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_buffer_cell_size(), target);
  }

  // optional uint32 asio_run_thread_cnt = 7 [default = 1];
  if (cached_has_bits & 0x00000080u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(7, this->_internal_asio_run_thread_cnt(), target);
  }

  // optional uint32 asio_snd_thread_cnt = 8 [default = 1];
  if (cached_has_bits & 0x00000100u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(8, this->_internal_asio_snd_thread_cnt(), target);
  }

  // optional uint32 asio_recv_thread_cnt = 9 [default = 1];
  if (cached_has_bits & 0x00000200u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(9, this->_internal_asio_recv_thread_cnt(), target);
  }

  // optional uint32 msg_timeout_th_ms = 10 [default = 100];
  if (cached_has_bits & 0x00000400u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(10, this->_internal_msg_timeout_th_ms(), target);
  }

  // optional uint32 asio_snd_retry_cnt = 11 [default = 1];
  if (cached_has_bits & 0x00000800u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(11, this->_internal_asio_snd_retry_cnt(), target);
  }

  // optional uint32 asio_snd_retry_th_ms = 12 [default = 5];
  if (cached_has_bits & 0x00001000u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(12, this->_internal_asio_snd_retry_th_ms(), target);
  }

  // optional .robosense.rs_hmi.config.UdpControlConfig udp_control_config = 13;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        13, _Internal::udp_control_config(this), target, stream);
  }

  // optional .robosense.proto_compress_msgs.RS_POST_DATA_COMPRESSION_FORMAT compress_format = 14 [default = RS_POST_DATA_COMPRESSION_NOTHING];
  if (cached_has_bits & 0x00002000u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      14, this->_internal_compress_format(), target);
  }

  // optional uint32 compress_level = 15 [default = 9];
  if (cached_has_bits & 0x00004000u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(15, this->_internal_compress_level(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.rs_hmi.config.UdpMulticastEndPointConfig)
  return target;
}

size_t UdpMulticastEndPointConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.rs_hmi.config.UdpMulticastEndPointConfig)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    // optional string multicast_ip = 2 [default = "***********"];
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_multicast_ip());
    }

    // optional string multicast_host_ip = 4 [default = "***********"];
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_multicast_host_ip());
    }

    // optional .robosense.rs_hmi.config.UdpControlConfig udp_control_config = 13;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *udp_control_config_);
    }

    // optional .robosense.rs_hmi.config.RS_SOCKET_ROLE_TYPE role_type = 1;
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_role_type());
    }

    // optional uint32 multicast_port = 3 [default = 9096];
    if (cached_has_bits & 0x00000010u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_multicast_port());
    }

    // optional uint32 max_msg_size = 5 [default = 4096];
    if (cached_has_bits & 0x00000020u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_max_msg_size());
    }

    // optional uint32 buffer_cell_size = 6 [default = 128];
    if (cached_has_bits & 0x00000040u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_buffer_cell_size());
    }

    // optional uint32 asio_run_thread_cnt = 7 [default = 1];
    if (cached_has_bits & 0x00000080u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_asio_run_thread_cnt());
    }

  }
  if (cached_has_bits & 0x00007f00u) {
    // optional uint32 asio_snd_thread_cnt = 8 [default = 1];
    if (cached_has_bits & 0x00000100u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_asio_snd_thread_cnt());
    }

    // optional uint32 asio_recv_thread_cnt = 9 [default = 1];
    if (cached_has_bits & 0x00000200u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_asio_recv_thread_cnt());
    }

    // optional uint32 msg_timeout_th_ms = 10 [default = 100];
    if (cached_has_bits & 0x00000400u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_msg_timeout_th_ms());
    }

    // optional uint32 asio_snd_retry_cnt = 11 [default = 1];
    if (cached_has_bits & 0x00000800u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_asio_snd_retry_cnt());
    }

    // optional uint32 asio_snd_retry_th_ms = 12 [default = 5];
    if (cached_has_bits & 0x00001000u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_asio_snd_retry_th_ms());
    }

    // optional .robosense.proto_compress_msgs.RS_POST_DATA_COMPRESSION_FORMAT compress_format = 14 [default = RS_POST_DATA_COMPRESSION_NOTHING];
    if (cached_has_bits & 0x00002000u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_compress_format());
    }

    // optional uint32 compress_level = 15 [default = 9];
    if (cached_has_bits & 0x00004000u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_compress_level());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void UdpMulticastEndPointConfig::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.rs_hmi.config.UdpMulticastEndPointConfig)
  GOOGLE_DCHECK_NE(&from, this);
  const UdpMulticastEndPointConfig* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<UdpMulticastEndPointConfig>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.rs_hmi.config.UdpMulticastEndPointConfig)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.rs_hmi.config.UdpMulticastEndPointConfig)
    MergeFrom(*source);
  }
}

void UdpMulticastEndPointConfig::MergeFrom(const UdpMulticastEndPointConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.rs_hmi.config.UdpMulticastEndPointConfig)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_multicast_ip(from._internal_multicast_ip());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_set_multicast_host_ip(from._internal_multicast_host_ip());
    }
    if (cached_has_bits & 0x00000004u) {
      _internal_mutable_udp_control_config()->::robosense::rs_hmi::config::UdpControlConfig::MergeFrom(from._internal_udp_control_config());
    }
    if (cached_has_bits & 0x00000008u) {
      role_type_ = from.role_type_;
    }
    if (cached_has_bits & 0x00000010u) {
      multicast_port_ = from.multicast_port_;
    }
    if (cached_has_bits & 0x00000020u) {
      max_msg_size_ = from.max_msg_size_;
    }
    if (cached_has_bits & 0x00000040u) {
      buffer_cell_size_ = from.buffer_cell_size_;
    }
    if (cached_has_bits & 0x00000080u) {
      asio_run_thread_cnt_ = from.asio_run_thread_cnt_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  if (cached_has_bits & 0x00007f00u) {
    if (cached_has_bits & 0x00000100u) {
      asio_snd_thread_cnt_ = from.asio_snd_thread_cnt_;
    }
    if (cached_has_bits & 0x00000200u) {
      asio_recv_thread_cnt_ = from.asio_recv_thread_cnt_;
    }
    if (cached_has_bits & 0x00000400u) {
      msg_timeout_th_ms_ = from.msg_timeout_th_ms_;
    }
    if (cached_has_bits & 0x00000800u) {
      asio_snd_retry_cnt_ = from.asio_snd_retry_cnt_;
    }
    if (cached_has_bits & 0x00001000u) {
      asio_snd_retry_th_ms_ = from.asio_snd_retry_th_ms_;
    }
    if (cached_has_bits & 0x00002000u) {
      compress_format_ = from.compress_format_;
    }
    if (cached_has_bits & 0x00004000u) {
      compress_level_ = from.compress_level_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void UdpMulticastEndPointConfig::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.rs_hmi.config.UdpMulticastEndPointConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void UdpMulticastEndPointConfig::CopyFrom(const UdpMulticastEndPointConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.rs_hmi.config.UdpMulticastEndPointConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UdpMulticastEndPointConfig::IsInitialized() const {
  return true;
}

void UdpMulticastEndPointConfig::InternalSwap(UdpMulticastEndPointConfig* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  multicast_ip_.Swap(&other->multicast_ip_, nullptr, GetArena());
  multicast_host_ip_.Swap(&other->multicast_host_ip_, nullptr, GetArena());
  swap(udp_control_config_, other->udp_control_config_);
  swap(role_type_, other->role_type_);
  swap(multicast_port_, other->multicast_port_);
  swap(max_msg_size_, other->max_msg_size_);
  swap(buffer_cell_size_, other->buffer_cell_size_);
  swap(asio_run_thread_cnt_, other->asio_run_thread_cnt_);
  swap(asio_snd_thread_cnt_, other->asio_snd_thread_cnt_);
  swap(asio_recv_thread_cnt_, other->asio_recv_thread_cnt_);
  swap(msg_timeout_th_ms_, other->msg_timeout_th_ms_);
  swap(asio_snd_retry_cnt_, other->asio_snd_retry_cnt_);
  swap(asio_snd_retry_th_ms_, other->asio_snd_retry_th_ms_);
  swap(compress_format_, other->compress_format_);
  swap(compress_level_, other->compress_level_);
}

::PROTOBUF_NAMESPACE_ID::Metadata UdpMulticastEndPointConfig::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class UdpP2PEndPointConfig::_Internal {
 public:
  using HasBits = decltype(std::declval<UdpP2PEndPointConfig>()._has_bits_);
  static void set_has_role_type(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_remote_ip(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_remote_port(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static void set_has_max_msg_size(HasBits* has_bits) {
    (*has_bits)[0] |= 32u;
  }
  static void set_has_buffer_cell_size(HasBits* has_bits) {
    (*has_bits)[0] |= 64u;
  }
  static void set_has_asio_run_thread_cnt(HasBits* has_bits) {
    (*has_bits)[0] |= 128u;
  }
  static void set_has_asio_snd_thread_cnt(HasBits* has_bits) {
    (*has_bits)[0] |= 256u;
  }
  static void set_has_asio_recv_thread_cnt(HasBits* has_bits) {
    (*has_bits)[0] |= 512u;
  }
  static void set_has_msg_timeout_th_ms(HasBits* has_bits) {
    (*has_bits)[0] |= 1024u;
  }
  static void set_has_asio_snd_retry_cnt(HasBits* has_bits) {
    (*has_bits)[0] |= 2048u;
  }
  static void set_has_asio_snd_retry_th_ms(HasBits* has_bits) {
    (*has_bits)[0] |= 4096u;
  }
  static void set_has_socket_buffer_size(HasBits* has_bits) {
    (*has_bits)[0] |= 8192u;
  }
  static const ::robosense::rs_hmi::config::UdpControlConfig& udp_control_config(const UdpP2PEndPointConfig* msg);
  static void set_has_udp_control_config(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_compress_format(HasBits* has_bits) {
    (*has_bits)[0] |= 16384u;
  }
  static void set_has_compress_level(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
};

const ::robosense::rs_hmi::config::UdpControlConfig&
UdpP2PEndPointConfig::_Internal::udp_control_config(const UdpP2PEndPointConfig* msg) {
  return *msg->udp_control_config_;
}
UdpP2PEndPointConfig::UdpP2PEndPointConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.rs_hmi.config.UdpP2PEndPointConfig)
}
UdpP2PEndPointConfig::UdpP2PEndPointConfig(const UdpP2PEndPointConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  remote_ip_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_remote_ip()) {
    remote_ip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_remote_ip(), 
      GetArena());
  }
  if (from._internal_has_udp_control_config()) {
    udp_control_config_ = new ::robosense::rs_hmi::config::UdpControlConfig(*from.udp_control_config_);
  } else {
    udp_control_config_ = nullptr;
  }
  ::memcpy(&compress_level_, &from.compress_level_,
    static_cast<size_t>(reinterpret_cast<char*>(&compress_format_) -
    reinterpret_cast<char*>(&compress_level_)) + sizeof(compress_format_));
  // @@protoc_insertion_point(copy_constructor:robosense.rs_hmi.config.UdpP2PEndPointConfig)
}

void UdpP2PEndPointConfig::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_UdpP2PEndPointConfig_config_2eproto.base);
  remote_ip_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  udp_control_config_ = nullptr;
  compress_level_ = 9u;
  role_type_ = 1;
  remote_port_ = 9097u;
  max_msg_size_ = 4096u;
  buffer_cell_size_ = 128u;
  asio_run_thread_cnt_ = 1u;
  asio_snd_thread_cnt_ = 1u;
  asio_recv_thread_cnt_ = 1u;
  msg_timeout_th_ms_ = 100u;
  asio_snd_retry_cnt_ = 1u;
  asio_snd_retry_th_ms_ = 5u;
  socket_buffer_size_ = 262144u;
  compress_format_ = 1;
}

UdpP2PEndPointConfig::~UdpP2PEndPointConfig() {
  // @@protoc_insertion_point(destructor:robosense.rs_hmi.config.UdpP2PEndPointConfig)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void UdpP2PEndPointConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  remote_ip_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete udp_control_config_;
}

void UdpP2PEndPointConfig::ArenaDtor(void* object) {
  UdpP2PEndPointConfig* _this = reinterpret_cast< UdpP2PEndPointConfig* >(object);
  (void)_this;
}
void UdpP2PEndPointConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UdpP2PEndPointConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const UdpP2PEndPointConfig& UdpP2PEndPointConfig::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_UdpP2PEndPointConfig_config_2eproto.base);
  return *internal_default_instance();
}


void UdpP2PEndPointConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.rs_hmi.config.UdpP2PEndPointConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    if (cached_has_bits & 0x00000001u) {
      remote_ip_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      GOOGLE_DCHECK(udp_control_config_ != nullptr);
      udp_control_config_->Clear();
    }
    compress_level_ = 9u;
    role_type_ = 1;
    remote_port_ = 9097u;
    max_msg_size_ = 4096u;
    buffer_cell_size_ = 128u;
    asio_run_thread_cnt_ = 1u;
  }
  if (cached_has_bits & 0x00007f00u) {
    asio_snd_thread_cnt_ = 1u;
    asio_recv_thread_cnt_ = 1u;
    msg_timeout_th_ms_ = 100u;
    asio_snd_retry_cnt_ = 1u;
    asio_snd_retry_th_ms_ = 5u;
    socket_buffer_size_ = 262144u;
    compress_format_ = 1;
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* UdpP2PEndPointConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional .robosense.rs_hmi.config.RS_SOCKET_ROLE_TYPE role_type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE_IsValid(val))) {
            _internal_set_role_type(static_cast<::robosense::rs_hmi::config::RS_SOCKET_ROLE_TYPE>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(1, val, mutable_unknown_fields());
          }
        } else goto handle_unusual;
        continue;
      // optional string remote_ip = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_remote_ip();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.rs_hmi.config.UdpP2PEndPointConfig.remote_ip");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 remote_port = 3 [default = 9097];
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          _Internal::set_has_remote_port(&has_bits);
          remote_port_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 max_msg_size = 5 [default = 4096];
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          _Internal::set_has_max_msg_size(&has_bits);
          max_msg_size_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 buffer_cell_size = 6 [default = 128];
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          _Internal::set_has_buffer_cell_size(&has_bits);
          buffer_cell_size_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 asio_run_thread_cnt = 7 [default = 1];
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          _Internal::set_has_asio_run_thread_cnt(&has_bits);
          asio_run_thread_cnt_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 asio_snd_thread_cnt = 8 [default = 1];
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64)) {
          _Internal::set_has_asio_snd_thread_cnt(&has_bits);
          asio_snd_thread_cnt_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 asio_recv_thread_cnt = 9 [default = 1];
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 72)) {
          _Internal::set_has_asio_recv_thread_cnt(&has_bits);
          asio_recv_thread_cnt_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 msg_timeout_th_ms = 10 [default = 100];
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 80)) {
          _Internal::set_has_msg_timeout_th_ms(&has_bits);
          msg_timeout_th_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 asio_snd_retry_cnt = 11 [default = 1];
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 88)) {
          _Internal::set_has_asio_snd_retry_cnt(&has_bits);
          asio_snd_retry_cnt_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 asio_snd_retry_th_ms = 12 [default = 5];
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 96)) {
          _Internal::set_has_asio_snd_retry_th_ms(&has_bits);
          asio_snd_retry_th_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 socket_buffer_size = 13 [default = 262144];
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 104)) {
          _Internal::set_has_socket_buffer_size(&has_bits);
          socket_buffer_size_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .robosense.rs_hmi.config.UdpControlConfig udp_control_config = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 114)) {
          ptr = ctx->ParseMessage(_internal_mutable_udp_control_config(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .robosense.proto_compress_msgs.RS_POST_DATA_COMPRESSION_FORMAT compress_format = 15 [default = RS_POST_DATA_COMPRESSION_NOTHING];
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 120)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT_IsValid(val))) {
            _internal_set_compress_format(static_cast<::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(15, val, mutable_unknown_fields());
          }
        } else goto handle_unusual;
        continue;
      // optional uint32 compress_level = 16 [default = 9];
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 128)) {
          _Internal::set_has_compress_level(&has_bits);
          compress_level_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* UdpP2PEndPointConfig::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.rs_hmi.config.UdpP2PEndPointConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional .robosense.rs_hmi.config.RS_SOCKET_ROLE_TYPE role_type = 1;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_role_type(), target);
  }

  // optional string remote_ip = 2;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_remote_ip().data(), static_cast<int>(this->_internal_remote_ip().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.rs_hmi.config.UdpP2PEndPointConfig.remote_ip");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_remote_ip(), target);
  }

  // optional uint32 remote_port = 3 [default = 9097];
  if (cached_has_bits & 0x00000010u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_remote_port(), target);
  }

  // optional uint32 max_msg_size = 5 [default = 4096];
  if (cached_has_bits & 0x00000020u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_max_msg_size(), target);
  }

  // optional uint32 buffer_cell_size = 6 [default = 128];
  if (cached_has_bits & 0x00000040u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_buffer_cell_size(), target);
  }

  // optional uint32 asio_run_thread_cnt = 7 [default = 1];
  if (cached_has_bits & 0x00000080u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(7, this->_internal_asio_run_thread_cnt(), target);
  }

  // optional uint32 asio_snd_thread_cnt = 8 [default = 1];
  if (cached_has_bits & 0x00000100u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(8, this->_internal_asio_snd_thread_cnt(), target);
  }

  // optional uint32 asio_recv_thread_cnt = 9 [default = 1];
  if (cached_has_bits & 0x00000200u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(9, this->_internal_asio_recv_thread_cnt(), target);
  }

  // optional uint32 msg_timeout_th_ms = 10 [default = 100];
  if (cached_has_bits & 0x00000400u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(10, this->_internal_msg_timeout_th_ms(), target);
  }

  // optional uint32 asio_snd_retry_cnt = 11 [default = 1];
  if (cached_has_bits & 0x00000800u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(11, this->_internal_asio_snd_retry_cnt(), target);
  }

  // optional uint32 asio_snd_retry_th_ms = 12 [default = 5];
  if (cached_has_bits & 0x00001000u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(12, this->_internal_asio_snd_retry_th_ms(), target);
  }

  // optional uint32 socket_buffer_size = 13 [default = 262144];
  if (cached_has_bits & 0x00002000u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(13, this->_internal_socket_buffer_size(), target);
  }

  // optional .robosense.rs_hmi.config.UdpControlConfig udp_control_config = 14;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        14, _Internal::udp_control_config(this), target, stream);
  }

  // optional .robosense.proto_compress_msgs.RS_POST_DATA_COMPRESSION_FORMAT compress_format = 15 [default = RS_POST_DATA_COMPRESSION_NOTHING];
  if (cached_has_bits & 0x00004000u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      15, this->_internal_compress_format(), target);
  }

  // optional uint32 compress_level = 16 [default = 9];
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(16, this->_internal_compress_level(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.rs_hmi.config.UdpP2PEndPointConfig)
  return target;
}

size_t UdpP2PEndPointConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.rs_hmi.config.UdpP2PEndPointConfig)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    // optional string remote_ip = 2;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_remote_ip());
    }

    // optional .robosense.rs_hmi.config.UdpControlConfig udp_control_config = 14;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *udp_control_config_);
    }

    // optional uint32 compress_level = 16 [default = 9];
    if (cached_has_bits & 0x00000004u) {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_compress_level());
    }

    // optional .robosense.rs_hmi.config.RS_SOCKET_ROLE_TYPE role_type = 1;
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_role_type());
    }

    // optional uint32 remote_port = 3 [default = 9097];
    if (cached_has_bits & 0x00000010u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_remote_port());
    }

    // optional uint32 max_msg_size = 5 [default = 4096];
    if (cached_has_bits & 0x00000020u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_max_msg_size());
    }

    // optional uint32 buffer_cell_size = 6 [default = 128];
    if (cached_has_bits & 0x00000040u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_buffer_cell_size());
    }

    // optional uint32 asio_run_thread_cnt = 7 [default = 1];
    if (cached_has_bits & 0x00000080u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_asio_run_thread_cnt());
    }

  }
  if (cached_has_bits & 0x00007f00u) {
    // optional uint32 asio_snd_thread_cnt = 8 [default = 1];
    if (cached_has_bits & 0x00000100u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_asio_snd_thread_cnt());
    }

    // optional uint32 asio_recv_thread_cnt = 9 [default = 1];
    if (cached_has_bits & 0x00000200u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_asio_recv_thread_cnt());
    }

    // optional uint32 msg_timeout_th_ms = 10 [default = 100];
    if (cached_has_bits & 0x00000400u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_msg_timeout_th_ms());
    }

    // optional uint32 asio_snd_retry_cnt = 11 [default = 1];
    if (cached_has_bits & 0x00000800u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_asio_snd_retry_cnt());
    }

    // optional uint32 asio_snd_retry_th_ms = 12 [default = 5];
    if (cached_has_bits & 0x00001000u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_asio_snd_retry_th_ms());
    }

    // optional uint32 socket_buffer_size = 13 [default = 262144];
    if (cached_has_bits & 0x00002000u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_socket_buffer_size());
    }

    // optional .robosense.proto_compress_msgs.RS_POST_DATA_COMPRESSION_FORMAT compress_format = 15 [default = RS_POST_DATA_COMPRESSION_NOTHING];
    if (cached_has_bits & 0x00004000u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_compress_format());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void UdpP2PEndPointConfig::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.rs_hmi.config.UdpP2PEndPointConfig)
  GOOGLE_DCHECK_NE(&from, this);
  const UdpP2PEndPointConfig* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<UdpP2PEndPointConfig>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.rs_hmi.config.UdpP2PEndPointConfig)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.rs_hmi.config.UdpP2PEndPointConfig)
    MergeFrom(*source);
  }
}

void UdpP2PEndPointConfig::MergeFrom(const UdpP2PEndPointConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.rs_hmi.config.UdpP2PEndPointConfig)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_remote_ip(from._internal_remote_ip());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_mutable_udp_control_config()->::robosense::rs_hmi::config::UdpControlConfig::MergeFrom(from._internal_udp_control_config());
    }
    if (cached_has_bits & 0x00000004u) {
      compress_level_ = from.compress_level_;
    }
    if (cached_has_bits & 0x00000008u) {
      role_type_ = from.role_type_;
    }
    if (cached_has_bits & 0x00000010u) {
      remote_port_ = from.remote_port_;
    }
    if (cached_has_bits & 0x00000020u) {
      max_msg_size_ = from.max_msg_size_;
    }
    if (cached_has_bits & 0x00000040u) {
      buffer_cell_size_ = from.buffer_cell_size_;
    }
    if (cached_has_bits & 0x00000080u) {
      asio_run_thread_cnt_ = from.asio_run_thread_cnt_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  if (cached_has_bits & 0x00007f00u) {
    if (cached_has_bits & 0x00000100u) {
      asio_snd_thread_cnt_ = from.asio_snd_thread_cnt_;
    }
    if (cached_has_bits & 0x00000200u) {
      asio_recv_thread_cnt_ = from.asio_recv_thread_cnt_;
    }
    if (cached_has_bits & 0x00000400u) {
      msg_timeout_th_ms_ = from.msg_timeout_th_ms_;
    }
    if (cached_has_bits & 0x00000800u) {
      asio_snd_retry_cnt_ = from.asio_snd_retry_cnt_;
    }
    if (cached_has_bits & 0x00001000u) {
      asio_snd_retry_th_ms_ = from.asio_snd_retry_th_ms_;
    }
    if (cached_has_bits & 0x00002000u) {
      socket_buffer_size_ = from.socket_buffer_size_;
    }
    if (cached_has_bits & 0x00004000u) {
      compress_format_ = from.compress_format_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void UdpP2PEndPointConfig::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.rs_hmi.config.UdpP2PEndPointConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void UdpP2PEndPointConfig::CopyFrom(const UdpP2PEndPointConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.rs_hmi.config.UdpP2PEndPointConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UdpP2PEndPointConfig::IsInitialized() const {
  return true;
}

void UdpP2PEndPointConfig::InternalSwap(UdpP2PEndPointConfig* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  remote_ip_.Swap(&other->remote_ip_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  swap(udp_control_config_, other->udp_control_config_);
  swap(compress_level_, other->compress_level_);
  swap(role_type_, other->role_type_);
  swap(remote_port_, other->remote_port_);
  swap(max_msg_size_, other->max_msg_size_);
  swap(buffer_cell_size_, other->buffer_cell_size_);
  swap(asio_run_thread_cnt_, other->asio_run_thread_cnt_);
  swap(asio_snd_thread_cnt_, other->asio_snd_thread_cnt_);
  swap(asio_recv_thread_cnt_, other->asio_recv_thread_cnt_);
  swap(msg_timeout_th_ms_, other->msg_timeout_th_ms_);
  swap(asio_snd_retry_cnt_, other->asio_snd_retry_cnt_);
  swap(asio_snd_retry_th_ms_, other->asio_snd_retry_th_ms_);
  swap(socket_buffer_size_, other->socket_buffer_size_);
  swap(compress_format_, other->compress_format_);
}

::PROTOBUF_NAMESPACE_ID::Metadata UdpP2PEndPointConfig::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class WebsocketSendControlConfig::_Internal {
 public:
  using HasBits = decltype(std::declval<WebsocketSendControlConfig>()._has_bits_);
  static void set_has_message_type(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_enable_loss(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_loss_buffer_size(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_loss_gap_size(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_enable_loss2(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static void set_has_loss_buffer_size2(HasBits* has_bits) {
    (*has_bits)[0] |= 64u;
  }
  static void set_has_loss_gap_size2(HasBits* has_bits) {
    (*has_bits)[0] |= 128u;
  }
  static void set_has_enable_clear(HasBits* has_bits) {
    (*has_bits)[0] |= 32u;
  }
  static void set_has_clear_buffer_size(HasBits* has_bits) {
    (*has_bits)[0] |= 256u;
  }
};

WebsocketSendControlConfig::WebsocketSendControlConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.rs_hmi.config.WebsocketSendControlConfig)
}
WebsocketSendControlConfig::WebsocketSendControlConfig(const WebsocketSendControlConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  message_type_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_message_type()) {
    message_type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_message_type(), 
      GetArena());
  }
  ::memcpy(&loss_buffer_size_, &from.loss_buffer_size_,
    static_cast<size_t>(reinterpret_cast<char*>(&clear_buffer_size_) -
    reinterpret_cast<char*>(&loss_buffer_size_)) + sizeof(clear_buffer_size_));
  // @@protoc_insertion_point(copy_constructor:robosense.rs_hmi.config.WebsocketSendControlConfig)
}

void WebsocketSendControlConfig::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_WebsocketSendControlConfig_config_2eproto.base);
  message_type_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  loss_buffer_size_ = 10u;
  loss_gap_size_ = 2u;
  enable_loss_ = true;
  enable_loss2_ = true;
  enable_clear_ = true;
  loss_buffer_size2_ = 15u;
  loss_gap_size2_ = 3u;
  clear_buffer_size_ = 20u;
}

WebsocketSendControlConfig::~WebsocketSendControlConfig() {
  // @@protoc_insertion_point(destructor:robosense.rs_hmi.config.WebsocketSendControlConfig)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void WebsocketSendControlConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  message_type_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void WebsocketSendControlConfig::ArenaDtor(void* object) {
  WebsocketSendControlConfig* _this = reinterpret_cast< WebsocketSendControlConfig* >(object);
  (void)_this;
}
void WebsocketSendControlConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void WebsocketSendControlConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const WebsocketSendControlConfig& WebsocketSendControlConfig::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_WebsocketSendControlConfig_config_2eproto.base);
  return *internal_default_instance();
}


void WebsocketSendControlConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.rs_hmi.config.WebsocketSendControlConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    if (cached_has_bits & 0x00000001u) {
      message_type_.ClearNonDefaultToEmpty();
    }
    loss_buffer_size_ = 10u;
    loss_gap_size_ = 2u;
    enable_loss_ = true;
    enable_loss2_ = true;
    enable_clear_ = true;
    loss_buffer_size2_ = 15u;
    loss_gap_size2_ = 3u;
  }
  clear_buffer_size_ = 20u;
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* WebsocketSendControlConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional string message_type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_message_type();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.rs_hmi.config.WebsocketSendControlConfig.message_type");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional bool enable_loss = 2 [default = true];
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          _Internal::set_has_enable_loss(&has_bits);
          enable_loss_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 loss_buffer_size = 3 [default = 10];
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          _Internal::set_has_loss_buffer_size(&has_bits);
          loss_buffer_size_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 loss_gap_size = 4 [default = 2];
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          _Internal::set_has_loss_gap_size(&has_bits);
          loss_gap_size_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional bool enable_loss2 = 5 [default = true];
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          _Internal::set_has_enable_loss2(&has_bits);
          enable_loss2_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 loss_buffer_size2 = 6 [default = 15];
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          _Internal::set_has_loss_buffer_size2(&has_bits);
          loss_buffer_size2_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 loss_gap_size2 = 7 [default = 3];
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          _Internal::set_has_loss_gap_size2(&has_bits);
          loss_gap_size2_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional bool enable_clear = 8 [default = true];
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64)) {
          _Internal::set_has_enable_clear(&has_bits);
          enable_clear_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 clear_buffer_size = 9 [default = 20];
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 72)) {
          _Internal::set_has_clear_buffer_size(&has_bits);
          clear_buffer_size_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* WebsocketSendControlConfig::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.rs_hmi.config.WebsocketSendControlConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional string message_type = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_message_type().data(), static_cast<int>(this->_internal_message_type().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.rs_hmi.config.WebsocketSendControlConfig.message_type");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_message_type(), target);
  }

  // optional bool enable_loss = 2 [default = true];
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_enable_loss(), target);
  }

  // optional uint32 loss_buffer_size = 3 [default = 10];
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_loss_buffer_size(), target);
  }

  // optional uint32 loss_gap_size = 4 [default = 2];
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_loss_gap_size(), target);
  }

  // optional bool enable_loss2 = 5 [default = true];
  if (cached_has_bits & 0x00000010u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(5, this->_internal_enable_loss2(), target);
  }

  // optional uint32 loss_buffer_size2 = 6 [default = 15];
  if (cached_has_bits & 0x00000040u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_loss_buffer_size2(), target);
  }

  // optional uint32 loss_gap_size2 = 7 [default = 3];
  if (cached_has_bits & 0x00000080u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(7, this->_internal_loss_gap_size2(), target);
  }

  // optional bool enable_clear = 8 [default = true];
  if (cached_has_bits & 0x00000020u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(8, this->_internal_enable_clear(), target);
  }

  // optional uint32 clear_buffer_size = 9 [default = 20];
  if (cached_has_bits & 0x00000100u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(9, this->_internal_clear_buffer_size(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.rs_hmi.config.WebsocketSendControlConfig)
  return target;
}

size_t WebsocketSendControlConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.rs_hmi.config.WebsocketSendControlConfig)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    // optional string message_type = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_message_type());
    }

    // optional uint32 loss_buffer_size = 3 [default = 10];
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_loss_buffer_size());
    }

    // optional uint32 loss_gap_size = 4 [default = 2];
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_loss_gap_size());
    }

    // optional bool enable_loss = 2 [default = true];
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 + 1;
    }

    // optional bool enable_loss2 = 5 [default = true];
    if (cached_has_bits & 0x00000010u) {
      total_size += 1 + 1;
    }

    // optional bool enable_clear = 8 [default = true];
    if (cached_has_bits & 0x00000020u) {
      total_size += 1 + 1;
    }

    // optional uint32 loss_buffer_size2 = 6 [default = 15];
    if (cached_has_bits & 0x00000040u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_loss_buffer_size2());
    }

    // optional uint32 loss_gap_size2 = 7 [default = 3];
    if (cached_has_bits & 0x00000080u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_loss_gap_size2());
    }

  }
  // optional uint32 clear_buffer_size = 9 [default = 20];
  if (cached_has_bits & 0x00000100u) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_clear_buffer_size());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void WebsocketSendControlConfig::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.rs_hmi.config.WebsocketSendControlConfig)
  GOOGLE_DCHECK_NE(&from, this);
  const WebsocketSendControlConfig* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<WebsocketSendControlConfig>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.rs_hmi.config.WebsocketSendControlConfig)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.rs_hmi.config.WebsocketSendControlConfig)
    MergeFrom(*source);
  }
}

void WebsocketSendControlConfig::MergeFrom(const WebsocketSendControlConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.rs_hmi.config.WebsocketSendControlConfig)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_message_type(from._internal_message_type());
    }
    if (cached_has_bits & 0x00000002u) {
      loss_buffer_size_ = from.loss_buffer_size_;
    }
    if (cached_has_bits & 0x00000004u) {
      loss_gap_size_ = from.loss_gap_size_;
    }
    if (cached_has_bits & 0x00000008u) {
      enable_loss_ = from.enable_loss_;
    }
    if (cached_has_bits & 0x00000010u) {
      enable_loss2_ = from.enable_loss2_;
    }
    if (cached_has_bits & 0x00000020u) {
      enable_clear_ = from.enable_clear_;
    }
    if (cached_has_bits & 0x00000040u) {
      loss_buffer_size2_ = from.loss_buffer_size2_;
    }
    if (cached_has_bits & 0x00000080u) {
      loss_gap_size2_ = from.loss_gap_size2_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  if (cached_has_bits & 0x00000100u) {
    _internal_set_clear_buffer_size(from._internal_clear_buffer_size());
  }
}

void WebsocketSendControlConfig::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.rs_hmi.config.WebsocketSendControlConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void WebsocketSendControlConfig::CopyFrom(const WebsocketSendControlConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.rs_hmi.config.WebsocketSendControlConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool WebsocketSendControlConfig::IsInitialized() const {
  return true;
}

void WebsocketSendControlConfig::InternalSwap(WebsocketSendControlConfig* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  message_type_.Swap(&other->message_type_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  swap(loss_buffer_size_, other->loss_buffer_size_);
  swap(loss_gap_size_, other->loss_gap_size_);
  swap(enable_loss_, other->enable_loss_);
  swap(enable_loss2_, other->enable_loss2_);
  swap(enable_clear_, other->enable_clear_);
  swap(loss_buffer_size2_, other->loss_buffer_size2_);
  swap(loss_gap_size2_, other->loss_gap_size2_);
  swap(clear_buffer_size_, other->clear_buffer_size_);
}

::PROTOBUF_NAMESPACE_ID::Metadata WebsocketSendControlConfig::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class WebsocketTaskConfig::_Internal {
 public:
  using HasBits = decltype(std::declval<WebsocketTaskConfig>()._has_bits_);
  static void set_has_task_socket_key(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static const ::robosense::rs_hmi::config::WebsocketEndPointConfig& websocket_endpoint(const WebsocketTaskConfig* msg);
  static void set_has_websocket_endpoint(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

const ::robosense::rs_hmi::config::WebsocketEndPointConfig&
WebsocketTaskConfig::_Internal::websocket_endpoint(const WebsocketTaskConfig* msg) {
  return *msg->websocket_endpoint_;
}
WebsocketTaskConfig::WebsocketTaskConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  websocket_send_controls_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.rs_hmi.config.WebsocketTaskConfig)
}
WebsocketTaskConfig::WebsocketTaskConfig(const WebsocketTaskConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_),
      websocket_send_controls_(from.websocket_send_controls_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  task_socket_key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_task_socket_key()) {
    task_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_task_socket_key(), 
      GetArena());
  }
  if (from._internal_has_websocket_endpoint()) {
    websocket_endpoint_ = new ::robosense::rs_hmi::config::WebsocketEndPointConfig(*from.websocket_endpoint_);
  } else {
    websocket_endpoint_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:robosense.rs_hmi.config.WebsocketTaskConfig)
}

void WebsocketTaskConfig::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_WebsocketTaskConfig_config_2eproto.base);
  task_socket_key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  websocket_endpoint_ = nullptr;
}

WebsocketTaskConfig::~WebsocketTaskConfig() {
  // @@protoc_insertion_point(destructor:robosense.rs_hmi.config.WebsocketTaskConfig)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void WebsocketTaskConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  task_socket_key_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete websocket_endpoint_;
}

void WebsocketTaskConfig::ArenaDtor(void* object) {
  WebsocketTaskConfig* _this = reinterpret_cast< WebsocketTaskConfig* >(object);
  (void)_this;
}
void WebsocketTaskConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void WebsocketTaskConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const WebsocketTaskConfig& WebsocketTaskConfig::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_WebsocketTaskConfig_config_2eproto.base);
  return *internal_default_instance();
}


void WebsocketTaskConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.rs_hmi.config.WebsocketTaskConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  websocket_send_controls_.Clear();
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      task_socket_key_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      GOOGLE_DCHECK(websocket_endpoint_ != nullptr);
      websocket_endpoint_->Clear();
    }
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* WebsocketTaskConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional string task_socket_key = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_task_socket_key();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.rs_hmi.config.WebsocketTaskConfig.task_socket_key");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .robosense.rs_hmi.config.WebsocketEndPointConfig websocket_endpoint = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_websocket_endpoint(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .robosense.rs_hmi.config.WebsocketSendControlConfig websocket_send_controls = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_websocket_send_controls(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* WebsocketTaskConfig::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.rs_hmi.config.WebsocketTaskConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional string task_socket_key = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_task_socket_key().data(), static_cast<int>(this->_internal_task_socket_key().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.rs_hmi.config.WebsocketTaskConfig.task_socket_key");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_task_socket_key(), target);
  }

  // optional .robosense.rs_hmi.config.WebsocketEndPointConfig websocket_endpoint = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::websocket_endpoint(this), target, stream);
  }

  // repeated .robosense.rs_hmi.config.WebsocketSendControlConfig websocket_send_controls = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_websocket_send_controls_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, this->_internal_websocket_send_controls(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.rs_hmi.config.WebsocketTaskConfig)
  return target;
}

size_t WebsocketTaskConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.rs_hmi.config.WebsocketTaskConfig)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .robosense.rs_hmi.config.WebsocketSendControlConfig websocket_send_controls = 3;
  total_size += 1UL * this->_internal_websocket_send_controls_size();
  for (const auto& msg : this->websocket_send_controls_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional string task_socket_key = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_task_socket_key());
    }

    // optional .robosense.rs_hmi.config.WebsocketEndPointConfig websocket_endpoint = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *websocket_endpoint_);
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void WebsocketTaskConfig::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.rs_hmi.config.WebsocketTaskConfig)
  GOOGLE_DCHECK_NE(&from, this);
  const WebsocketTaskConfig* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<WebsocketTaskConfig>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.rs_hmi.config.WebsocketTaskConfig)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.rs_hmi.config.WebsocketTaskConfig)
    MergeFrom(*source);
  }
}

void WebsocketTaskConfig::MergeFrom(const WebsocketTaskConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.rs_hmi.config.WebsocketTaskConfig)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  websocket_send_controls_.MergeFrom(from.websocket_send_controls_);
  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_task_socket_key(from._internal_task_socket_key());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_mutable_websocket_endpoint()->::robosense::rs_hmi::config::WebsocketEndPointConfig::MergeFrom(from._internal_websocket_endpoint());
    }
  }
}

void WebsocketTaskConfig::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.rs_hmi.config.WebsocketTaskConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void WebsocketTaskConfig::CopyFrom(const WebsocketTaskConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.rs_hmi.config.WebsocketTaskConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool WebsocketTaskConfig::IsInitialized() const {
  return true;
}

void WebsocketTaskConfig::InternalSwap(WebsocketTaskConfig* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  websocket_send_controls_.InternalSwap(&other->websocket_send_controls_);
  task_socket_key_.Swap(&other->task_socket_key_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  swap(websocket_endpoint_, other->websocket_endpoint_);
}

::PROTOBUF_NAMESPACE_ID::Metadata WebsocketTaskConfig::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class UdpMulticastTaskConfig::_Internal {
 public:
  using HasBits = decltype(std::declval<UdpMulticastTaskConfig>()._has_bits_);
  static void set_has_task_socket_key(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static const ::robosense::rs_hmi::config::UdpMulticastEndPointConfig& udp_multicast_endpoint(const UdpMulticastTaskConfig* msg);
  static void set_has_udp_multicast_endpoint(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

const ::robosense::rs_hmi::config::UdpMulticastEndPointConfig&
UdpMulticastTaskConfig::_Internal::udp_multicast_endpoint(const UdpMulticastTaskConfig* msg) {
  return *msg->udp_multicast_endpoint_;
}
UdpMulticastTaskConfig::UdpMulticastTaskConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.rs_hmi.config.UdpMulticastTaskConfig)
}
UdpMulticastTaskConfig::UdpMulticastTaskConfig(const UdpMulticastTaskConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  task_socket_key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_task_socket_key()) {
    task_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_task_socket_key(), 
      GetArena());
  }
  if (from._internal_has_udp_multicast_endpoint()) {
    udp_multicast_endpoint_ = new ::robosense::rs_hmi::config::UdpMulticastEndPointConfig(*from.udp_multicast_endpoint_);
  } else {
    udp_multicast_endpoint_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:robosense.rs_hmi.config.UdpMulticastTaskConfig)
}

void UdpMulticastTaskConfig::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_UdpMulticastTaskConfig_config_2eproto.base);
  task_socket_key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  udp_multicast_endpoint_ = nullptr;
}

UdpMulticastTaskConfig::~UdpMulticastTaskConfig() {
  // @@protoc_insertion_point(destructor:robosense.rs_hmi.config.UdpMulticastTaskConfig)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void UdpMulticastTaskConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  task_socket_key_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete udp_multicast_endpoint_;
}

void UdpMulticastTaskConfig::ArenaDtor(void* object) {
  UdpMulticastTaskConfig* _this = reinterpret_cast< UdpMulticastTaskConfig* >(object);
  (void)_this;
}
void UdpMulticastTaskConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UdpMulticastTaskConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const UdpMulticastTaskConfig& UdpMulticastTaskConfig::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_UdpMulticastTaskConfig_config_2eproto.base);
  return *internal_default_instance();
}


void UdpMulticastTaskConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.rs_hmi.config.UdpMulticastTaskConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      task_socket_key_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      GOOGLE_DCHECK(udp_multicast_endpoint_ != nullptr);
      udp_multicast_endpoint_->Clear();
    }
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* UdpMulticastTaskConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional string task_socket_key = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_task_socket_key();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.rs_hmi.config.UdpMulticastTaskConfig.task_socket_key");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .robosense.rs_hmi.config.UdpMulticastEndPointConfig udp_multicast_endpoint = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_udp_multicast_endpoint(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* UdpMulticastTaskConfig::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.rs_hmi.config.UdpMulticastTaskConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional string task_socket_key = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_task_socket_key().data(), static_cast<int>(this->_internal_task_socket_key().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.rs_hmi.config.UdpMulticastTaskConfig.task_socket_key");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_task_socket_key(), target);
  }

  // optional .robosense.rs_hmi.config.UdpMulticastEndPointConfig udp_multicast_endpoint = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::udp_multicast_endpoint(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.rs_hmi.config.UdpMulticastTaskConfig)
  return target;
}

size_t UdpMulticastTaskConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.rs_hmi.config.UdpMulticastTaskConfig)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional string task_socket_key = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_task_socket_key());
    }

    // optional .robosense.rs_hmi.config.UdpMulticastEndPointConfig udp_multicast_endpoint = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *udp_multicast_endpoint_);
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void UdpMulticastTaskConfig::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.rs_hmi.config.UdpMulticastTaskConfig)
  GOOGLE_DCHECK_NE(&from, this);
  const UdpMulticastTaskConfig* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<UdpMulticastTaskConfig>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.rs_hmi.config.UdpMulticastTaskConfig)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.rs_hmi.config.UdpMulticastTaskConfig)
    MergeFrom(*source);
  }
}

void UdpMulticastTaskConfig::MergeFrom(const UdpMulticastTaskConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.rs_hmi.config.UdpMulticastTaskConfig)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_task_socket_key(from._internal_task_socket_key());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_mutable_udp_multicast_endpoint()->::robosense::rs_hmi::config::UdpMulticastEndPointConfig::MergeFrom(from._internal_udp_multicast_endpoint());
    }
  }
}

void UdpMulticastTaskConfig::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.rs_hmi.config.UdpMulticastTaskConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void UdpMulticastTaskConfig::CopyFrom(const UdpMulticastTaskConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.rs_hmi.config.UdpMulticastTaskConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UdpMulticastTaskConfig::IsInitialized() const {
  return true;
}

void UdpMulticastTaskConfig::InternalSwap(UdpMulticastTaskConfig* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  task_socket_key_.Swap(&other->task_socket_key_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  swap(udp_multicast_endpoint_, other->udp_multicast_endpoint_);
}

::PROTOBUF_NAMESPACE_ID::Metadata UdpMulticastTaskConfig::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class UdpP2PTaskConfig::_Internal {
 public:
  using HasBits = decltype(std::declval<UdpP2PTaskConfig>()._has_bits_);
  static void set_has_task_socket_key(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static const ::robosense::rs_hmi::config::UdpP2PEndPointConfig& udp_p2p_endpoint(const UdpP2PTaskConfig* msg);
  static void set_has_udp_p2p_endpoint(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

const ::robosense::rs_hmi::config::UdpP2PEndPointConfig&
UdpP2PTaskConfig::_Internal::udp_p2p_endpoint(const UdpP2PTaskConfig* msg) {
  return *msg->udp_p2p_endpoint_;
}
UdpP2PTaskConfig::UdpP2PTaskConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.rs_hmi.config.UdpP2PTaskConfig)
}
UdpP2PTaskConfig::UdpP2PTaskConfig(const UdpP2PTaskConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  task_socket_key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_task_socket_key()) {
    task_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_task_socket_key(), 
      GetArena());
  }
  if (from._internal_has_udp_p2p_endpoint()) {
    udp_p2p_endpoint_ = new ::robosense::rs_hmi::config::UdpP2PEndPointConfig(*from.udp_p2p_endpoint_);
  } else {
    udp_p2p_endpoint_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:robosense.rs_hmi.config.UdpP2PTaskConfig)
}

void UdpP2PTaskConfig::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_UdpP2PTaskConfig_config_2eproto.base);
  task_socket_key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  udp_p2p_endpoint_ = nullptr;
}

UdpP2PTaskConfig::~UdpP2PTaskConfig() {
  // @@protoc_insertion_point(destructor:robosense.rs_hmi.config.UdpP2PTaskConfig)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void UdpP2PTaskConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  task_socket_key_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete udp_p2p_endpoint_;
}

void UdpP2PTaskConfig::ArenaDtor(void* object) {
  UdpP2PTaskConfig* _this = reinterpret_cast< UdpP2PTaskConfig* >(object);
  (void)_this;
}
void UdpP2PTaskConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UdpP2PTaskConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const UdpP2PTaskConfig& UdpP2PTaskConfig::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_UdpP2PTaskConfig_config_2eproto.base);
  return *internal_default_instance();
}


void UdpP2PTaskConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.rs_hmi.config.UdpP2PTaskConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      task_socket_key_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      GOOGLE_DCHECK(udp_p2p_endpoint_ != nullptr);
      udp_p2p_endpoint_->Clear();
    }
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* UdpP2PTaskConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional string task_socket_key = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_task_socket_key();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.rs_hmi.config.UdpP2PTaskConfig.task_socket_key");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .robosense.rs_hmi.config.UdpP2PEndPointConfig udp_p2p_endpoint = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_udp_p2p_endpoint(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* UdpP2PTaskConfig::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.rs_hmi.config.UdpP2PTaskConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional string task_socket_key = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_task_socket_key().data(), static_cast<int>(this->_internal_task_socket_key().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.rs_hmi.config.UdpP2PTaskConfig.task_socket_key");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_task_socket_key(), target);
  }

  // optional .robosense.rs_hmi.config.UdpP2PEndPointConfig udp_p2p_endpoint = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::udp_p2p_endpoint(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.rs_hmi.config.UdpP2PTaskConfig)
  return target;
}

size_t UdpP2PTaskConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.rs_hmi.config.UdpP2PTaskConfig)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional string task_socket_key = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_task_socket_key());
    }

    // optional .robosense.rs_hmi.config.UdpP2PEndPointConfig udp_p2p_endpoint = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *udp_p2p_endpoint_);
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void UdpP2PTaskConfig::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.rs_hmi.config.UdpP2PTaskConfig)
  GOOGLE_DCHECK_NE(&from, this);
  const UdpP2PTaskConfig* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<UdpP2PTaskConfig>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.rs_hmi.config.UdpP2PTaskConfig)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.rs_hmi.config.UdpP2PTaskConfig)
    MergeFrom(*source);
  }
}

void UdpP2PTaskConfig::MergeFrom(const UdpP2PTaskConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.rs_hmi.config.UdpP2PTaskConfig)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_task_socket_key(from._internal_task_socket_key());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_mutable_udp_p2p_endpoint()->::robosense::rs_hmi::config::UdpP2PEndPointConfig::MergeFrom(from._internal_udp_p2p_endpoint());
    }
  }
}

void UdpP2PTaskConfig::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.rs_hmi.config.UdpP2PTaskConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void UdpP2PTaskConfig::CopyFrom(const UdpP2PTaskConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.rs_hmi.config.UdpP2PTaskConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UdpP2PTaskConfig::IsInitialized() const {
  return true;
}

void UdpP2PTaskConfig::InternalSwap(UdpP2PTaskConfig* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  task_socket_key_.Swap(&other->task_socket_key_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  swap(udp_p2p_endpoint_, other->udp_p2p_endpoint_);
}

::PROTOBUF_NAMESPACE_ID::Metadata UdpP2PTaskConfig::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class SocketTaskConfig::_Internal {
 public:
  static const ::robosense::rs_hmi::config::WebsocketTaskConfig& websocket_task(const SocketTaskConfig* msg);
  static const ::robosense::rs_hmi::config::UdpMulticastTaskConfig& udp_multicast_task(const SocketTaskConfig* msg);
  static const ::robosense::rs_hmi::config::UdpP2PTaskConfig& udp_p2p_task(const SocketTaskConfig* msg);
};

const ::robosense::rs_hmi::config::WebsocketTaskConfig&
SocketTaskConfig::_Internal::websocket_task(const SocketTaskConfig* msg) {
  return *msg->socket_endpoint_config_.websocket_task_;
}
const ::robosense::rs_hmi::config::UdpMulticastTaskConfig&
SocketTaskConfig::_Internal::udp_multicast_task(const SocketTaskConfig* msg) {
  return *msg->socket_endpoint_config_.udp_multicast_task_;
}
const ::robosense::rs_hmi::config::UdpP2PTaskConfig&
SocketTaskConfig::_Internal::udp_p2p_task(const SocketTaskConfig* msg) {
  return *msg->socket_endpoint_config_.udp_p2p_task_;
}
void SocketTaskConfig::set_allocated_websocket_task(::robosense::rs_hmi::config::WebsocketTaskConfig* websocket_task) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  clear_socket_endpoint_config();
  if (websocket_task) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(websocket_task);
    if (message_arena != submessage_arena) {
      websocket_task = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, websocket_task, submessage_arena);
    }
    set_has_websocket_task();
    socket_endpoint_config_.websocket_task_ = websocket_task;
  }
  // @@protoc_insertion_point(field_set_allocated:robosense.rs_hmi.config.SocketTaskConfig.websocket_task)
}
void SocketTaskConfig::set_allocated_udp_multicast_task(::robosense::rs_hmi::config::UdpMulticastTaskConfig* udp_multicast_task) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  clear_socket_endpoint_config();
  if (udp_multicast_task) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(udp_multicast_task);
    if (message_arena != submessage_arena) {
      udp_multicast_task = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, udp_multicast_task, submessage_arena);
    }
    set_has_udp_multicast_task();
    socket_endpoint_config_.udp_multicast_task_ = udp_multicast_task;
  }
  // @@protoc_insertion_point(field_set_allocated:robosense.rs_hmi.config.SocketTaskConfig.udp_multicast_task)
}
void SocketTaskConfig::set_allocated_udp_p2p_task(::robosense::rs_hmi::config::UdpP2PTaskConfig* udp_p2p_task) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  clear_socket_endpoint_config();
  if (udp_p2p_task) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(udp_p2p_task);
    if (message_arena != submessage_arena) {
      udp_p2p_task = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, udp_p2p_task, submessage_arena);
    }
    set_has_udp_p2p_task();
    socket_endpoint_config_.udp_p2p_task_ = udp_p2p_task;
  }
  // @@protoc_insertion_point(field_set_allocated:robosense.rs_hmi.config.SocketTaskConfig.udp_p2p_task)
}
SocketTaskConfig::SocketTaskConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.rs_hmi.config.SocketTaskConfig)
}
SocketTaskConfig::SocketTaskConfig(const SocketTaskConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  clear_has_socket_endpoint_config();
  switch (from.socket_endpoint_config_case()) {
    case kWebsocketTask: {
      _internal_mutable_websocket_task()->::robosense::rs_hmi::config::WebsocketTaskConfig::MergeFrom(from._internal_websocket_task());
      break;
    }
    case kUdpMulticastTask: {
      _internal_mutable_udp_multicast_task()->::robosense::rs_hmi::config::UdpMulticastTaskConfig::MergeFrom(from._internal_udp_multicast_task());
      break;
    }
    case kUdpP2PTask: {
      _internal_mutable_udp_p2p_task()->::robosense::rs_hmi::config::UdpP2PTaskConfig::MergeFrom(from._internal_udp_p2p_task());
      break;
    }
    case SOCKET_ENDPOINT_CONFIG_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:robosense.rs_hmi.config.SocketTaskConfig)
}

void SocketTaskConfig::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_SocketTaskConfig_config_2eproto.base);
  clear_has_socket_endpoint_config();
}

SocketTaskConfig::~SocketTaskConfig() {
  // @@protoc_insertion_point(destructor:robosense.rs_hmi.config.SocketTaskConfig)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void SocketTaskConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  if (has_socket_endpoint_config()) {
    clear_socket_endpoint_config();
  }
}

void SocketTaskConfig::ArenaDtor(void* object) {
  SocketTaskConfig* _this = reinterpret_cast< SocketTaskConfig* >(object);
  (void)_this;
}
void SocketTaskConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SocketTaskConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const SocketTaskConfig& SocketTaskConfig::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_SocketTaskConfig_config_2eproto.base);
  return *internal_default_instance();
}


void SocketTaskConfig::clear_socket_endpoint_config() {
// @@protoc_insertion_point(one_of_clear_start:robosense.rs_hmi.config.SocketTaskConfig)
  switch (socket_endpoint_config_case()) {
    case kWebsocketTask: {
      if (GetArena() == nullptr) {
        delete socket_endpoint_config_.websocket_task_;
      }
      break;
    }
    case kUdpMulticastTask: {
      if (GetArena() == nullptr) {
        delete socket_endpoint_config_.udp_multicast_task_;
      }
      break;
    }
    case kUdpP2PTask: {
      if (GetArena() == nullptr) {
        delete socket_endpoint_config_.udp_p2p_task_;
      }
      break;
    }
    case SOCKET_ENDPOINT_CONFIG_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = SOCKET_ENDPOINT_CONFIG_NOT_SET;
}


void SocketTaskConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.rs_hmi.config.SocketTaskConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_socket_endpoint_config();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SocketTaskConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // .robosense.rs_hmi.config.WebsocketTaskConfig websocket_task = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_websocket_task(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .robosense.rs_hmi.config.UdpMulticastTaskConfig udp_multicast_task = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_udp_multicast_task(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .robosense.rs_hmi.config.UdpP2PTaskConfig udp_p2p_task = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_udp_p2p_task(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* SocketTaskConfig::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.rs_hmi.config.SocketTaskConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  switch (socket_endpoint_config_case()) {
    case kWebsocketTask: {
      target = stream->EnsureSpace(target);
      target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(
          1, _Internal::websocket_task(this), target, stream);
      break;
    }
    case kUdpMulticastTask: {
      target = stream->EnsureSpace(target);
      target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(
          2, _Internal::udp_multicast_task(this), target, stream);
      break;
    }
    case kUdpP2PTask: {
      target = stream->EnsureSpace(target);
      target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(
          3, _Internal::udp_p2p_task(this), target, stream);
      break;
    }
    default: ;
  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.rs_hmi.config.SocketTaskConfig)
  return target;
}

size_t SocketTaskConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.rs_hmi.config.SocketTaskConfig)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  switch (socket_endpoint_config_case()) {
    // .robosense.rs_hmi.config.WebsocketTaskConfig websocket_task = 1;
    case kWebsocketTask: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *socket_endpoint_config_.websocket_task_);
      break;
    }
    // .robosense.rs_hmi.config.UdpMulticastTaskConfig udp_multicast_task = 2;
    case kUdpMulticastTask: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *socket_endpoint_config_.udp_multicast_task_);
      break;
    }
    // .robosense.rs_hmi.config.UdpP2PTaskConfig udp_p2p_task = 3;
    case kUdpP2PTask: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *socket_endpoint_config_.udp_p2p_task_);
      break;
    }
    case SOCKET_ENDPOINT_CONFIG_NOT_SET: {
      break;
    }
  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SocketTaskConfig::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.rs_hmi.config.SocketTaskConfig)
  GOOGLE_DCHECK_NE(&from, this);
  const SocketTaskConfig* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<SocketTaskConfig>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.rs_hmi.config.SocketTaskConfig)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.rs_hmi.config.SocketTaskConfig)
    MergeFrom(*source);
  }
}

void SocketTaskConfig::MergeFrom(const SocketTaskConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.rs_hmi.config.SocketTaskConfig)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.socket_endpoint_config_case()) {
    case kWebsocketTask: {
      _internal_mutable_websocket_task()->::robosense::rs_hmi::config::WebsocketTaskConfig::MergeFrom(from._internal_websocket_task());
      break;
    }
    case kUdpMulticastTask: {
      _internal_mutable_udp_multicast_task()->::robosense::rs_hmi::config::UdpMulticastTaskConfig::MergeFrom(from._internal_udp_multicast_task());
      break;
    }
    case kUdpP2PTask: {
      _internal_mutable_udp_p2p_task()->::robosense::rs_hmi::config::UdpP2PTaskConfig::MergeFrom(from._internal_udp_p2p_task());
      break;
    }
    case SOCKET_ENDPOINT_CONFIG_NOT_SET: {
      break;
    }
  }
}

void SocketTaskConfig::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.rs_hmi.config.SocketTaskConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SocketTaskConfig::CopyFrom(const SocketTaskConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.rs_hmi.config.SocketTaskConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SocketTaskConfig::IsInitialized() const {
  return true;
}

void SocketTaskConfig::InternalSwap(SocketTaskConfig* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(socket_endpoint_config_, other->socket_endpoint_config_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata SocketTaskConfig::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class HmiSocketTaskConfig::_Internal {
 public:
};

HmiSocketTaskConfig::HmiSocketTaskConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  socket_task_configs_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.rs_hmi.config.HmiSocketTaskConfig)
}
HmiSocketTaskConfig::HmiSocketTaskConfig(const HmiSocketTaskConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      socket_task_configs_(from.socket_task_configs_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:robosense.rs_hmi.config.HmiSocketTaskConfig)
}

void HmiSocketTaskConfig::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_HmiSocketTaskConfig_config_2eproto.base);
}

HmiSocketTaskConfig::~HmiSocketTaskConfig() {
  // @@protoc_insertion_point(destructor:robosense.rs_hmi.config.HmiSocketTaskConfig)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void HmiSocketTaskConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void HmiSocketTaskConfig::ArenaDtor(void* object) {
  HmiSocketTaskConfig* _this = reinterpret_cast< HmiSocketTaskConfig* >(object);
  (void)_this;
}
void HmiSocketTaskConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void HmiSocketTaskConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const HmiSocketTaskConfig& HmiSocketTaskConfig::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_HmiSocketTaskConfig_config_2eproto.base);
  return *internal_default_instance();
}


void HmiSocketTaskConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.rs_hmi.config.HmiSocketTaskConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  socket_task_configs_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* HmiSocketTaskConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated .robosense.rs_hmi.config.SocketTaskConfig socket_task_configs = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_socket_task_configs(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* HmiSocketTaskConfig::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.rs_hmi.config.HmiSocketTaskConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .robosense.rs_hmi.config.SocketTaskConfig socket_task_configs = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_socket_task_configs_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_socket_task_configs(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.rs_hmi.config.HmiSocketTaskConfig)
  return target;
}

size_t HmiSocketTaskConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.rs_hmi.config.HmiSocketTaskConfig)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .robosense.rs_hmi.config.SocketTaskConfig socket_task_configs = 1;
  total_size += 1UL * this->_internal_socket_task_configs_size();
  for (const auto& msg : this->socket_task_configs_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void HmiSocketTaskConfig::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.rs_hmi.config.HmiSocketTaskConfig)
  GOOGLE_DCHECK_NE(&from, this);
  const HmiSocketTaskConfig* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<HmiSocketTaskConfig>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.rs_hmi.config.HmiSocketTaskConfig)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.rs_hmi.config.HmiSocketTaskConfig)
    MergeFrom(*source);
  }
}

void HmiSocketTaskConfig::MergeFrom(const HmiSocketTaskConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.rs_hmi.config.HmiSocketTaskConfig)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  socket_task_configs_.MergeFrom(from.socket_task_configs_);
}

void HmiSocketTaskConfig::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.rs_hmi.config.HmiSocketTaskConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void HmiSocketTaskConfig::CopyFrom(const HmiSocketTaskConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.rs_hmi.config.HmiSocketTaskConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HmiSocketTaskConfig::IsInitialized() const {
  return true;
}

void HmiSocketTaskConfig::InternalSwap(HmiSocketTaskConfig* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  socket_task_configs_.InternalSwap(&other->socket_task_configs_);
}

::PROTOBUF_NAMESPACE_ID::Metadata HmiSocketTaskConfig::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class HmiSocketTopicTaskConfig::_Internal {
 public:
  using HasBits = decltype(std::declval<HmiSocketTopicTaskConfig>()._has_bits_);
  static void set_has_task_socket_key(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

HmiSocketTopicTaskConfig::HmiSocketTopicTaskConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  support_message_topics_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.rs_hmi.config.HmiSocketTopicTaskConfig)
}
HmiSocketTopicTaskConfig::HmiSocketTopicTaskConfig(const HmiSocketTopicTaskConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_),
      support_message_topics_(from.support_message_topics_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  task_socket_key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_task_socket_key()) {
    task_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_task_socket_key(), 
      GetArena());
  }
  // @@protoc_insertion_point(copy_constructor:robosense.rs_hmi.config.HmiSocketTopicTaskConfig)
}

void HmiSocketTopicTaskConfig::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_HmiSocketTopicTaskConfig_config_2eproto.base);
  task_socket_key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

HmiSocketTopicTaskConfig::~HmiSocketTopicTaskConfig() {
  // @@protoc_insertion_point(destructor:robosense.rs_hmi.config.HmiSocketTopicTaskConfig)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void HmiSocketTopicTaskConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  task_socket_key_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void HmiSocketTopicTaskConfig::ArenaDtor(void* object) {
  HmiSocketTopicTaskConfig* _this = reinterpret_cast< HmiSocketTopicTaskConfig* >(object);
  (void)_this;
}
void HmiSocketTopicTaskConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void HmiSocketTopicTaskConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const HmiSocketTopicTaskConfig& HmiSocketTopicTaskConfig::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_HmiSocketTopicTaskConfig_config_2eproto.base);
  return *internal_default_instance();
}


void HmiSocketTopicTaskConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.rs_hmi.config.HmiSocketTopicTaskConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  support_message_topics_.Clear();
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    task_socket_key_.ClearNonDefaultToEmpty();
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* HmiSocketTopicTaskConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated string support_message_topics = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_support_message_topics();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            #ifndef NDEBUG
            ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.rs_hmi.config.HmiSocketTopicTaskConfig.support_message_topics");
            #endif  // !NDEBUG
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else goto handle_unusual;
        continue;
      // optional string task_socket_key = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_task_socket_key();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.rs_hmi.config.HmiSocketTopicTaskConfig.task_socket_key");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* HmiSocketTopicTaskConfig::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.rs_hmi.config.HmiSocketTopicTaskConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string support_message_topics = 1;
  for (int i = 0, n = this->_internal_support_message_topics_size(); i < n; i++) {
    const auto& s = this->_internal_support_message_topics(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.rs_hmi.config.HmiSocketTopicTaskConfig.support_message_topics");
    target = stream->WriteString(1, s, target);
  }

  cached_has_bits = _has_bits_[0];
  // optional string task_socket_key = 2;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_task_socket_key().data(), static_cast<int>(this->_internal_task_socket_key().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.rs_hmi.config.HmiSocketTopicTaskConfig.task_socket_key");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_task_socket_key(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.rs_hmi.config.HmiSocketTopicTaskConfig)
  return target;
}

size_t HmiSocketTopicTaskConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.rs_hmi.config.HmiSocketTopicTaskConfig)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string support_message_topics = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(support_message_topics_.size());
  for (int i = 0, n = support_message_topics_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      support_message_topics_.Get(i));
  }

  // optional string task_socket_key = 2;
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_task_socket_key());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void HmiSocketTopicTaskConfig::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.rs_hmi.config.HmiSocketTopicTaskConfig)
  GOOGLE_DCHECK_NE(&from, this);
  const HmiSocketTopicTaskConfig* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<HmiSocketTopicTaskConfig>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.rs_hmi.config.HmiSocketTopicTaskConfig)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.rs_hmi.config.HmiSocketTopicTaskConfig)
    MergeFrom(*source);
  }
}

void HmiSocketTopicTaskConfig::MergeFrom(const HmiSocketTopicTaskConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.rs_hmi.config.HmiSocketTopicTaskConfig)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  support_message_topics_.MergeFrom(from.support_message_topics_);
  if (from._internal_has_task_socket_key()) {
    _internal_set_task_socket_key(from._internal_task_socket_key());
  }
}

void HmiSocketTopicTaskConfig::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.rs_hmi.config.HmiSocketTopicTaskConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void HmiSocketTopicTaskConfig::CopyFrom(const HmiSocketTopicTaskConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.rs_hmi.config.HmiSocketTopicTaskConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HmiSocketTopicTaskConfig::IsInitialized() const {
  return true;
}

void HmiSocketTopicTaskConfig::InternalSwap(HmiSocketTopicTaskConfig* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  support_message_topics_.InternalSwap(&other->support_message_topics_);
  task_socket_key_.Swap(&other->task_socket_key_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}

::PROTOBUF_NAMESPACE_ID::Metadata HmiSocketTopicTaskConfig::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class HmiTaskSocketConfig::_Internal {
 public:
  using HasBits = decltype(std::declval<HmiTaskSocketConfig>()._has_bits_);
  static void set_has_enable_task(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

HmiTaskSocketConfig::HmiTaskSocketConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  task_socket_configs_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.rs_hmi.config.HmiTaskSocketConfig)
}
HmiTaskSocketConfig::HmiTaskSocketConfig(const HmiTaskSocketConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_),
      task_socket_configs_(from.task_socket_configs_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  enable_task_ = from.enable_task_;
  // @@protoc_insertion_point(copy_constructor:robosense.rs_hmi.config.HmiTaskSocketConfig)
}

void HmiTaskSocketConfig::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_HmiTaskSocketConfig_config_2eproto.base);
  enable_task_ = false;
}

HmiTaskSocketConfig::~HmiTaskSocketConfig() {
  // @@protoc_insertion_point(destructor:robosense.rs_hmi.config.HmiTaskSocketConfig)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void HmiTaskSocketConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void HmiTaskSocketConfig::ArenaDtor(void* object) {
  HmiTaskSocketConfig* _this = reinterpret_cast< HmiTaskSocketConfig* >(object);
  (void)_this;
}
void HmiTaskSocketConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void HmiTaskSocketConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const HmiTaskSocketConfig& HmiTaskSocketConfig::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_HmiTaskSocketConfig_config_2eproto.base);
  return *internal_default_instance();
}


void HmiTaskSocketConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.rs_hmi.config.HmiTaskSocketConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  task_socket_configs_.Clear();
  enable_task_ = false;
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* HmiTaskSocketConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional bool enable_task = 1 [default = false];
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          _Internal::set_has_enable_task(&has_bits);
          enable_task_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .robosense.rs_hmi.config.HmiSocketTopicTaskConfig task_socket_configs = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_task_socket_configs(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* HmiTaskSocketConfig::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.rs_hmi.config.HmiTaskSocketConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional bool enable_task = 1 [default = false];
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_enable_task(), target);
  }

  // repeated .robosense.rs_hmi.config.HmiSocketTopicTaskConfig task_socket_configs = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_task_socket_configs_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_task_socket_configs(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.rs_hmi.config.HmiTaskSocketConfig)
  return target;
}

size_t HmiTaskSocketConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.rs_hmi.config.HmiTaskSocketConfig)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .robosense.rs_hmi.config.HmiSocketTopicTaskConfig task_socket_configs = 2;
  total_size += 1UL * this->_internal_task_socket_configs_size();
  for (const auto& msg : this->task_socket_configs_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // optional bool enable_task = 1 [default = false];
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size += 1 + 1;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void HmiTaskSocketConfig::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.rs_hmi.config.HmiTaskSocketConfig)
  GOOGLE_DCHECK_NE(&from, this);
  const HmiTaskSocketConfig* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<HmiTaskSocketConfig>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.rs_hmi.config.HmiTaskSocketConfig)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.rs_hmi.config.HmiTaskSocketConfig)
    MergeFrom(*source);
  }
}

void HmiTaskSocketConfig::MergeFrom(const HmiTaskSocketConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.rs_hmi.config.HmiTaskSocketConfig)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  task_socket_configs_.MergeFrom(from.task_socket_configs_);
  if (from._internal_has_enable_task()) {
    _internal_set_enable_task(from._internal_enable_task());
  }
}

void HmiTaskSocketConfig::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.rs_hmi.config.HmiTaskSocketConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void HmiTaskSocketConfig::CopyFrom(const HmiTaskSocketConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.rs_hmi.config.HmiTaskSocketConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HmiTaskSocketConfig::IsInitialized() const {
  return true;
}

void HmiTaskSocketConfig::InternalSwap(HmiTaskSocketConfig* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  task_socket_configs_.InternalSwap(&other->task_socket_configs_);
  swap(enable_task_, other->enable_task_);
}

::PROTOBUF_NAMESPACE_ID::Metadata HmiTaskSocketConfig::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class WebsocketRenderTaskConfig::_Internal {
 public:
  using HasBits = decltype(std::declval<WebsocketRenderTaskConfig>()._has_bits_);
  static const ::robosense::rs_hmi::config::HmiTaskSocketConfig& websocket_task(const WebsocketRenderTaskConfig* msg);
  static void set_has_websocket_task(HasBits* has_bits) {
    (*has_bits)[0] |= 32u;
  }
  static void set_has_enable_websocket_buffer(HasBits* has_bits) {
    (*has_bits)[0] |= 1024u;
  }
  static void set_has_websocket_buffer_size(HasBits* has_bits) {
    (*has_bits)[0] |= 2048u;
  }
  static void set_has_low_websocket_buffer_size(HasBits* has_bits) {
    (*has_bits)[0] |= 4096u;
  }
  static void set_has_websocket_cmd_socket_key(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_websocket_socket_key(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_udp_multicast_socket_key(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_udp_double_multicast_socket_key(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_udp_p2p_socket_key(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static void set_has_udp_2_sample_1_buffer_size(HasBits* has_bits) {
    (*has_bits)[0] |= 8192u;
  }
  static void set_has_udp_3_sample_1_buffer_size(HasBits* has_bits) {
    (*has_bits)[0] |= 16384u;
  }
  static void set_has_udp_loss_all_buffer_size(HasBits* has_bits) {
    (*has_bits)[0] |= 32768u;
  }
  static void set_has_max_send_render_buffer_size(HasBits* has_bits) {
    (*has_bits)[0] |= 512u;
  }
  static const ::robosense::rs_hmi::config::UdpMulticastEndPointConfig& udp_multicast_render_config(const WebsocketRenderTaskConfig* msg);
  static void set_has_udp_multicast_render_config(HasBits* has_bits) {
    (*has_bits)[0] |= 64u;
  }
  static const ::robosense::rs_hmi::config::UdpP2PEndPointConfig& udp_p2p_render_config(const WebsocketRenderTaskConfig* msg);
  static void set_has_udp_p2p_render_config(HasBits* has_bits) {
    (*has_bits)[0] |= 128u;
  }
  static const ::robosense::rs_hmi::config::UdpMulticastEndPointConfig& double_udp_multicast_render_config(const WebsocketRenderTaskConfig* msg);
  static void set_has_double_udp_multicast_render_config(HasBits* has_bits) {
    (*has_bits)[0] |= 256u;
  }
};

const ::robosense::rs_hmi::config::HmiTaskSocketConfig&
WebsocketRenderTaskConfig::_Internal::websocket_task(const WebsocketRenderTaskConfig* msg) {
  return *msg->websocket_task_;
}
const ::robosense::rs_hmi::config::UdpMulticastEndPointConfig&
WebsocketRenderTaskConfig::_Internal::udp_multicast_render_config(const WebsocketRenderTaskConfig* msg) {
  return *msg->udp_multicast_render_config_;
}
const ::robosense::rs_hmi::config::UdpP2PEndPointConfig&
WebsocketRenderTaskConfig::_Internal::udp_p2p_render_config(const WebsocketRenderTaskConfig* msg) {
  return *msg->udp_p2p_render_config_;
}
const ::robosense::rs_hmi::config::UdpMulticastEndPointConfig&
WebsocketRenderTaskConfig::_Internal::double_udp_multicast_render_config(const WebsocketRenderTaskConfig* msg) {
  return *msg->double_udp_multicast_render_config_;
}
WebsocketRenderTaskConfig::WebsocketRenderTaskConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.rs_hmi.config.WebsocketRenderTaskConfig)
}
WebsocketRenderTaskConfig::WebsocketRenderTaskConfig(const WebsocketRenderTaskConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  websocket_cmd_socket_key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_websocket_cmd_socket_key()) {
    websocket_cmd_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_websocket_cmd_socket_key(), 
      GetArena());
  }
  websocket_socket_key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_websocket_socket_key()) {
    websocket_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_websocket_socket_key(), 
      GetArena());
  }
  udp_multicast_socket_key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_udp_multicast_socket_key()) {
    udp_multicast_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_udp_multicast_socket_key(), 
      GetArena());
  }
  udp_double_multicast_socket_key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_udp_double_multicast_socket_key()) {
    udp_double_multicast_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_udp_double_multicast_socket_key(), 
      GetArena());
  }
  udp_p2p_socket_key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_udp_p2p_socket_key()) {
    udp_p2p_socket_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_udp_p2p_socket_key(), 
      GetArena());
  }
  if (from._internal_has_websocket_task()) {
    websocket_task_ = new ::robosense::rs_hmi::config::HmiTaskSocketConfig(*from.websocket_task_);
  } else {
    websocket_task_ = nullptr;
  }
  if (from._internal_has_udp_multicast_render_config()) {
    udp_multicast_render_config_ = new ::robosense::rs_hmi::config::UdpMulticastEndPointConfig(*from.udp_multicast_render_config_);
  } else {
    udp_multicast_render_config_ = nullptr;
  }
  if (from._internal_has_udp_p2p_render_config()) {
    udp_p2p_render_config_ = new ::robosense::rs_hmi::config::UdpP2PEndPointConfig(*from.udp_p2p_render_config_);
  } else {
    udp_p2p_render_config_ = nullptr;
  }
  if (from._internal_has_double_udp_multicast_render_config()) {
    double_udp_multicast_render_config_ = new ::robosense::rs_hmi::config::UdpMulticastEndPointConfig(*from.double_udp_multicast_render_config_);
  } else {
    double_udp_multicast_render_config_ = nullptr;
  }
  ::memcpy(&max_send_render_buffer_size_, &from.max_send_render_buffer_size_,
    static_cast<size_t>(reinterpret_cast<char*>(&udp_loss_all_buffer_size_) -
    reinterpret_cast<char*>(&max_send_render_buffer_size_)) + sizeof(udp_loss_all_buffer_size_));
  // @@protoc_insertion_point(copy_constructor:robosense.rs_hmi.config.WebsocketRenderTaskConfig)
}

void WebsocketRenderTaskConfig::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_WebsocketRenderTaskConfig_config_2eproto.base);
  websocket_cmd_socket_key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  websocket_socket_key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  udp_multicast_socket_key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  udp_double_multicast_socket_key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  udp_p2p_socket_key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&websocket_task_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&double_udp_multicast_render_config_) -
      reinterpret_cast<char*>(&websocket_task_)) + sizeof(double_udp_multicast_render_config_));
  max_send_render_buffer_size_ = 15u;
  enable_websocket_buffer_ = true;
  websocket_buffer_size_ = 2097152u;
  low_websocket_buffer_size_ = 524288u;
  udp_2_sample_1_buffer_size_ = 3u;
  udp_3_sample_1_buffer_size_ = 5u;
  udp_loss_all_buffer_size_ = 10u;
}

WebsocketRenderTaskConfig::~WebsocketRenderTaskConfig() {
  // @@protoc_insertion_point(destructor:robosense.rs_hmi.config.WebsocketRenderTaskConfig)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void WebsocketRenderTaskConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  websocket_cmd_socket_key_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  websocket_socket_key_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  udp_multicast_socket_key_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  udp_double_multicast_socket_key_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  udp_p2p_socket_key_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete websocket_task_;
  if (this != internal_default_instance()) delete udp_multicast_render_config_;
  if (this != internal_default_instance()) delete udp_p2p_render_config_;
  if (this != internal_default_instance()) delete double_udp_multicast_render_config_;
}

void WebsocketRenderTaskConfig::ArenaDtor(void* object) {
  WebsocketRenderTaskConfig* _this = reinterpret_cast< WebsocketRenderTaskConfig* >(object);
  (void)_this;
}
void WebsocketRenderTaskConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void WebsocketRenderTaskConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const WebsocketRenderTaskConfig& WebsocketRenderTaskConfig::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_WebsocketRenderTaskConfig_config_2eproto.base);
  return *internal_default_instance();
}


void WebsocketRenderTaskConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.rs_hmi.config.WebsocketRenderTaskConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    if (cached_has_bits & 0x00000001u) {
      websocket_cmd_socket_key_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      websocket_socket_key_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000004u) {
      udp_multicast_socket_key_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000008u) {
      udp_double_multicast_socket_key_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000010u) {
      udp_p2p_socket_key_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000020u) {
      GOOGLE_DCHECK(websocket_task_ != nullptr);
      websocket_task_->Clear();
    }
    if (cached_has_bits & 0x00000040u) {
      GOOGLE_DCHECK(udp_multicast_render_config_ != nullptr);
      udp_multicast_render_config_->Clear();
    }
    if (cached_has_bits & 0x00000080u) {
      GOOGLE_DCHECK(udp_p2p_render_config_ != nullptr);
      udp_p2p_render_config_->Clear();
    }
  }
  if (cached_has_bits & 0x0000ff00u) {
    if (cached_has_bits & 0x00000100u) {
      GOOGLE_DCHECK(double_udp_multicast_render_config_ != nullptr);
      double_udp_multicast_render_config_->Clear();
    }
    max_send_render_buffer_size_ = 15u;
    enable_websocket_buffer_ = true;
    websocket_buffer_size_ = 2097152u;
    low_websocket_buffer_size_ = 524288u;
    udp_2_sample_1_buffer_size_ = 3u;
    udp_3_sample_1_buffer_size_ = 5u;
    udp_loss_all_buffer_size_ = 10u;
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* WebsocketRenderTaskConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional .robosense.rs_hmi.config.HmiTaskSocketConfig websocket_task = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_websocket_task(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional bool enable_websocket_buffer = 2 [default = true];
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          _Internal::set_has_enable_websocket_buffer(&has_bits);
          enable_websocket_buffer_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 websocket_buffer_size = 3 [default = 2097152];
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          _Internal::set_has_websocket_buffer_size(&has_bits);
          websocket_buffer_size_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 low_websocket_buffer_size = 4 [default = 524288];
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          _Internal::set_has_low_websocket_buffer_size(&has_bits);
          low_websocket_buffer_size_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string websocket_cmd_socket_key = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          auto str = _internal_mutable_websocket_cmd_socket_key();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.rs_hmi.config.WebsocketRenderTaskConfig.websocket_cmd_socket_key");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string websocket_socket_key = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          auto str = _internal_mutable_websocket_socket_key();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.rs_hmi.config.WebsocketRenderTaskConfig.websocket_socket_key");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string udp_multicast_socket_key = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          auto str = _internal_mutable_udp_multicast_socket_key();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_multicast_socket_key");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string udp_double_multicast_socket_key = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 66)) {
          auto str = _internal_mutable_udp_double_multicast_socket_key();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_double_multicast_socket_key");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string udp_p2p_socket_key = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          auto str = _internal_mutable_udp_p2p_socket_key();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_p2p_socket_key");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 udp_2_sample_1_buffer_size = 10 [default = 3];
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 80)) {
          _Internal::set_has_udp_2_sample_1_buffer_size(&has_bits);
          udp_2_sample_1_buffer_size_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 udp_3_sample_1_buffer_size = 11 [default = 5];
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 88)) {
          _Internal::set_has_udp_3_sample_1_buffer_size(&has_bits);
          udp_3_sample_1_buffer_size_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 udp_loss_all_buffer_size = 12 [default = 10];
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 96)) {
          _Internal::set_has_udp_loss_all_buffer_size(&has_bits);
          udp_loss_all_buffer_size_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 max_send_render_buffer_size = 13 [default = 15];
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 104)) {
          _Internal::set_has_max_send_render_buffer_size(&has_bits);
          max_send_render_buffer_size_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .robosense.rs_hmi.config.UdpMulticastEndPointConfig udp_multicast_render_config = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 114)) {
          ptr = ctx->ParseMessage(_internal_mutable_udp_multicast_render_config(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .robosense.rs_hmi.config.UdpP2PEndPointConfig udp_p2p_render_config = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 122)) {
          ptr = ctx->ParseMessage(_internal_mutable_udp_p2p_render_config(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .robosense.rs_hmi.config.UdpMulticastEndPointConfig double_udp_multicast_render_config = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 130)) {
          ptr = ctx->ParseMessage(_internal_mutable_double_udp_multicast_render_config(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* WebsocketRenderTaskConfig::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.rs_hmi.config.WebsocketRenderTaskConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional .robosense.rs_hmi.config.HmiTaskSocketConfig websocket_task = 1;
  if (cached_has_bits & 0x00000020u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::websocket_task(this), target, stream);
  }

  // optional bool enable_websocket_buffer = 2 [default = true];
  if (cached_has_bits & 0x00000400u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_enable_websocket_buffer(), target);
  }

  // optional uint32 websocket_buffer_size = 3 [default = 2097152];
  if (cached_has_bits & 0x00000800u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_websocket_buffer_size(), target);
  }

  // optional uint32 low_websocket_buffer_size = 4 [default = 524288];
  if (cached_has_bits & 0x00001000u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_low_websocket_buffer_size(), target);
  }

  // optional string websocket_cmd_socket_key = 5;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_websocket_cmd_socket_key().data(), static_cast<int>(this->_internal_websocket_cmd_socket_key().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.rs_hmi.config.WebsocketRenderTaskConfig.websocket_cmd_socket_key");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_websocket_cmd_socket_key(), target);
  }

  // optional string websocket_socket_key = 6;
  if (cached_has_bits & 0x00000002u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_websocket_socket_key().data(), static_cast<int>(this->_internal_websocket_socket_key().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.rs_hmi.config.WebsocketRenderTaskConfig.websocket_socket_key");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_websocket_socket_key(), target);
  }

  // optional string udp_multicast_socket_key = 7;
  if (cached_has_bits & 0x00000004u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_udp_multicast_socket_key().data(), static_cast<int>(this->_internal_udp_multicast_socket_key().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_multicast_socket_key");
    target = stream->WriteStringMaybeAliased(
        7, this->_internal_udp_multicast_socket_key(), target);
  }

  // optional string udp_double_multicast_socket_key = 8;
  if (cached_has_bits & 0x00000008u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_udp_double_multicast_socket_key().data(), static_cast<int>(this->_internal_udp_double_multicast_socket_key().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_double_multicast_socket_key");
    target = stream->WriteStringMaybeAliased(
        8, this->_internal_udp_double_multicast_socket_key(), target);
  }

  // optional string udp_p2p_socket_key = 9;
  if (cached_has_bits & 0x00000010u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_udp_p2p_socket_key().data(), static_cast<int>(this->_internal_udp_p2p_socket_key().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.rs_hmi.config.WebsocketRenderTaskConfig.udp_p2p_socket_key");
    target = stream->WriteStringMaybeAliased(
        9, this->_internal_udp_p2p_socket_key(), target);
  }

  // optional uint32 udp_2_sample_1_buffer_size = 10 [default = 3];
  if (cached_has_bits & 0x00002000u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(10, this->_internal_udp_2_sample_1_buffer_size(), target);
  }

  // optional uint32 udp_3_sample_1_buffer_size = 11 [default = 5];
  if (cached_has_bits & 0x00004000u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(11, this->_internal_udp_3_sample_1_buffer_size(), target);
  }

  // optional uint32 udp_loss_all_buffer_size = 12 [default = 10];
  if (cached_has_bits & 0x00008000u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(12, this->_internal_udp_loss_all_buffer_size(), target);
  }

  // optional uint32 max_send_render_buffer_size = 13 [default = 15];
  if (cached_has_bits & 0x00000200u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(13, this->_internal_max_send_render_buffer_size(), target);
  }

  // optional .robosense.rs_hmi.config.UdpMulticastEndPointConfig udp_multicast_render_config = 14;
  if (cached_has_bits & 0x00000040u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        14, _Internal::udp_multicast_render_config(this), target, stream);
  }

  // optional .robosense.rs_hmi.config.UdpP2PEndPointConfig udp_p2p_render_config = 15;
  if (cached_has_bits & 0x00000080u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        15, _Internal::udp_p2p_render_config(this), target, stream);
  }

  // optional .robosense.rs_hmi.config.UdpMulticastEndPointConfig double_udp_multicast_render_config = 16;
  if (cached_has_bits & 0x00000100u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        16, _Internal::double_udp_multicast_render_config(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.rs_hmi.config.WebsocketRenderTaskConfig)
  return target;
}

size_t WebsocketRenderTaskConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.rs_hmi.config.WebsocketRenderTaskConfig)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    // optional string websocket_cmd_socket_key = 5;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_websocket_cmd_socket_key());
    }

    // optional string websocket_socket_key = 6;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_websocket_socket_key());
    }

    // optional string udp_multicast_socket_key = 7;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_udp_multicast_socket_key());
    }

    // optional string udp_double_multicast_socket_key = 8;
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_udp_double_multicast_socket_key());
    }

    // optional string udp_p2p_socket_key = 9;
    if (cached_has_bits & 0x00000010u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_udp_p2p_socket_key());
    }

    // optional .robosense.rs_hmi.config.HmiTaskSocketConfig websocket_task = 1;
    if (cached_has_bits & 0x00000020u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *websocket_task_);
    }

    // optional .robosense.rs_hmi.config.UdpMulticastEndPointConfig udp_multicast_render_config = 14;
    if (cached_has_bits & 0x00000040u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *udp_multicast_render_config_);
    }

    // optional .robosense.rs_hmi.config.UdpP2PEndPointConfig udp_p2p_render_config = 15;
    if (cached_has_bits & 0x00000080u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *udp_p2p_render_config_);
    }

  }
  if (cached_has_bits & 0x0000ff00u) {
    // optional .robosense.rs_hmi.config.UdpMulticastEndPointConfig double_udp_multicast_render_config = 16;
    if (cached_has_bits & 0x00000100u) {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *double_udp_multicast_render_config_);
    }

    // optional uint32 max_send_render_buffer_size = 13 [default = 15];
    if (cached_has_bits & 0x00000200u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_max_send_render_buffer_size());
    }

    // optional bool enable_websocket_buffer = 2 [default = true];
    if (cached_has_bits & 0x00000400u) {
      total_size += 1 + 1;
    }

    // optional uint32 websocket_buffer_size = 3 [default = 2097152];
    if (cached_has_bits & 0x00000800u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_websocket_buffer_size());
    }

    // optional uint32 low_websocket_buffer_size = 4 [default = 524288];
    if (cached_has_bits & 0x00001000u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_low_websocket_buffer_size());
    }

    // optional uint32 udp_2_sample_1_buffer_size = 10 [default = 3];
    if (cached_has_bits & 0x00002000u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_udp_2_sample_1_buffer_size());
    }

    // optional uint32 udp_3_sample_1_buffer_size = 11 [default = 5];
    if (cached_has_bits & 0x00004000u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_udp_3_sample_1_buffer_size());
    }

    // optional uint32 udp_loss_all_buffer_size = 12 [default = 10];
    if (cached_has_bits & 0x00008000u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_udp_loss_all_buffer_size());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void WebsocketRenderTaskConfig::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.rs_hmi.config.WebsocketRenderTaskConfig)
  GOOGLE_DCHECK_NE(&from, this);
  const WebsocketRenderTaskConfig* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<WebsocketRenderTaskConfig>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.rs_hmi.config.WebsocketRenderTaskConfig)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.rs_hmi.config.WebsocketRenderTaskConfig)
    MergeFrom(*source);
  }
}

void WebsocketRenderTaskConfig::MergeFrom(const WebsocketRenderTaskConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.rs_hmi.config.WebsocketRenderTaskConfig)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_websocket_cmd_socket_key(from._internal_websocket_cmd_socket_key());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_set_websocket_socket_key(from._internal_websocket_socket_key());
    }
    if (cached_has_bits & 0x00000004u) {
      _internal_set_udp_multicast_socket_key(from._internal_udp_multicast_socket_key());
    }
    if (cached_has_bits & 0x00000008u) {
      _internal_set_udp_double_multicast_socket_key(from._internal_udp_double_multicast_socket_key());
    }
    if (cached_has_bits & 0x00000010u) {
      _internal_set_udp_p2p_socket_key(from._internal_udp_p2p_socket_key());
    }
    if (cached_has_bits & 0x00000020u) {
      _internal_mutable_websocket_task()->::robosense::rs_hmi::config::HmiTaskSocketConfig::MergeFrom(from._internal_websocket_task());
    }
    if (cached_has_bits & 0x00000040u) {
      _internal_mutable_udp_multicast_render_config()->::robosense::rs_hmi::config::UdpMulticastEndPointConfig::MergeFrom(from._internal_udp_multicast_render_config());
    }
    if (cached_has_bits & 0x00000080u) {
      _internal_mutable_udp_p2p_render_config()->::robosense::rs_hmi::config::UdpP2PEndPointConfig::MergeFrom(from._internal_udp_p2p_render_config());
    }
  }
  if (cached_has_bits & 0x0000ff00u) {
    if (cached_has_bits & 0x00000100u) {
      _internal_mutable_double_udp_multicast_render_config()->::robosense::rs_hmi::config::UdpMulticastEndPointConfig::MergeFrom(from._internal_double_udp_multicast_render_config());
    }
    if (cached_has_bits & 0x00000200u) {
      max_send_render_buffer_size_ = from.max_send_render_buffer_size_;
    }
    if (cached_has_bits & 0x00000400u) {
      enable_websocket_buffer_ = from.enable_websocket_buffer_;
    }
    if (cached_has_bits & 0x00000800u) {
      websocket_buffer_size_ = from.websocket_buffer_size_;
    }
    if (cached_has_bits & 0x00001000u) {
      low_websocket_buffer_size_ = from.low_websocket_buffer_size_;
    }
    if (cached_has_bits & 0x00002000u) {
      udp_2_sample_1_buffer_size_ = from.udp_2_sample_1_buffer_size_;
    }
    if (cached_has_bits & 0x00004000u) {
      udp_3_sample_1_buffer_size_ = from.udp_3_sample_1_buffer_size_;
    }
    if (cached_has_bits & 0x00008000u) {
      udp_loss_all_buffer_size_ = from.udp_loss_all_buffer_size_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void WebsocketRenderTaskConfig::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.rs_hmi.config.WebsocketRenderTaskConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void WebsocketRenderTaskConfig::CopyFrom(const WebsocketRenderTaskConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.rs_hmi.config.WebsocketRenderTaskConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool WebsocketRenderTaskConfig::IsInitialized() const {
  return true;
}

void WebsocketRenderTaskConfig::InternalSwap(WebsocketRenderTaskConfig* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  websocket_cmd_socket_key_.Swap(&other->websocket_cmd_socket_key_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  websocket_socket_key_.Swap(&other->websocket_socket_key_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  udp_multicast_socket_key_.Swap(&other->udp_multicast_socket_key_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  udp_double_multicast_socket_key_.Swap(&other->udp_double_multicast_socket_key_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  udp_p2p_socket_key_.Swap(&other->udp_p2p_socket_key_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(WebsocketRenderTaskConfig, double_udp_multicast_render_config_)
      + sizeof(WebsocketRenderTaskConfig::double_udp_multicast_render_config_)
      - PROTOBUF_FIELD_OFFSET(WebsocketRenderTaskConfig, websocket_task_)>(
          reinterpret_cast<char*>(&websocket_task_),
          reinterpret_cast<char*>(&other->websocket_task_));
  swap(max_send_render_buffer_size_, other->max_send_render_buffer_size_);
  swap(enable_websocket_buffer_, other->enable_websocket_buffer_);
  swap(websocket_buffer_size_, other->websocket_buffer_size_);
  swap(low_websocket_buffer_size_, other->low_websocket_buffer_size_);
  swap(udp_2_sample_1_buffer_size_, other->udp_2_sample_1_buffer_size_);
  swap(udp_3_sample_1_buffer_size_, other->udp_3_sample_1_buffer_size_);
  swap(udp_loss_all_buffer_size_, other->udp_loss_all_buffer_size_);
}

::PROTOBUF_NAMESPACE_ID::Metadata WebsocketRenderTaskConfig::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class Config::_Internal {
 public:
  using HasBits = decltype(std::declval<Config>()._has_bits_);
  static void set_has_enable_websocket_buffer_check(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_websocket_buffer_check_timeout_th_ms(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static const ::robosense::rs_hmi::config::WebsocketRenderTaskConfig& render_config(const Config* msg);
  static void set_has_render_config(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static const ::robosense::rs_hmi::config::HmiSocketTaskConfig& hmi_socket_task_config(const Config* msg);
  static void set_has_hmi_socket_task_config(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

const ::robosense::rs_hmi::config::WebsocketRenderTaskConfig&
Config::_Internal::render_config(const Config* msg) {
  return *msg->render_config_;
}
const ::robosense::rs_hmi::config::HmiSocketTaskConfig&
Config::_Internal::hmi_socket_task_config(const Config* msg) {
  return *msg->hmi_socket_task_config_;
}
Config::Config(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.rs_hmi.config.Config)
}
Config::Config(const Config& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_render_config()) {
    render_config_ = new ::robosense::rs_hmi::config::WebsocketRenderTaskConfig(*from.render_config_);
  } else {
    render_config_ = nullptr;
  }
  if (from._internal_has_hmi_socket_task_config()) {
    hmi_socket_task_config_ = new ::robosense::rs_hmi::config::HmiSocketTaskConfig(*from.hmi_socket_task_config_);
  } else {
    hmi_socket_task_config_ = nullptr;
  }
  ::memcpy(&enable_websocket_buffer_check_, &from.enable_websocket_buffer_check_,
    static_cast<size_t>(reinterpret_cast<char*>(&websocket_buffer_check_timeout_th_ms_) -
    reinterpret_cast<char*>(&enable_websocket_buffer_check_)) + sizeof(websocket_buffer_check_timeout_th_ms_));
  // @@protoc_insertion_point(copy_constructor:robosense.rs_hmi.config.Config)
}

void Config::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_Config_config_2eproto.base);
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&render_config_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&hmi_socket_task_config_) -
      reinterpret_cast<char*>(&render_config_)) + sizeof(hmi_socket_task_config_));
  enable_websocket_buffer_check_ = true;
  websocket_buffer_check_timeout_th_ms_ = 2000u;
}

Config::~Config() {
  // @@protoc_insertion_point(destructor:robosense.rs_hmi.config.Config)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void Config::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  if (this != internal_default_instance()) delete render_config_;
  if (this != internal_default_instance()) delete hmi_socket_task_config_;
}

void Config::ArenaDtor(void* object) {
  Config* _this = reinterpret_cast< Config* >(object);
  (void)_this;
}
void Config::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Config::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const Config& Config::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_Config_config_2eproto.base);
  return *internal_default_instance();
}


void Config::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.rs_hmi.config.Config)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    if (cached_has_bits & 0x00000001u) {
      GOOGLE_DCHECK(render_config_ != nullptr);
      render_config_->Clear();
    }
    if (cached_has_bits & 0x00000002u) {
      GOOGLE_DCHECK(hmi_socket_task_config_ != nullptr);
      hmi_socket_task_config_->Clear();
    }
    enable_websocket_buffer_check_ = true;
    websocket_buffer_check_timeout_th_ms_ = 2000u;
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Config::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional bool enable_websocket_buffer_check = 1 [default = true];
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          _Internal::set_has_enable_websocket_buffer_check(&has_bits);
          enable_websocket_buffer_check_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 websocket_buffer_check_timeout_th_ms = 2 [default = 2000];
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          _Internal::set_has_websocket_buffer_check_timeout_th_ms(&has_bits);
          websocket_buffer_check_timeout_th_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .robosense.rs_hmi.config.WebsocketRenderTaskConfig render_config = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_render_config(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .robosense.rs_hmi.config.HmiSocketTaskConfig hmi_socket_task_config = 17;
      case 17:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 138)) {
          ptr = ctx->ParseMessage(_internal_mutable_hmi_socket_task_config(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* Config::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.rs_hmi.config.Config)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional bool enable_websocket_buffer_check = 1 [default = true];
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_enable_websocket_buffer_check(), target);
  }

  // optional uint32 websocket_buffer_check_timeout_th_ms = 2 [default = 2000];
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_websocket_buffer_check_timeout_th_ms(), target);
  }

  // optional .robosense.rs_hmi.config.WebsocketRenderTaskConfig render_config = 4;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::render_config(this), target, stream);
  }

  // optional .robosense.rs_hmi.config.HmiSocketTaskConfig hmi_socket_task_config = 17;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        17, _Internal::hmi_socket_task_config(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.rs_hmi.config.Config)
  return target;
}

size_t Config::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.rs_hmi.config.Config)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    // optional .robosense.rs_hmi.config.WebsocketRenderTaskConfig render_config = 4;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *render_config_);
    }

    // optional .robosense.rs_hmi.config.HmiSocketTaskConfig hmi_socket_task_config = 17;
    if (cached_has_bits & 0x00000002u) {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *hmi_socket_task_config_);
    }

    // optional bool enable_websocket_buffer_check = 1 [default = true];
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 + 1;
    }

    // optional uint32 websocket_buffer_check_timeout_th_ms = 2 [default = 2000];
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_websocket_buffer_check_timeout_th_ms());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Config::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.rs_hmi.config.Config)
  GOOGLE_DCHECK_NE(&from, this);
  const Config* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<Config>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.rs_hmi.config.Config)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.rs_hmi.config.Config)
    MergeFrom(*source);
  }
}

void Config::MergeFrom(const Config& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.rs_hmi.config.Config)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    if (cached_has_bits & 0x00000001u) {
      _internal_mutable_render_config()->::robosense::rs_hmi::config::WebsocketRenderTaskConfig::MergeFrom(from._internal_render_config());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_mutable_hmi_socket_task_config()->::robosense::rs_hmi::config::HmiSocketTaskConfig::MergeFrom(from._internal_hmi_socket_task_config());
    }
    if (cached_has_bits & 0x00000004u) {
      enable_websocket_buffer_check_ = from.enable_websocket_buffer_check_;
    }
    if (cached_has_bits & 0x00000008u) {
      websocket_buffer_check_timeout_th_ms_ = from.websocket_buffer_check_timeout_th_ms_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void Config::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.rs_hmi.config.Config)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Config::CopyFrom(const Config& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.rs_hmi.config.Config)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Config::IsInitialized() const {
  return true;
}

void Config::InternalSwap(Config* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Config, hmi_socket_task_config_)
      + sizeof(Config::hmi_socket_task_config_)
      - PROTOBUF_FIELD_OFFSET(Config, render_config_)>(
          reinterpret_cast<char*>(&render_config_),
          reinterpret_cast<char*>(&other->render_config_));
  swap(enable_websocket_buffer_check_, other->enable_websocket_buffer_check_);
  swap(websocket_buffer_check_timeout_th_ms_, other->websocket_buffer_check_timeout_th_ms_);
}

::PROTOBUF_NAMESPACE_ID::Metadata Config::GetMetadata() const {
  return GetMetadataStatic();
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace config
}  // namespace rs_hmi
}  // namespace robosense
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::robosense::rs_hmi::config::WebsocketEndPointConfig* Arena::CreateMaybeMessage< ::robosense::rs_hmi::config::WebsocketEndPointConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::rs_hmi::config::WebsocketEndPointConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::rs_hmi::config::UdpControlConfig* Arena::CreateMaybeMessage< ::robosense::rs_hmi::config::UdpControlConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::rs_hmi::config::UdpControlConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::rs_hmi::config::UdpBufferControlConfig* Arena::CreateMaybeMessage< ::robosense::rs_hmi::config::UdpBufferControlConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::rs_hmi::config::UdpBufferControlConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::rs_hmi::config::UdpMulticastEndPointConfig* Arena::CreateMaybeMessage< ::robosense::rs_hmi::config::UdpMulticastEndPointConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::rs_hmi::config::UdpMulticastEndPointConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::rs_hmi::config::UdpP2PEndPointConfig* Arena::CreateMaybeMessage< ::robosense::rs_hmi::config::UdpP2PEndPointConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::rs_hmi::config::UdpP2PEndPointConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::rs_hmi::config::WebsocketSendControlConfig* Arena::CreateMaybeMessage< ::robosense::rs_hmi::config::WebsocketSendControlConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::rs_hmi::config::WebsocketSendControlConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::rs_hmi::config::WebsocketTaskConfig* Arena::CreateMaybeMessage< ::robosense::rs_hmi::config::WebsocketTaskConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::rs_hmi::config::WebsocketTaskConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::rs_hmi::config::UdpMulticastTaskConfig* Arena::CreateMaybeMessage< ::robosense::rs_hmi::config::UdpMulticastTaskConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::rs_hmi::config::UdpMulticastTaskConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::rs_hmi::config::UdpP2PTaskConfig* Arena::CreateMaybeMessage< ::robosense::rs_hmi::config::UdpP2PTaskConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::rs_hmi::config::UdpP2PTaskConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::rs_hmi::config::SocketTaskConfig* Arena::CreateMaybeMessage< ::robosense::rs_hmi::config::SocketTaskConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::rs_hmi::config::SocketTaskConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::rs_hmi::config::HmiSocketTaskConfig* Arena::CreateMaybeMessage< ::robosense::rs_hmi::config::HmiSocketTaskConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::rs_hmi::config::HmiSocketTaskConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::rs_hmi::config::HmiSocketTopicTaskConfig* Arena::CreateMaybeMessage< ::robosense::rs_hmi::config::HmiSocketTopicTaskConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::rs_hmi::config::HmiSocketTopicTaskConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::rs_hmi::config::HmiTaskSocketConfig* Arena::CreateMaybeMessage< ::robosense::rs_hmi::config::HmiTaskSocketConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::rs_hmi::config::HmiTaskSocketConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::rs_hmi::config::WebsocketRenderTaskConfig* Arena::CreateMaybeMessage< ::robosense::rs_hmi::config::WebsocketRenderTaskConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::rs_hmi::config::WebsocketRenderTaskConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::rs_hmi::config::Config* Arena::CreateMaybeMessage< ::robosense::rs_hmi::config::Config >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::rs_hmi::config::Config >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
