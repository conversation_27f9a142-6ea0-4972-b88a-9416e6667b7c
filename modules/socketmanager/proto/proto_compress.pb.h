// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto_compress.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_proto_5fcompress_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_proto_5fcompress_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3014000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3014000 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_proto_5fcompress_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_proto_5fcompress_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_5fcompress_2eproto;
PROTOBUF_NAMESPACE_OPEN
PROTOBUF_NAMESPACE_CLOSE
namespace robosense {
namespace proto_compress_msgs {

enum RS_POST_DATA_COMPRESSION_FORMAT : int {
  RS_POST_DATA_COMPRESSION_NOTHING = 1,
  RS_POST_DATA_COMPRESSION_GZIP = 2,
  RS_POST_DATA_COMPRESSION_ZLIB = 3,
  RS_POST_DATA_COMPRESSION_LZ4 = 4,
  RS_POST_DATA_COMPRESSION_ZSTD = 5
};
bool RS_POST_DATA_COMPRESSION_FORMAT_IsValid(int value);
constexpr RS_POST_DATA_COMPRESSION_FORMAT RS_POST_DATA_COMPRESSION_FORMAT_MIN = RS_POST_DATA_COMPRESSION_NOTHING;
constexpr RS_POST_DATA_COMPRESSION_FORMAT RS_POST_DATA_COMPRESSION_FORMAT_MAX = RS_POST_DATA_COMPRESSION_ZSTD;
constexpr int RS_POST_DATA_COMPRESSION_FORMAT_ARRAYSIZE = RS_POST_DATA_COMPRESSION_FORMAT_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RS_POST_DATA_COMPRESSION_FORMAT_descriptor();
template<typename T>
inline const std::string& RS_POST_DATA_COMPRESSION_FORMAT_Name(T enum_t_value) {
  static_assert(::std::is_same<T, RS_POST_DATA_COMPRESSION_FORMAT>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function RS_POST_DATA_COMPRESSION_FORMAT_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    RS_POST_DATA_COMPRESSION_FORMAT_descriptor(), enum_t_value);
}
inline bool RS_POST_DATA_COMPRESSION_FORMAT_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, RS_POST_DATA_COMPRESSION_FORMAT* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<RS_POST_DATA_COMPRESSION_FORMAT>(
    RS_POST_DATA_COMPRESSION_FORMAT_descriptor(), name, value);
}
// ===================================================================


// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace proto_compress_msgs
}  // namespace robosense

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT>() {
  return ::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_proto_5fcompress_2eproto
