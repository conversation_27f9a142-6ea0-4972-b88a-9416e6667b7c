// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto_compress.proto

#include "proto_compress.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
namespace robosense {
namespace proto_compress_msgs {
}  // namespace proto_compress_msgs
}  // namespace robosense
static constexpr ::PROTOBUF_NAMESPACE_ID::Metadata* file_level_metadata_proto_5fcompress_2eproto = nullptr;
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_proto_5fcompress_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_proto_5fcompress_2eproto = nullptr;
const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_proto_5fcompress_2eproto::offsets[1] = {};
static constexpr ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema* schemas = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::Message* const* file_default_instances = nullptr;

const char descriptor_table_protodef_proto_5fcompress_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\024proto_compress.proto\022\035robosense.proto_"
  "compress_msgs*\322\001\n\037RS_POST_DATA_COMPRESSI"
  "ON_FORMAT\022$\n RS_POST_DATA_COMPRESSION_NO"
  "THING\020\001\022!\n\035RS_POST_DATA_COMPRESSION_GZIP"
  "\020\002\022!\n\035RS_POST_DATA_COMPRESSION_ZLIB\020\003\022 \n"
  "\034RS_POST_DATA_COMPRESSION_LZ4\020\004\022!\n\035RS_PO"
  "ST_DATA_COMPRESSION_ZSTD\020\005"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_proto_5fcompress_2eproto_deps[1] = {
};
static ::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase*const descriptor_table_proto_5fcompress_2eproto_sccs[1] = {
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_proto_5fcompress_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_5fcompress_2eproto = {
  false, false, descriptor_table_protodef_proto_5fcompress_2eproto, "proto_compress.proto", 266,
  &descriptor_table_proto_5fcompress_2eproto_once, descriptor_table_proto_5fcompress_2eproto_sccs, descriptor_table_proto_5fcompress_2eproto_deps, 0, 0,
  schemas, file_default_instances, TableStruct_proto_5fcompress_2eproto::offsets,
  file_level_metadata_proto_5fcompress_2eproto, 0, file_level_enum_descriptors_proto_5fcompress_2eproto, file_level_service_descriptors_proto_5fcompress_2eproto,
};

// Force running AddDescriptors() at dynamic initialization time.
static bool dynamic_init_dummy_proto_5fcompress_2eproto = (static_cast<void>(::PROTOBUF_NAMESPACE_ID::internal::AddDescriptors(&descriptor_table_proto_5fcompress_2eproto)), true);
namespace robosense {
namespace proto_compress_msgs {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RS_POST_DATA_COMPRESSION_FORMAT_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_proto_5fcompress_2eproto);
  return file_level_enum_descriptors_proto_5fcompress_2eproto[0];
}
bool RS_POST_DATA_COMPRESSION_FORMAT_IsValid(int value) {
  switch (value) {
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
      return true;
    default:
      return false;
  }
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace proto_compress_msgs
}  // namespace robosense
PROTOBUF_NAMESPACE_OPEN
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
