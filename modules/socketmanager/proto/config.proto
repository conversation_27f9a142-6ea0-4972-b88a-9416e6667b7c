syntax = "proto2"; 

import "proto_compress.proto";
package robosense.rs_hmi.config; 

enum RS_SOCKET_ROLE_TYPE
{
    RS_WEBSOCKET_SERVER_ROLE = 1; 
    RS_WEBSOCKET_CLIENT_ROLE = 2; 
    RS_UDP_MULTICAST_SENDER_ROLE   = 3; 
    RS_UDP_MULTICAST_RECEIVER_ROLE = 4;
    RS_UDP_P2P_SENDER_ROLE = 5; 
    RS_UDP_P2P_RECEIVER_ROLE = 6;
    RS_UDP_P2P_BOTH_ROLE = 7; 
} 

message WebsocketEndPointConfig {
    optional RS_SOCKET_ROLE_TYPE    role_type                = 1; 
    optional string                 server_ip                = 2[default = "0.0.0.0"]; 
    optional uint32                 server_port              = 3[default = 8080]; 
    optional uint32                 run_thread_cnt           = 4[default = 4]; 
    optional uint32                 send_thread_cnt          = 5[default = 4]; 
    optional uint32                 server_client_timeout_ms = 6[default = 20000]; 
    optional uint32                 server_check_timeout_ms  = 7[default = 3000]; 
    optional robosense.proto_compress_msgs.RS_POST_DATA_COMPRESSION_FORMAT compress_format = 8[default = RS_POST_DATA_COMPRESSION_NOTHING]; 
    optional uint32                                                        compress_level  = 9[default = 9];
}

enum RS_UDP_CONTROL_MODE_TYPE {
    RS_UDP_CONTROL_NOTHING            = 1; 
    RS_UDP_CONTROL_TOTAL_CONTROL_TIME = 2; 
    RS_UDP_CONTROL_DATA_CONTROL_TIME  = 3; 
}

enum RS_UDP_BUFFER_CONTROL_MODE_TYPE {
    RS_UDP_BUFFER_CONTROL_NOTHING = 1; 
    RS_UDP_BUFFER_CONTROL_ENABLE  = 2; 
}

message UdpControlConfig {
    optional RS_UDP_CONTROL_MODE_TYPE udp_control_type                 = 1[default = RS_UDP_CONTROL_TOTAL_CONTROL_TIME]; 
    optional uint32                   udp_total_control_time_ms        = 2[default = 60]; 
    optional uint32                   udp_total_control_single_time_ms = 3[default = 2]; 
    optional uint32                   udp_data_control_size            = 4[default = 262144]; 
    optional uint32                   udp_data_control_time_ms         = 5[default = 2]; 
}

message UdpBufferControlConfig {
    optional RS_UDP_BUFFER_CONTROL_MODE_TYPE udp_buffer_control_type       = 1[default = RS_UDP_BUFFER_CONTROL_ENABLE]; 
    optional uint32                          udp_dynamic_single_control_th = 2[default = 3]; 
    optional uint32                          udp_static_single_control_th  = 3[default = 3];
    optional uint32                          udp_combine_control_th        = 4[default = 6]; 
    optional uint32                          udp_buffer_control_send_frame_gap = 5[default = 2];  
}

message UdpMulticastEndPointConfig {
    optional RS_SOCKET_ROLE_TYPE role_type        = 1; 
    optional string   multicast_ip                = 2[default = "***********"]; 
    optional uint32   multicast_port              = 3[default = 9096];
    optional string   multicast_host_ip           = 4[default = "***********"];  
    optional uint32   max_msg_size                = 5[default = 4096];  
    optional uint32   buffer_cell_size            = 6[default = 128]; 
    optional uint32   asio_run_thread_cnt         = 7[default = 1]; 
    optional uint32   asio_snd_thread_cnt         = 8[default = 1]; 
    optional uint32   asio_recv_thread_cnt        = 9[default = 1]; 
    optional uint32   msg_timeout_th_ms           = 10[default = 100]; 
    optional uint32   asio_snd_retry_cnt          = 11[default = 1]; 
    optional uint32   asio_snd_retry_th_ms        = 12[default = 5]; 
    optional UdpControlConfig udp_control_config  = 13;  
    optional robosense.proto_compress_msgs.RS_POST_DATA_COMPRESSION_FORMAT compress_format = 14[default = RS_POST_DATA_COMPRESSION_NOTHING]; 
    optional uint32                                                        compress_level  = 15[default = 9];
}

message UdpP2PEndPointConfig {
    optional RS_SOCKET_ROLE_TYPE role_type        = 1; 
    optional string   remote_ip                   = 2; 
    optional uint32   remote_port                 = 3[default = 9097];  
    optional uint32   max_msg_size                = 5[default = 4096];  
    optional uint32   buffer_cell_size            = 6[default = 128]; 
    optional uint32   asio_run_thread_cnt         = 7[default = 1]; 
    optional uint32   asio_snd_thread_cnt         = 8[default = 1]; 
    optional uint32   asio_recv_thread_cnt        = 9[default = 1]; 
    optional uint32   msg_timeout_th_ms           = 10[default = 100]; 
    optional uint32   asio_snd_retry_cnt          = 11[default = 1]; 
    optional uint32   asio_snd_retry_th_ms        = 12[default = 5];    
    optional uint32   socket_buffer_size          = 13[default = 262144]; 
    optional UdpControlConfig udp_control_config  = 14;  
    optional robosense.proto_compress_msgs.RS_POST_DATA_COMPRESSION_FORMAT compress_format = 15[default = RS_POST_DATA_COMPRESSION_NOTHING]; 
    optional uint32                                                        compress_level  = 16[default = 9];
}

enum RS_HMI_TASK_TYPE 
{
    RS_HMI_TASK_CARAPP        = 1; 
    RS_HMI_TASK_RENDER        = 2; 
    RS_HMI_TASK_SYSTEMSTATUS  = 3; 
    RS_HMI_TASK_PNCAPP        = 4; 
    RS_HMI_TASK_EGOCARSTATUS  = 5; 
    RS_HMI_TASK_FAULTREPORTER = 6; 
    RS_HMI_TASK_VERSION       = 7;
    RS_HMI_TASK_FREQUENCE     = 8; 
    RS_HMI_TASK_AUDIO         = 9; 
    RS_HMI_TASK_RTK_TIEM_SYNC = 10; 
    RS_HMI_TASK_PREFABRICATEDROUTE = 11;
    RS_HMI_TASK_LOCALIZATION = 12; 
    RS_HMI_TASK_REMOTE_CONTROL = 13; 
    RS_HMI_TASK_QUICK_DATA     = 14; 
}

enum RS_HD_MAP_TRANSFORM_LEVEL 
{
    RS_HD_MAP_TRANSFORM_BASIC = 1; 
    RS_HD_MAP_TRANSFORM_FULL  = 2; 
}

message WebsocketSendControlConfig {
    optional string message_type             = 1; 
    optional bool         enable_loss        = 2[default = true];
    optional uint32  loss_buffer_size        = 3[default = 10]; 
    optional uint32  loss_gap_size           = 4[default = 2]; 
    optional bool         enable_loss2       = 5[default = true];
    optional uint32  loss_buffer_size2       = 6[default = 15]; 
    optional uint32  loss_gap_size2          = 7[default = 3]; 
    optional bool        enable_clear        = 8[default = true];
    optional uint32 clear_buffer_size        = 9[default = 20];  
}

message WebsocketTaskConfig {
    optional string                             task_socket_key = 1;
    optional WebsocketEndPointConfig websocket_endpoint         = 2;  
    repeated WebsocketSendControlConfig websocket_send_controls = 3;  
}

message UdpMulticastTaskConfig {
    optional string                            task_socket_key = 1; 
    optional UdpMulticastEndPointConfig udp_multicast_endpoint = 2; 
}

message UdpP2PTaskConfig {
    optional string                task_socket_key = 1; 
    optional UdpP2PEndPointConfig udp_p2p_endpoint = 2; 
}

message SocketTaskConfig {
    oneof socket_endpoint_config {
        WebsocketTaskConfig websocket_task = 1; 
        UdpMulticastTaskConfig udp_multicast_task = 2; 
        UdpP2PTaskConfig udp_p2p_task = 3; 
    }
}

message HmiSocketTaskConfig {
    repeated SocketTaskConfig socket_task_configs = 1; 
}

message HmiSocketTopicTaskConfig {
    repeated string support_message_topics  = 1; 
    optional string task_socket_key         = 2;  
} 

message HmiTaskSocketConfig {
    optional bool                             enable_task = 1 [default = false]; 
    repeated HmiSocketTopicTaskConfig task_socket_configs = 2; 
}

message WebsocketRenderTaskConfig {
    optional HmiTaskSocketConfig websocket_task         = 1;
    optional bool   enable_websocket_buffer             = 2 [default = true]; 
    optional uint32 websocket_buffer_size               = 3 [default = 2097152]; 
    optional uint32 low_websocket_buffer_size           = 4 [default = 524288]; 
    optional string websocket_cmd_socket_key            = 5; 
    optional string websocket_socket_key                = 6; 

    optional string udp_multicast_socket_key            = 7; 
    optional string udp_double_multicast_socket_key     = 8; 
    optional string udp_p2p_socket_key                  = 9; 
    optional uint32 udp_2_sample_1_buffer_size          = 10[default = 3]; 
    optional uint32 udp_3_sample_1_buffer_size          = 11[default = 5]; 
    optional uint32 udp_loss_all_buffer_size            = 12[default = 10]; 
    optional uint32 max_send_render_buffer_size         = 13[default = 15]; 
    optional UdpMulticastEndPointConfig          udp_multicast_render_config        = 14; 
    optional UdpP2PEndPointConfig                udp_p2p_render_config              = 15; 
    optional UdpMulticastEndPointConfig          double_udp_multicast_render_config = 16; 
}


message Config {
    optional bool                    enable_websocket_buffer_check = 1[default = true]; 
    optional uint32           websocket_buffer_check_timeout_th_ms = 2[default = 2000]; 
    optional WebsocketRenderTaskConfig               render_config = 4; 
    optional HmiSocketTaskConfig                   hmi_socket_task_config = 17; 
}
