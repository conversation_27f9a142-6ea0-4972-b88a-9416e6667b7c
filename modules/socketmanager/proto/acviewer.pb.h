// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: acviewer.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_acviewer_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_acviewer_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3014000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3014000 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_acviewer_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_acviewer_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[22]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_acviewer_2eproto;
namespace robosense {
namespace acviewer_msgs {
class RSDepthImage;
class RSDepthImageDefaultTypeInternal;
extern RSDepthImageDefaultTypeInternal _RSDepthImage_default_instance_;
class RSDeviceInfo;
class RSDeviceInfoDefaultTypeInternal;
extern RSDeviceInfoDefaultTypeInternal _RSDeviceInfo_default_instance_;
class RSOperatorDeviceData;
class RSOperatorDeviceDataDefaultTypeInternal;
extern RSOperatorDeviceDataDefaultTypeInternal _RSOperatorDeviceData_default_instance_;
class RSPointCloud;
class RSPointCloudDefaultTypeInternal;
extern RSPointCloudDefaultTypeInternal _RSPointCloud_default_instance_;
class RSPositon;
class RSPositonDefaultTypeInternal;
extern RSPositonDefaultTypeInternal _RSPositon_default_instance_;
class RSReaderConfig;
class RSReaderConfigDefaultTypeInternal;
extern RSReaderConfigDefaultTypeInternal _RSReaderConfig_default_instance_;
class RSReaderProgress;
class RSReaderProgressDefaultTypeInternal;
extern RSReaderProgressDefaultTypeInternal _RSReaderProgress_default_instance_;
class RSReaderSetting;
class RSReaderSettingDefaultTypeInternal;
extern RSReaderSettingDefaultTypeInternal _RSReaderSetting_default_instance_;
class RSRender;
class RSRenderDefaultTypeInternal;
extern RSRenderDefaultTypeInternal _RSRender_default_instance_;
class RSRequest;
class RSRequestDefaultTypeInternal;
extern RSRequestDefaultTypeInternal _RSRequest_default_instance_;
class RSRequestData;
class RSRequestDataDefaultTypeInternal;
extern RSRequestDataDefaultTypeInternal _RSRequestData_default_instance_;
class RSResponse;
class RSResponseDefaultTypeInternal;
extern RSResponseDefaultTypeInternal _RSResponse_default_instance_;
class RSResponseData;
class RSResponseDataDefaultTypeInternal;
extern RSResponseDataDefaultTypeInternal _RSResponseData_default_instance_;
class RSRgbJpeg;
class RSRgbJpegDefaultTypeInternal;
extern RSRgbJpegDefaultTypeInternal _RSRgbJpeg_default_instance_;
class RSShowConfigData;
class RSShowConfigDataDefaultTypeInternal;
extern RSShowConfigDataDefaultTypeInternal _RSShowConfigData_default_instance_;
class RSTopicConfig;
class RSTopicConfigDefaultTypeInternal;
extern RSTopicConfigDefaultTypeInternal _RSTopicConfig_default_instance_;
class RSTopicSetting;
class RSTopicSettingDefaultTypeInternal;
extern RSTopicSettingDefaultTypeInternal _RSTopicSetting_default_instance_;
class RSTrangleFacet;
class RSTrangleFacetDefaultTypeInternal;
extern RSTrangleFacetDefaultTypeInternal _RSTrangleFacet_default_instance_;
class RSWriterConfig;
class RSWriterConfigDefaultTypeInternal;
extern RSWriterConfigDefaultTypeInternal _RSWriterConfig_default_instance_;
class RSWriterSetting;
class RSWriterSettingDefaultTypeInternal;
extern RSWriterSettingDefaultTypeInternal _RSWriterSetting_default_instance_;
class RenderSwitch;
class RenderSwitchDefaultTypeInternal;
extern RenderSwitchDefaultTypeInternal _RenderSwitch_default_instance_;
class UdpControlConfig;
class UdpControlConfigDefaultTypeInternal;
extern UdpControlConfigDefaultTypeInternal _UdpControlConfig_default_instance_;
}  // namespace acviewer_msgs
}  // namespace robosense
PROTOBUF_NAMESPACE_OPEN
template<> ::robosense::acviewer_msgs::RSDepthImage* Arena::CreateMaybeMessage<::robosense::acviewer_msgs::RSDepthImage>(Arena*);
template<> ::robosense::acviewer_msgs::RSDeviceInfo* Arena::CreateMaybeMessage<::robosense::acviewer_msgs::RSDeviceInfo>(Arena*);
template<> ::robosense::acviewer_msgs::RSOperatorDeviceData* Arena::CreateMaybeMessage<::robosense::acviewer_msgs::RSOperatorDeviceData>(Arena*);
template<> ::robosense::acviewer_msgs::RSPointCloud* Arena::CreateMaybeMessage<::robosense::acviewer_msgs::RSPointCloud>(Arena*);
template<> ::robosense::acviewer_msgs::RSPositon* Arena::CreateMaybeMessage<::robosense::acviewer_msgs::RSPositon>(Arena*);
template<> ::robosense::acviewer_msgs::RSReaderConfig* Arena::CreateMaybeMessage<::robosense::acviewer_msgs::RSReaderConfig>(Arena*);
template<> ::robosense::acviewer_msgs::RSReaderProgress* Arena::CreateMaybeMessage<::robosense::acviewer_msgs::RSReaderProgress>(Arena*);
template<> ::robosense::acviewer_msgs::RSReaderSetting* Arena::CreateMaybeMessage<::robosense::acviewer_msgs::RSReaderSetting>(Arena*);
template<> ::robosense::acviewer_msgs::RSRender* Arena::CreateMaybeMessage<::robosense::acviewer_msgs::RSRender>(Arena*);
template<> ::robosense::acviewer_msgs::RSRequest* Arena::CreateMaybeMessage<::robosense::acviewer_msgs::RSRequest>(Arena*);
template<> ::robosense::acviewer_msgs::RSRequestData* Arena::CreateMaybeMessage<::robosense::acviewer_msgs::RSRequestData>(Arena*);
template<> ::robosense::acviewer_msgs::RSResponse* Arena::CreateMaybeMessage<::robosense::acviewer_msgs::RSResponse>(Arena*);
template<> ::robosense::acviewer_msgs::RSResponseData* Arena::CreateMaybeMessage<::robosense::acviewer_msgs::RSResponseData>(Arena*);
template<> ::robosense::acviewer_msgs::RSRgbJpeg* Arena::CreateMaybeMessage<::robosense::acviewer_msgs::RSRgbJpeg>(Arena*);
template<> ::robosense::acviewer_msgs::RSShowConfigData* Arena::CreateMaybeMessage<::robosense::acviewer_msgs::RSShowConfigData>(Arena*);
template<> ::robosense::acviewer_msgs::RSTopicConfig* Arena::CreateMaybeMessage<::robosense::acviewer_msgs::RSTopicConfig>(Arena*);
template<> ::robosense::acviewer_msgs::RSTopicSetting* Arena::CreateMaybeMessage<::robosense::acviewer_msgs::RSTopicSetting>(Arena*);
template<> ::robosense::acviewer_msgs::RSTrangleFacet* Arena::CreateMaybeMessage<::robosense::acviewer_msgs::RSTrangleFacet>(Arena*);
template<> ::robosense::acviewer_msgs::RSWriterConfig* Arena::CreateMaybeMessage<::robosense::acviewer_msgs::RSWriterConfig>(Arena*);
template<> ::robosense::acviewer_msgs::RSWriterSetting* Arena::CreateMaybeMessage<::robosense::acviewer_msgs::RSWriterSetting>(Arena*);
template<> ::robosense::acviewer_msgs::RenderSwitch* Arena::CreateMaybeMessage<::robosense::acviewer_msgs::RenderSwitch>(Arena*);
template<> ::robosense::acviewer_msgs::UdpControlConfig* Arena::CreateMaybeMessage<::robosense::acviewer_msgs::UdpControlConfig>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace robosense {
namespace acviewer_msgs {

enum RS_CMD_TYPE : int {
  RS_HEART_BEAT = 0,
  RS_SET_RUN_MODE = 1,
  RS_SET_SHOW_CONFIG = 2,
  RS_GET_DEVICE_INFO = 3,
  RS_OPERATOR_DEVICE = 4,
  RS_GET_WRITER_CONFIG = 5,
  RS_START_WRITER = 6,
  RS_STOP_WRITER = 7,
  RS_GET_FILE_LIST = 8,
  RS_GET_FILE_TOPICS = 9,
  RS_START_READER = 10,
  RS_LOADING_PROGRESS = 11,
  RS_GET_READER_PROGRESS = 12,
  RS_SKIP_FRAME = 13,
  RS_PLAY = 14,
  RS_PAUSE = 15,
  RS_STOP_READER = 16
};
bool RS_CMD_TYPE_IsValid(int value);
constexpr RS_CMD_TYPE RS_CMD_TYPE_MIN = RS_HEART_BEAT;
constexpr RS_CMD_TYPE RS_CMD_TYPE_MAX = RS_STOP_READER;
constexpr int RS_CMD_TYPE_ARRAYSIZE = RS_CMD_TYPE_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RS_CMD_TYPE_descriptor();
template<typename T>
inline const std::string& RS_CMD_TYPE_Name(T enum_t_value) {
  static_assert(::std::is_same<T, RS_CMD_TYPE>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function RS_CMD_TYPE_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    RS_CMD_TYPE_descriptor(), enum_t_value);
}
inline bool RS_CMD_TYPE_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, RS_CMD_TYPE* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<RS_CMD_TYPE>(
    RS_CMD_TYPE_descriptor(), name, value);
}
enum RS_RENDER_COMMUNICATION_TYPE : int {
  RS_RENDER_COMM_WEBSOCKET = 1,
  RS_RENDER_COMM_UDP_MULTICAST = 2,
  RS_RENDER_COMM_DOUBLE_UDP_MULTICAST = 3,
  RS_RENDER_COMM_UDP_P2P = 4
};
bool RS_RENDER_COMMUNICATION_TYPE_IsValid(int value);
constexpr RS_RENDER_COMMUNICATION_TYPE RS_RENDER_COMMUNICATION_TYPE_MIN = RS_RENDER_COMM_WEBSOCKET;
constexpr RS_RENDER_COMMUNICATION_TYPE RS_RENDER_COMMUNICATION_TYPE_MAX = RS_RENDER_COMM_UDP_P2P;
constexpr int RS_RENDER_COMMUNICATION_TYPE_ARRAYSIZE = RS_RENDER_COMMUNICATION_TYPE_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RS_RENDER_COMMUNICATION_TYPE_descriptor();
template<typename T>
inline const std::string& RS_RENDER_COMMUNICATION_TYPE_Name(T enum_t_value) {
  static_assert(::std::is_same<T, RS_RENDER_COMMUNICATION_TYPE>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function RS_RENDER_COMMUNICATION_TYPE_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    RS_RENDER_COMMUNICATION_TYPE_descriptor(), enum_t_value);
}
inline bool RS_RENDER_COMMUNICATION_TYPE_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, RS_RENDER_COMMUNICATION_TYPE* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<RS_RENDER_COMMUNICATION_TYPE>(
    RS_RENDER_COMMUNICATION_TYPE_descriptor(), name, value);
}
enum RS_UDP_CONTROL_MODE_TYPE : int {
  RS_UDP_CONTROL_NOTHING = 1,
  RS_UDP_CONTROL_TOTAL_CONTROL_TIME = 2,
  RS_UDP_CONTROL_DATA_CONTROL_TIME = 3
};
bool RS_UDP_CONTROL_MODE_TYPE_IsValid(int value);
constexpr RS_UDP_CONTROL_MODE_TYPE RS_UDP_CONTROL_MODE_TYPE_MIN = RS_UDP_CONTROL_NOTHING;
constexpr RS_UDP_CONTROL_MODE_TYPE RS_UDP_CONTROL_MODE_TYPE_MAX = RS_UDP_CONTROL_DATA_CONTROL_TIME;
constexpr int RS_UDP_CONTROL_MODE_TYPE_ARRAYSIZE = RS_UDP_CONTROL_MODE_TYPE_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RS_UDP_CONTROL_MODE_TYPE_descriptor();
template<typename T>
inline const std::string& RS_UDP_CONTROL_MODE_TYPE_Name(T enum_t_value) {
  static_assert(::std::is_same<T, RS_UDP_CONTROL_MODE_TYPE>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function RS_UDP_CONTROL_MODE_TYPE_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    RS_UDP_CONTROL_MODE_TYPE_descriptor(), enum_t_value);
}
inline bool RS_UDP_CONTROL_MODE_TYPE_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, RS_UDP_CONTROL_MODE_TYPE* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<RS_UDP_CONTROL_MODE_TYPE>(
    RS_UDP_CONTROL_MODE_TYPE_descriptor(), name, value);
}
enum RS_UDP_BUFFER_CONTROL_MODE_TYPE : int {
  RS_UDP_BUFFER_CONTROL_NOTHING = 1,
  RS_UDP_BUFFER_CONTROL_ENABLE = 2
};
bool RS_UDP_BUFFER_CONTROL_MODE_TYPE_IsValid(int value);
constexpr RS_UDP_BUFFER_CONTROL_MODE_TYPE RS_UDP_BUFFER_CONTROL_MODE_TYPE_MIN = RS_UDP_BUFFER_CONTROL_NOTHING;
constexpr RS_UDP_BUFFER_CONTROL_MODE_TYPE RS_UDP_BUFFER_CONTROL_MODE_TYPE_MAX = RS_UDP_BUFFER_CONTROL_ENABLE;
constexpr int RS_UDP_BUFFER_CONTROL_MODE_TYPE_ARRAYSIZE = RS_UDP_BUFFER_CONTROL_MODE_TYPE_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RS_UDP_BUFFER_CONTROL_MODE_TYPE_descriptor();
template<typename T>
inline const std::string& RS_UDP_BUFFER_CONTROL_MODE_TYPE_Name(T enum_t_value) {
  static_assert(::std::is_same<T, RS_UDP_BUFFER_CONTROL_MODE_TYPE>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function RS_UDP_BUFFER_CONTROL_MODE_TYPE_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    RS_UDP_BUFFER_CONTROL_MODE_TYPE_descriptor(), enum_t_value);
}
inline bool RS_UDP_BUFFER_CONTROL_MODE_TYPE_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, RS_UDP_BUFFER_CONTROL_MODE_TYPE* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<RS_UDP_BUFFER_CONTROL_MODE_TYPE>(
    RS_UDP_BUFFER_CONTROL_MODE_TYPE_descriptor(), name, value);
}
// ===================================================================

class RSRequest PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.acviewer_msgs.RSRequest) */ {
 public:
  inline RSRequest() : RSRequest(nullptr) {}
  virtual ~RSRequest();

  RSRequest(const RSRequest& from);
  RSRequest(RSRequest&& from) noexcept
    : RSRequest() {
    *this = ::std::move(from);
  }

  inline RSRequest& operator=(const RSRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline RSRequest& operator=(RSRequest&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RSRequest& default_instance();

  static inline const RSRequest* internal_default_instance() {
    return reinterpret_cast<const RSRequest*>(
               &_RSRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(RSRequest& a, RSRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(RSRequest* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RSRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RSRequest* New() const final {
    return CreateMaybeMessage<RSRequest>(nullptr);
  }

  RSRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RSRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RSRequest& from);
  void MergeFrom(const RSRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RSRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.acviewer_msgs.RSRequest";
  }
  protected:
  explicit RSRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_acviewer_2eproto);
    return ::descriptor_table_acviewer_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRequestDataFieldNumber = 3,
    kRequestIdFieldNumber = 2,
    kCmdTypeFieldNumber = 1,
  };
  // optional .robosense.acviewer_msgs.RSRequestData request_data = 3;
  bool has_request_data() const;
  private:
  bool _internal_has_request_data() const;
  public:
  void clear_request_data();
  const ::robosense::acviewer_msgs::RSRequestData& request_data() const;
  ::robosense::acviewer_msgs::RSRequestData* release_request_data();
  ::robosense::acviewer_msgs::RSRequestData* mutable_request_data();
  void set_allocated_request_data(::robosense::acviewer_msgs::RSRequestData* request_data);
  private:
  const ::robosense::acviewer_msgs::RSRequestData& _internal_request_data() const;
  ::robosense::acviewer_msgs::RSRequestData* _internal_mutable_request_data();
  public:
  void unsafe_arena_set_allocated_request_data(
      ::robosense::acviewer_msgs::RSRequestData* request_data);
  ::robosense::acviewer_msgs::RSRequestData* unsafe_arena_release_request_data();

  // required uint64 request_Id = 2;
  bool has_request_id() const;
  private:
  bool _internal_has_request_id() const;
  public:
  void clear_request_id();
  ::PROTOBUF_NAMESPACE_ID::uint64 request_id() const;
  void set_request_id(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_request_id() const;
  void _internal_set_request_id(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // required .robosense.acviewer_msgs.RS_CMD_TYPE cmd_type = 1;
  bool has_cmd_type() const;
  private:
  bool _internal_has_cmd_type() const;
  public:
  void clear_cmd_type();
  ::robosense::acviewer_msgs::RS_CMD_TYPE cmd_type() const;
  void set_cmd_type(::robosense::acviewer_msgs::RS_CMD_TYPE value);
  private:
  ::robosense::acviewer_msgs::RS_CMD_TYPE _internal_cmd_type() const;
  void _internal_set_cmd_type(::robosense::acviewer_msgs::RS_CMD_TYPE value);
  public:

  // @@protoc_insertion_point(class_scope:robosense.acviewer_msgs.RSRequest)
 private:
  class _Internal;

  // helper for ByteSizeLong()
  size_t RequiredFieldsByteSizeFallback() const;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::robosense::acviewer_msgs::RSRequestData* request_data_;
  ::PROTOBUF_NAMESPACE_ID::uint64 request_id_;
  int cmd_type_;
  friend struct ::TableStruct_acviewer_2eproto;
};
// -------------------------------------------------------------------

class RSShowConfigData PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.acviewer_msgs.RSShowConfigData) */ {
 public:
  inline RSShowConfigData() : RSShowConfigData(nullptr) {}
  virtual ~RSShowConfigData();

  RSShowConfigData(const RSShowConfigData& from);
  RSShowConfigData(RSShowConfigData&& from) noexcept
    : RSShowConfigData() {
    *this = ::std::move(from);
  }

  inline RSShowConfigData& operator=(const RSShowConfigData& from) {
    CopyFrom(from);
    return *this;
  }
  inline RSShowConfigData& operator=(RSShowConfigData&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RSShowConfigData& default_instance();

  static inline const RSShowConfigData* internal_default_instance() {
    return reinterpret_cast<const RSShowConfigData*>(
               &_RSShowConfigData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(RSShowConfigData& a, RSShowConfigData& b) {
    a.Swap(&b);
  }
  inline void Swap(RSShowConfigData* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RSShowConfigData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RSShowConfigData* New() const final {
    return CreateMaybeMessage<RSShowConfigData>(nullptr);
  }

  RSShowConfigData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RSShowConfigData>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RSShowConfigData& from);
  void MergeFrom(const RSShowConfigData& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RSShowConfigData* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.acviewer_msgs.RSShowConfigData";
  }
  protected:
  explicit RSShowConfigData(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_acviewer_2eproto);
    return ::descriptor_table_acviewer_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kShowPosFieldNumber = 1,
    kShowPcFieldNumber = 2,
    kShowPcSlamFieldNumber = 3,
    kShowTriFieldNumber = 4,
    kShowRgbFieldNumber = 5,
    kShowDepthFieldNumber = 6,
  };
  // optional bool show_pos = 1;
  bool has_show_pos() const;
  private:
  bool _internal_has_show_pos() const;
  public:
  void clear_show_pos();
  bool show_pos() const;
  void set_show_pos(bool value);
  private:
  bool _internal_show_pos() const;
  void _internal_set_show_pos(bool value);
  public:

  // optional bool show_pc = 2;
  bool has_show_pc() const;
  private:
  bool _internal_has_show_pc() const;
  public:
  void clear_show_pc();
  bool show_pc() const;
  void set_show_pc(bool value);
  private:
  bool _internal_show_pc() const;
  void _internal_set_show_pc(bool value);
  public:

  // optional bool show_pc_slam = 3;
  bool has_show_pc_slam() const;
  private:
  bool _internal_has_show_pc_slam() const;
  public:
  void clear_show_pc_slam();
  bool show_pc_slam() const;
  void set_show_pc_slam(bool value);
  private:
  bool _internal_show_pc_slam() const;
  void _internal_set_show_pc_slam(bool value);
  public:

  // optional bool show_tri = 4;
  bool has_show_tri() const;
  private:
  bool _internal_has_show_tri() const;
  public:
  void clear_show_tri();
  bool show_tri() const;
  void set_show_tri(bool value);
  private:
  bool _internal_show_tri() const;
  void _internal_set_show_tri(bool value);
  public:

  // optional bool show_rgb = 5;
  bool has_show_rgb() const;
  private:
  bool _internal_has_show_rgb() const;
  public:
  void clear_show_rgb();
  bool show_rgb() const;
  void set_show_rgb(bool value);
  private:
  bool _internal_show_rgb() const;
  void _internal_set_show_rgb(bool value);
  public:

  // optional bool show_depth = 6;
  bool has_show_depth() const;
  private:
  bool _internal_has_show_depth() const;
  public:
  void clear_show_depth();
  bool show_depth() const;
  void set_show_depth(bool value);
  private:
  bool _internal_show_depth() const;
  void _internal_set_show_depth(bool value);
  public:

  // @@protoc_insertion_point(class_scope:robosense.acviewer_msgs.RSShowConfigData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  bool show_pos_;
  bool show_pc_;
  bool show_pc_slam_;
  bool show_tri_;
  bool show_rgb_;
  bool show_depth_;
  friend struct ::TableStruct_acviewer_2eproto;
};
// -------------------------------------------------------------------

class RSOperatorDeviceData PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.acviewer_msgs.RSOperatorDeviceData) */ {
 public:
  inline RSOperatorDeviceData() : RSOperatorDeviceData(nullptr) {}
  virtual ~RSOperatorDeviceData();

  RSOperatorDeviceData(const RSOperatorDeviceData& from);
  RSOperatorDeviceData(RSOperatorDeviceData&& from) noexcept
    : RSOperatorDeviceData() {
    *this = ::std::move(from);
  }

  inline RSOperatorDeviceData& operator=(const RSOperatorDeviceData& from) {
    CopyFrom(from);
    return *this;
  }
  inline RSOperatorDeviceData& operator=(RSOperatorDeviceData&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RSOperatorDeviceData& default_instance();

  static inline const RSOperatorDeviceData* internal_default_instance() {
    return reinterpret_cast<const RSOperatorDeviceData*>(
               &_RSOperatorDeviceData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(RSOperatorDeviceData& a, RSOperatorDeviceData& b) {
    a.Swap(&b);
  }
  inline void Swap(RSOperatorDeviceData* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RSOperatorDeviceData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RSOperatorDeviceData* New() const final {
    return CreateMaybeMessage<RSOperatorDeviceData>(nullptr);
  }

  RSOperatorDeviceData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RSOperatorDeviceData>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RSOperatorDeviceData& from);
  void MergeFrom(const RSOperatorDeviceData& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RSOperatorDeviceData* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.acviewer_msgs.RSOperatorDeviceData";
  }
  protected:
  explicit RSOperatorDeviceData(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_acviewer_2eproto);
    return ::descriptor_table_acviewer_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kUuidFieldNumber = 1,
    kOperatorTypeFieldNumber = 2,
  };
  // required string uuid = 1;
  bool has_uuid() const;
  private:
  bool _internal_has_uuid() const;
  public:
  void clear_uuid();
  const std::string& uuid() const;
  void set_uuid(const std::string& value);
  void set_uuid(std::string&& value);
  void set_uuid(const char* value);
  void set_uuid(const char* value, size_t size);
  std::string* mutable_uuid();
  std::string* release_uuid();
  void set_allocated_uuid(std::string* uuid);
  private:
  const std::string& _internal_uuid() const;
  void _internal_set_uuid(const std::string& value);
  std::string* _internal_mutable_uuid();
  public:

  // required uint32 operator_type = 2;
  bool has_operator_type() const;
  private:
  bool _internal_has_operator_type() const;
  public:
  void clear_operator_type();
  ::PROTOBUF_NAMESPACE_ID::uint32 operator_type() const;
  void set_operator_type(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_operator_type() const;
  void _internal_set_operator_type(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:robosense.acviewer_msgs.RSOperatorDeviceData)
 private:
  class _Internal;

  // helper for ByteSizeLong()
  size_t RequiredFieldsByteSizeFallback() const;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr uuid_;
  ::PROTOBUF_NAMESPACE_ID::uint32 operator_type_;
  friend struct ::TableStruct_acviewer_2eproto;
};
// -------------------------------------------------------------------

class RSWriterSetting PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.acviewer_msgs.RSWriterSetting) */ {
 public:
  inline RSWriterSetting() : RSWriterSetting(nullptr) {}
  virtual ~RSWriterSetting();

  RSWriterSetting(const RSWriterSetting& from);
  RSWriterSetting(RSWriterSetting&& from) noexcept
    : RSWriterSetting() {
    *this = ::std::move(from);
  }

  inline RSWriterSetting& operator=(const RSWriterSetting& from) {
    CopyFrom(from);
    return *this;
  }
  inline RSWriterSetting& operator=(RSWriterSetting&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RSWriterSetting& default_instance();

  static inline const RSWriterSetting* internal_default_instance() {
    return reinterpret_cast<const RSWriterSetting*>(
               &_RSWriterSetting_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(RSWriterSetting& a, RSWriterSetting& b) {
    a.Swap(&b);
  }
  inline void Swap(RSWriterSetting* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RSWriterSetting* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RSWriterSetting* New() const final {
    return CreateMaybeMessage<RSWriterSetting>(nullptr);
  }

  RSWriterSetting* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RSWriterSetting>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RSWriterSetting& from);
  void MergeFrom(const RSWriterSetting& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RSWriterSetting* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.acviewer_msgs.RSWriterSetting";
  }
  protected:
  explicit RSWriterSetting(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_acviewer_2eproto);
    return ::descriptor_table_acviewer_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTopicSettingListFieldNumber = 3,
    kFileNameFieldNumber = 1,
    kFileFormatFieldNumber = 2,
  };
  // repeated .robosense.acviewer_msgs.RSTopicSetting topic_setting_list = 3;
  int topic_setting_list_size() const;
  private:
  int _internal_topic_setting_list_size() const;
  public:
  void clear_topic_setting_list();
  ::robosense::acviewer_msgs::RSTopicSetting* mutable_topic_setting_list(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::robosense::acviewer_msgs::RSTopicSetting >*
      mutable_topic_setting_list();
  private:
  const ::robosense::acviewer_msgs::RSTopicSetting& _internal_topic_setting_list(int index) const;
  ::robosense::acviewer_msgs::RSTopicSetting* _internal_add_topic_setting_list();
  public:
  const ::robosense::acviewer_msgs::RSTopicSetting& topic_setting_list(int index) const;
  ::robosense::acviewer_msgs::RSTopicSetting* add_topic_setting_list();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::robosense::acviewer_msgs::RSTopicSetting >&
      topic_setting_list() const;

  // optional string file_name = 1;
  bool has_file_name() const;
  private:
  bool _internal_has_file_name() const;
  public:
  void clear_file_name();
  const std::string& file_name() const;
  void set_file_name(const std::string& value);
  void set_file_name(std::string&& value);
  void set_file_name(const char* value);
  void set_file_name(const char* value, size_t size);
  std::string* mutable_file_name();
  std::string* release_file_name();
  void set_allocated_file_name(std::string* file_name);
  private:
  const std::string& _internal_file_name() const;
  void _internal_set_file_name(const std::string& value);
  std::string* _internal_mutable_file_name();
  public:

  // optional string file_format = 2;
  bool has_file_format() const;
  private:
  bool _internal_has_file_format() const;
  public:
  void clear_file_format();
  const std::string& file_format() const;
  void set_file_format(const std::string& value);
  void set_file_format(std::string&& value);
  void set_file_format(const char* value);
  void set_file_format(const char* value, size_t size);
  std::string* mutable_file_format();
  std::string* release_file_format();
  void set_allocated_file_format(std::string* file_format);
  private:
  const std::string& _internal_file_format() const;
  void _internal_set_file_format(const std::string& value);
  std::string* _internal_mutable_file_format();
  public:

  // @@protoc_insertion_point(class_scope:robosense.acviewer_msgs.RSWriterSetting)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::robosense::acviewer_msgs::RSTopicSetting > topic_setting_list_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr file_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr file_format_;
  friend struct ::TableStruct_acviewer_2eproto;
};
// -------------------------------------------------------------------

class RSTopicSetting PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.acviewer_msgs.RSTopicSetting) */ {
 public:
  inline RSTopicSetting() : RSTopicSetting(nullptr) {}
  virtual ~RSTopicSetting();

  RSTopicSetting(const RSTopicSetting& from);
  RSTopicSetting(RSTopicSetting&& from) noexcept
    : RSTopicSetting() {
    *this = ::std::move(from);
  }

  inline RSTopicSetting& operator=(const RSTopicSetting& from) {
    CopyFrom(from);
    return *this;
  }
  inline RSTopicSetting& operator=(RSTopicSetting&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RSTopicSetting& default_instance();

  static inline const RSTopicSetting* internal_default_instance() {
    return reinterpret_cast<const RSTopicSetting*>(
               &_RSTopicSetting_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(RSTopicSetting& a, RSTopicSetting& b) {
    a.Swap(&b);
  }
  inline void Swap(RSTopicSetting* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RSTopicSetting* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RSTopicSetting* New() const final {
    return CreateMaybeMessage<RSTopicSetting>(nullptr);
  }

  RSTopicSetting* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RSTopicSetting>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RSTopicSetting& from);
  void MergeFrom(const RSTopicSetting& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RSTopicSetting* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.acviewer_msgs.RSTopicSetting";
  }
  protected:
  explicit RSTopicSetting(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_acviewer_2eproto);
    return ::descriptor_table_acviewer_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTopicFieldNumber = 1,
    kCompressedTypeFieldNumber = 2,
  };
  // optional string topic = 1;
  bool has_topic() const;
  private:
  bool _internal_has_topic() const;
  public:
  void clear_topic();
  const std::string& topic() const;
  void set_topic(const std::string& value);
  void set_topic(std::string&& value);
  void set_topic(const char* value);
  void set_topic(const char* value, size_t size);
  std::string* mutable_topic();
  std::string* release_topic();
  void set_allocated_topic(std::string* topic);
  private:
  const std::string& _internal_topic() const;
  void _internal_set_topic(const std::string& value);
  std::string* _internal_mutable_topic();
  public:

  // optional string compressed_type = 2;
  bool has_compressed_type() const;
  private:
  bool _internal_has_compressed_type() const;
  public:
  void clear_compressed_type();
  const std::string& compressed_type() const;
  void set_compressed_type(const std::string& value);
  void set_compressed_type(std::string&& value);
  void set_compressed_type(const char* value);
  void set_compressed_type(const char* value, size_t size);
  std::string* mutable_compressed_type();
  std::string* release_compressed_type();
  void set_allocated_compressed_type(std::string* compressed_type);
  private:
  const std::string& _internal_compressed_type() const;
  void _internal_set_compressed_type(const std::string& value);
  std::string* _internal_mutable_compressed_type();
  public:

  // @@protoc_insertion_point(class_scope:robosense.acviewer_msgs.RSTopicSetting)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr topic_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr compressed_type_;
  friend struct ::TableStruct_acviewer_2eproto;
};
// -------------------------------------------------------------------

class RSReaderSetting PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.acviewer_msgs.RSReaderSetting) */ {
 public:
  inline RSReaderSetting() : RSReaderSetting(nullptr) {}
  virtual ~RSReaderSetting();

  RSReaderSetting(const RSReaderSetting& from);
  RSReaderSetting(RSReaderSetting&& from) noexcept
    : RSReaderSetting() {
    *this = ::std::move(from);
  }

  inline RSReaderSetting& operator=(const RSReaderSetting& from) {
    CopyFrom(from);
    return *this;
  }
  inline RSReaderSetting& operator=(RSReaderSetting&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RSReaderSetting& default_instance();

  static inline const RSReaderSetting* internal_default_instance() {
    return reinterpret_cast<const RSReaderSetting*>(
               &_RSReaderSetting_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(RSReaderSetting& a, RSReaderSetting& b) {
    a.Swap(&b);
  }
  inline void Swap(RSReaderSetting* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RSReaderSetting* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RSReaderSetting* New() const final {
    return CreateMaybeMessage<RSReaderSetting>(nullptr);
  }

  RSReaderSetting* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RSReaderSetting>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RSReaderSetting& from);
  void MergeFrom(const RSReaderSetting& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RSReaderSetting* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.acviewer_msgs.RSReaderSetting";
  }
  protected:
  explicit RSReaderSetting(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_acviewer_2eproto);
    return ::descriptor_table_acviewer_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kReadTopicNamesFieldNumber = 4,
    kFileNameFieldNumber = 1,
    kMainSyncTopicFieldNumber = 2,
    kPreLoadMainsSyncCntFieldNumber = 3,
    kReadStartTimestampNsFieldNumber = 5,
    kReadEndTimestampNsFieldNumber = 6,
  };
  // repeated string read_topic_names = 4;
  int read_topic_names_size() const;
  private:
  int _internal_read_topic_names_size() const;
  public:
  void clear_read_topic_names();
  const std::string& read_topic_names(int index) const;
  std::string* mutable_read_topic_names(int index);
  void set_read_topic_names(int index, const std::string& value);
  void set_read_topic_names(int index, std::string&& value);
  void set_read_topic_names(int index, const char* value);
  void set_read_topic_names(int index, const char* value, size_t size);
  std::string* add_read_topic_names();
  void add_read_topic_names(const std::string& value);
  void add_read_topic_names(std::string&& value);
  void add_read_topic_names(const char* value);
  void add_read_topic_names(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& read_topic_names() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_read_topic_names();
  private:
  const std::string& _internal_read_topic_names(int index) const;
  std::string* _internal_add_read_topic_names();
  public:

  // optional string file_name = 1;
  bool has_file_name() const;
  private:
  bool _internal_has_file_name() const;
  public:
  void clear_file_name();
  const std::string& file_name() const;
  void set_file_name(const std::string& value);
  void set_file_name(std::string&& value);
  void set_file_name(const char* value);
  void set_file_name(const char* value, size_t size);
  std::string* mutable_file_name();
  std::string* release_file_name();
  void set_allocated_file_name(std::string* file_name);
  private:
  const std::string& _internal_file_name() const;
  void _internal_set_file_name(const std::string& value);
  std::string* _internal_mutable_file_name();
  public:

  // optional string main_sync_topic = 2;
  bool has_main_sync_topic() const;
  private:
  bool _internal_has_main_sync_topic() const;
  public:
  void clear_main_sync_topic();
  const std::string& main_sync_topic() const;
  void set_main_sync_topic(const std::string& value);
  void set_main_sync_topic(std::string&& value);
  void set_main_sync_topic(const char* value);
  void set_main_sync_topic(const char* value, size_t size);
  std::string* mutable_main_sync_topic();
  std::string* release_main_sync_topic();
  void set_allocated_main_sync_topic(std::string* main_sync_topic);
  private:
  const std::string& _internal_main_sync_topic() const;
  void _internal_set_main_sync_topic(const std::string& value);
  std::string* _internal_mutable_main_sync_topic();
  public:

  // optional uint32 pre_load_mains_sync_cnt = 3;
  bool has_pre_load_mains_sync_cnt() const;
  private:
  bool _internal_has_pre_load_mains_sync_cnt() const;
  public:
  void clear_pre_load_mains_sync_cnt();
  ::PROTOBUF_NAMESPACE_ID::uint32 pre_load_mains_sync_cnt() const;
  void set_pre_load_mains_sync_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_pre_load_mains_sync_cnt() const;
  void _internal_set_pre_load_mains_sync_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional int64 read_start_timestamp_ns = 5 [default = -1];
  bool has_read_start_timestamp_ns() const;
  private:
  bool _internal_has_read_start_timestamp_ns() const;
  public:
  void clear_read_start_timestamp_ns();
  ::PROTOBUF_NAMESPACE_ID::int64 read_start_timestamp_ns() const;
  void set_read_start_timestamp_ns(::PROTOBUF_NAMESPACE_ID::int64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int64 _internal_read_start_timestamp_ns() const;
  void _internal_set_read_start_timestamp_ns(::PROTOBUF_NAMESPACE_ID::int64 value);
  public:

  // optional int64 read_end_timestamp_ns = 6 [default = -1];
  bool has_read_end_timestamp_ns() const;
  private:
  bool _internal_has_read_end_timestamp_ns() const;
  public:
  void clear_read_end_timestamp_ns();
  ::PROTOBUF_NAMESPACE_ID::int64 read_end_timestamp_ns() const;
  void set_read_end_timestamp_ns(::PROTOBUF_NAMESPACE_ID::int64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int64 _internal_read_end_timestamp_ns() const;
  void _internal_set_read_end_timestamp_ns(::PROTOBUF_NAMESPACE_ID::int64 value);
  public:

  // @@protoc_insertion_point(class_scope:robosense.acviewer_msgs.RSReaderSetting)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> read_topic_names_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr file_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr main_sync_topic_;
  ::PROTOBUF_NAMESPACE_ID::uint32 pre_load_mains_sync_cnt_;
  ::PROTOBUF_NAMESPACE_ID::int64 read_start_timestamp_ns_;
  ::PROTOBUF_NAMESPACE_ID::int64 read_end_timestamp_ns_;
  friend struct ::TableStruct_acviewer_2eproto;
};
// -------------------------------------------------------------------

class RSRequestData PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.acviewer_msgs.RSRequestData) */ {
 public:
  inline RSRequestData() : RSRequestData(nullptr) {}
  virtual ~RSRequestData();

  RSRequestData(const RSRequestData& from);
  RSRequestData(RSRequestData&& from) noexcept
    : RSRequestData() {
    *this = ::std::move(from);
  }

  inline RSRequestData& operator=(const RSRequestData& from) {
    CopyFrom(from);
    return *this;
  }
  inline RSRequestData& operator=(RSRequestData&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RSRequestData& default_instance();

  static inline const RSRequestData* internal_default_instance() {
    return reinterpret_cast<const RSRequestData*>(
               &_RSRequestData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(RSRequestData& a, RSRequestData& b) {
    a.Swap(&b);
  }
  inline void Swap(RSRequestData* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RSRequestData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RSRequestData* New() const final {
    return CreateMaybeMessage<RSRequestData>(nullptr);
  }

  RSRequestData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RSRequestData>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RSRequestData& from);
  void MergeFrom(const RSRequestData& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RSRequestData* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.acviewer_msgs.RSRequestData";
  }
  protected:
  explicit RSRequestData(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_acviewer_2eproto);
    return ::descriptor_table_acviewer_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTopicFilePathFieldNumber = 6,
    kShowConfigDataFieldNumber = 2,
    kOperatorDeviceDataFieldNumber = 3,
    kWriterSettingFieldNumber = 4,
    kReaderSettingFieldNumber = 5,
    kRunModeFieldNumber = 1,
    kSkipFrameFieldNumber = 7,
  };
  // optional string topic_file_path = 6;
  bool has_topic_file_path() const;
  private:
  bool _internal_has_topic_file_path() const;
  public:
  void clear_topic_file_path();
  const std::string& topic_file_path() const;
  void set_topic_file_path(const std::string& value);
  void set_topic_file_path(std::string&& value);
  void set_topic_file_path(const char* value);
  void set_topic_file_path(const char* value, size_t size);
  std::string* mutable_topic_file_path();
  std::string* release_topic_file_path();
  void set_allocated_topic_file_path(std::string* topic_file_path);
  private:
  const std::string& _internal_topic_file_path() const;
  void _internal_set_topic_file_path(const std::string& value);
  std::string* _internal_mutable_topic_file_path();
  public:

  // optional .robosense.acviewer_msgs.RSShowConfigData show_config_data = 2;
  bool has_show_config_data() const;
  private:
  bool _internal_has_show_config_data() const;
  public:
  void clear_show_config_data();
  const ::robosense::acviewer_msgs::RSShowConfigData& show_config_data() const;
  ::robosense::acviewer_msgs::RSShowConfigData* release_show_config_data();
  ::robosense::acviewer_msgs::RSShowConfigData* mutable_show_config_data();
  void set_allocated_show_config_data(::robosense::acviewer_msgs::RSShowConfigData* show_config_data);
  private:
  const ::robosense::acviewer_msgs::RSShowConfigData& _internal_show_config_data() const;
  ::robosense::acviewer_msgs::RSShowConfigData* _internal_mutable_show_config_data();
  public:
  void unsafe_arena_set_allocated_show_config_data(
      ::robosense::acviewer_msgs::RSShowConfigData* show_config_data);
  ::robosense::acviewer_msgs::RSShowConfigData* unsafe_arena_release_show_config_data();

  // optional .robosense.acviewer_msgs.RSOperatorDeviceData operator_device_data = 3;
  bool has_operator_device_data() const;
  private:
  bool _internal_has_operator_device_data() const;
  public:
  void clear_operator_device_data();
  const ::robosense::acviewer_msgs::RSOperatorDeviceData& operator_device_data() const;
  ::robosense::acviewer_msgs::RSOperatorDeviceData* release_operator_device_data();
  ::robosense::acviewer_msgs::RSOperatorDeviceData* mutable_operator_device_data();
  void set_allocated_operator_device_data(::robosense::acviewer_msgs::RSOperatorDeviceData* operator_device_data);
  private:
  const ::robosense::acviewer_msgs::RSOperatorDeviceData& _internal_operator_device_data() const;
  ::robosense::acviewer_msgs::RSOperatorDeviceData* _internal_mutable_operator_device_data();
  public:
  void unsafe_arena_set_allocated_operator_device_data(
      ::robosense::acviewer_msgs::RSOperatorDeviceData* operator_device_data);
  ::robosense::acviewer_msgs::RSOperatorDeviceData* unsafe_arena_release_operator_device_data();

  // optional .robosense.acviewer_msgs.RSWriterSetting writer_setting = 4;
  bool has_writer_setting() const;
  private:
  bool _internal_has_writer_setting() const;
  public:
  void clear_writer_setting();
  const ::robosense::acviewer_msgs::RSWriterSetting& writer_setting() const;
  ::robosense::acviewer_msgs::RSWriterSetting* release_writer_setting();
  ::robosense::acviewer_msgs::RSWriterSetting* mutable_writer_setting();
  void set_allocated_writer_setting(::robosense::acviewer_msgs::RSWriterSetting* writer_setting);
  private:
  const ::robosense::acviewer_msgs::RSWriterSetting& _internal_writer_setting() const;
  ::robosense::acviewer_msgs::RSWriterSetting* _internal_mutable_writer_setting();
  public:
  void unsafe_arena_set_allocated_writer_setting(
      ::robosense::acviewer_msgs::RSWriterSetting* writer_setting);
  ::robosense::acviewer_msgs::RSWriterSetting* unsafe_arena_release_writer_setting();

  // optional .robosense.acviewer_msgs.RSReaderSetting reader_setting = 5;
  bool has_reader_setting() const;
  private:
  bool _internal_has_reader_setting() const;
  public:
  void clear_reader_setting();
  const ::robosense::acviewer_msgs::RSReaderSetting& reader_setting() const;
  ::robosense::acviewer_msgs::RSReaderSetting* release_reader_setting();
  ::robosense::acviewer_msgs::RSReaderSetting* mutable_reader_setting();
  void set_allocated_reader_setting(::robosense::acviewer_msgs::RSReaderSetting* reader_setting);
  private:
  const ::robosense::acviewer_msgs::RSReaderSetting& _internal_reader_setting() const;
  ::robosense::acviewer_msgs::RSReaderSetting* _internal_mutable_reader_setting();
  public:
  void unsafe_arena_set_allocated_reader_setting(
      ::robosense::acviewer_msgs::RSReaderSetting* reader_setting);
  ::robosense::acviewer_msgs::RSReaderSetting* unsafe_arena_release_reader_setting();

  // optional uint32 runMode = 1;
  bool has_runmode() const;
  private:
  bool _internal_has_runmode() const;
  public:
  void clear_runmode();
  ::PROTOBUF_NAMESPACE_ID::uint32 runmode() const;
  void set_runmode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_runmode() const;
  void _internal_set_runmode(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional int32 skip_frame = 7;
  bool has_skip_frame() const;
  private:
  bool _internal_has_skip_frame() const;
  public:
  void clear_skip_frame();
  ::PROTOBUF_NAMESPACE_ID::int32 skip_frame() const;
  void set_skip_frame(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_skip_frame() const;
  void _internal_set_skip_frame(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // @@protoc_insertion_point(class_scope:robosense.acviewer_msgs.RSRequestData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr topic_file_path_;
  ::robosense::acviewer_msgs::RSShowConfigData* show_config_data_;
  ::robosense::acviewer_msgs::RSOperatorDeviceData* operator_device_data_;
  ::robosense::acviewer_msgs::RSWriterSetting* writer_setting_;
  ::robosense::acviewer_msgs::RSReaderSetting* reader_setting_;
  ::PROTOBUF_NAMESPACE_ID::uint32 runmode_;
  ::PROTOBUF_NAMESPACE_ID::int32 skip_frame_;
  friend struct ::TableStruct_acviewer_2eproto;
};
// -------------------------------------------------------------------

class RSResponse PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.acviewer_msgs.RSResponse) */ {
 public:
  inline RSResponse() : RSResponse(nullptr) {}
  virtual ~RSResponse();

  RSResponse(const RSResponse& from);
  RSResponse(RSResponse&& from) noexcept
    : RSResponse() {
    *this = ::std::move(from);
  }

  inline RSResponse& operator=(const RSResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline RSResponse& operator=(RSResponse&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RSResponse& default_instance();

  static inline const RSResponse* internal_default_instance() {
    return reinterpret_cast<const RSResponse*>(
               &_RSResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(RSResponse& a, RSResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(RSResponse* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RSResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RSResponse* New() const final {
    return CreateMaybeMessage<RSResponse>(nullptr);
  }

  RSResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RSResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RSResponse& from);
  void MergeFrom(const RSResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RSResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.acviewer_msgs.RSResponse";
  }
  protected:
  explicit RSResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_acviewer_2eproto);
    return ::descriptor_table_acviewer_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kResponseInfoFieldNumber = 5,
    kResponseDataFieldNumber = 6,
    kRequestIdFieldNumber = 2,
    kCmdTypeFieldNumber = 1,
    kResponseCodeFieldNumber = 4,
    kResponseIdFieldNumber = 3,
  };
  // optional string response_info = 5;
  bool has_response_info() const;
  private:
  bool _internal_has_response_info() const;
  public:
  void clear_response_info();
  const std::string& response_info() const;
  void set_response_info(const std::string& value);
  void set_response_info(std::string&& value);
  void set_response_info(const char* value);
  void set_response_info(const char* value, size_t size);
  std::string* mutable_response_info();
  std::string* release_response_info();
  void set_allocated_response_info(std::string* response_info);
  private:
  const std::string& _internal_response_info() const;
  void _internal_set_response_info(const std::string& value);
  std::string* _internal_mutable_response_info();
  public:

  // optional .robosense.acviewer_msgs.RSResponseData response_data = 6;
  bool has_response_data() const;
  private:
  bool _internal_has_response_data() const;
  public:
  void clear_response_data();
  const ::robosense::acviewer_msgs::RSResponseData& response_data() const;
  ::robosense::acviewer_msgs::RSResponseData* release_response_data();
  ::robosense::acviewer_msgs::RSResponseData* mutable_response_data();
  void set_allocated_response_data(::robosense::acviewer_msgs::RSResponseData* response_data);
  private:
  const ::robosense::acviewer_msgs::RSResponseData& _internal_response_data() const;
  ::robosense::acviewer_msgs::RSResponseData* _internal_mutable_response_data();
  public:
  void unsafe_arena_set_allocated_response_data(
      ::robosense::acviewer_msgs::RSResponseData* response_data);
  ::robosense::acviewer_msgs::RSResponseData* unsafe_arena_release_response_data();

  // required uint64 request_Id = 2;
  bool has_request_id() const;
  private:
  bool _internal_has_request_id() const;
  public:
  void clear_request_id();
  ::PROTOBUF_NAMESPACE_ID::uint64 request_id() const;
  void set_request_id(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_request_id() const;
  void _internal_set_request_id(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // required .robosense.acviewer_msgs.RS_CMD_TYPE cmd_type = 1;
  bool has_cmd_type() const;
  private:
  bool _internal_has_cmd_type() const;
  public:
  void clear_cmd_type();
  ::robosense::acviewer_msgs::RS_CMD_TYPE cmd_type() const;
  void set_cmd_type(::robosense::acviewer_msgs::RS_CMD_TYPE value);
  private:
  ::robosense::acviewer_msgs::RS_CMD_TYPE _internal_cmd_type() const;
  void _internal_set_cmd_type(::robosense::acviewer_msgs::RS_CMD_TYPE value);
  public:

  // required uint32 response_code = 4;
  bool has_response_code() const;
  private:
  bool _internal_has_response_code() const;
  public:
  void clear_response_code();
  ::PROTOBUF_NAMESPACE_ID::uint32 response_code() const;
  void set_response_code(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_response_code() const;
  void _internal_set_response_code(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // required uint64 response_Id = 3;
  bool has_response_id() const;
  private:
  bool _internal_has_response_id() const;
  public:
  void clear_response_id();
  ::PROTOBUF_NAMESPACE_ID::uint64 response_id() const;
  void set_response_id(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_response_id() const;
  void _internal_set_response_id(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // @@protoc_insertion_point(class_scope:robosense.acviewer_msgs.RSResponse)
 private:
  class _Internal;

  // helper for ByteSizeLong()
  size_t RequiredFieldsByteSizeFallback() const;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr response_info_;
  ::robosense::acviewer_msgs::RSResponseData* response_data_;
  ::PROTOBUF_NAMESPACE_ID::uint64 request_id_;
  int cmd_type_;
  ::PROTOBUF_NAMESPACE_ID::uint32 response_code_;
  ::PROTOBUF_NAMESPACE_ID::uint64 response_id_;
  friend struct ::TableStruct_acviewer_2eproto;
};
// -------------------------------------------------------------------

class RSDeviceInfo PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.acviewer_msgs.RSDeviceInfo) */ {
 public:
  inline RSDeviceInfo() : RSDeviceInfo(nullptr) {}
  virtual ~RSDeviceInfo();

  RSDeviceInfo(const RSDeviceInfo& from);
  RSDeviceInfo(RSDeviceInfo&& from) noexcept
    : RSDeviceInfo() {
    *this = ::std::move(from);
  }

  inline RSDeviceInfo& operator=(const RSDeviceInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline RSDeviceInfo& operator=(RSDeviceInfo&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RSDeviceInfo& default_instance();

  static inline const RSDeviceInfo* internal_default_instance() {
    return reinterpret_cast<const RSDeviceInfo*>(
               &_RSDeviceInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(RSDeviceInfo& a, RSDeviceInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(RSDeviceInfo* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RSDeviceInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RSDeviceInfo* New() const final {
    return CreateMaybeMessage<RSDeviceInfo>(nullptr);
  }

  RSDeviceInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RSDeviceInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RSDeviceInfo& from);
  void MergeFrom(const RSDeviceInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RSDeviceInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.acviewer_msgs.RSDeviceInfo";
  }
  protected:
  explicit RSDeviceInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_acviewer_2eproto);
    return ::descriptor_table_acviewer_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kUuidFieldNumber = 1,
    kEventTypeFieldNumber = 2,
  };
  // optional string uuid = 1;
  bool has_uuid() const;
  private:
  bool _internal_has_uuid() const;
  public:
  void clear_uuid();
  const std::string& uuid() const;
  void set_uuid(const std::string& value);
  void set_uuid(std::string&& value);
  void set_uuid(const char* value);
  void set_uuid(const char* value, size_t size);
  std::string* mutable_uuid();
  std::string* release_uuid();
  void set_allocated_uuid(std::string* uuid);
  private:
  const std::string& _internal_uuid() const;
  void _internal_set_uuid(const std::string& value);
  std::string* _internal_mutable_uuid();
  public:

  // optional uint32 event_type = 2;
  bool has_event_type() const;
  private:
  bool _internal_has_event_type() const;
  public:
  void clear_event_type();
  ::PROTOBUF_NAMESPACE_ID::uint32 event_type() const;
  void set_event_type(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_event_type() const;
  void _internal_set_event_type(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:robosense.acviewer_msgs.RSDeviceInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr uuid_;
  ::PROTOBUF_NAMESPACE_ID::uint32 event_type_;
  friend struct ::TableStruct_acviewer_2eproto;
};
// -------------------------------------------------------------------

class RSWriterConfig PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.acviewer_msgs.RSWriterConfig) */ {
 public:
  inline RSWriterConfig() : RSWriterConfig(nullptr) {}
  virtual ~RSWriterConfig();

  RSWriterConfig(const RSWriterConfig& from);
  RSWriterConfig(RSWriterConfig&& from) noexcept
    : RSWriterConfig() {
    *this = ::std::move(from);
  }

  inline RSWriterConfig& operator=(const RSWriterConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline RSWriterConfig& operator=(RSWriterConfig&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RSWriterConfig& default_instance();

  static inline const RSWriterConfig* internal_default_instance() {
    return reinterpret_cast<const RSWriterConfig*>(
               &_RSWriterConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(RSWriterConfig& a, RSWriterConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(RSWriterConfig* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RSWriterConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RSWriterConfig* New() const final {
    return CreateMaybeMessage<RSWriterConfig>(nullptr);
  }

  RSWriterConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RSWriterConfig>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RSWriterConfig& from);
  void MergeFrom(const RSWriterConfig& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RSWriterConfig* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.acviewer_msgs.RSWriterConfig";
  }
  protected:
  explicit RSWriterConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_acviewer_2eproto);
    return ::descriptor_table_acviewer_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTopicConfigListFieldNumber = 1,
    kFileFormatFieldNumber = 2,
  };
  // repeated .robosense.acviewer_msgs.RSTopicConfig topic_config_list = 1;
  int topic_config_list_size() const;
  private:
  int _internal_topic_config_list_size() const;
  public:
  void clear_topic_config_list();
  ::robosense::acviewer_msgs::RSTopicConfig* mutable_topic_config_list(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::robosense::acviewer_msgs::RSTopicConfig >*
      mutable_topic_config_list();
  private:
  const ::robosense::acviewer_msgs::RSTopicConfig& _internal_topic_config_list(int index) const;
  ::robosense::acviewer_msgs::RSTopicConfig* _internal_add_topic_config_list();
  public:
  const ::robosense::acviewer_msgs::RSTopicConfig& topic_config_list(int index) const;
  ::robosense::acviewer_msgs::RSTopicConfig* add_topic_config_list();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::robosense::acviewer_msgs::RSTopicConfig >&
      topic_config_list() const;

  // repeated string file_format = 2;
  int file_format_size() const;
  private:
  int _internal_file_format_size() const;
  public:
  void clear_file_format();
  const std::string& file_format(int index) const;
  std::string* mutable_file_format(int index);
  void set_file_format(int index, const std::string& value);
  void set_file_format(int index, std::string&& value);
  void set_file_format(int index, const char* value);
  void set_file_format(int index, const char* value, size_t size);
  std::string* add_file_format();
  void add_file_format(const std::string& value);
  void add_file_format(std::string&& value);
  void add_file_format(const char* value);
  void add_file_format(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& file_format() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_file_format();
  private:
  const std::string& _internal_file_format(int index) const;
  std::string* _internal_add_file_format();
  public:

  // @@protoc_insertion_point(class_scope:robosense.acviewer_msgs.RSWriterConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::robosense::acviewer_msgs::RSTopicConfig > topic_config_list_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> file_format_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_acviewer_2eproto;
};
// -------------------------------------------------------------------

class RSReaderConfig PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.acviewer_msgs.RSReaderConfig) */ {
 public:
  inline RSReaderConfig() : RSReaderConfig(nullptr) {}
  virtual ~RSReaderConfig();

  RSReaderConfig(const RSReaderConfig& from);
  RSReaderConfig(RSReaderConfig&& from) noexcept
    : RSReaderConfig() {
    *this = ::std::move(from);
  }

  inline RSReaderConfig& operator=(const RSReaderConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline RSReaderConfig& operator=(RSReaderConfig&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RSReaderConfig& default_instance();

  static inline const RSReaderConfig* internal_default_instance() {
    return reinterpret_cast<const RSReaderConfig*>(
               &_RSReaderConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(RSReaderConfig& a, RSReaderConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(RSReaderConfig* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RSReaderConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RSReaderConfig* New() const final {
    return CreateMaybeMessage<RSReaderConfig>(nullptr);
  }

  RSReaderConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RSReaderConfig>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RSReaderConfig& from);
  void MergeFrom(const RSReaderConfig& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RSReaderConfig* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.acviewer_msgs.RSReaderConfig";
  }
  protected:
  explicit RSReaderConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_acviewer_2eproto);
    return ::descriptor_table_acviewer_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFileTopicsFieldNumber = 1,
    kStartTimestampNsFieldNumber = 2,
    kEndTimestampNsFieldNumber = 3,
  };
  // repeated string file_topics = 1;
  int file_topics_size() const;
  private:
  int _internal_file_topics_size() const;
  public:
  void clear_file_topics();
  const std::string& file_topics(int index) const;
  std::string* mutable_file_topics(int index);
  void set_file_topics(int index, const std::string& value);
  void set_file_topics(int index, std::string&& value);
  void set_file_topics(int index, const char* value);
  void set_file_topics(int index, const char* value, size_t size);
  std::string* add_file_topics();
  void add_file_topics(const std::string& value);
  void add_file_topics(std::string&& value);
  void add_file_topics(const char* value);
  void add_file_topics(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& file_topics() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_file_topics();
  private:
  const std::string& _internal_file_topics(int index) const;
  std::string* _internal_add_file_topics();
  public:

  // optional int64 start_timestamp_ns = 2;
  bool has_start_timestamp_ns() const;
  private:
  bool _internal_has_start_timestamp_ns() const;
  public:
  void clear_start_timestamp_ns();
  ::PROTOBUF_NAMESPACE_ID::int64 start_timestamp_ns() const;
  void set_start_timestamp_ns(::PROTOBUF_NAMESPACE_ID::int64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int64 _internal_start_timestamp_ns() const;
  void _internal_set_start_timestamp_ns(::PROTOBUF_NAMESPACE_ID::int64 value);
  public:

  // optional int64 end_timestamp_ns = 3;
  bool has_end_timestamp_ns() const;
  private:
  bool _internal_has_end_timestamp_ns() const;
  public:
  void clear_end_timestamp_ns();
  ::PROTOBUF_NAMESPACE_ID::int64 end_timestamp_ns() const;
  void set_end_timestamp_ns(::PROTOBUF_NAMESPACE_ID::int64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int64 _internal_end_timestamp_ns() const;
  void _internal_set_end_timestamp_ns(::PROTOBUF_NAMESPACE_ID::int64 value);
  public:

  // @@protoc_insertion_point(class_scope:robosense.acviewer_msgs.RSReaderConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> file_topics_;
  ::PROTOBUF_NAMESPACE_ID::int64 start_timestamp_ns_;
  ::PROTOBUF_NAMESPACE_ID::int64 end_timestamp_ns_;
  friend struct ::TableStruct_acviewer_2eproto;
};
// -------------------------------------------------------------------

class RSTopicConfig PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.acviewer_msgs.RSTopicConfig) */ {
 public:
  inline RSTopicConfig() : RSTopicConfig(nullptr) {}
  virtual ~RSTopicConfig();

  RSTopicConfig(const RSTopicConfig& from);
  RSTopicConfig(RSTopicConfig&& from) noexcept
    : RSTopicConfig() {
    *this = ::std::move(from);
  }

  inline RSTopicConfig& operator=(const RSTopicConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline RSTopicConfig& operator=(RSTopicConfig&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RSTopicConfig& default_instance();

  static inline const RSTopicConfig* internal_default_instance() {
    return reinterpret_cast<const RSTopicConfig*>(
               &_RSTopicConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(RSTopicConfig& a, RSTopicConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(RSTopicConfig* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RSTopicConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RSTopicConfig* New() const final {
    return CreateMaybeMessage<RSTopicConfig>(nullptr);
  }

  RSTopicConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RSTopicConfig>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RSTopicConfig& from);
  void MergeFrom(const RSTopicConfig& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RSTopicConfig* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.acviewer_msgs.RSTopicConfig";
  }
  protected:
  explicit RSTopicConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_acviewer_2eproto);
    return ::descriptor_table_acviewer_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kProcessTypesFieldNumber = 3,
    kTopicFieldNumber = 1,
    kTypeFieldNumber = 2,
  };
  // repeated string process_types = 3;
  int process_types_size() const;
  private:
  int _internal_process_types_size() const;
  public:
  void clear_process_types();
  const std::string& process_types(int index) const;
  std::string* mutable_process_types(int index);
  void set_process_types(int index, const std::string& value);
  void set_process_types(int index, std::string&& value);
  void set_process_types(int index, const char* value);
  void set_process_types(int index, const char* value, size_t size);
  std::string* add_process_types();
  void add_process_types(const std::string& value);
  void add_process_types(std::string&& value);
  void add_process_types(const char* value);
  void add_process_types(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& process_types() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_process_types();
  private:
  const std::string& _internal_process_types(int index) const;
  std::string* _internal_add_process_types();
  public:

  // optional string topic = 1;
  bool has_topic() const;
  private:
  bool _internal_has_topic() const;
  public:
  void clear_topic();
  const std::string& topic() const;
  void set_topic(const std::string& value);
  void set_topic(std::string&& value);
  void set_topic(const char* value);
  void set_topic(const char* value, size_t size);
  std::string* mutable_topic();
  std::string* release_topic();
  void set_allocated_topic(std::string* topic);
  private:
  const std::string& _internal_topic() const;
  void _internal_set_topic(const std::string& value);
  std::string* _internal_mutable_topic();
  public:

  // optional string type = 2;
  bool has_type() const;
  private:
  bool _internal_has_type() const;
  public:
  void clear_type();
  const std::string& type() const;
  void set_type(const std::string& value);
  void set_type(std::string&& value);
  void set_type(const char* value);
  void set_type(const char* value, size_t size);
  std::string* mutable_type();
  std::string* release_type();
  void set_allocated_type(std::string* type);
  private:
  const std::string& _internal_type() const;
  void _internal_set_type(const std::string& value);
  std::string* _internal_mutable_type();
  public:

  // @@protoc_insertion_point(class_scope:robosense.acviewer_msgs.RSTopicConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> process_types_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr topic_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr type_;
  friend struct ::TableStruct_acviewer_2eproto;
};
// -------------------------------------------------------------------

class RSReaderProgress PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.acviewer_msgs.RSReaderProgress) */ {
 public:
  inline RSReaderProgress() : RSReaderProgress(nullptr) {}
  virtual ~RSReaderProgress();

  RSReaderProgress(const RSReaderProgress& from);
  RSReaderProgress(RSReaderProgress&& from) noexcept
    : RSReaderProgress() {
    *this = ::std::move(from);
  }

  inline RSReaderProgress& operator=(const RSReaderProgress& from) {
    CopyFrom(from);
    return *this;
  }
  inline RSReaderProgress& operator=(RSReaderProgress&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RSReaderProgress& default_instance();

  static inline const RSReaderProgress* internal_default_instance() {
    return reinterpret_cast<const RSReaderProgress*>(
               &_RSReaderProgress_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(RSReaderProgress& a, RSReaderProgress& b) {
    a.Swap(&b);
  }
  inline void Swap(RSReaderProgress* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RSReaderProgress* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RSReaderProgress* New() const final {
    return CreateMaybeMessage<RSReaderProgress>(nullptr);
  }

  RSReaderProgress* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RSReaderProgress>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RSReaderProgress& from);
  void MergeFrom(const RSReaderProgress& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RSReaderProgress* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.acviewer_msgs.RSReaderProgress";
  }
  protected:
  explicit RSReaderProgress(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_acviewer_2eproto);
    return ::descriptor_table_acviewer_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTotalFrameCountFieldNumber = 1,
    kIsPlayingFieldNumber = 2,
    kCurrentTimestampFieldNumber = 4,
    kCurrentFrameIndexFieldNumber = 3,
  };
  // optional uint32 total_frame_count = 1;
  bool has_total_frame_count() const;
  private:
  bool _internal_has_total_frame_count() const;
  public:
  void clear_total_frame_count();
  ::PROTOBUF_NAMESPACE_ID::uint32 total_frame_count() const;
  void set_total_frame_count(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_total_frame_count() const;
  void _internal_set_total_frame_count(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional bool is_playing = 2;
  bool has_is_playing() const;
  private:
  bool _internal_has_is_playing() const;
  public:
  void clear_is_playing();
  bool is_playing() const;
  void set_is_playing(bool value);
  private:
  bool _internal_is_playing() const;
  void _internal_set_is_playing(bool value);
  public:

  // optional uint64 current_timestamp = 4;
  bool has_current_timestamp() const;
  private:
  bool _internal_has_current_timestamp() const;
  public:
  void clear_current_timestamp();
  ::PROTOBUF_NAMESPACE_ID::uint64 current_timestamp() const;
  void set_current_timestamp(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_current_timestamp() const;
  void _internal_set_current_timestamp(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // optional int32 current_frame_index = 3;
  bool has_current_frame_index() const;
  private:
  bool _internal_has_current_frame_index() const;
  public:
  void clear_current_frame_index();
  ::PROTOBUF_NAMESPACE_ID::int32 current_frame_index() const;
  void set_current_frame_index(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_current_frame_index() const;
  void _internal_set_current_frame_index(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // @@protoc_insertion_point(class_scope:robosense.acviewer_msgs.RSReaderProgress)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 total_frame_count_;
  bool is_playing_;
  ::PROTOBUF_NAMESPACE_ID::uint64 current_timestamp_;
  ::PROTOBUF_NAMESPACE_ID::int32 current_frame_index_;
  friend struct ::TableStruct_acviewer_2eproto;
};
// -------------------------------------------------------------------

class RSResponseData PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.acviewer_msgs.RSResponseData) */ {
 public:
  inline RSResponseData() : RSResponseData(nullptr) {}
  virtual ~RSResponseData();

  RSResponseData(const RSResponseData& from);
  RSResponseData(RSResponseData&& from) noexcept
    : RSResponseData() {
    *this = ::std::move(from);
  }

  inline RSResponseData& operator=(const RSResponseData& from) {
    CopyFrom(from);
    return *this;
  }
  inline RSResponseData& operator=(RSResponseData&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RSResponseData& default_instance();

  static inline const RSResponseData* internal_default_instance() {
    return reinterpret_cast<const RSResponseData*>(
               &_RSResponseData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(RSResponseData& a, RSResponseData& b) {
    a.Swap(&b);
  }
  inline void Swap(RSResponseData* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RSResponseData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RSResponseData* New() const final {
    return CreateMaybeMessage<RSResponseData>(nullptr);
  }

  RSResponseData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RSResponseData>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RSResponseData& from);
  void MergeFrom(const RSResponseData& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RSResponseData* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.acviewer_msgs.RSResponseData";
  }
  protected:
  explicit RSResponseData(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_acviewer_2eproto);
    return ::descriptor_table_acviewer_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceInfoListFieldNumber = 1,
    kFileListFieldNumber = 3,
    kWriterConfigFieldNumber = 2,
    kReaderConfigFieldNumber = 4,
    kReaderProgressFieldNumber = 6,
    kLoadingProgressFieldNumber = 5,
  };
  // repeated .robosense.acviewer_msgs.RSDeviceInfo device_info_list = 1;
  int device_info_list_size() const;
  private:
  int _internal_device_info_list_size() const;
  public:
  void clear_device_info_list();
  ::robosense::acviewer_msgs::RSDeviceInfo* mutable_device_info_list(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::robosense::acviewer_msgs::RSDeviceInfo >*
      mutable_device_info_list();
  private:
  const ::robosense::acviewer_msgs::RSDeviceInfo& _internal_device_info_list(int index) const;
  ::robosense::acviewer_msgs::RSDeviceInfo* _internal_add_device_info_list();
  public:
  const ::robosense::acviewer_msgs::RSDeviceInfo& device_info_list(int index) const;
  ::robosense::acviewer_msgs::RSDeviceInfo* add_device_info_list();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::robosense::acviewer_msgs::RSDeviceInfo >&
      device_info_list() const;

  // repeated string file_list = 3;
  int file_list_size() const;
  private:
  int _internal_file_list_size() const;
  public:
  void clear_file_list();
  const std::string& file_list(int index) const;
  std::string* mutable_file_list(int index);
  void set_file_list(int index, const std::string& value);
  void set_file_list(int index, std::string&& value);
  void set_file_list(int index, const char* value);
  void set_file_list(int index, const char* value, size_t size);
  std::string* add_file_list();
  void add_file_list(const std::string& value);
  void add_file_list(std::string&& value);
  void add_file_list(const char* value);
  void add_file_list(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& file_list() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_file_list();
  private:
  const std::string& _internal_file_list(int index) const;
  std::string* _internal_add_file_list();
  public:

  // optional .robosense.acviewer_msgs.RSWriterConfig writer_config = 2;
  bool has_writer_config() const;
  private:
  bool _internal_has_writer_config() const;
  public:
  void clear_writer_config();
  const ::robosense::acviewer_msgs::RSWriterConfig& writer_config() const;
  ::robosense::acviewer_msgs::RSWriterConfig* release_writer_config();
  ::robosense::acviewer_msgs::RSWriterConfig* mutable_writer_config();
  void set_allocated_writer_config(::robosense::acviewer_msgs::RSWriterConfig* writer_config);
  private:
  const ::robosense::acviewer_msgs::RSWriterConfig& _internal_writer_config() const;
  ::robosense::acviewer_msgs::RSWriterConfig* _internal_mutable_writer_config();
  public:
  void unsafe_arena_set_allocated_writer_config(
      ::robosense::acviewer_msgs::RSWriterConfig* writer_config);
  ::robosense::acviewer_msgs::RSWriterConfig* unsafe_arena_release_writer_config();

  // optional .robosense.acviewer_msgs.RSReaderConfig reader_config = 4;
  bool has_reader_config() const;
  private:
  bool _internal_has_reader_config() const;
  public:
  void clear_reader_config();
  const ::robosense::acviewer_msgs::RSReaderConfig& reader_config() const;
  ::robosense::acviewer_msgs::RSReaderConfig* release_reader_config();
  ::robosense::acviewer_msgs::RSReaderConfig* mutable_reader_config();
  void set_allocated_reader_config(::robosense::acviewer_msgs::RSReaderConfig* reader_config);
  private:
  const ::robosense::acviewer_msgs::RSReaderConfig& _internal_reader_config() const;
  ::robosense::acviewer_msgs::RSReaderConfig* _internal_mutable_reader_config();
  public:
  void unsafe_arena_set_allocated_reader_config(
      ::robosense::acviewer_msgs::RSReaderConfig* reader_config);
  ::robosense::acviewer_msgs::RSReaderConfig* unsafe_arena_release_reader_config();

  // optional .robosense.acviewer_msgs.RSReaderProgress reader_progress = 6;
  bool has_reader_progress() const;
  private:
  bool _internal_has_reader_progress() const;
  public:
  void clear_reader_progress();
  const ::robosense::acviewer_msgs::RSReaderProgress& reader_progress() const;
  ::robosense::acviewer_msgs::RSReaderProgress* release_reader_progress();
  ::robosense::acviewer_msgs::RSReaderProgress* mutable_reader_progress();
  void set_allocated_reader_progress(::robosense::acviewer_msgs::RSReaderProgress* reader_progress);
  private:
  const ::robosense::acviewer_msgs::RSReaderProgress& _internal_reader_progress() const;
  ::robosense::acviewer_msgs::RSReaderProgress* _internal_mutable_reader_progress();
  public:
  void unsafe_arena_set_allocated_reader_progress(
      ::robosense::acviewer_msgs::RSReaderProgress* reader_progress);
  ::robosense::acviewer_msgs::RSReaderProgress* unsafe_arena_release_reader_progress();

  // optional uint32 loading_progress = 5;
  bool has_loading_progress() const;
  private:
  bool _internal_has_loading_progress() const;
  public:
  void clear_loading_progress();
  ::PROTOBUF_NAMESPACE_ID::uint32 loading_progress() const;
  void set_loading_progress(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_loading_progress() const;
  void _internal_set_loading_progress(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:robosense.acviewer_msgs.RSResponseData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::robosense::acviewer_msgs::RSDeviceInfo > device_info_list_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> file_list_;
  ::robosense::acviewer_msgs::RSWriterConfig* writer_config_;
  ::robosense::acviewer_msgs::RSReaderConfig* reader_config_;
  ::robosense::acviewer_msgs::RSReaderProgress* reader_progress_;
  ::PROTOBUF_NAMESPACE_ID::uint32 loading_progress_;
  friend struct ::TableStruct_acviewer_2eproto;
};
// -------------------------------------------------------------------

class UdpControlConfig PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.acviewer_msgs.UdpControlConfig) */ {
 public:
  inline UdpControlConfig() : UdpControlConfig(nullptr) {}
  virtual ~UdpControlConfig();

  UdpControlConfig(const UdpControlConfig& from);
  UdpControlConfig(UdpControlConfig&& from) noexcept
    : UdpControlConfig() {
    *this = ::std::move(from);
  }

  inline UdpControlConfig& operator=(const UdpControlConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline UdpControlConfig& operator=(UdpControlConfig&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const UdpControlConfig& default_instance();

  static inline const UdpControlConfig* internal_default_instance() {
    return reinterpret_cast<const UdpControlConfig*>(
               &_UdpControlConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(UdpControlConfig& a, UdpControlConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(UdpControlConfig* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UdpControlConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline UdpControlConfig* New() const final {
    return CreateMaybeMessage<UdpControlConfig>(nullptr);
  }

  UdpControlConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<UdpControlConfig>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const UdpControlConfig& from);
  void MergeFrom(const UdpControlConfig& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UdpControlConfig* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.acviewer_msgs.UdpControlConfig";
  }
  protected:
  explicit UdpControlConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_acviewer_2eproto);
    return ::descriptor_table_acviewer_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kUdpDataControlTimeMsFieldNumber = 5,
    kUdpControlTypeFieldNumber = 1,
    kUdpTotalControlTimeMsFieldNumber = 2,
    kUdpTotalControlSingleTimeMsFieldNumber = 3,
    kUdpDataControlSizeFieldNumber = 4,
  };
  // optional uint32 udp_data_control_time_ms = 5 [default = 2];
  bool has_udp_data_control_time_ms() const;
  private:
  bool _internal_has_udp_data_control_time_ms() const;
  public:
  void clear_udp_data_control_time_ms();
  ::PROTOBUF_NAMESPACE_ID::uint32 udp_data_control_time_ms() const;
  void set_udp_data_control_time_ms(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_udp_data_control_time_ms() const;
  void _internal_set_udp_data_control_time_ms(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional .robosense.acviewer_msgs.RS_UDP_CONTROL_MODE_TYPE udp_control_type = 1 [default = RS_UDP_CONTROL_TOTAL_CONTROL_TIME];
  bool has_udp_control_type() const;
  private:
  bool _internal_has_udp_control_type() const;
  public:
  void clear_udp_control_type();
  ::robosense::acviewer_msgs::RS_UDP_CONTROL_MODE_TYPE udp_control_type() const;
  void set_udp_control_type(::robosense::acviewer_msgs::RS_UDP_CONTROL_MODE_TYPE value);
  private:
  ::robosense::acviewer_msgs::RS_UDP_CONTROL_MODE_TYPE _internal_udp_control_type() const;
  void _internal_set_udp_control_type(::robosense::acviewer_msgs::RS_UDP_CONTROL_MODE_TYPE value);
  public:

  // optional uint32 udp_total_control_time_ms = 2 [default = 60];
  bool has_udp_total_control_time_ms() const;
  private:
  bool _internal_has_udp_total_control_time_ms() const;
  public:
  void clear_udp_total_control_time_ms();
  ::PROTOBUF_NAMESPACE_ID::uint32 udp_total_control_time_ms() const;
  void set_udp_total_control_time_ms(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_udp_total_control_time_ms() const;
  void _internal_set_udp_total_control_time_ms(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 udp_total_control_single_time_ms = 3 [default = 2];
  bool has_udp_total_control_single_time_ms() const;
  private:
  bool _internal_has_udp_total_control_single_time_ms() const;
  public:
  void clear_udp_total_control_single_time_ms();
  ::PROTOBUF_NAMESPACE_ID::uint32 udp_total_control_single_time_ms() const;
  void set_udp_total_control_single_time_ms(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_udp_total_control_single_time_ms() const;
  void _internal_set_udp_total_control_single_time_ms(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 udp_data_control_size = 4 [default = 262144];
  bool has_udp_data_control_size() const;
  private:
  bool _internal_has_udp_data_control_size() const;
  public:
  void clear_udp_data_control_size();
  ::PROTOBUF_NAMESPACE_ID::uint32 udp_data_control_size() const;
  void set_udp_data_control_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_udp_data_control_size() const;
  void _internal_set_udp_data_control_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:robosense.acviewer_msgs.UdpControlConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 udp_data_control_time_ms_;
  int udp_control_type_;
  ::PROTOBUF_NAMESPACE_ID::uint32 udp_total_control_time_ms_;
  ::PROTOBUF_NAMESPACE_ID::uint32 udp_total_control_single_time_ms_;
  ::PROTOBUF_NAMESPACE_ID::uint32 udp_data_control_size_;
  friend struct ::TableStruct_acviewer_2eproto;
};
// -------------------------------------------------------------------

class RenderSwitch PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.acviewer_msgs.RenderSwitch) */ {
 public:
  inline RenderSwitch() : RenderSwitch(nullptr) {}
  virtual ~RenderSwitch();

  RenderSwitch(const RenderSwitch& from);
  RenderSwitch(RenderSwitch&& from) noexcept
    : RenderSwitch() {
    *this = ::std::move(from);
  }

  inline RenderSwitch& operator=(const RenderSwitch& from) {
    CopyFrom(from);
    return *this;
  }
  inline RenderSwitch& operator=(RenderSwitch&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RenderSwitch& default_instance();

  static inline const RenderSwitch* internal_default_instance() {
    return reinterpret_cast<const RenderSwitch*>(
               &_RenderSwitch_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(RenderSwitch& a, RenderSwitch& b) {
    a.Swap(&b);
  }
  inline void Swap(RenderSwitch* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RenderSwitch* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RenderSwitch* New() const final {
    return CreateMaybeMessage<RenderSwitch>(nullptr);
  }

  RenderSwitch* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RenderSwitch>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RenderSwitch& from);
  void MergeFrom(const RenderSwitch& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RenderSwitch* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.acviewer_msgs.RenderSwitch";
  }
  protected:
  explicit RenderSwitch(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_acviewer_2eproto);
    return ::descriptor_table_acviewer_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kUdpP2PCarappIpFieldNumber = 3,
    kUdpControlConfigFieldNumber = 4,
    kEnableRenderFieldNumber = 1,
    kRenderCommTypeFieldNumber = 2,
  };
  // optional string udp_p2p_carapp_ip = 3;
  bool has_udp_p2p_carapp_ip() const;
  private:
  bool _internal_has_udp_p2p_carapp_ip() const;
  public:
  void clear_udp_p2p_carapp_ip();
  const std::string& udp_p2p_carapp_ip() const;
  void set_udp_p2p_carapp_ip(const std::string& value);
  void set_udp_p2p_carapp_ip(std::string&& value);
  void set_udp_p2p_carapp_ip(const char* value);
  void set_udp_p2p_carapp_ip(const char* value, size_t size);
  std::string* mutable_udp_p2p_carapp_ip();
  std::string* release_udp_p2p_carapp_ip();
  void set_allocated_udp_p2p_carapp_ip(std::string* udp_p2p_carapp_ip);
  private:
  const std::string& _internal_udp_p2p_carapp_ip() const;
  void _internal_set_udp_p2p_carapp_ip(const std::string& value);
  std::string* _internal_mutable_udp_p2p_carapp_ip();
  public:

  // optional .robosense.acviewer_msgs.UdpControlConfig udp_control_config = 4;
  bool has_udp_control_config() const;
  private:
  bool _internal_has_udp_control_config() const;
  public:
  void clear_udp_control_config();
  const ::robosense::acviewer_msgs::UdpControlConfig& udp_control_config() const;
  ::robosense::acviewer_msgs::UdpControlConfig* release_udp_control_config();
  ::robosense::acviewer_msgs::UdpControlConfig* mutable_udp_control_config();
  void set_allocated_udp_control_config(::robosense::acviewer_msgs::UdpControlConfig* udp_control_config);
  private:
  const ::robosense::acviewer_msgs::UdpControlConfig& _internal_udp_control_config() const;
  ::robosense::acviewer_msgs::UdpControlConfig* _internal_mutable_udp_control_config();
  public:
  void unsafe_arena_set_allocated_udp_control_config(
      ::robosense::acviewer_msgs::UdpControlConfig* udp_control_config);
  ::robosense::acviewer_msgs::UdpControlConfig* unsafe_arena_release_udp_control_config();

  // optional bool enable_render = 1 [default = true];
  bool has_enable_render() const;
  private:
  bool _internal_has_enable_render() const;
  public:
  void clear_enable_render();
  bool enable_render() const;
  void set_enable_render(bool value);
  private:
  bool _internal_enable_render() const;
  void _internal_set_enable_render(bool value);
  public:

  // optional .robosense.acviewer_msgs.RS_RENDER_COMMUNICATION_TYPE render_comm_type = 2 [default = RS_RENDER_COMM_WEBSOCKET];
  bool has_render_comm_type() const;
  private:
  bool _internal_has_render_comm_type() const;
  public:
  void clear_render_comm_type();
  ::robosense::acviewer_msgs::RS_RENDER_COMMUNICATION_TYPE render_comm_type() const;
  void set_render_comm_type(::robosense::acviewer_msgs::RS_RENDER_COMMUNICATION_TYPE value);
  private:
  ::robosense::acviewer_msgs::RS_RENDER_COMMUNICATION_TYPE _internal_render_comm_type() const;
  void _internal_set_render_comm_type(::robosense::acviewer_msgs::RS_RENDER_COMMUNICATION_TYPE value);
  public:

  // @@protoc_insertion_point(class_scope:robosense.acviewer_msgs.RenderSwitch)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr udp_p2p_carapp_ip_;
  ::robosense::acviewer_msgs::UdpControlConfig* udp_control_config_;
  bool enable_render_;
  int render_comm_type_;
  friend struct ::TableStruct_acviewer_2eproto;
};
// -------------------------------------------------------------------

class RSPositon PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.acviewer_msgs.RSPositon) */ {
 public:
  inline RSPositon() : RSPositon(nullptr) {}
  virtual ~RSPositon();

  RSPositon(const RSPositon& from);
  RSPositon(RSPositon&& from) noexcept
    : RSPositon() {
    *this = ::std::move(from);
  }

  inline RSPositon& operator=(const RSPositon& from) {
    CopyFrom(from);
    return *this;
  }
  inline RSPositon& operator=(RSPositon&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RSPositon& default_instance();

  static inline const RSPositon* internal_default_instance() {
    return reinterpret_cast<const RSPositon*>(
               &_RSPositon_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(RSPositon& a, RSPositon& b) {
    a.Swap(&b);
  }
  inline void Swap(RSPositon* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RSPositon* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RSPositon* New() const final {
    return CreateMaybeMessage<RSPositon>(nullptr);
  }

  RSPositon* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RSPositon>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RSPositon& from);
  void MergeFrom(const RSPositon& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RSPositon* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.acviewer_msgs.RSPositon";
  }
  protected:
  explicit RSPositon(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_acviewer_2eproto);
    return ::descriptor_table_acviewer_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kXFieldNumber = 1,
    kYFieldNumber = 2,
    kTimestampFieldNumber = 4,
    kZFieldNumber = 3,
  };
  // optional float x = 1;
  bool has_x() const;
  private:
  bool _internal_has_x() const;
  public:
  void clear_x();
  float x() const;
  void set_x(float value);
  private:
  float _internal_x() const;
  void _internal_set_x(float value);
  public:

  // optional float y = 2;
  bool has_y() const;
  private:
  bool _internal_has_y() const;
  public:
  void clear_y();
  float y() const;
  void set_y(float value);
  private:
  float _internal_y() const;
  void _internal_set_y(float value);
  public:

  // optional uint64 timestamp = 4;
  bool has_timestamp() const;
  private:
  bool _internal_has_timestamp() const;
  public:
  void clear_timestamp();
  ::PROTOBUF_NAMESPACE_ID::uint64 timestamp() const;
  void set_timestamp(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_timestamp() const;
  void _internal_set_timestamp(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // optional float z = 3;
  bool has_z() const;
  private:
  bool _internal_has_z() const;
  public:
  void clear_z();
  float z() const;
  void set_z(float value);
  private:
  float _internal_z() const;
  void _internal_set_z(float value);
  public:

  // @@protoc_insertion_point(class_scope:robosense.acviewer_msgs.RSPositon)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  float x_;
  float y_;
  ::PROTOBUF_NAMESPACE_ID::uint64 timestamp_;
  float z_;
  friend struct ::TableStruct_acviewer_2eproto;
};
// -------------------------------------------------------------------

class RSPointCloud PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.acviewer_msgs.RSPointCloud) */ {
 public:
  inline RSPointCloud() : RSPointCloud(nullptr) {}
  virtual ~RSPointCloud();

  RSPointCloud(const RSPointCloud& from);
  RSPointCloud(RSPointCloud&& from) noexcept
    : RSPointCloud() {
    *this = ::std::move(from);
  }

  inline RSPointCloud& operator=(const RSPointCloud& from) {
    CopyFrom(from);
    return *this;
  }
  inline RSPointCloud& operator=(RSPointCloud&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RSPointCloud& default_instance();

  static inline const RSPointCloud* internal_default_instance() {
    return reinterpret_cast<const RSPointCloud*>(
               &_RSPointCloud_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(RSPointCloud& a, RSPointCloud& b) {
    a.Swap(&b);
  }
  inline void Swap(RSPointCloud* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RSPointCloud* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RSPointCloud* New() const final {
    return CreateMaybeMessage<RSPointCloud>(nullptr);
  }

  RSPointCloud* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RSPointCloud>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RSPointCloud& from);
  void MergeFrom(const RSPointCloud& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RSPointCloud* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.acviewer_msgs.RSPointCloud";
  }
  protected:
  explicit RSPointCloud(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_acviewer_2eproto);
    return ::descriptor_table_acviewer_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDataFieldNumber = 1,
    kSizeFieldNumber = 2,
  };
  // optional bytes data = 1;
  bool has_data() const;
  private:
  bool _internal_has_data() const;
  public:
  void clear_data();
  const std::string& data() const;
  void set_data(const std::string& value);
  void set_data(std::string&& value);
  void set_data(const char* value);
  void set_data(const void* value, size_t size);
  std::string* mutable_data();
  std::string* release_data();
  void set_allocated_data(std::string* data);
  private:
  const std::string& _internal_data() const;
  void _internal_set_data(const std::string& value);
  std::string* _internal_mutable_data();
  public:

  // optional uint32 size = 2 [default = 0];
  bool has_size() const;
  private:
  bool _internal_has_size() const;
  public:
  void clear_size();
  ::PROTOBUF_NAMESPACE_ID::uint32 size() const;
  void set_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_size() const;
  void _internal_set_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:robosense.acviewer_msgs.RSPointCloud)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr data_;
  ::PROTOBUF_NAMESPACE_ID::uint32 size_;
  friend struct ::TableStruct_acviewer_2eproto;
};
// -------------------------------------------------------------------

class RSTrangleFacet PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.acviewer_msgs.RSTrangleFacet) */ {
 public:
  inline RSTrangleFacet() : RSTrangleFacet(nullptr) {}
  virtual ~RSTrangleFacet();

  RSTrangleFacet(const RSTrangleFacet& from);
  RSTrangleFacet(RSTrangleFacet&& from) noexcept
    : RSTrangleFacet() {
    *this = ::std::move(from);
  }

  inline RSTrangleFacet& operator=(const RSTrangleFacet& from) {
    CopyFrom(from);
    return *this;
  }
  inline RSTrangleFacet& operator=(RSTrangleFacet&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RSTrangleFacet& default_instance();

  static inline const RSTrangleFacet* internal_default_instance() {
    return reinterpret_cast<const RSTrangleFacet*>(
               &_RSTrangleFacet_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(RSTrangleFacet& a, RSTrangleFacet& b) {
    a.Swap(&b);
  }
  inline void Swap(RSTrangleFacet* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RSTrangleFacet* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RSTrangleFacet* New() const final {
    return CreateMaybeMessage<RSTrangleFacet>(nullptr);
  }

  RSTrangleFacet* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RSTrangleFacet>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RSTrangleFacet& from);
  void MergeFrom(const RSTrangleFacet& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RSTrangleFacet* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.acviewer_msgs.RSTrangleFacet";
  }
  protected:
  explicit RSTrangleFacet(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_acviewer_2eproto);
    return ::descriptor_table_acviewer_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDataFieldNumber = 1,
    kSizeFieldNumber = 2,
  };
  // optional bytes data = 1;
  bool has_data() const;
  private:
  bool _internal_has_data() const;
  public:
  void clear_data();
  const std::string& data() const;
  void set_data(const std::string& value);
  void set_data(std::string&& value);
  void set_data(const char* value);
  void set_data(const void* value, size_t size);
  std::string* mutable_data();
  std::string* release_data();
  void set_allocated_data(std::string* data);
  private:
  const std::string& _internal_data() const;
  void _internal_set_data(const std::string& value);
  std::string* _internal_mutable_data();
  public:

  // optional uint32 size = 2 [default = 0];
  bool has_size() const;
  private:
  bool _internal_has_size() const;
  public:
  void clear_size();
  ::PROTOBUF_NAMESPACE_ID::uint32 size() const;
  void set_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_size() const;
  void _internal_set_size(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:robosense.acviewer_msgs.RSTrangleFacet)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr data_;
  ::PROTOBUF_NAMESPACE_ID::uint32 size_;
  friend struct ::TableStruct_acviewer_2eproto;
};
// -------------------------------------------------------------------

class RSRgbJpeg PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.acviewer_msgs.RSRgbJpeg) */ {
 public:
  inline RSRgbJpeg() : RSRgbJpeg(nullptr) {}
  virtual ~RSRgbJpeg();

  RSRgbJpeg(const RSRgbJpeg& from);
  RSRgbJpeg(RSRgbJpeg&& from) noexcept
    : RSRgbJpeg() {
    *this = ::std::move(from);
  }

  inline RSRgbJpeg& operator=(const RSRgbJpeg& from) {
    CopyFrom(from);
    return *this;
  }
  inline RSRgbJpeg& operator=(RSRgbJpeg&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RSRgbJpeg& default_instance();

  static inline const RSRgbJpeg* internal_default_instance() {
    return reinterpret_cast<const RSRgbJpeg*>(
               &_RSRgbJpeg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(RSRgbJpeg& a, RSRgbJpeg& b) {
    a.Swap(&b);
  }
  inline void Swap(RSRgbJpeg* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RSRgbJpeg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RSRgbJpeg* New() const final {
    return CreateMaybeMessage<RSRgbJpeg>(nullptr);
  }

  RSRgbJpeg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RSRgbJpeg>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RSRgbJpeg& from);
  void MergeFrom(const RSRgbJpeg& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RSRgbJpeg* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.acviewer_msgs.RSRgbJpeg";
  }
  protected:
  explicit RSRgbJpeg(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_acviewer_2eproto);
    return ::descriptor_table_acviewer_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDataFieldNumber = 4,
    kIsJpegCompressFieldNumber = 1,
    kWidthFieldNumber = 2,
    kTimestampFieldNumber = 5,
    kHeightFieldNumber = 3,
  };
  // optional bytes data = 4;
  bool has_data() const;
  private:
  bool _internal_has_data() const;
  public:
  void clear_data();
  const std::string& data() const;
  void set_data(const std::string& value);
  void set_data(std::string&& value);
  void set_data(const char* value);
  void set_data(const void* value, size_t size);
  std::string* mutable_data();
  std::string* release_data();
  void set_allocated_data(std::string* data);
  private:
  const std::string& _internal_data() const;
  void _internal_set_data(const std::string& value);
  std::string* _internal_mutable_data();
  public:

  // optional bool is_jpeg_compress = 1 [default = false];
  bool has_is_jpeg_compress() const;
  private:
  bool _internal_has_is_jpeg_compress() const;
  public:
  void clear_is_jpeg_compress();
  bool is_jpeg_compress() const;
  void set_is_jpeg_compress(bool value);
  private:
  bool _internal_is_jpeg_compress() const;
  void _internal_set_is_jpeg_compress(bool value);
  public:

  // optional uint32 width = 2;
  bool has_width() const;
  private:
  bool _internal_has_width() const;
  public:
  void clear_width();
  ::PROTOBUF_NAMESPACE_ID::uint32 width() const;
  void set_width(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_width() const;
  void _internal_set_width(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint64 timestamp = 5;
  bool has_timestamp() const;
  private:
  bool _internal_has_timestamp() const;
  public:
  void clear_timestamp();
  ::PROTOBUF_NAMESPACE_ID::uint64 timestamp() const;
  void set_timestamp(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_timestamp() const;
  void _internal_set_timestamp(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // optional uint32 height = 3;
  bool has_height() const;
  private:
  bool _internal_has_height() const;
  public:
  void clear_height();
  ::PROTOBUF_NAMESPACE_ID::uint32 height() const;
  void set_height(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_height() const;
  void _internal_set_height(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:robosense.acviewer_msgs.RSRgbJpeg)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr data_;
  bool is_jpeg_compress_;
  ::PROTOBUF_NAMESPACE_ID::uint32 width_;
  ::PROTOBUF_NAMESPACE_ID::uint64 timestamp_;
  ::PROTOBUF_NAMESPACE_ID::uint32 height_;
  friend struct ::TableStruct_acviewer_2eproto;
};
// -------------------------------------------------------------------

class RSDepthImage PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.acviewer_msgs.RSDepthImage) */ {
 public:
  inline RSDepthImage() : RSDepthImage(nullptr) {}
  virtual ~RSDepthImage();

  RSDepthImage(const RSDepthImage& from);
  RSDepthImage(RSDepthImage&& from) noexcept
    : RSDepthImage() {
    *this = ::std::move(from);
  }

  inline RSDepthImage& operator=(const RSDepthImage& from) {
    CopyFrom(from);
    return *this;
  }
  inline RSDepthImage& operator=(RSDepthImage&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RSDepthImage& default_instance();

  static inline const RSDepthImage* internal_default_instance() {
    return reinterpret_cast<const RSDepthImage*>(
               &_RSDepthImage_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    20;

  friend void swap(RSDepthImage& a, RSDepthImage& b) {
    a.Swap(&b);
  }
  inline void Swap(RSDepthImage* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RSDepthImage* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RSDepthImage* New() const final {
    return CreateMaybeMessage<RSDepthImage>(nullptr);
  }

  RSDepthImage* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RSDepthImage>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RSDepthImage& from);
  void MergeFrom(const RSDepthImage& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RSDepthImage* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.acviewer_msgs.RSDepthImage";
  }
  protected:
  explicit RSDepthImage(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_acviewer_2eproto);
    return ::descriptor_table_acviewer_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDataFieldNumber = 4,
    kWidthFieldNumber = 1,
    kHeightFieldNumber = 2,
    kTimestampFieldNumber = 3,
  };
  // optional bytes data = 4;
  bool has_data() const;
  private:
  bool _internal_has_data() const;
  public:
  void clear_data();
  const std::string& data() const;
  void set_data(const std::string& value);
  void set_data(std::string&& value);
  void set_data(const char* value);
  void set_data(const void* value, size_t size);
  std::string* mutable_data();
  std::string* release_data();
  void set_allocated_data(std::string* data);
  private:
  const std::string& _internal_data() const;
  void _internal_set_data(const std::string& value);
  std::string* _internal_mutable_data();
  public:

  // optional uint32 width = 1;
  bool has_width() const;
  private:
  bool _internal_has_width() const;
  public:
  void clear_width();
  ::PROTOBUF_NAMESPACE_ID::uint32 width() const;
  void set_width(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_width() const;
  void _internal_set_width(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint32 height = 2;
  bool has_height() const;
  private:
  bool _internal_has_height() const;
  public:
  void clear_height();
  ::PROTOBUF_NAMESPACE_ID::uint32 height() const;
  void set_height(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_height() const;
  void _internal_set_height(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional uint64 timestamp = 3;
  bool has_timestamp() const;
  private:
  bool _internal_has_timestamp() const;
  public:
  void clear_timestamp();
  ::PROTOBUF_NAMESPACE_ID::uint64 timestamp() const;
  void set_timestamp(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_timestamp() const;
  void _internal_set_timestamp(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // @@protoc_insertion_point(class_scope:robosense.acviewer_msgs.RSDepthImage)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr data_;
  ::PROTOBUF_NAMESPACE_ID::uint32 width_;
  ::PROTOBUF_NAMESPACE_ID::uint32 height_;
  ::PROTOBUF_NAMESPACE_ID::uint64 timestamp_;
  friend struct ::TableStruct_acviewer_2eproto;
};
// -------------------------------------------------------------------

class RSRender PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.acviewer_msgs.RSRender) */ {
 public:
  inline RSRender() : RSRender(nullptr) {}
  virtual ~RSRender();

  RSRender(const RSRender& from);
  RSRender(RSRender&& from) noexcept
    : RSRender() {
    *this = ::std::move(from);
  }

  inline RSRender& operator=(const RSRender& from) {
    CopyFrom(from);
    return *this;
  }
  inline RSRender& operator=(RSRender&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RSRender& default_instance();

  static inline const RSRender* internal_default_instance() {
    return reinterpret_cast<const RSRender*>(
               &_RSRender_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    21;

  friend void swap(RSRender& a, RSRender& b) {
    a.Swap(&b);
  }
  inline void Swap(RSRender* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RSRender* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RSRender* New() const final {
    return CreateMaybeMessage<RSRender>(nullptr);
  }

  RSRender* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RSRender>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RSRender& from);
  void MergeFrom(const RSRender& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RSRender* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.acviewer_msgs.RSRender";
  }
  protected:
  explicit RSRender(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_acviewer_2eproto);
    return ::descriptor_table_acviewer_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPositionFieldNumber = 1,
    kPointCloudFieldNumber = 2,
    kPointCloudSlamFieldNumber = 3,
    kTriangleListFieldNumber = 4,
    kRgbJpegFieldNumber = 5,
    kDepthImageFieldNumber = 6,
  };
  // optional .robosense.acviewer_msgs.RSPositon position = 1;
  bool has_position() const;
  private:
  bool _internal_has_position() const;
  public:
  void clear_position();
  const ::robosense::acviewer_msgs::RSPositon& position() const;
  ::robosense::acviewer_msgs::RSPositon* release_position();
  ::robosense::acviewer_msgs::RSPositon* mutable_position();
  void set_allocated_position(::robosense::acviewer_msgs::RSPositon* position);
  private:
  const ::robosense::acviewer_msgs::RSPositon& _internal_position() const;
  ::robosense::acviewer_msgs::RSPositon* _internal_mutable_position();
  public:
  void unsafe_arena_set_allocated_position(
      ::robosense::acviewer_msgs::RSPositon* position);
  ::robosense::acviewer_msgs::RSPositon* unsafe_arena_release_position();

  // optional .robosense.acviewer_msgs.RSPointCloud point_cloud = 2;
  bool has_point_cloud() const;
  private:
  bool _internal_has_point_cloud() const;
  public:
  void clear_point_cloud();
  const ::robosense::acviewer_msgs::RSPointCloud& point_cloud() const;
  ::robosense::acviewer_msgs::RSPointCloud* release_point_cloud();
  ::robosense::acviewer_msgs::RSPointCloud* mutable_point_cloud();
  void set_allocated_point_cloud(::robosense::acviewer_msgs::RSPointCloud* point_cloud);
  private:
  const ::robosense::acviewer_msgs::RSPointCloud& _internal_point_cloud() const;
  ::robosense::acviewer_msgs::RSPointCloud* _internal_mutable_point_cloud();
  public:
  void unsafe_arena_set_allocated_point_cloud(
      ::robosense::acviewer_msgs::RSPointCloud* point_cloud);
  ::robosense::acviewer_msgs::RSPointCloud* unsafe_arena_release_point_cloud();

  // optional .robosense.acviewer_msgs.RSPointCloud point_cloud_slam = 3;
  bool has_point_cloud_slam() const;
  private:
  bool _internal_has_point_cloud_slam() const;
  public:
  void clear_point_cloud_slam();
  const ::robosense::acviewer_msgs::RSPointCloud& point_cloud_slam() const;
  ::robosense::acviewer_msgs::RSPointCloud* release_point_cloud_slam();
  ::robosense::acviewer_msgs::RSPointCloud* mutable_point_cloud_slam();
  void set_allocated_point_cloud_slam(::robosense::acviewer_msgs::RSPointCloud* point_cloud_slam);
  private:
  const ::robosense::acviewer_msgs::RSPointCloud& _internal_point_cloud_slam() const;
  ::robosense::acviewer_msgs::RSPointCloud* _internal_mutable_point_cloud_slam();
  public:
  void unsafe_arena_set_allocated_point_cloud_slam(
      ::robosense::acviewer_msgs::RSPointCloud* point_cloud_slam);
  ::robosense::acviewer_msgs::RSPointCloud* unsafe_arena_release_point_cloud_slam();

  // optional .robosense.acviewer_msgs.RSTrangleFacet triangle_list = 4;
  bool has_triangle_list() const;
  private:
  bool _internal_has_triangle_list() const;
  public:
  void clear_triangle_list();
  const ::robosense::acviewer_msgs::RSTrangleFacet& triangle_list() const;
  ::robosense::acviewer_msgs::RSTrangleFacet* release_triangle_list();
  ::robosense::acviewer_msgs::RSTrangleFacet* mutable_triangle_list();
  void set_allocated_triangle_list(::robosense::acviewer_msgs::RSTrangleFacet* triangle_list);
  private:
  const ::robosense::acviewer_msgs::RSTrangleFacet& _internal_triangle_list() const;
  ::robosense::acviewer_msgs::RSTrangleFacet* _internal_mutable_triangle_list();
  public:
  void unsafe_arena_set_allocated_triangle_list(
      ::robosense::acviewer_msgs::RSTrangleFacet* triangle_list);
  ::robosense::acviewer_msgs::RSTrangleFacet* unsafe_arena_release_triangle_list();

  // optional .robosense.acviewer_msgs.RSRgbJpeg rgb_jpeg = 5;
  bool has_rgb_jpeg() const;
  private:
  bool _internal_has_rgb_jpeg() const;
  public:
  void clear_rgb_jpeg();
  const ::robosense::acviewer_msgs::RSRgbJpeg& rgb_jpeg() const;
  ::robosense::acviewer_msgs::RSRgbJpeg* release_rgb_jpeg();
  ::robosense::acviewer_msgs::RSRgbJpeg* mutable_rgb_jpeg();
  void set_allocated_rgb_jpeg(::robosense::acviewer_msgs::RSRgbJpeg* rgb_jpeg);
  private:
  const ::robosense::acviewer_msgs::RSRgbJpeg& _internal_rgb_jpeg() const;
  ::robosense::acviewer_msgs::RSRgbJpeg* _internal_mutable_rgb_jpeg();
  public:
  void unsafe_arena_set_allocated_rgb_jpeg(
      ::robosense::acviewer_msgs::RSRgbJpeg* rgb_jpeg);
  ::robosense::acviewer_msgs::RSRgbJpeg* unsafe_arena_release_rgb_jpeg();

  // optional .robosense.acviewer_msgs.RSDepthImage depth_image = 6;
  bool has_depth_image() const;
  private:
  bool _internal_has_depth_image() const;
  public:
  void clear_depth_image();
  const ::robosense::acviewer_msgs::RSDepthImage& depth_image() const;
  ::robosense::acviewer_msgs::RSDepthImage* release_depth_image();
  ::robosense::acviewer_msgs::RSDepthImage* mutable_depth_image();
  void set_allocated_depth_image(::robosense::acviewer_msgs::RSDepthImage* depth_image);
  private:
  const ::robosense::acviewer_msgs::RSDepthImage& _internal_depth_image() const;
  ::robosense::acviewer_msgs::RSDepthImage* _internal_mutable_depth_image();
  public:
  void unsafe_arena_set_allocated_depth_image(
      ::robosense::acviewer_msgs::RSDepthImage* depth_image);
  ::robosense::acviewer_msgs::RSDepthImage* unsafe_arena_release_depth_image();

  // @@protoc_insertion_point(class_scope:robosense.acviewer_msgs.RSRender)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::robosense::acviewer_msgs::RSPositon* position_;
  ::robosense::acviewer_msgs::RSPointCloud* point_cloud_;
  ::robosense::acviewer_msgs::RSPointCloud* point_cloud_slam_;
  ::robosense::acviewer_msgs::RSTrangleFacet* triangle_list_;
  ::robosense::acviewer_msgs::RSRgbJpeg* rgb_jpeg_;
  ::robosense::acviewer_msgs::RSDepthImage* depth_image_;
  friend struct ::TableStruct_acviewer_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// RSRequest

// required .robosense.acviewer_msgs.RS_CMD_TYPE cmd_type = 1;
inline bool RSRequest::_internal_has_cmd_type() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool RSRequest::has_cmd_type() const {
  return _internal_has_cmd_type();
}
inline void RSRequest::clear_cmd_type() {
  cmd_type_ = 0;
  _has_bits_[0] &= ~0x00000004u;
}
inline ::robosense::acviewer_msgs::RS_CMD_TYPE RSRequest::_internal_cmd_type() const {
  return static_cast< ::robosense::acviewer_msgs::RS_CMD_TYPE >(cmd_type_);
}
inline ::robosense::acviewer_msgs::RS_CMD_TYPE RSRequest::cmd_type() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSRequest.cmd_type)
  return _internal_cmd_type();
}
inline void RSRequest::_internal_set_cmd_type(::robosense::acviewer_msgs::RS_CMD_TYPE value) {
  assert(::robosense::acviewer_msgs::RS_CMD_TYPE_IsValid(value));
  _has_bits_[0] |= 0x00000004u;
  cmd_type_ = value;
}
inline void RSRequest::set_cmd_type(::robosense::acviewer_msgs::RS_CMD_TYPE value) {
  _internal_set_cmd_type(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSRequest.cmd_type)
}

// required uint64 request_Id = 2;
inline bool RSRequest::_internal_has_request_id() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool RSRequest::has_request_id() const {
  return _internal_has_request_id();
}
inline void RSRequest::clear_request_id() {
  request_id_ = PROTOBUF_ULONGLONG(0);
  _has_bits_[0] &= ~0x00000002u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 RSRequest::_internal_request_id() const {
  return request_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 RSRequest::request_id() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSRequest.request_Id)
  return _internal_request_id();
}
inline void RSRequest::_internal_set_request_id(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _has_bits_[0] |= 0x00000002u;
  request_id_ = value;
}
inline void RSRequest::set_request_id(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_request_id(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSRequest.request_Id)
}

// optional .robosense.acviewer_msgs.RSRequestData request_data = 3;
inline bool RSRequest::_internal_has_request_data() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || request_data_ != nullptr);
  return value;
}
inline bool RSRequest::has_request_data() const {
  return _internal_has_request_data();
}
inline void RSRequest::clear_request_data() {
  if (request_data_ != nullptr) request_data_->Clear();
  _has_bits_[0] &= ~0x00000001u;
}
inline const ::robosense::acviewer_msgs::RSRequestData& RSRequest::_internal_request_data() const {
  const ::robosense::acviewer_msgs::RSRequestData* p = request_data_;
  return p != nullptr ? *p : reinterpret_cast<const ::robosense::acviewer_msgs::RSRequestData&>(
      ::robosense::acviewer_msgs::_RSRequestData_default_instance_);
}
inline const ::robosense::acviewer_msgs::RSRequestData& RSRequest::request_data() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSRequest.request_data)
  return _internal_request_data();
}
inline void RSRequest::unsafe_arena_set_allocated_request_data(
    ::robosense::acviewer_msgs::RSRequestData* request_data) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(request_data_);
  }
  request_data_ = request_data;
  if (request_data) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:robosense.acviewer_msgs.RSRequest.request_data)
}
inline ::robosense::acviewer_msgs::RSRequestData* RSRequest::release_request_data() {
  _has_bits_[0] &= ~0x00000001u;
  ::robosense::acviewer_msgs::RSRequestData* temp = request_data_;
  request_data_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::robosense::acviewer_msgs::RSRequestData* RSRequest::unsafe_arena_release_request_data() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RSRequest.request_data)
  _has_bits_[0] &= ~0x00000001u;
  ::robosense::acviewer_msgs::RSRequestData* temp = request_data_;
  request_data_ = nullptr;
  return temp;
}
inline ::robosense::acviewer_msgs::RSRequestData* RSRequest::_internal_mutable_request_data() {
  _has_bits_[0] |= 0x00000001u;
  if (request_data_ == nullptr) {
    auto* p = CreateMaybeMessage<::robosense::acviewer_msgs::RSRequestData>(GetArena());
    request_data_ = p;
  }
  return request_data_;
}
inline ::robosense::acviewer_msgs::RSRequestData* RSRequest::mutable_request_data() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSRequest.request_data)
  return _internal_mutable_request_data();
}
inline void RSRequest::set_allocated_request_data(::robosense::acviewer_msgs::RSRequestData* request_data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete request_data_;
  }
  if (request_data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(request_data);
    if (message_arena != submessage_arena) {
      request_data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, request_data, submessage_arena);
    }
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  request_data_ = request_data;
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RSRequest.request_data)
}

// -------------------------------------------------------------------

// RSShowConfigData

// optional bool show_pos = 1;
inline bool RSShowConfigData::_internal_has_show_pos() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool RSShowConfigData::has_show_pos() const {
  return _internal_has_show_pos();
}
inline void RSShowConfigData::clear_show_pos() {
  show_pos_ = false;
  _has_bits_[0] &= ~0x00000001u;
}
inline bool RSShowConfigData::_internal_show_pos() const {
  return show_pos_;
}
inline bool RSShowConfigData::show_pos() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSShowConfigData.show_pos)
  return _internal_show_pos();
}
inline void RSShowConfigData::_internal_set_show_pos(bool value) {
  _has_bits_[0] |= 0x00000001u;
  show_pos_ = value;
}
inline void RSShowConfigData::set_show_pos(bool value) {
  _internal_set_show_pos(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSShowConfigData.show_pos)
}

// optional bool show_pc = 2;
inline bool RSShowConfigData::_internal_has_show_pc() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool RSShowConfigData::has_show_pc() const {
  return _internal_has_show_pc();
}
inline void RSShowConfigData::clear_show_pc() {
  show_pc_ = false;
  _has_bits_[0] &= ~0x00000002u;
}
inline bool RSShowConfigData::_internal_show_pc() const {
  return show_pc_;
}
inline bool RSShowConfigData::show_pc() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSShowConfigData.show_pc)
  return _internal_show_pc();
}
inline void RSShowConfigData::_internal_set_show_pc(bool value) {
  _has_bits_[0] |= 0x00000002u;
  show_pc_ = value;
}
inline void RSShowConfigData::set_show_pc(bool value) {
  _internal_set_show_pc(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSShowConfigData.show_pc)
}

// optional bool show_pc_slam = 3;
inline bool RSShowConfigData::_internal_has_show_pc_slam() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool RSShowConfigData::has_show_pc_slam() const {
  return _internal_has_show_pc_slam();
}
inline void RSShowConfigData::clear_show_pc_slam() {
  show_pc_slam_ = false;
  _has_bits_[0] &= ~0x00000004u;
}
inline bool RSShowConfigData::_internal_show_pc_slam() const {
  return show_pc_slam_;
}
inline bool RSShowConfigData::show_pc_slam() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSShowConfigData.show_pc_slam)
  return _internal_show_pc_slam();
}
inline void RSShowConfigData::_internal_set_show_pc_slam(bool value) {
  _has_bits_[0] |= 0x00000004u;
  show_pc_slam_ = value;
}
inline void RSShowConfigData::set_show_pc_slam(bool value) {
  _internal_set_show_pc_slam(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSShowConfigData.show_pc_slam)
}

// optional bool show_tri = 4;
inline bool RSShowConfigData::_internal_has_show_tri() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool RSShowConfigData::has_show_tri() const {
  return _internal_has_show_tri();
}
inline void RSShowConfigData::clear_show_tri() {
  show_tri_ = false;
  _has_bits_[0] &= ~0x00000008u;
}
inline bool RSShowConfigData::_internal_show_tri() const {
  return show_tri_;
}
inline bool RSShowConfigData::show_tri() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSShowConfigData.show_tri)
  return _internal_show_tri();
}
inline void RSShowConfigData::_internal_set_show_tri(bool value) {
  _has_bits_[0] |= 0x00000008u;
  show_tri_ = value;
}
inline void RSShowConfigData::set_show_tri(bool value) {
  _internal_set_show_tri(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSShowConfigData.show_tri)
}

// optional bool show_rgb = 5;
inline bool RSShowConfigData::_internal_has_show_rgb() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool RSShowConfigData::has_show_rgb() const {
  return _internal_has_show_rgb();
}
inline void RSShowConfigData::clear_show_rgb() {
  show_rgb_ = false;
  _has_bits_[0] &= ~0x00000010u;
}
inline bool RSShowConfigData::_internal_show_rgb() const {
  return show_rgb_;
}
inline bool RSShowConfigData::show_rgb() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSShowConfigData.show_rgb)
  return _internal_show_rgb();
}
inline void RSShowConfigData::_internal_set_show_rgb(bool value) {
  _has_bits_[0] |= 0x00000010u;
  show_rgb_ = value;
}
inline void RSShowConfigData::set_show_rgb(bool value) {
  _internal_set_show_rgb(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSShowConfigData.show_rgb)
}

// optional bool show_depth = 6;
inline bool RSShowConfigData::_internal_has_show_depth() const {
  bool value = (_has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool RSShowConfigData::has_show_depth() const {
  return _internal_has_show_depth();
}
inline void RSShowConfigData::clear_show_depth() {
  show_depth_ = false;
  _has_bits_[0] &= ~0x00000020u;
}
inline bool RSShowConfigData::_internal_show_depth() const {
  return show_depth_;
}
inline bool RSShowConfigData::show_depth() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSShowConfigData.show_depth)
  return _internal_show_depth();
}
inline void RSShowConfigData::_internal_set_show_depth(bool value) {
  _has_bits_[0] |= 0x00000020u;
  show_depth_ = value;
}
inline void RSShowConfigData::set_show_depth(bool value) {
  _internal_set_show_depth(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSShowConfigData.show_depth)
}

// -------------------------------------------------------------------

// RSOperatorDeviceData

// required string uuid = 1;
inline bool RSOperatorDeviceData::_internal_has_uuid() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool RSOperatorDeviceData::has_uuid() const {
  return _internal_has_uuid();
}
inline void RSOperatorDeviceData::clear_uuid() {
  uuid_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& RSOperatorDeviceData::uuid() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSOperatorDeviceData.uuid)
  return _internal_uuid();
}
inline void RSOperatorDeviceData::set_uuid(const std::string& value) {
  _internal_set_uuid(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSOperatorDeviceData.uuid)
}
inline std::string* RSOperatorDeviceData::mutable_uuid() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSOperatorDeviceData.uuid)
  return _internal_mutable_uuid();
}
inline const std::string& RSOperatorDeviceData::_internal_uuid() const {
  return uuid_.Get();
}
inline void RSOperatorDeviceData::_internal_set_uuid(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void RSOperatorDeviceData::set_uuid(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  uuid_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.acviewer_msgs.RSOperatorDeviceData.uuid)
}
inline void RSOperatorDeviceData::set_uuid(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.acviewer_msgs.RSOperatorDeviceData.uuid)
}
inline void RSOperatorDeviceData::set_uuid(const char* value,
    size_t size) {
  _has_bits_[0] |= 0x00000001u;
  uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.acviewer_msgs.RSOperatorDeviceData.uuid)
}
inline std::string* RSOperatorDeviceData::_internal_mutable_uuid() {
  _has_bits_[0] |= 0x00000001u;
  return uuid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* RSOperatorDeviceData::release_uuid() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RSOperatorDeviceData.uuid)
  if (!_internal_has_uuid()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return uuid_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void RSOperatorDeviceData::set_allocated_uuid(std::string* uuid) {
  if (uuid != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  uuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), uuid,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RSOperatorDeviceData.uuid)
}

// required uint32 operator_type = 2;
inline bool RSOperatorDeviceData::_internal_has_operator_type() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool RSOperatorDeviceData::has_operator_type() const {
  return _internal_has_operator_type();
}
inline void RSOperatorDeviceData::clear_operator_type() {
  operator_type_ = 0u;
  _has_bits_[0] &= ~0x00000002u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 RSOperatorDeviceData::_internal_operator_type() const {
  return operator_type_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 RSOperatorDeviceData::operator_type() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSOperatorDeviceData.operator_type)
  return _internal_operator_type();
}
inline void RSOperatorDeviceData::_internal_set_operator_type(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000002u;
  operator_type_ = value;
}
inline void RSOperatorDeviceData::set_operator_type(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_operator_type(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSOperatorDeviceData.operator_type)
}

// -------------------------------------------------------------------

// RSWriterSetting

// optional string file_name = 1;
inline bool RSWriterSetting::_internal_has_file_name() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool RSWriterSetting::has_file_name() const {
  return _internal_has_file_name();
}
inline void RSWriterSetting::clear_file_name() {
  file_name_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& RSWriterSetting::file_name() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSWriterSetting.file_name)
  return _internal_file_name();
}
inline void RSWriterSetting::set_file_name(const std::string& value) {
  _internal_set_file_name(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSWriterSetting.file_name)
}
inline std::string* RSWriterSetting::mutable_file_name() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSWriterSetting.file_name)
  return _internal_mutable_file_name();
}
inline const std::string& RSWriterSetting::_internal_file_name() const {
  return file_name_.Get();
}
inline void RSWriterSetting::_internal_set_file_name(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  file_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void RSWriterSetting::set_file_name(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  file_name_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.acviewer_msgs.RSWriterSetting.file_name)
}
inline void RSWriterSetting::set_file_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  file_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.acviewer_msgs.RSWriterSetting.file_name)
}
inline void RSWriterSetting::set_file_name(const char* value,
    size_t size) {
  _has_bits_[0] |= 0x00000001u;
  file_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.acviewer_msgs.RSWriterSetting.file_name)
}
inline std::string* RSWriterSetting::_internal_mutable_file_name() {
  _has_bits_[0] |= 0x00000001u;
  return file_name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* RSWriterSetting::release_file_name() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RSWriterSetting.file_name)
  if (!_internal_has_file_name()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return file_name_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void RSWriterSetting::set_allocated_file_name(std::string* file_name) {
  if (file_name != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  file_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), file_name,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RSWriterSetting.file_name)
}

// optional string file_format = 2;
inline bool RSWriterSetting::_internal_has_file_format() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool RSWriterSetting::has_file_format() const {
  return _internal_has_file_format();
}
inline void RSWriterSetting::clear_file_format() {
  file_format_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000002u;
}
inline const std::string& RSWriterSetting::file_format() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSWriterSetting.file_format)
  return _internal_file_format();
}
inline void RSWriterSetting::set_file_format(const std::string& value) {
  _internal_set_file_format(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSWriterSetting.file_format)
}
inline std::string* RSWriterSetting::mutable_file_format() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSWriterSetting.file_format)
  return _internal_mutable_file_format();
}
inline const std::string& RSWriterSetting::_internal_file_format() const {
  return file_format_.Get();
}
inline void RSWriterSetting::_internal_set_file_format(const std::string& value) {
  _has_bits_[0] |= 0x00000002u;
  file_format_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void RSWriterSetting::set_file_format(std::string&& value) {
  _has_bits_[0] |= 0x00000002u;
  file_format_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.acviewer_msgs.RSWriterSetting.file_format)
}
inline void RSWriterSetting::set_file_format(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000002u;
  file_format_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.acviewer_msgs.RSWriterSetting.file_format)
}
inline void RSWriterSetting::set_file_format(const char* value,
    size_t size) {
  _has_bits_[0] |= 0x00000002u;
  file_format_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.acviewer_msgs.RSWriterSetting.file_format)
}
inline std::string* RSWriterSetting::_internal_mutable_file_format() {
  _has_bits_[0] |= 0x00000002u;
  return file_format_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* RSWriterSetting::release_file_format() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RSWriterSetting.file_format)
  if (!_internal_has_file_format()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000002u;
  return file_format_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void RSWriterSetting::set_allocated_file_format(std::string* file_format) {
  if (file_format != nullptr) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  file_format_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), file_format,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RSWriterSetting.file_format)
}

// repeated .robosense.acviewer_msgs.RSTopicSetting topic_setting_list = 3;
inline int RSWriterSetting::_internal_topic_setting_list_size() const {
  return topic_setting_list_.size();
}
inline int RSWriterSetting::topic_setting_list_size() const {
  return _internal_topic_setting_list_size();
}
inline void RSWriterSetting::clear_topic_setting_list() {
  topic_setting_list_.Clear();
}
inline ::robosense::acviewer_msgs::RSTopicSetting* RSWriterSetting::mutable_topic_setting_list(int index) {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSWriterSetting.topic_setting_list)
  return topic_setting_list_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::robosense::acviewer_msgs::RSTopicSetting >*
RSWriterSetting::mutable_topic_setting_list() {
  // @@protoc_insertion_point(field_mutable_list:robosense.acviewer_msgs.RSWriterSetting.topic_setting_list)
  return &topic_setting_list_;
}
inline const ::robosense::acviewer_msgs::RSTopicSetting& RSWriterSetting::_internal_topic_setting_list(int index) const {
  return topic_setting_list_.Get(index);
}
inline const ::robosense::acviewer_msgs::RSTopicSetting& RSWriterSetting::topic_setting_list(int index) const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSWriterSetting.topic_setting_list)
  return _internal_topic_setting_list(index);
}
inline ::robosense::acviewer_msgs::RSTopicSetting* RSWriterSetting::_internal_add_topic_setting_list() {
  return topic_setting_list_.Add();
}
inline ::robosense::acviewer_msgs::RSTopicSetting* RSWriterSetting::add_topic_setting_list() {
  // @@protoc_insertion_point(field_add:robosense.acviewer_msgs.RSWriterSetting.topic_setting_list)
  return _internal_add_topic_setting_list();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::robosense::acviewer_msgs::RSTopicSetting >&
RSWriterSetting::topic_setting_list() const {
  // @@protoc_insertion_point(field_list:robosense.acviewer_msgs.RSWriterSetting.topic_setting_list)
  return topic_setting_list_;
}

// -------------------------------------------------------------------

// RSTopicSetting

// optional string topic = 1;
inline bool RSTopicSetting::_internal_has_topic() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool RSTopicSetting::has_topic() const {
  return _internal_has_topic();
}
inline void RSTopicSetting::clear_topic() {
  topic_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& RSTopicSetting::topic() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSTopicSetting.topic)
  return _internal_topic();
}
inline void RSTopicSetting::set_topic(const std::string& value) {
  _internal_set_topic(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSTopicSetting.topic)
}
inline std::string* RSTopicSetting::mutable_topic() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSTopicSetting.topic)
  return _internal_mutable_topic();
}
inline const std::string& RSTopicSetting::_internal_topic() const {
  return topic_.Get();
}
inline void RSTopicSetting::_internal_set_topic(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  topic_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void RSTopicSetting::set_topic(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  topic_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.acviewer_msgs.RSTopicSetting.topic)
}
inline void RSTopicSetting::set_topic(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  topic_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.acviewer_msgs.RSTopicSetting.topic)
}
inline void RSTopicSetting::set_topic(const char* value,
    size_t size) {
  _has_bits_[0] |= 0x00000001u;
  topic_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.acviewer_msgs.RSTopicSetting.topic)
}
inline std::string* RSTopicSetting::_internal_mutable_topic() {
  _has_bits_[0] |= 0x00000001u;
  return topic_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* RSTopicSetting::release_topic() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RSTopicSetting.topic)
  if (!_internal_has_topic()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return topic_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void RSTopicSetting::set_allocated_topic(std::string* topic) {
  if (topic != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  topic_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), topic,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RSTopicSetting.topic)
}

// optional string compressed_type = 2;
inline bool RSTopicSetting::_internal_has_compressed_type() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool RSTopicSetting::has_compressed_type() const {
  return _internal_has_compressed_type();
}
inline void RSTopicSetting::clear_compressed_type() {
  compressed_type_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000002u;
}
inline const std::string& RSTopicSetting::compressed_type() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSTopicSetting.compressed_type)
  return _internal_compressed_type();
}
inline void RSTopicSetting::set_compressed_type(const std::string& value) {
  _internal_set_compressed_type(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSTopicSetting.compressed_type)
}
inline std::string* RSTopicSetting::mutable_compressed_type() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSTopicSetting.compressed_type)
  return _internal_mutable_compressed_type();
}
inline const std::string& RSTopicSetting::_internal_compressed_type() const {
  return compressed_type_.Get();
}
inline void RSTopicSetting::_internal_set_compressed_type(const std::string& value) {
  _has_bits_[0] |= 0x00000002u;
  compressed_type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void RSTopicSetting::set_compressed_type(std::string&& value) {
  _has_bits_[0] |= 0x00000002u;
  compressed_type_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.acviewer_msgs.RSTopicSetting.compressed_type)
}
inline void RSTopicSetting::set_compressed_type(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000002u;
  compressed_type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.acviewer_msgs.RSTopicSetting.compressed_type)
}
inline void RSTopicSetting::set_compressed_type(const char* value,
    size_t size) {
  _has_bits_[0] |= 0x00000002u;
  compressed_type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.acviewer_msgs.RSTopicSetting.compressed_type)
}
inline std::string* RSTopicSetting::_internal_mutable_compressed_type() {
  _has_bits_[0] |= 0x00000002u;
  return compressed_type_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* RSTopicSetting::release_compressed_type() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RSTopicSetting.compressed_type)
  if (!_internal_has_compressed_type()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000002u;
  return compressed_type_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void RSTopicSetting::set_allocated_compressed_type(std::string* compressed_type) {
  if (compressed_type != nullptr) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  compressed_type_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), compressed_type,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RSTopicSetting.compressed_type)
}

// -------------------------------------------------------------------

// RSReaderSetting

// optional string file_name = 1;
inline bool RSReaderSetting::_internal_has_file_name() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool RSReaderSetting::has_file_name() const {
  return _internal_has_file_name();
}
inline void RSReaderSetting::clear_file_name() {
  file_name_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& RSReaderSetting::file_name() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSReaderSetting.file_name)
  return _internal_file_name();
}
inline void RSReaderSetting::set_file_name(const std::string& value) {
  _internal_set_file_name(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSReaderSetting.file_name)
}
inline std::string* RSReaderSetting::mutable_file_name() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSReaderSetting.file_name)
  return _internal_mutable_file_name();
}
inline const std::string& RSReaderSetting::_internal_file_name() const {
  return file_name_.Get();
}
inline void RSReaderSetting::_internal_set_file_name(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  file_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void RSReaderSetting::set_file_name(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  file_name_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.acviewer_msgs.RSReaderSetting.file_name)
}
inline void RSReaderSetting::set_file_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  file_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.acviewer_msgs.RSReaderSetting.file_name)
}
inline void RSReaderSetting::set_file_name(const char* value,
    size_t size) {
  _has_bits_[0] |= 0x00000001u;
  file_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.acviewer_msgs.RSReaderSetting.file_name)
}
inline std::string* RSReaderSetting::_internal_mutable_file_name() {
  _has_bits_[0] |= 0x00000001u;
  return file_name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* RSReaderSetting::release_file_name() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RSReaderSetting.file_name)
  if (!_internal_has_file_name()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return file_name_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void RSReaderSetting::set_allocated_file_name(std::string* file_name) {
  if (file_name != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  file_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), file_name,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RSReaderSetting.file_name)
}

// optional string main_sync_topic = 2;
inline bool RSReaderSetting::_internal_has_main_sync_topic() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool RSReaderSetting::has_main_sync_topic() const {
  return _internal_has_main_sync_topic();
}
inline void RSReaderSetting::clear_main_sync_topic() {
  main_sync_topic_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000002u;
}
inline const std::string& RSReaderSetting::main_sync_topic() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSReaderSetting.main_sync_topic)
  return _internal_main_sync_topic();
}
inline void RSReaderSetting::set_main_sync_topic(const std::string& value) {
  _internal_set_main_sync_topic(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSReaderSetting.main_sync_topic)
}
inline std::string* RSReaderSetting::mutable_main_sync_topic() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSReaderSetting.main_sync_topic)
  return _internal_mutable_main_sync_topic();
}
inline const std::string& RSReaderSetting::_internal_main_sync_topic() const {
  return main_sync_topic_.Get();
}
inline void RSReaderSetting::_internal_set_main_sync_topic(const std::string& value) {
  _has_bits_[0] |= 0x00000002u;
  main_sync_topic_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void RSReaderSetting::set_main_sync_topic(std::string&& value) {
  _has_bits_[0] |= 0x00000002u;
  main_sync_topic_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.acviewer_msgs.RSReaderSetting.main_sync_topic)
}
inline void RSReaderSetting::set_main_sync_topic(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000002u;
  main_sync_topic_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.acviewer_msgs.RSReaderSetting.main_sync_topic)
}
inline void RSReaderSetting::set_main_sync_topic(const char* value,
    size_t size) {
  _has_bits_[0] |= 0x00000002u;
  main_sync_topic_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.acviewer_msgs.RSReaderSetting.main_sync_topic)
}
inline std::string* RSReaderSetting::_internal_mutable_main_sync_topic() {
  _has_bits_[0] |= 0x00000002u;
  return main_sync_topic_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* RSReaderSetting::release_main_sync_topic() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RSReaderSetting.main_sync_topic)
  if (!_internal_has_main_sync_topic()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000002u;
  return main_sync_topic_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void RSReaderSetting::set_allocated_main_sync_topic(std::string* main_sync_topic) {
  if (main_sync_topic != nullptr) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  main_sync_topic_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), main_sync_topic,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RSReaderSetting.main_sync_topic)
}

// optional uint32 pre_load_mains_sync_cnt = 3;
inline bool RSReaderSetting::_internal_has_pre_load_mains_sync_cnt() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool RSReaderSetting::has_pre_load_mains_sync_cnt() const {
  return _internal_has_pre_load_mains_sync_cnt();
}
inline void RSReaderSetting::clear_pre_load_mains_sync_cnt() {
  pre_load_mains_sync_cnt_ = 0u;
  _has_bits_[0] &= ~0x00000004u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 RSReaderSetting::_internal_pre_load_mains_sync_cnt() const {
  return pre_load_mains_sync_cnt_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 RSReaderSetting::pre_load_mains_sync_cnt() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSReaderSetting.pre_load_mains_sync_cnt)
  return _internal_pre_load_mains_sync_cnt();
}
inline void RSReaderSetting::_internal_set_pre_load_mains_sync_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000004u;
  pre_load_mains_sync_cnt_ = value;
}
inline void RSReaderSetting::set_pre_load_mains_sync_cnt(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_pre_load_mains_sync_cnt(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSReaderSetting.pre_load_mains_sync_cnt)
}

// repeated string read_topic_names = 4;
inline int RSReaderSetting::_internal_read_topic_names_size() const {
  return read_topic_names_.size();
}
inline int RSReaderSetting::read_topic_names_size() const {
  return _internal_read_topic_names_size();
}
inline void RSReaderSetting::clear_read_topic_names() {
  read_topic_names_.Clear();
}
inline std::string* RSReaderSetting::add_read_topic_names() {
  // @@protoc_insertion_point(field_add_mutable:robosense.acviewer_msgs.RSReaderSetting.read_topic_names)
  return _internal_add_read_topic_names();
}
inline const std::string& RSReaderSetting::_internal_read_topic_names(int index) const {
  return read_topic_names_.Get(index);
}
inline const std::string& RSReaderSetting::read_topic_names(int index) const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSReaderSetting.read_topic_names)
  return _internal_read_topic_names(index);
}
inline std::string* RSReaderSetting::mutable_read_topic_names(int index) {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSReaderSetting.read_topic_names)
  return read_topic_names_.Mutable(index);
}
inline void RSReaderSetting::set_read_topic_names(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSReaderSetting.read_topic_names)
  read_topic_names_.Mutable(index)->assign(value);
}
inline void RSReaderSetting::set_read_topic_names(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSReaderSetting.read_topic_names)
  read_topic_names_.Mutable(index)->assign(std::move(value));
}
inline void RSReaderSetting::set_read_topic_names(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  read_topic_names_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:robosense.acviewer_msgs.RSReaderSetting.read_topic_names)
}
inline void RSReaderSetting::set_read_topic_names(int index, const char* value, size_t size) {
  read_topic_names_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:robosense.acviewer_msgs.RSReaderSetting.read_topic_names)
}
inline std::string* RSReaderSetting::_internal_add_read_topic_names() {
  return read_topic_names_.Add();
}
inline void RSReaderSetting::add_read_topic_names(const std::string& value) {
  read_topic_names_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:robosense.acviewer_msgs.RSReaderSetting.read_topic_names)
}
inline void RSReaderSetting::add_read_topic_names(std::string&& value) {
  read_topic_names_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:robosense.acviewer_msgs.RSReaderSetting.read_topic_names)
}
inline void RSReaderSetting::add_read_topic_names(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  read_topic_names_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:robosense.acviewer_msgs.RSReaderSetting.read_topic_names)
}
inline void RSReaderSetting::add_read_topic_names(const char* value, size_t size) {
  read_topic_names_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:robosense.acviewer_msgs.RSReaderSetting.read_topic_names)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
RSReaderSetting::read_topic_names() const {
  // @@protoc_insertion_point(field_list:robosense.acviewer_msgs.RSReaderSetting.read_topic_names)
  return read_topic_names_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
RSReaderSetting::mutable_read_topic_names() {
  // @@protoc_insertion_point(field_mutable_list:robosense.acviewer_msgs.RSReaderSetting.read_topic_names)
  return &read_topic_names_;
}

// optional int64 read_start_timestamp_ns = 5 [default = -1];
inline bool RSReaderSetting::_internal_has_read_start_timestamp_ns() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool RSReaderSetting::has_read_start_timestamp_ns() const {
  return _internal_has_read_start_timestamp_ns();
}
inline void RSReaderSetting::clear_read_start_timestamp_ns() {
  read_start_timestamp_ns_ = PROTOBUF_LONGLONG(-1);
  _has_bits_[0] &= ~0x00000008u;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 RSReaderSetting::_internal_read_start_timestamp_ns() const {
  return read_start_timestamp_ns_;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 RSReaderSetting::read_start_timestamp_ns() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSReaderSetting.read_start_timestamp_ns)
  return _internal_read_start_timestamp_ns();
}
inline void RSReaderSetting::_internal_set_read_start_timestamp_ns(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _has_bits_[0] |= 0x00000008u;
  read_start_timestamp_ns_ = value;
}
inline void RSReaderSetting::set_read_start_timestamp_ns(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _internal_set_read_start_timestamp_ns(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSReaderSetting.read_start_timestamp_ns)
}

// optional int64 read_end_timestamp_ns = 6 [default = -1];
inline bool RSReaderSetting::_internal_has_read_end_timestamp_ns() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool RSReaderSetting::has_read_end_timestamp_ns() const {
  return _internal_has_read_end_timestamp_ns();
}
inline void RSReaderSetting::clear_read_end_timestamp_ns() {
  read_end_timestamp_ns_ = PROTOBUF_LONGLONG(-1);
  _has_bits_[0] &= ~0x00000010u;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 RSReaderSetting::_internal_read_end_timestamp_ns() const {
  return read_end_timestamp_ns_;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 RSReaderSetting::read_end_timestamp_ns() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSReaderSetting.read_end_timestamp_ns)
  return _internal_read_end_timestamp_ns();
}
inline void RSReaderSetting::_internal_set_read_end_timestamp_ns(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _has_bits_[0] |= 0x00000010u;
  read_end_timestamp_ns_ = value;
}
inline void RSReaderSetting::set_read_end_timestamp_ns(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _internal_set_read_end_timestamp_ns(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSReaderSetting.read_end_timestamp_ns)
}

// -------------------------------------------------------------------

// RSRequestData

// optional uint32 runMode = 1;
inline bool RSRequestData::_internal_has_runmode() const {
  bool value = (_has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool RSRequestData::has_runmode() const {
  return _internal_has_runmode();
}
inline void RSRequestData::clear_runmode() {
  runmode_ = 0u;
  _has_bits_[0] &= ~0x00000020u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 RSRequestData::_internal_runmode() const {
  return runmode_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 RSRequestData::runmode() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSRequestData.runMode)
  return _internal_runmode();
}
inline void RSRequestData::_internal_set_runmode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000020u;
  runmode_ = value;
}
inline void RSRequestData::set_runmode(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_runmode(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSRequestData.runMode)
}

// optional .robosense.acviewer_msgs.RSShowConfigData show_config_data = 2;
inline bool RSRequestData::_internal_has_show_config_data() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  PROTOBUF_ASSUME(!value || show_config_data_ != nullptr);
  return value;
}
inline bool RSRequestData::has_show_config_data() const {
  return _internal_has_show_config_data();
}
inline void RSRequestData::clear_show_config_data() {
  if (show_config_data_ != nullptr) show_config_data_->Clear();
  _has_bits_[0] &= ~0x00000002u;
}
inline const ::robosense::acviewer_msgs::RSShowConfigData& RSRequestData::_internal_show_config_data() const {
  const ::robosense::acviewer_msgs::RSShowConfigData* p = show_config_data_;
  return p != nullptr ? *p : reinterpret_cast<const ::robosense::acviewer_msgs::RSShowConfigData&>(
      ::robosense::acviewer_msgs::_RSShowConfigData_default_instance_);
}
inline const ::robosense::acviewer_msgs::RSShowConfigData& RSRequestData::show_config_data() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSRequestData.show_config_data)
  return _internal_show_config_data();
}
inline void RSRequestData::unsafe_arena_set_allocated_show_config_data(
    ::robosense::acviewer_msgs::RSShowConfigData* show_config_data) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(show_config_data_);
  }
  show_config_data_ = show_config_data;
  if (show_config_data) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:robosense.acviewer_msgs.RSRequestData.show_config_data)
}
inline ::robosense::acviewer_msgs::RSShowConfigData* RSRequestData::release_show_config_data() {
  _has_bits_[0] &= ~0x00000002u;
  ::robosense::acviewer_msgs::RSShowConfigData* temp = show_config_data_;
  show_config_data_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::robosense::acviewer_msgs::RSShowConfigData* RSRequestData::unsafe_arena_release_show_config_data() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RSRequestData.show_config_data)
  _has_bits_[0] &= ~0x00000002u;
  ::robosense::acviewer_msgs::RSShowConfigData* temp = show_config_data_;
  show_config_data_ = nullptr;
  return temp;
}
inline ::robosense::acviewer_msgs::RSShowConfigData* RSRequestData::_internal_mutable_show_config_data() {
  _has_bits_[0] |= 0x00000002u;
  if (show_config_data_ == nullptr) {
    auto* p = CreateMaybeMessage<::robosense::acviewer_msgs::RSShowConfigData>(GetArena());
    show_config_data_ = p;
  }
  return show_config_data_;
}
inline ::robosense::acviewer_msgs::RSShowConfigData* RSRequestData::mutable_show_config_data() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSRequestData.show_config_data)
  return _internal_mutable_show_config_data();
}
inline void RSRequestData::set_allocated_show_config_data(::robosense::acviewer_msgs::RSShowConfigData* show_config_data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete show_config_data_;
  }
  if (show_config_data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(show_config_data);
    if (message_arena != submessage_arena) {
      show_config_data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, show_config_data, submessage_arena);
    }
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  show_config_data_ = show_config_data;
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RSRequestData.show_config_data)
}

// optional .robosense.acviewer_msgs.RSOperatorDeviceData operator_device_data = 3;
inline bool RSRequestData::_internal_has_operator_device_data() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  PROTOBUF_ASSUME(!value || operator_device_data_ != nullptr);
  return value;
}
inline bool RSRequestData::has_operator_device_data() const {
  return _internal_has_operator_device_data();
}
inline void RSRequestData::clear_operator_device_data() {
  if (operator_device_data_ != nullptr) operator_device_data_->Clear();
  _has_bits_[0] &= ~0x00000004u;
}
inline const ::robosense::acviewer_msgs::RSOperatorDeviceData& RSRequestData::_internal_operator_device_data() const {
  const ::robosense::acviewer_msgs::RSOperatorDeviceData* p = operator_device_data_;
  return p != nullptr ? *p : reinterpret_cast<const ::robosense::acviewer_msgs::RSOperatorDeviceData&>(
      ::robosense::acviewer_msgs::_RSOperatorDeviceData_default_instance_);
}
inline const ::robosense::acviewer_msgs::RSOperatorDeviceData& RSRequestData::operator_device_data() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSRequestData.operator_device_data)
  return _internal_operator_device_data();
}
inline void RSRequestData::unsafe_arena_set_allocated_operator_device_data(
    ::robosense::acviewer_msgs::RSOperatorDeviceData* operator_device_data) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(operator_device_data_);
  }
  operator_device_data_ = operator_device_data;
  if (operator_device_data) {
    _has_bits_[0] |= 0x00000004u;
  } else {
    _has_bits_[0] &= ~0x00000004u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:robosense.acviewer_msgs.RSRequestData.operator_device_data)
}
inline ::robosense::acviewer_msgs::RSOperatorDeviceData* RSRequestData::release_operator_device_data() {
  _has_bits_[0] &= ~0x00000004u;
  ::robosense::acviewer_msgs::RSOperatorDeviceData* temp = operator_device_data_;
  operator_device_data_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::robosense::acviewer_msgs::RSOperatorDeviceData* RSRequestData::unsafe_arena_release_operator_device_data() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RSRequestData.operator_device_data)
  _has_bits_[0] &= ~0x00000004u;
  ::robosense::acviewer_msgs::RSOperatorDeviceData* temp = operator_device_data_;
  operator_device_data_ = nullptr;
  return temp;
}
inline ::robosense::acviewer_msgs::RSOperatorDeviceData* RSRequestData::_internal_mutable_operator_device_data() {
  _has_bits_[0] |= 0x00000004u;
  if (operator_device_data_ == nullptr) {
    auto* p = CreateMaybeMessage<::robosense::acviewer_msgs::RSOperatorDeviceData>(GetArena());
    operator_device_data_ = p;
  }
  return operator_device_data_;
}
inline ::robosense::acviewer_msgs::RSOperatorDeviceData* RSRequestData::mutable_operator_device_data() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSRequestData.operator_device_data)
  return _internal_mutable_operator_device_data();
}
inline void RSRequestData::set_allocated_operator_device_data(::robosense::acviewer_msgs::RSOperatorDeviceData* operator_device_data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete operator_device_data_;
  }
  if (operator_device_data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(operator_device_data);
    if (message_arena != submessage_arena) {
      operator_device_data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, operator_device_data, submessage_arena);
    }
    _has_bits_[0] |= 0x00000004u;
  } else {
    _has_bits_[0] &= ~0x00000004u;
  }
  operator_device_data_ = operator_device_data;
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RSRequestData.operator_device_data)
}

// optional .robosense.acviewer_msgs.RSWriterSetting writer_setting = 4;
inline bool RSRequestData::_internal_has_writer_setting() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  PROTOBUF_ASSUME(!value || writer_setting_ != nullptr);
  return value;
}
inline bool RSRequestData::has_writer_setting() const {
  return _internal_has_writer_setting();
}
inline void RSRequestData::clear_writer_setting() {
  if (writer_setting_ != nullptr) writer_setting_->Clear();
  _has_bits_[0] &= ~0x00000008u;
}
inline const ::robosense::acviewer_msgs::RSWriterSetting& RSRequestData::_internal_writer_setting() const {
  const ::robosense::acviewer_msgs::RSWriterSetting* p = writer_setting_;
  return p != nullptr ? *p : reinterpret_cast<const ::robosense::acviewer_msgs::RSWriterSetting&>(
      ::robosense::acviewer_msgs::_RSWriterSetting_default_instance_);
}
inline const ::robosense::acviewer_msgs::RSWriterSetting& RSRequestData::writer_setting() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSRequestData.writer_setting)
  return _internal_writer_setting();
}
inline void RSRequestData::unsafe_arena_set_allocated_writer_setting(
    ::robosense::acviewer_msgs::RSWriterSetting* writer_setting) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(writer_setting_);
  }
  writer_setting_ = writer_setting;
  if (writer_setting) {
    _has_bits_[0] |= 0x00000008u;
  } else {
    _has_bits_[0] &= ~0x00000008u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:robosense.acviewer_msgs.RSRequestData.writer_setting)
}
inline ::robosense::acviewer_msgs::RSWriterSetting* RSRequestData::release_writer_setting() {
  _has_bits_[0] &= ~0x00000008u;
  ::robosense::acviewer_msgs::RSWriterSetting* temp = writer_setting_;
  writer_setting_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::robosense::acviewer_msgs::RSWriterSetting* RSRequestData::unsafe_arena_release_writer_setting() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RSRequestData.writer_setting)
  _has_bits_[0] &= ~0x00000008u;
  ::robosense::acviewer_msgs::RSWriterSetting* temp = writer_setting_;
  writer_setting_ = nullptr;
  return temp;
}
inline ::robosense::acviewer_msgs::RSWriterSetting* RSRequestData::_internal_mutable_writer_setting() {
  _has_bits_[0] |= 0x00000008u;
  if (writer_setting_ == nullptr) {
    auto* p = CreateMaybeMessage<::robosense::acviewer_msgs::RSWriterSetting>(GetArena());
    writer_setting_ = p;
  }
  return writer_setting_;
}
inline ::robosense::acviewer_msgs::RSWriterSetting* RSRequestData::mutable_writer_setting() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSRequestData.writer_setting)
  return _internal_mutable_writer_setting();
}
inline void RSRequestData::set_allocated_writer_setting(::robosense::acviewer_msgs::RSWriterSetting* writer_setting) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete writer_setting_;
  }
  if (writer_setting) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(writer_setting);
    if (message_arena != submessage_arena) {
      writer_setting = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, writer_setting, submessage_arena);
    }
    _has_bits_[0] |= 0x00000008u;
  } else {
    _has_bits_[0] &= ~0x00000008u;
  }
  writer_setting_ = writer_setting;
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RSRequestData.writer_setting)
}

// optional .robosense.acviewer_msgs.RSReaderSetting reader_setting = 5;
inline bool RSRequestData::_internal_has_reader_setting() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  PROTOBUF_ASSUME(!value || reader_setting_ != nullptr);
  return value;
}
inline bool RSRequestData::has_reader_setting() const {
  return _internal_has_reader_setting();
}
inline void RSRequestData::clear_reader_setting() {
  if (reader_setting_ != nullptr) reader_setting_->Clear();
  _has_bits_[0] &= ~0x00000010u;
}
inline const ::robosense::acviewer_msgs::RSReaderSetting& RSRequestData::_internal_reader_setting() const {
  const ::robosense::acviewer_msgs::RSReaderSetting* p = reader_setting_;
  return p != nullptr ? *p : reinterpret_cast<const ::robosense::acviewer_msgs::RSReaderSetting&>(
      ::robosense::acviewer_msgs::_RSReaderSetting_default_instance_);
}
inline const ::robosense::acviewer_msgs::RSReaderSetting& RSRequestData::reader_setting() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSRequestData.reader_setting)
  return _internal_reader_setting();
}
inline void RSRequestData::unsafe_arena_set_allocated_reader_setting(
    ::robosense::acviewer_msgs::RSReaderSetting* reader_setting) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(reader_setting_);
  }
  reader_setting_ = reader_setting;
  if (reader_setting) {
    _has_bits_[0] |= 0x00000010u;
  } else {
    _has_bits_[0] &= ~0x00000010u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:robosense.acviewer_msgs.RSRequestData.reader_setting)
}
inline ::robosense::acviewer_msgs::RSReaderSetting* RSRequestData::release_reader_setting() {
  _has_bits_[0] &= ~0x00000010u;
  ::robosense::acviewer_msgs::RSReaderSetting* temp = reader_setting_;
  reader_setting_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::robosense::acviewer_msgs::RSReaderSetting* RSRequestData::unsafe_arena_release_reader_setting() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RSRequestData.reader_setting)
  _has_bits_[0] &= ~0x00000010u;
  ::robosense::acviewer_msgs::RSReaderSetting* temp = reader_setting_;
  reader_setting_ = nullptr;
  return temp;
}
inline ::robosense::acviewer_msgs::RSReaderSetting* RSRequestData::_internal_mutable_reader_setting() {
  _has_bits_[0] |= 0x00000010u;
  if (reader_setting_ == nullptr) {
    auto* p = CreateMaybeMessage<::robosense::acviewer_msgs::RSReaderSetting>(GetArena());
    reader_setting_ = p;
  }
  return reader_setting_;
}
inline ::robosense::acviewer_msgs::RSReaderSetting* RSRequestData::mutable_reader_setting() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSRequestData.reader_setting)
  return _internal_mutable_reader_setting();
}
inline void RSRequestData::set_allocated_reader_setting(::robosense::acviewer_msgs::RSReaderSetting* reader_setting) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete reader_setting_;
  }
  if (reader_setting) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(reader_setting);
    if (message_arena != submessage_arena) {
      reader_setting = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, reader_setting, submessage_arena);
    }
    _has_bits_[0] |= 0x00000010u;
  } else {
    _has_bits_[0] &= ~0x00000010u;
  }
  reader_setting_ = reader_setting;
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RSRequestData.reader_setting)
}

// optional string topic_file_path = 6;
inline bool RSRequestData::_internal_has_topic_file_path() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool RSRequestData::has_topic_file_path() const {
  return _internal_has_topic_file_path();
}
inline void RSRequestData::clear_topic_file_path() {
  topic_file_path_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& RSRequestData::topic_file_path() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSRequestData.topic_file_path)
  return _internal_topic_file_path();
}
inline void RSRequestData::set_topic_file_path(const std::string& value) {
  _internal_set_topic_file_path(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSRequestData.topic_file_path)
}
inline std::string* RSRequestData::mutable_topic_file_path() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSRequestData.topic_file_path)
  return _internal_mutable_topic_file_path();
}
inline const std::string& RSRequestData::_internal_topic_file_path() const {
  return topic_file_path_.Get();
}
inline void RSRequestData::_internal_set_topic_file_path(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  topic_file_path_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void RSRequestData::set_topic_file_path(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  topic_file_path_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.acviewer_msgs.RSRequestData.topic_file_path)
}
inline void RSRequestData::set_topic_file_path(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  topic_file_path_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.acviewer_msgs.RSRequestData.topic_file_path)
}
inline void RSRequestData::set_topic_file_path(const char* value,
    size_t size) {
  _has_bits_[0] |= 0x00000001u;
  topic_file_path_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.acviewer_msgs.RSRequestData.topic_file_path)
}
inline std::string* RSRequestData::_internal_mutable_topic_file_path() {
  _has_bits_[0] |= 0x00000001u;
  return topic_file_path_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* RSRequestData::release_topic_file_path() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RSRequestData.topic_file_path)
  if (!_internal_has_topic_file_path()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return topic_file_path_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void RSRequestData::set_allocated_topic_file_path(std::string* topic_file_path) {
  if (topic_file_path != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  topic_file_path_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), topic_file_path,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RSRequestData.topic_file_path)
}

// optional int32 skip_frame = 7;
inline bool RSRequestData::_internal_has_skip_frame() const {
  bool value = (_has_bits_[0] & 0x00000040u) != 0;
  return value;
}
inline bool RSRequestData::has_skip_frame() const {
  return _internal_has_skip_frame();
}
inline void RSRequestData::clear_skip_frame() {
  skip_frame_ = 0;
  _has_bits_[0] &= ~0x00000040u;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RSRequestData::_internal_skip_frame() const {
  return skip_frame_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RSRequestData::skip_frame() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSRequestData.skip_frame)
  return _internal_skip_frame();
}
inline void RSRequestData::_internal_set_skip_frame(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _has_bits_[0] |= 0x00000040u;
  skip_frame_ = value;
}
inline void RSRequestData::set_skip_frame(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_skip_frame(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSRequestData.skip_frame)
}

// -------------------------------------------------------------------

// RSResponse

// required .robosense.acviewer_msgs.RS_CMD_TYPE cmd_type = 1;
inline bool RSResponse::_internal_has_cmd_type() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool RSResponse::has_cmd_type() const {
  return _internal_has_cmd_type();
}
inline void RSResponse::clear_cmd_type() {
  cmd_type_ = 0;
  _has_bits_[0] &= ~0x00000008u;
}
inline ::robosense::acviewer_msgs::RS_CMD_TYPE RSResponse::_internal_cmd_type() const {
  return static_cast< ::robosense::acviewer_msgs::RS_CMD_TYPE >(cmd_type_);
}
inline ::robosense::acviewer_msgs::RS_CMD_TYPE RSResponse::cmd_type() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSResponse.cmd_type)
  return _internal_cmd_type();
}
inline void RSResponse::_internal_set_cmd_type(::robosense::acviewer_msgs::RS_CMD_TYPE value) {
  assert(::robosense::acviewer_msgs::RS_CMD_TYPE_IsValid(value));
  _has_bits_[0] |= 0x00000008u;
  cmd_type_ = value;
}
inline void RSResponse::set_cmd_type(::robosense::acviewer_msgs::RS_CMD_TYPE value) {
  _internal_set_cmd_type(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSResponse.cmd_type)
}

// required uint64 request_Id = 2;
inline bool RSResponse::_internal_has_request_id() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool RSResponse::has_request_id() const {
  return _internal_has_request_id();
}
inline void RSResponse::clear_request_id() {
  request_id_ = PROTOBUF_ULONGLONG(0);
  _has_bits_[0] &= ~0x00000004u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 RSResponse::_internal_request_id() const {
  return request_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 RSResponse::request_id() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSResponse.request_Id)
  return _internal_request_id();
}
inline void RSResponse::_internal_set_request_id(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _has_bits_[0] |= 0x00000004u;
  request_id_ = value;
}
inline void RSResponse::set_request_id(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_request_id(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSResponse.request_Id)
}

// required uint64 response_Id = 3;
inline bool RSResponse::_internal_has_response_id() const {
  bool value = (_has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool RSResponse::has_response_id() const {
  return _internal_has_response_id();
}
inline void RSResponse::clear_response_id() {
  response_id_ = PROTOBUF_ULONGLONG(0);
  _has_bits_[0] &= ~0x00000020u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 RSResponse::_internal_response_id() const {
  return response_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 RSResponse::response_id() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSResponse.response_Id)
  return _internal_response_id();
}
inline void RSResponse::_internal_set_response_id(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _has_bits_[0] |= 0x00000020u;
  response_id_ = value;
}
inline void RSResponse::set_response_id(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_response_id(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSResponse.response_Id)
}

// required uint32 response_code = 4;
inline bool RSResponse::_internal_has_response_code() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool RSResponse::has_response_code() const {
  return _internal_has_response_code();
}
inline void RSResponse::clear_response_code() {
  response_code_ = 0u;
  _has_bits_[0] &= ~0x00000010u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 RSResponse::_internal_response_code() const {
  return response_code_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 RSResponse::response_code() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSResponse.response_code)
  return _internal_response_code();
}
inline void RSResponse::_internal_set_response_code(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000010u;
  response_code_ = value;
}
inline void RSResponse::set_response_code(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_response_code(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSResponse.response_code)
}

// optional string response_info = 5;
inline bool RSResponse::_internal_has_response_info() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool RSResponse::has_response_info() const {
  return _internal_has_response_info();
}
inline void RSResponse::clear_response_info() {
  response_info_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& RSResponse::response_info() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSResponse.response_info)
  return _internal_response_info();
}
inline void RSResponse::set_response_info(const std::string& value) {
  _internal_set_response_info(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSResponse.response_info)
}
inline std::string* RSResponse::mutable_response_info() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSResponse.response_info)
  return _internal_mutable_response_info();
}
inline const std::string& RSResponse::_internal_response_info() const {
  return response_info_.Get();
}
inline void RSResponse::_internal_set_response_info(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  response_info_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void RSResponse::set_response_info(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  response_info_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.acviewer_msgs.RSResponse.response_info)
}
inline void RSResponse::set_response_info(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  response_info_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.acviewer_msgs.RSResponse.response_info)
}
inline void RSResponse::set_response_info(const char* value,
    size_t size) {
  _has_bits_[0] |= 0x00000001u;
  response_info_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.acviewer_msgs.RSResponse.response_info)
}
inline std::string* RSResponse::_internal_mutable_response_info() {
  _has_bits_[0] |= 0x00000001u;
  return response_info_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* RSResponse::release_response_info() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RSResponse.response_info)
  if (!_internal_has_response_info()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return response_info_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void RSResponse::set_allocated_response_info(std::string* response_info) {
  if (response_info != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  response_info_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), response_info,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RSResponse.response_info)
}

// optional .robosense.acviewer_msgs.RSResponseData response_data = 6;
inline bool RSResponse::_internal_has_response_data() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  PROTOBUF_ASSUME(!value || response_data_ != nullptr);
  return value;
}
inline bool RSResponse::has_response_data() const {
  return _internal_has_response_data();
}
inline void RSResponse::clear_response_data() {
  if (response_data_ != nullptr) response_data_->Clear();
  _has_bits_[0] &= ~0x00000002u;
}
inline const ::robosense::acviewer_msgs::RSResponseData& RSResponse::_internal_response_data() const {
  const ::robosense::acviewer_msgs::RSResponseData* p = response_data_;
  return p != nullptr ? *p : reinterpret_cast<const ::robosense::acviewer_msgs::RSResponseData&>(
      ::robosense::acviewer_msgs::_RSResponseData_default_instance_);
}
inline const ::robosense::acviewer_msgs::RSResponseData& RSResponse::response_data() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSResponse.response_data)
  return _internal_response_data();
}
inline void RSResponse::unsafe_arena_set_allocated_response_data(
    ::robosense::acviewer_msgs::RSResponseData* response_data) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(response_data_);
  }
  response_data_ = response_data;
  if (response_data) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:robosense.acviewer_msgs.RSResponse.response_data)
}
inline ::robosense::acviewer_msgs::RSResponseData* RSResponse::release_response_data() {
  _has_bits_[0] &= ~0x00000002u;
  ::robosense::acviewer_msgs::RSResponseData* temp = response_data_;
  response_data_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::robosense::acviewer_msgs::RSResponseData* RSResponse::unsafe_arena_release_response_data() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RSResponse.response_data)
  _has_bits_[0] &= ~0x00000002u;
  ::robosense::acviewer_msgs::RSResponseData* temp = response_data_;
  response_data_ = nullptr;
  return temp;
}
inline ::robosense::acviewer_msgs::RSResponseData* RSResponse::_internal_mutable_response_data() {
  _has_bits_[0] |= 0x00000002u;
  if (response_data_ == nullptr) {
    auto* p = CreateMaybeMessage<::robosense::acviewer_msgs::RSResponseData>(GetArena());
    response_data_ = p;
  }
  return response_data_;
}
inline ::robosense::acviewer_msgs::RSResponseData* RSResponse::mutable_response_data() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSResponse.response_data)
  return _internal_mutable_response_data();
}
inline void RSResponse::set_allocated_response_data(::robosense::acviewer_msgs::RSResponseData* response_data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete response_data_;
  }
  if (response_data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(response_data);
    if (message_arena != submessage_arena) {
      response_data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, response_data, submessage_arena);
    }
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  response_data_ = response_data;
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RSResponse.response_data)
}

// -------------------------------------------------------------------

// RSDeviceInfo

// optional string uuid = 1;
inline bool RSDeviceInfo::_internal_has_uuid() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool RSDeviceInfo::has_uuid() const {
  return _internal_has_uuid();
}
inline void RSDeviceInfo::clear_uuid() {
  uuid_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& RSDeviceInfo::uuid() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSDeviceInfo.uuid)
  return _internal_uuid();
}
inline void RSDeviceInfo::set_uuid(const std::string& value) {
  _internal_set_uuid(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSDeviceInfo.uuid)
}
inline std::string* RSDeviceInfo::mutable_uuid() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSDeviceInfo.uuid)
  return _internal_mutable_uuid();
}
inline const std::string& RSDeviceInfo::_internal_uuid() const {
  return uuid_.Get();
}
inline void RSDeviceInfo::_internal_set_uuid(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void RSDeviceInfo::set_uuid(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  uuid_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.acviewer_msgs.RSDeviceInfo.uuid)
}
inline void RSDeviceInfo::set_uuid(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.acviewer_msgs.RSDeviceInfo.uuid)
}
inline void RSDeviceInfo::set_uuid(const char* value,
    size_t size) {
  _has_bits_[0] |= 0x00000001u;
  uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.acviewer_msgs.RSDeviceInfo.uuid)
}
inline std::string* RSDeviceInfo::_internal_mutable_uuid() {
  _has_bits_[0] |= 0x00000001u;
  return uuid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* RSDeviceInfo::release_uuid() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RSDeviceInfo.uuid)
  if (!_internal_has_uuid()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return uuid_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void RSDeviceInfo::set_allocated_uuid(std::string* uuid) {
  if (uuid != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  uuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), uuid,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RSDeviceInfo.uuid)
}

// optional uint32 event_type = 2;
inline bool RSDeviceInfo::_internal_has_event_type() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool RSDeviceInfo::has_event_type() const {
  return _internal_has_event_type();
}
inline void RSDeviceInfo::clear_event_type() {
  event_type_ = 0u;
  _has_bits_[0] &= ~0x00000002u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 RSDeviceInfo::_internal_event_type() const {
  return event_type_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 RSDeviceInfo::event_type() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSDeviceInfo.event_type)
  return _internal_event_type();
}
inline void RSDeviceInfo::_internal_set_event_type(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000002u;
  event_type_ = value;
}
inline void RSDeviceInfo::set_event_type(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_event_type(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSDeviceInfo.event_type)
}

// -------------------------------------------------------------------

// RSWriterConfig

// repeated .robosense.acviewer_msgs.RSTopicConfig topic_config_list = 1;
inline int RSWriterConfig::_internal_topic_config_list_size() const {
  return topic_config_list_.size();
}
inline int RSWriterConfig::topic_config_list_size() const {
  return _internal_topic_config_list_size();
}
inline void RSWriterConfig::clear_topic_config_list() {
  topic_config_list_.Clear();
}
inline ::robosense::acviewer_msgs::RSTopicConfig* RSWriterConfig::mutable_topic_config_list(int index) {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSWriterConfig.topic_config_list)
  return topic_config_list_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::robosense::acviewer_msgs::RSTopicConfig >*
RSWriterConfig::mutable_topic_config_list() {
  // @@protoc_insertion_point(field_mutable_list:robosense.acviewer_msgs.RSWriterConfig.topic_config_list)
  return &topic_config_list_;
}
inline const ::robosense::acviewer_msgs::RSTopicConfig& RSWriterConfig::_internal_topic_config_list(int index) const {
  return topic_config_list_.Get(index);
}
inline const ::robosense::acviewer_msgs::RSTopicConfig& RSWriterConfig::topic_config_list(int index) const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSWriterConfig.topic_config_list)
  return _internal_topic_config_list(index);
}
inline ::robosense::acviewer_msgs::RSTopicConfig* RSWriterConfig::_internal_add_topic_config_list() {
  return topic_config_list_.Add();
}
inline ::robosense::acviewer_msgs::RSTopicConfig* RSWriterConfig::add_topic_config_list() {
  // @@protoc_insertion_point(field_add:robosense.acviewer_msgs.RSWriterConfig.topic_config_list)
  return _internal_add_topic_config_list();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::robosense::acviewer_msgs::RSTopicConfig >&
RSWriterConfig::topic_config_list() const {
  // @@protoc_insertion_point(field_list:robosense.acviewer_msgs.RSWriterConfig.topic_config_list)
  return topic_config_list_;
}

// repeated string file_format = 2;
inline int RSWriterConfig::_internal_file_format_size() const {
  return file_format_.size();
}
inline int RSWriterConfig::file_format_size() const {
  return _internal_file_format_size();
}
inline void RSWriterConfig::clear_file_format() {
  file_format_.Clear();
}
inline std::string* RSWriterConfig::add_file_format() {
  // @@protoc_insertion_point(field_add_mutable:robosense.acviewer_msgs.RSWriterConfig.file_format)
  return _internal_add_file_format();
}
inline const std::string& RSWriterConfig::_internal_file_format(int index) const {
  return file_format_.Get(index);
}
inline const std::string& RSWriterConfig::file_format(int index) const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSWriterConfig.file_format)
  return _internal_file_format(index);
}
inline std::string* RSWriterConfig::mutable_file_format(int index) {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSWriterConfig.file_format)
  return file_format_.Mutable(index);
}
inline void RSWriterConfig::set_file_format(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSWriterConfig.file_format)
  file_format_.Mutable(index)->assign(value);
}
inline void RSWriterConfig::set_file_format(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSWriterConfig.file_format)
  file_format_.Mutable(index)->assign(std::move(value));
}
inline void RSWriterConfig::set_file_format(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  file_format_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:robosense.acviewer_msgs.RSWriterConfig.file_format)
}
inline void RSWriterConfig::set_file_format(int index, const char* value, size_t size) {
  file_format_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:robosense.acviewer_msgs.RSWriterConfig.file_format)
}
inline std::string* RSWriterConfig::_internal_add_file_format() {
  return file_format_.Add();
}
inline void RSWriterConfig::add_file_format(const std::string& value) {
  file_format_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:robosense.acviewer_msgs.RSWriterConfig.file_format)
}
inline void RSWriterConfig::add_file_format(std::string&& value) {
  file_format_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:robosense.acviewer_msgs.RSWriterConfig.file_format)
}
inline void RSWriterConfig::add_file_format(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  file_format_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:robosense.acviewer_msgs.RSWriterConfig.file_format)
}
inline void RSWriterConfig::add_file_format(const char* value, size_t size) {
  file_format_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:robosense.acviewer_msgs.RSWriterConfig.file_format)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
RSWriterConfig::file_format() const {
  // @@protoc_insertion_point(field_list:robosense.acviewer_msgs.RSWriterConfig.file_format)
  return file_format_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
RSWriterConfig::mutable_file_format() {
  // @@protoc_insertion_point(field_mutable_list:robosense.acviewer_msgs.RSWriterConfig.file_format)
  return &file_format_;
}

// -------------------------------------------------------------------

// RSReaderConfig

// repeated string file_topics = 1;
inline int RSReaderConfig::_internal_file_topics_size() const {
  return file_topics_.size();
}
inline int RSReaderConfig::file_topics_size() const {
  return _internal_file_topics_size();
}
inline void RSReaderConfig::clear_file_topics() {
  file_topics_.Clear();
}
inline std::string* RSReaderConfig::add_file_topics() {
  // @@protoc_insertion_point(field_add_mutable:robosense.acviewer_msgs.RSReaderConfig.file_topics)
  return _internal_add_file_topics();
}
inline const std::string& RSReaderConfig::_internal_file_topics(int index) const {
  return file_topics_.Get(index);
}
inline const std::string& RSReaderConfig::file_topics(int index) const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSReaderConfig.file_topics)
  return _internal_file_topics(index);
}
inline std::string* RSReaderConfig::mutable_file_topics(int index) {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSReaderConfig.file_topics)
  return file_topics_.Mutable(index);
}
inline void RSReaderConfig::set_file_topics(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSReaderConfig.file_topics)
  file_topics_.Mutable(index)->assign(value);
}
inline void RSReaderConfig::set_file_topics(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSReaderConfig.file_topics)
  file_topics_.Mutable(index)->assign(std::move(value));
}
inline void RSReaderConfig::set_file_topics(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  file_topics_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:robosense.acviewer_msgs.RSReaderConfig.file_topics)
}
inline void RSReaderConfig::set_file_topics(int index, const char* value, size_t size) {
  file_topics_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:robosense.acviewer_msgs.RSReaderConfig.file_topics)
}
inline std::string* RSReaderConfig::_internal_add_file_topics() {
  return file_topics_.Add();
}
inline void RSReaderConfig::add_file_topics(const std::string& value) {
  file_topics_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:robosense.acviewer_msgs.RSReaderConfig.file_topics)
}
inline void RSReaderConfig::add_file_topics(std::string&& value) {
  file_topics_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:robosense.acviewer_msgs.RSReaderConfig.file_topics)
}
inline void RSReaderConfig::add_file_topics(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  file_topics_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:robosense.acviewer_msgs.RSReaderConfig.file_topics)
}
inline void RSReaderConfig::add_file_topics(const char* value, size_t size) {
  file_topics_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:robosense.acviewer_msgs.RSReaderConfig.file_topics)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
RSReaderConfig::file_topics() const {
  // @@protoc_insertion_point(field_list:robosense.acviewer_msgs.RSReaderConfig.file_topics)
  return file_topics_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
RSReaderConfig::mutable_file_topics() {
  // @@protoc_insertion_point(field_mutable_list:robosense.acviewer_msgs.RSReaderConfig.file_topics)
  return &file_topics_;
}

// optional int64 start_timestamp_ns = 2;
inline bool RSReaderConfig::_internal_has_start_timestamp_ns() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool RSReaderConfig::has_start_timestamp_ns() const {
  return _internal_has_start_timestamp_ns();
}
inline void RSReaderConfig::clear_start_timestamp_ns() {
  start_timestamp_ns_ = PROTOBUF_LONGLONG(0);
  _has_bits_[0] &= ~0x00000001u;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 RSReaderConfig::_internal_start_timestamp_ns() const {
  return start_timestamp_ns_;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 RSReaderConfig::start_timestamp_ns() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSReaderConfig.start_timestamp_ns)
  return _internal_start_timestamp_ns();
}
inline void RSReaderConfig::_internal_set_start_timestamp_ns(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _has_bits_[0] |= 0x00000001u;
  start_timestamp_ns_ = value;
}
inline void RSReaderConfig::set_start_timestamp_ns(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _internal_set_start_timestamp_ns(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSReaderConfig.start_timestamp_ns)
}

// optional int64 end_timestamp_ns = 3;
inline bool RSReaderConfig::_internal_has_end_timestamp_ns() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool RSReaderConfig::has_end_timestamp_ns() const {
  return _internal_has_end_timestamp_ns();
}
inline void RSReaderConfig::clear_end_timestamp_ns() {
  end_timestamp_ns_ = PROTOBUF_LONGLONG(0);
  _has_bits_[0] &= ~0x00000002u;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 RSReaderConfig::_internal_end_timestamp_ns() const {
  return end_timestamp_ns_;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 RSReaderConfig::end_timestamp_ns() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSReaderConfig.end_timestamp_ns)
  return _internal_end_timestamp_ns();
}
inline void RSReaderConfig::_internal_set_end_timestamp_ns(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _has_bits_[0] |= 0x00000002u;
  end_timestamp_ns_ = value;
}
inline void RSReaderConfig::set_end_timestamp_ns(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _internal_set_end_timestamp_ns(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSReaderConfig.end_timestamp_ns)
}

// -------------------------------------------------------------------

// RSTopicConfig

// optional string topic = 1;
inline bool RSTopicConfig::_internal_has_topic() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool RSTopicConfig::has_topic() const {
  return _internal_has_topic();
}
inline void RSTopicConfig::clear_topic() {
  topic_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& RSTopicConfig::topic() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSTopicConfig.topic)
  return _internal_topic();
}
inline void RSTopicConfig::set_topic(const std::string& value) {
  _internal_set_topic(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSTopicConfig.topic)
}
inline std::string* RSTopicConfig::mutable_topic() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSTopicConfig.topic)
  return _internal_mutable_topic();
}
inline const std::string& RSTopicConfig::_internal_topic() const {
  return topic_.Get();
}
inline void RSTopicConfig::_internal_set_topic(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  topic_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void RSTopicConfig::set_topic(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  topic_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.acviewer_msgs.RSTopicConfig.topic)
}
inline void RSTopicConfig::set_topic(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  topic_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.acviewer_msgs.RSTopicConfig.topic)
}
inline void RSTopicConfig::set_topic(const char* value,
    size_t size) {
  _has_bits_[0] |= 0x00000001u;
  topic_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.acviewer_msgs.RSTopicConfig.topic)
}
inline std::string* RSTopicConfig::_internal_mutable_topic() {
  _has_bits_[0] |= 0x00000001u;
  return topic_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* RSTopicConfig::release_topic() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RSTopicConfig.topic)
  if (!_internal_has_topic()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return topic_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void RSTopicConfig::set_allocated_topic(std::string* topic) {
  if (topic != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  topic_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), topic,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RSTopicConfig.topic)
}

// optional string type = 2;
inline bool RSTopicConfig::_internal_has_type() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool RSTopicConfig::has_type() const {
  return _internal_has_type();
}
inline void RSTopicConfig::clear_type() {
  type_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000002u;
}
inline const std::string& RSTopicConfig::type() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSTopicConfig.type)
  return _internal_type();
}
inline void RSTopicConfig::set_type(const std::string& value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSTopicConfig.type)
}
inline std::string* RSTopicConfig::mutable_type() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSTopicConfig.type)
  return _internal_mutable_type();
}
inline const std::string& RSTopicConfig::_internal_type() const {
  return type_.Get();
}
inline void RSTopicConfig::_internal_set_type(const std::string& value) {
  _has_bits_[0] |= 0x00000002u;
  type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void RSTopicConfig::set_type(std::string&& value) {
  _has_bits_[0] |= 0x00000002u;
  type_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.acviewer_msgs.RSTopicConfig.type)
}
inline void RSTopicConfig::set_type(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000002u;
  type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.acviewer_msgs.RSTopicConfig.type)
}
inline void RSTopicConfig::set_type(const char* value,
    size_t size) {
  _has_bits_[0] |= 0x00000002u;
  type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.acviewer_msgs.RSTopicConfig.type)
}
inline std::string* RSTopicConfig::_internal_mutable_type() {
  _has_bits_[0] |= 0x00000002u;
  return type_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* RSTopicConfig::release_type() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RSTopicConfig.type)
  if (!_internal_has_type()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000002u;
  return type_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void RSTopicConfig::set_allocated_type(std::string* type) {
  if (type != nullptr) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  type_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), type,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RSTopicConfig.type)
}

// repeated string process_types = 3;
inline int RSTopicConfig::_internal_process_types_size() const {
  return process_types_.size();
}
inline int RSTopicConfig::process_types_size() const {
  return _internal_process_types_size();
}
inline void RSTopicConfig::clear_process_types() {
  process_types_.Clear();
}
inline std::string* RSTopicConfig::add_process_types() {
  // @@protoc_insertion_point(field_add_mutable:robosense.acviewer_msgs.RSTopicConfig.process_types)
  return _internal_add_process_types();
}
inline const std::string& RSTopicConfig::_internal_process_types(int index) const {
  return process_types_.Get(index);
}
inline const std::string& RSTopicConfig::process_types(int index) const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSTopicConfig.process_types)
  return _internal_process_types(index);
}
inline std::string* RSTopicConfig::mutable_process_types(int index) {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSTopicConfig.process_types)
  return process_types_.Mutable(index);
}
inline void RSTopicConfig::set_process_types(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSTopicConfig.process_types)
  process_types_.Mutable(index)->assign(value);
}
inline void RSTopicConfig::set_process_types(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSTopicConfig.process_types)
  process_types_.Mutable(index)->assign(std::move(value));
}
inline void RSTopicConfig::set_process_types(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  process_types_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:robosense.acviewer_msgs.RSTopicConfig.process_types)
}
inline void RSTopicConfig::set_process_types(int index, const char* value, size_t size) {
  process_types_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:robosense.acviewer_msgs.RSTopicConfig.process_types)
}
inline std::string* RSTopicConfig::_internal_add_process_types() {
  return process_types_.Add();
}
inline void RSTopicConfig::add_process_types(const std::string& value) {
  process_types_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:robosense.acviewer_msgs.RSTopicConfig.process_types)
}
inline void RSTopicConfig::add_process_types(std::string&& value) {
  process_types_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:robosense.acviewer_msgs.RSTopicConfig.process_types)
}
inline void RSTopicConfig::add_process_types(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  process_types_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:robosense.acviewer_msgs.RSTopicConfig.process_types)
}
inline void RSTopicConfig::add_process_types(const char* value, size_t size) {
  process_types_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:robosense.acviewer_msgs.RSTopicConfig.process_types)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
RSTopicConfig::process_types() const {
  // @@protoc_insertion_point(field_list:robosense.acviewer_msgs.RSTopicConfig.process_types)
  return process_types_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
RSTopicConfig::mutable_process_types() {
  // @@protoc_insertion_point(field_mutable_list:robosense.acviewer_msgs.RSTopicConfig.process_types)
  return &process_types_;
}

// -------------------------------------------------------------------

// RSReaderProgress

// optional uint32 total_frame_count = 1;
inline bool RSReaderProgress::_internal_has_total_frame_count() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool RSReaderProgress::has_total_frame_count() const {
  return _internal_has_total_frame_count();
}
inline void RSReaderProgress::clear_total_frame_count() {
  total_frame_count_ = 0u;
  _has_bits_[0] &= ~0x00000001u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 RSReaderProgress::_internal_total_frame_count() const {
  return total_frame_count_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 RSReaderProgress::total_frame_count() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSReaderProgress.total_frame_count)
  return _internal_total_frame_count();
}
inline void RSReaderProgress::_internal_set_total_frame_count(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000001u;
  total_frame_count_ = value;
}
inline void RSReaderProgress::set_total_frame_count(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_total_frame_count(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSReaderProgress.total_frame_count)
}

// optional bool is_playing = 2;
inline bool RSReaderProgress::_internal_has_is_playing() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool RSReaderProgress::has_is_playing() const {
  return _internal_has_is_playing();
}
inline void RSReaderProgress::clear_is_playing() {
  is_playing_ = false;
  _has_bits_[0] &= ~0x00000002u;
}
inline bool RSReaderProgress::_internal_is_playing() const {
  return is_playing_;
}
inline bool RSReaderProgress::is_playing() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSReaderProgress.is_playing)
  return _internal_is_playing();
}
inline void RSReaderProgress::_internal_set_is_playing(bool value) {
  _has_bits_[0] |= 0x00000002u;
  is_playing_ = value;
}
inline void RSReaderProgress::set_is_playing(bool value) {
  _internal_set_is_playing(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSReaderProgress.is_playing)
}

// optional int32 current_frame_index = 3;
inline bool RSReaderProgress::_internal_has_current_frame_index() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool RSReaderProgress::has_current_frame_index() const {
  return _internal_has_current_frame_index();
}
inline void RSReaderProgress::clear_current_frame_index() {
  current_frame_index_ = 0;
  _has_bits_[0] &= ~0x00000008u;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RSReaderProgress::_internal_current_frame_index() const {
  return current_frame_index_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RSReaderProgress::current_frame_index() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSReaderProgress.current_frame_index)
  return _internal_current_frame_index();
}
inline void RSReaderProgress::_internal_set_current_frame_index(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _has_bits_[0] |= 0x00000008u;
  current_frame_index_ = value;
}
inline void RSReaderProgress::set_current_frame_index(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_current_frame_index(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSReaderProgress.current_frame_index)
}

// optional uint64 current_timestamp = 4;
inline bool RSReaderProgress::_internal_has_current_timestamp() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool RSReaderProgress::has_current_timestamp() const {
  return _internal_has_current_timestamp();
}
inline void RSReaderProgress::clear_current_timestamp() {
  current_timestamp_ = PROTOBUF_ULONGLONG(0);
  _has_bits_[0] &= ~0x00000004u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 RSReaderProgress::_internal_current_timestamp() const {
  return current_timestamp_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 RSReaderProgress::current_timestamp() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSReaderProgress.current_timestamp)
  return _internal_current_timestamp();
}
inline void RSReaderProgress::_internal_set_current_timestamp(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _has_bits_[0] |= 0x00000004u;
  current_timestamp_ = value;
}
inline void RSReaderProgress::set_current_timestamp(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_current_timestamp(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSReaderProgress.current_timestamp)
}

// -------------------------------------------------------------------

// RSResponseData

// repeated .robosense.acviewer_msgs.RSDeviceInfo device_info_list = 1;
inline int RSResponseData::_internal_device_info_list_size() const {
  return device_info_list_.size();
}
inline int RSResponseData::device_info_list_size() const {
  return _internal_device_info_list_size();
}
inline void RSResponseData::clear_device_info_list() {
  device_info_list_.Clear();
}
inline ::robosense::acviewer_msgs::RSDeviceInfo* RSResponseData::mutable_device_info_list(int index) {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSResponseData.device_info_list)
  return device_info_list_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::robosense::acviewer_msgs::RSDeviceInfo >*
RSResponseData::mutable_device_info_list() {
  // @@protoc_insertion_point(field_mutable_list:robosense.acviewer_msgs.RSResponseData.device_info_list)
  return &device_info_list_;
}
inline const ::robosense::acviewer_msgs::RSDeviceInfo& RSResponseData::_internal_device_info_list(int index) const {
  return device_info_list_.Get(index);
}
inline const ::robosense::acviewer_msgs::RSDeviceInfo& RSResponseData::device_info_list(int index) const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSResponseData.device_info_list)
  return _internal_device_info_list(index);
}
inline ::robosense::acviewer_msgs::RSDeviceInfo* RSResponseData::_internal_add_device_info_list() {
  return device_info_list_.Add();
}
inline ::robosense::acviewer_msgs::RSDeviceInfo* RSResponseData::add_device_info_list() {
  // @@protoc_insertion_point(field_add:robosense.acviewer_msgs.RSResponseData.device_info_list)
  return _internal_add_device_info_list();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::robosense::acviewer_msgs::RSDeviceInfo >&
RSResponseData::device_info_list() const {
  // @@protoc_insertion_point(field_list:robosense.acviewer_msgs.RSResponseData.device_info_list)
  return device_info_list_;
}

// optional .robosense.acviewer_msgs.RSWriterConfig writer_config = 2;
inline bool RSResponseData::_internal_has_writer_config() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || writer_config_ != nullptr);
  return value;
}
inline bool RSResponseData::has_writer_config() const {
  return _internal_has_writer_config();
}
inline void RSResponseData::clear_writer_config() {
  if (writer_config_ != nullptr) writer_config_->Clear();
  _has_bits_[0] &= ~0x00000001u;
}
inline const ::robosense::acviewer_msgs::RSWriterConfig& RSResponseData::_internal_writer_config() const {
  const ::robosense::acviewer_msgs::RSWriterConfig* p = writer_config_;
  return p != nullptr ? *p : reinterpret_cast<const ::robosense::acviewer_msgs::RSWriterConfig&>(
      ::robosense::acviewer_msgs::_RSWriterConfig_default_instance_);
}
inline const ::robosense::acviewer_msgs::RSWriterConfig& RSResponseData::writer_config() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSResponseData.writer_config)
  return _internal_writer_config();
}
inline void RSResponseData::unsafe_arena_set_allocated_writer_config(
    ::robosense::acviewer_msgs::RSWriterConfig* writer_config) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(writer_config_);
  }
  writer_config_ = writer_config;
  if (writer_config) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:robosense.acviewer_msgs.RSResponseData.writer_config)
}
inline ::robosense::acviewer_msgs::RSWriterConfig* RSResponseData::release_writer_config() {
  _has_bits_[0] &= ~0x00000001u;
  ::robosense::acviewer_msgs::RSWriterConfig* temp = writer_config_;
  writer_config_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::robosense::acviewer_msgs::RSWriterConfig* RSResponseData::unsafe_arena_release_writer_config() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RSResponseData.writer_config)
  _has_bits_[0] &= ~0x00000001u;
  ::robosense::acviewer_msgs::RSWriterConfig* temp = writer_config_;
  writer_config_ = nullptr;
  return temp;
}
inline ::robosense::acviewer_msgs::RSWriterConfig* RSResponseData::_internal_mutable_writer_config() {
  _has_bits_[0] |= 0x00000001u;
  if (writer_config_ == nullptr) {
    auto* p = CreateMaybeMessage<::robosense::acviewer_msgs::RSWriterConfig>(GetArena());
    writer_config_ = p;
  }
  return writer_config_;
}
inline ::robosense::acviewer_msgs::RSWriterConfig* RSResponseData::mutable_writer_config() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSResponseData.writer_config)
  return _internal_mutable_writer_config();
}
inline void RSResponseData::set_allocated_writer_config(::robosense::acviewer_msgs::RSWriterConfig* writer_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete writer_config_;
  }
  if (writer_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(writer_config);
    if (message_arena != submessage_arena) {
      writer_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, writer_config, submessage_arena);
    }
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  writer_config_ = writer_config;
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RSResponseData.writer_config)
}

// repeated string file_list = 3;
inline int RSResponseData::_internal_file_list_size() const {
  return file_list_.size();
}
inline int RSResponseData::file_list_size() const {
  return _internal_file_list_size();
}
inline void RSResponseData::clear_file_list() {
  file_list_.Clear();
}
inline std::string* RSResponseData::add_file_list() {
  // @@protoc_insertion_point(field_add_mutable:robosense.acviewer_msgs.RSResponseData.file_list)
  return _internal_add_file_list();
}
inline const std::string& RSResponseData::_internal_file_list(int index) const {
  return file_list_.Get(index);
}
inline const std::string& RSResponseData::file_list(int index) const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSResponseData.file_list)
  return _internal_file_list(index);
}
inline std::string* RSResponseData::mutable_file_list(int index) {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSResponseData.file_list)
  return file_list_.Mutable(index);
}
inline void RSResponseData::set_file_list(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSResponseData.file_list)
  file_list_.Mutable(index)->assign(value);
}
inline void RSResponseData::set_file_list(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSResponseData.file_list)
  file_list_.Mutable(index)->assign(std::move(value));
}
inline void RSResponseData::set_file_list(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  file_list_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:robosense.acviewer_msgs.RSResponseData.file_list)
}
inline void RSResponseData::set_file_list(int index, const char* value, size_t size) {
  file_list_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:robosense.acviewer_msgs.RSResponseData.file_list)
}
inline std::string* RSResponseData::_internal_add_file_list() {
  return file_list_.Add();
}
inline void RSResponseData::add_file_list(const std::string& value) {
  file_list_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:robosense.acviewer_msgs.RSResponseData.file_list)
}
inline void RSResponseData::add_file_list(std::string&& value) {
  file_list_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:robosense.acviewer_msgs.RSResponseData.file_list)
}
inline void RSResponseData::add_file_list(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  file_list_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:robosense.acviewer_msgs.RSResponseData.file_list)
}
inline void RSResponseData::add_file_list(const char* value, size_t size) {
  file_list_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:robosense.acviewer_msgs.RSResponseData.file_list)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
RSResponseData::file_list() const {
  // @@protoc_insertion_point(field_list:robosense.acviewer_msgs.RSResponseData.file_list)
  return file_list_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
RSResponseData::mutable_file_list() {
  // @@protoc_insertion_point(field_mutable_list:robosense.acviewer_msgs.RSResponseData.file_list)
  return &file_list_;
}

// optional .robosense.acviewer_msgs.RSReaderConfig reader_config = 4;
inline bool RSResponseData::_internal_has_reader_config() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  PROTOBUF_ASSUME(!value || reader_config_ != nullptr);
  return value;
}
inline bool RSResponseData::has_reader_config() const {
  return _internal_has_reader_config();
}
inline void RSResponseData::clear_reader_config() {
  if (reader_config_ != nullptr) reader_config_->Clear();
  _has_bits_[0] &= ~0x00000002u;
}
inline const ::robosense::acviewer_msgs::RSReaderConfig& RSResponseData::_internal_reader_config() const {
  const ::robosense::acviewer_msgs::RSReaderConfig* p = reader_config_;
  return p != nullptr ? *p : reinterpret_cast<const ::robosense::acviewer_msgs::RSReaderConfig&>(
      ::robosense::acviewer_msgs::_RSReaderConfig_default_instance_);
}
inline const ::robosense::acviewer_msgs::RSReaderConfig& RSResponseData::reader_config() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSResponseData.reader_config)
  return _internal_reader_config();
}
inline void RSResponseData::unsafe_arena_set_allocated_reader_config(
    ::robosense::acviewer_msgs::RSReaderConfig* reader_config) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(reader_config_);
  }
  reader_config_ = reader_config;
  if (reader_config) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:robosense.acviewer_msgs.RSResponseData.reader_config)
}
inline ::robosense::acviewer_msgs::RSReaderConfig* RSResponseData::release_reader_config() {
  _has_bits_[0] &= ~0x00000002u;
  ::robosense::acviewer_msgs::RSReaderConfig* temp = reader_config_;
  reader_config_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::robosense::acviewer_msgs::RSReaderConfig* RSResponseData::unsafe_arena_release_reader_config() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RSResponseData.reader_config)
  _has_bits_[0] &= ~0x00000002u;
  ::robosense::acviewer_msgs::RSReaderConfig* temp = reader_config_;
  reader_config_ = nullptr;
  return temp;
}
inline ::robosense::acviewer_msgs::RSReaderConfig* RSResponseData::_internal_mutable_reader_config() {
  _has_bits_[0] |= 0x00000002u;
  if (reader_config_ == nullptr) {
    auto* p = CreateMaybeMessage<::robosense::acviewer_msgs::RSReaderConfig>(GetArena());
    reader_config_ = p;
  }
  return reader_config_;
}
inline ::robosense::acviewer_msgs::RSReaderConfig* RSResponseData::mutable_reader_config() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSResponseData.reader_config)
  return _internal_mutable_reader_config();
}
inline void RSResponseData::set_allocated_reader_config(::robosense::acviewer_msgs::RSReaderConfig* reader_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete reader_config_;
  }
  if (reader_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(reader_config);
    if (message_arena != submessage_arena) {
      reader_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, reader_config, submessage_arena);
    }
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  reader_config_ = reader_config;
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RSResponseData.reader_config)
}

// optional uint32 loading_progress = 5;
inline bool RSResponseData::_internal_has_loading_progress() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool RSResponseData::has_loading_progress() const {
  return _internal_has_loading_progress();
}
inline void RSResponseData::clear_loading_progress() {
  loading_progress_ = 0u;
  _has_bits_[0] &= ~0x00000008u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 RSResponseData::_internal_loading_progress() const {
  return loading_progress_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 RSResponseData::loading_progress() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSResponseData.loading_progress)
  return _internal_loading_progress();
}
inline void RSResponseData::_internal_set_loading_progress(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000008u;
  loading_progress_ = value;
}
inline void RSResponseData::set_loading_progress(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_loading_progress(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSResponseData.loading_progress)
}

// optional .robosense.acviewer_msgs.RSReaderProgress reader_progress = 6;
inline bool RSResponseData::_internal_has_reader_progress() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  PROTOBUF_ASSUME(!value || reader_progress_ != nullptr);
  return value;
}
inline bool RSResponseData::has_reader_progress() const {
  return _internal_has_reader_progress();
}
inline void RSResponseData::clear_reader_progress() {
  if (reader_progress_ != nullptr) reader_progress_->Clear();
  _has_bits_[0] &= ~0x00000004u;
}
inline const ::robosense::acviewer_msgs::RSReaderProgress& RSResponseData::_internal_reader_progress() const {
  const ::robosense::acviewer_msgs::RSReaderProgress* p = reader_progress_;
  return p != nullptr ? *p : reinterpret_cast<const ::robosense::acviewer_msgs::RSReaderProgress&>(
      ::robosense::acviewer_msgs::_RSReaderProgress_default_instance_);
}
inline const ::robosense::acviewer_msgs::RSReaderProgress& RSResponseData::reader_progress() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSResponseData.reader_progress)
  return _internal_reader_progress();
}
inline void RSResponseData::unsafe_arena_set_allocated_reader_progress(
    ::robosense::acviewer_msgs::RSReaderProgress* reader_progress) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(reader_progress_);
  }
  reader_progress_ = reader_progress;
  if (reader_progress) {
    _has_bits_[0] |= 0x00000004u;
  } else {
    _has_bits_[0] &= ~0x00000004u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:robosense.acviewer_msgs.RSResponseData.reader_progress)
}
inline ::robosense::acviewer_msgs::RSReaderProgress* RSResponseData::release_reader_progress() {
  _has_bits_[0] &= ~0x00000004u;
  ::robosense::acviewer_msgs::RSReaderProgress* temp = reader_progress_;
  reader_progress_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::robosense::acviewer_msgs::RSReaderProgress* RSResponseData::unsafe_arena_release_reader_progress() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RSResponseData.reader_progress)
  _has_bits_[0] &= ~0x00000004u;
  ::robosense::acviewer_msgs::RSReaderProgress* temp = reader_progress_;
  reader_progress_ = nullptr;
  return temp;
}
inline ::robosense::acviewer_msgs::RSReaderProgress* RSResponseData::_internal_mutable_reader_progress() {
  _has_bits_[0] |= 0x00000004u;
  if (reader_progress_ == nullptr) {
    auto* p = CreateMaybeMessage<::robosense::acviewer_msgs::RSReaderProgress>(GetArena());
    reader_progress_ = p;
  }
  return reader_progress_;
}
inline ::robosense::acviewer_msgs::RSReaderProgress* RSResponseData::mutable_reader_progress() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSResponseData.reader_progress)
  return _internal_mutable_reader_progress();
}
inline void RSResponseData::set_allocated_reader_progress(::robosense::acviewer_msgs::RSReaderProgress* reader_progress) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete reader_progress_;
  }
  if (reader_progress) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(reader_progress);
    if (message_arena != submessage_arena) {
      reader_progress = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, reader_progress, submessage_arena);
    }
    _has_bits_[0] |= 0x00000004u;
  } else {
    _has_bits_[0] &= ~0x00000004u;
  }
  reader_progress_ = reader_progress;
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RSResponseData.reader_progress)
}

// -------------------------------------------------------------------

// UdpControlConfig

// optional .robosense.acviewer_msgs.RS_UDP_CONTROL_MODE_TYPE udp_control_type = 1 [default = RS_UDP_CONTROL_TOTAL_CONTROL_TIME];
inline bool UdpControlConfig::_internal_has_udp_control_type() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool UdpControlConfig::has_udp_control_type() const {
  return _internal_has_udp_control_type();
}
inline void UdpControlConfig::clear_udp_control_type() {
  udp_control_type_ = 2;
  _has_bits_[0] &= ~0x00000002u;
}
inline ::robosense::acviewer_msgs::RS_UDP_CONTROL_MODE_TYPE UdpControlConfig::_internal_udp_control_type() const {
  return static_cast< ::robosense::acviewer_msgs::RS_UDP_CONTROL_MODE_TYPE >(udp_control_type_);
}
inline ::robosense::acviewer_msgs::RS_UDP_CONTROL_MODE_TYPE UdpControlConfig::udp_control_type() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.UdpControlConfig.udp_control_type)
  return _internal_udp_control_type();
}
inline void UdpControlConfig::_internal_set_udp_control_type(::robosense::acviewer_msgs::RS_UDP_CONTROL_MODE_TYPE value) {
  assert(::robosense::acviewer_msgs::RS_UDP_CONTROL_MODE_TYPE_IsValid(value));
  _has_bits_[0] |= 0x00000002u;
  udp_control_type_ = value;
}
inline void UdpControlConfig::set_udp_control_type(::robosense::acviewer_msgs::RS_UDP_CONTROL_MODE_TYPE value) {
  _internal_set_udp_control_type(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.UdpControlConfig.udp_control_type)
}

// optional uint32 udp_total_control_time_ms = 2 [default = 60];
inline bool UdpControlConfig::_internal_has_udp_total_control_time_ms() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool UdpControlConfig::has_udp_total_control_time_ms() const {
  return _internal_has_udp_total_control_time_ms();
}
inline void UdpControlConfig::clear_udp_total_control_time_ms() {
  udp_total_control_time_ms_ = 60u;
  _has_bits_[0] &= ~0x00000004u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpControlConfig::_internal_udp_total_control_time_ms() const {
  return udp_total_control_time_ms_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpControlConfig::udp_total_control_time_ms() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.UdpControlConfig.udp_total_control_time_ms)
  return _internal_udp_total_control_time_ms();
}
inline void UdpControlConfig::_internal_set_udp_total_control_time_ms(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000004u;
  udp_total_control_time_ms_ = value;
}
inline void UdpControlConfig::set_udp_total_control_time_ms(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_udp_total_control_time_ms(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.UdpControlConfig.udp_total_control_time_ms)
}

// optional uint32 udp_total_control_single_time_ms = 3 [default = 2];
inline bool UdpControlConfig::_internal_has_udp_total_control_single_time_ms() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool UdpControlConfig::has_udp_total_control_single_time_ms() const {
  return _internal_has_udp_total_control_single_time_ms();
}
inline void UdpControlConfig::clear_udp_total_control_single_time_ms() {
  udp_total_control_single_time_ms_ = 2u;
  _has_bits_[0] &= ~0x00000008u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpControlConfig::_internal_udp_total_control_single_time_ms() const {
  return udp_total_control_single_time_ms_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpControlConfig::udp_total_control_single_time_ms() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.UdpControlConfig.udp_total_control_single_time_ms)
  return _internal_udp_total_control_single_time_ms();
}
inline void UdpControlConfig::_internal_set_udp_total_control_single_time_ms(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000008u;
  udp_total_control_single_time_ms_ = value;
}
inline void UdpControlConfig::set_udp_total_control_single_time_ms(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_udp_total_control_single_time_ms(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.UdpControlConfig.udp_total_control_single_time_ms)
}

// optional uint32 udp_data_control_size = 4 [default = 262144];
inline bool UdpControlConfig::_internal_has_udp_data_control_size() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool UdpControlConfig::has_udp_data_control_size() const {
  return _internal_has_udp_data_control_size();
}
inline void UdpControlConfig::clear_udp_data_control_size() {
  udp_data_control_size_ = 262144u;
  _has_bits_[0] &= ~0x00000010u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpControlConfig::_internal_udp_data_control_size() const {
  return udp_data_control_size_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpControlConfig::udp_data_control_size() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.UdpControlConfig.udp_data_control_size)
  return _internal_udp_data_control_size();
}
inline void UdpControlConfig::_internal_set_udp_data_control_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000010u;
  udp_data_control_size_ = value;
}
inline void UdpControlConfig::set_udp_data_control_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_udp_data_control_size(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.UdpControlConfig.udp_data_control_size)
}

// optional uint32 udp_data_control_time_ms = 5 [default = 2];
inline bool UdpControlConfig::_internal_has_udp_data_control_time_ms() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool UdpControlConfig::has_udp_data_control_time_ms() const {
  return _internal_has_udp_data_control_time_ms();
}
inline void UdpControlConfig::clear_udp_data_control_time_ms() {
  udp_data_control_time_ms_ = 2u;
  _has_bits_[0] &= ~0x00000001u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpControlConfig::_internal_udp_data_control_time_ms() const {
  return udp_data_control_time_ms_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UdpControlConfig::udp_data_control_time_ms() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.UdpControlConfig.udp_data_control_time_ms)
  return _internal_udp_data_control_time_ms();
}
inline void UdpControlConfig::_internal_set_udp_data_control_time_ms(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000001u;
  udp_data_control_time_ms_ = value;
}
inline void UdpControlConfig::set_udp_data_control_time_ms(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_udp_data_control_time_ms(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.UdpControlConfig.udp_data_control_time_ms)
}

// -------------------------------------------------------------------

// RenderSwitch

// optional bool enable_render = 1 [default = true];
inline bool RenderSwitch::_internal_has_enable_render() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool RenderSwitch::has_enable_render() const {
  return _internal_has_enable_render();
}
inline void RenderSwitch::clear_enable_render() {
  enable_render_ = true;
  _has_bits_[0] &= ~0x00000004u;
}
inline bool RenderSwitch::_internal_enable_render() const {
  return enable_render_;
}
inline bool RenderSwitch::enable_render() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RenderSwitch.enable_render)
  return _internal_enable_render();
}
inline void RenderSwitch::_internal_set_enable_render(bool value) {
  _has_bits_[0] |= 0x00000004u;
  enable_render_ = value;
}
inline void RenderSwitch::set_enable_render(bool value) {
  _internal_set_enable_render(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RenderSwitch.enable_render)
}

// optional .robosense.acviewer_msgs.RS_RENDER_COMMUNICATION_TYPE render_comm_type = 2 [default = RS_RENDER_COMM_WEBSOCKET];
inline bool RenderSwitch::_internal_has_render_comm_type() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool RenderSwitch::has_render_comm_type() const {
  return _internal_has_render_comm_type();
}
inline void RenderSwitch::clear_render_comm_type() {
  render_comm_type_ = 1;
  _has_bits_[0] &= ~0x00000008u;
}
inline ::robosense::acviewer_msgs::RS_RENDER_COMMUNICATION_TYPE RenderSwitch::_internal_render_comm_type() const {
  return static_cast< ::robosense::acviewer_msgs::RS_RENDER_COMMUNICATION_TYPE >(render_comm_type_);
}
inline ::robosense::acviewer_msgs::RS_RENDER_COMMUNICATION_TYPE RenderSwitch::render_comm_type() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RenderSwitch.render_comm_type)
  return _internal_render_comm_type();
}
inline void RenderSwitch::_internal_set_render_comm_type(::robosense::acviewer_msgs::RS_RENDER_COMMUNICATION_TYPE value) {
  assert(::robosense::acviewer_msgs::RS_RENDER_COMMUNICATION_TYPE_IsValid(value));
  _has_bits_[0] |= 0x00000008u;
  render_comm_type_ = value;
}
inline void RenderSwitch::set_render_comm_type(::robosense::acviewer_msgs::RS_RENDER_COMMUNICATION_TYPE value) {
  _internal_set_render_comm_type(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RenderSwitch.render_comm_type)
}

// optional string udp_p2p_carapp_ip = 3;
inline bool RenderSwitch::_internal_has_udp_p2p_carapp_ip() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool RenderSwitch::has_udp_p2p_carapp_ip() const {
  return _internal_has_udp_p2p_carapp_ip();
}
inline void RenderSwitch::clear_udp_p2p_carapp_ip() {
  udp_p2p_carapp_ip_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& RenderSwitch::udp_p2p_carapp_ip() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RenderSwitch.udp_p2p_carapp_ip)
  return _internal_udp_p2p_carapp_ip();
}
inline void RenderSwitch::set_udp_p2p_carapp_ip(const std::string& value) {
  _internal_set_udp_p2p_carapp_ip(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RenderSwitch.udp_p2p_carapp_ip)
}
inline std::string* RenderSwitch::mutable_udp_p2p_carapp_ip() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RenderSwitch.udp_p2p_carapp_ip)
  return _internal_mutable_udp_p2p_carapp_ip();
}
inline const std::string& RenderSwitch::_internal_udp_p2p_carapp_ip() const {
  return udp_p2p_carapp_ip_.Get();
}
inline void RenderSwitch::_internal_set_udp_p2p_carapp_ip(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  udp_p2p_carapp_ip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void RenderSwitch::set_udp_p2p_carapp_ip(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  udp_p2p_carapp_ip_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.acviewer_msgs.RenderSwitch.udp_p2p_carapp_ip)
}
inline void RenderSwitch::set_udp_p2p_carapp_ip(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  udp_p2p_carapp_ip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.acviewer_msgs.RenderSwitch.udp_p2p_carapp_ip)
}
inline void RenderSwitch::set_udp_p2p_carapp_ip(const char* value,
    size_t size) {
  _has_bits_[0] |= 0x00000001u;
  udp_p2p_carapp_ip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.acviewer_msgs.RenderSwitch.udp_p2p_carapp_ip)
}
inline std::string* RenderSwitch::_internal_mutable_udp_p2p_carapp_ip() {
  _has_bits_[0] |= 0x00000001u;
  return udp_p2p_carapp_ip_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* RenderSwitch::release_udp_p2p_carapp_ip() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RenderSwitch.udp_p2p_carapp_ip)
  if (!_internal_has_udp_p2p_carapp_ip()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return udp_p2p_carapp_ip_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void RenderSwitch::set_allocated_udp_p2p_carapp_ip(std::string* udp_p2p_carapp_ip) {
  if (udp_p2p_carapp_ip != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  udp_p2p_carapp_ip_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), udp_p2p_carapp_ip,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RenderSwitch.udp_p2p_carapp_ip)
}

// optional .robosense.acviewer_msgs.UdpControlConfig udp_control_config = 4;
inline bool RenderSwitch::_internal_has_udp_control_config() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  PROTOBUF_ASSUME(!value || udp_control_config_ != nullptr);
  return value;
}
inline bool RenderSwitch::has_udp_control_config() const {
  return _internal_has_udp_control_config();
}
inline void RenderSwitch::clear_udp_control_config() {
  if (udp_control_config_ != nullptr) udp_control_config_->Clear();
  _has_bits_[0] &= ~0x00000002u;
}
inline const ::robosense::acviewer_msgs::UdpControlConfig& RenderSwitch::_internal_udp_control_config() const {
  const ::robosense::acviewer_msgs::UdpControlConfig* p = udp_control_config_;
  return p != nullptr ? *p : reinterpret_cast<const ::robosense::acviewer_msgs::UdpControlConfig&>(
      ::robosense::acviewer_msgs::_UdpControlConfig_default_instance_);
}
inline const ::robosense::acviewer_msgs::UdpControlConfig& RenderSwitch::udp_control_config() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RenderSwitch.udp_control_config)
  return _internal_udp_control_config();
}
inline void RenderSwitch::unsafe_arena_set_allocated_udp_control_config(
    ::robosense::acviewer_msgs::UdpControlConfig* udp_control_config) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(udp_control_config_);
  }
  udp_control_config_ = udp_control_config;
  if (udp_control_config) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:robosense.acviewer_msgs.RenderSwitch.udp_control_config)
}
inline ::robosense::acviewer_msgs::UdpControlConfig* RenderSwitch::release_udp_control_config() {
  _has_bits_[0] &= ~0x00000002u;
  ::robosense::acviewer_msgs::UdpControlConfig* temp = udp_control_config_;
  udp_control_config_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::robosense::acviewer_msgs::UdpControlConfig* RenderSwitch::unsafe_arena_release_udp_control_config() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RenderSwitch.udp_control_config)
  _has_bits_[0] &= ~0x00000002u;
  ::robosense::acviewer_msgs::UdpControlConfig* temp = udp_control_config_;
  udp_control_config_ = nullptr;
  return temp;
}
inline ::robosense::acviewer_msgs::UdpControlConfig* RenderSwitch::_internal_mutable_udp_control_config() {
  _has_bits_[0] |= 0x00000002u;
  if (udp_control_config_ == nullptr) {
    auto* p = CreateMaybeMessage<::robosense::acviewer_msgs::UdpControlConfig>(GetArena());
    udp_control_config_ = p;
  }
  return udp_control_config_;
}
inline ::robosense::acviewer_msgs::UdpControlConfig* RenderSwitch::mutable_udp_control_config() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RenderSwitch.udp_control_config)
  return _internal_mutable_udp_control_config();
}
inline void RenderSwitch::set_allocated_udp_control_config(::robosense::acviewer_msgs::UdpControlConfig* udp_control_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete udp_control_config_;
  }
  if (udp_control_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(udp_control_config);
    if (message_arena != submessage_arena) {
      udp_control_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, udp_control_config, submessage_arena);
    }
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  udp_control_config_ = udp_control_config;
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RenderSwitch.udp_control_config)
}

// -------------------------------------------------------------------

// RSPositon

// optional float x = 1;
inline bool RSPositon::_internal_has_x() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool RSPositon::has_x() const {
  return _internal_has_x();
}
inline void RSPositon::clear_x() {
  x_ = 0;
  _has_bits_[0] &= ~0x00000001u;
}
inline float RSPositon::_internal_x() const {
  return x_;
}
inline float RSPositon::x() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSPositon.x)
  return _internal_x();
}
inline void RSPositon::_internal_set_x(float value) {
  _has_bits_[0] |= 0x00000001u;
  x_ = value;
}
inline void RSPositon::set_x(float value) {
  _internal_set_x(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSPositon.x)
}

// optional float y = 2;
inline bool RSPositon::_internal_has_y() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool RSPositon::has_y() const {
  return _internal_has_y();
}
inline void RSPositon::clear_y() {
  y_ = 0;
  _has_bits_[0] &= ~0x00000002u;
}
inline float RSPositon::_internal_y() const {
  return y_;
}
inline float RSPositon::y() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSPositon.y)
  return _internal_y();
}
inline void RSPositon::_internal_set_y(float value) {
  _has_bits_[0] |= 0x00000002u;
  y_ = value;
}
inline void RSPositon::set_y(float value) {
  _internal_set_y(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSPositon.y)
}

// optional float z = 3;
inline bool RSPositon::_internal_has_z() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool RSPositon::has_z() const {
  return _internal_has_z();
}
inline void RSPositon::clear_z() {
  z_ = 0;
  _has_bits_[0] &= ~0x00000008u;
}
inline float RSPositon::_internal_z() const {
  return z_;
}
inline float RSPositon::z() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSPositon.z)
  return _internal_z();
}
inline void RSPositon::_internal_set_z(float value) {
  _has_bits_[0] |= 0x00000008u;
  z_ = value;
}
inline void RSPositon::set_z(float value) {
  _internal_set_z(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSPositon.z)
}

// optional uint64 timestamp = 4;
inline bool RSPositon::_internal_has_timestamp() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool RSPositon::has_timestamp() const {
  return _internal_has_timestamp();
}
inline void RSPositon::clear_timestamp() {
  timestamp_ = PROTOBUF_ULONGLONG(0);
  _has_bits_[0] &= ~0x00000004u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 RSPositon::_internal_timestamp() const {
  return timestamp_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 RSPositon::timestamp() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSPositon.timestamp)
  return _internal_timestamp();
}
inline void RSPositon::_internal_set_timestamp(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _has_bits_[0] |= 0x00000004u;
  timestamp_ = value;
}
inline void RSPositon::set_timestamp(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_timestamp(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSPositon.timestamp)
}

// -------------------------------------------------------------------

// RSPointCloud

// optional bytes data = 1;
inline bool RSPointCloud::_internal_has_data() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool RSPointCloud::has_data() const {
  return _internal_has_data();
}
inline void RSPointCloud::clear_data() {
  data_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& RSPointCloud::data() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSPointCloud.data)
  return _internal_data();
}
inline void RSPointCloud::set_data(const std::string& value) {
  _internal_set_data(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSPointCloud.data)
}
inline std::string* RSPointCloud::mutable_data() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSPointCloud.data)
  return _internal_mutable_data();
}
inline const std::string& RSPointCloud::_internal_data() const {
  return data_.Get();
}
inline void RSPointCloud::_internal_set_data(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  data_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void RSPointCloud::set_data(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  data_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.acviewer_msgs.RSPointCloud.data)
}
inline void RSPointCloud::set_data(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  data_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.acviewer_msgs.RSPointCloud.data)
}
inline void RSPointCloud::set_data(const void* value,
    size_t size) {
  _has_bits_[0] |= 0x00000001u;
  data_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.acviewer_msgs.RSPointCloud.data)
}
inline std::string* RSPointCloud::_internal_mutable_data() {
  _has_bits_[0] |= 0x00000001u;
  return data_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* RSPointCloud::release_data() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RSPointCloud.data)
  if (!_internal_has_data()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return data_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void RSPointCloud::set_allocated_data(std::string* data) {
  if (data != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  data_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), data,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RSPointCloud.data)
}

// optional uint32 size = 2 [default = 0];
inline bool RSPointCloud::_internal_has_size() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool RSPointCloud::has_size() const {
  return _internal_has_size();
}
inline void RSPointCloud::clear_size() {
  size_ = 0u;
  _has_bits_[0] &= ~0x00000002u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 RSPointCloud::_internal_size() const {
  return size_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 RSPointCloud::size() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSPointCloud.size)
  return _internal_size();
}
inline void RSPointCloud::_internal_set_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000002u;
  size_ = value;
}
inline void RSPointCloud::set_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_size(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSPointCloud.size)
}

// -------------------------------------------------------------------

// RSTrangleFacet

// optional bytes data = 1;
inline bool RSTrangleFacet::_internal_has_data() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool RSTrangleFacet::has_data() const {
  return _internal_has_data();
}
inline void RSTrangleFacet::clear_data() {
  data_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& RSTrangleFacet::data() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSTrangleFacet.data)
  return _internal_data();
}
inline void RSTrangleFacet::set_data(const std::string& value) {
  _internal_set_data(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSTrangleFacet.data)
}
inline std::string* RSTrangleFacet::mutable_data() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSTrangleFacet.data)
  return _internal_mutable_data();
}
inline const std::string& RSTrangleFacet::_internal_data() const {
  return data_.Get();
}
inline void RSTrangleFacet::_internal_set_data(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  data_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void RSTrangleFacet::set_data(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  data_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.acviewer_msgs.RSTrangleFacet.data)
}
inline void RSTrangleFacet::set_data(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  data_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.acviewer_msgs.RSTrangleFacet.data)
}
inline void RSTrangleFacet::set_data(const void* value,
    size_t size) {
  _has_bits_[0] |= 0x00000001u;
  data_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.acviewer_msgs.RSTrangleFacet.data)
}
inline std::string* RSTrangleFacet::_internal_mutable_data() {
  _has_bits_[0] |= 0x00000001u;
  return data_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* RSTrangleFacet::release_data() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RSTrangleFacet.data)
  if (!_internal_has_data()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return data_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void RSTrangleFacet::set_allocated_data(std::string* data) {
  if (data != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  data_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), data,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RSTrangleFacet.data)
}

// optional uint32 size = 2 [default = 0];
inline bool RSTrangleFacet::_internal_has_size() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool RSTrangleFacet::has_size() const {
  return _internal_has_size();
}
inline void RSTrangleFacet::clear_size() {
  size_ = 0u;
  _has_bits_[0] &= ~0x00000002u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 RSTrangleFacet::_internal_size() const {
  return size_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 RSTrangleFacet::size() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSTrangleFacet.size)
  return _internal_size();
}
inline void RSTrangleFacet::_internal_set_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000002u;
  size_ = value;
}
inline void RSTrangleFacet::set_size(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_size(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSTrangleFacet.size)
}

// -------------------------------------------------------------------

// RSRgbJpeg

// optional bool is_jpeg_compress = 1 [default = false];
inline bool RSRgbJpeg::_internal_has_is_jpeg_compress() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool RSRgbJpeg::has_is_jpeg_compress() const {
  return _internal_has_is_jpeg_compress();
}
inline void RSRgbJpeg::clear_is_jpeg_compress() {
  is_jpeg_compress_ = false;
  _has_bits_[0] &= ~0x00000002u;
}
inline bool RSRgbJpeg::_internal_is_jpeg_compress() const {
  return is_jpeg_compress_;
}
inline bool RSRgbJpeg::is_jpeg_compress() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSRgbJpeg.is_jpeg_compress)
  return _internal_is_jpeg_compress();
}
inline void RSRgbJpeg::_internal_set_is_jpeg_compress(bool value) {
  _has_bits_[0] |= 0x00000002u;
  is_jpeg_compress_ = value;
}
inline void RSRgbJpeg::set_is_jpeg_compress(bool value) {
  _internal_set_is_jpeg_compress(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSRgbJpeg.is_jpeg_compress)
}

// optional uint32 width = 2;
inline bool RSRgbJpeg::_internal_has_width() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool RSRgbJpeg::has_width() const {
  return _internal_has_width();
}
inline void RSRgbJpeg::clear_width() {
  width_ = 0u;
  _has_bits_[0] &= ~0x00000004u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 RSRgbJpeg::_internal_width() const {
  return width_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 RSRgbJpeg::width() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSRgbJpeg.width)
  return _internal_width();
}
inline void RSRgbJpeg::_internal_set_width(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000004u;
  width_ = value;
}
inline void RSRgbJpeg::set_width(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_width(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSRgbJpeg.width)
}

// optional uint32 height = 3;
inline bool RSRgbJpeg::_internal_has_height() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool RSRgbJpeg::has_height() const {
  return _internal_has_height();
}
inline void RSRgbJpeg::clear_height() {
  height_ = 0u;
  _has_bits_[0] &= ~0x00000010u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 RSRgbJpeg::_internal_height() const {
  return height_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 RSRgbJpeg::height() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSRgbJpeg.height)
  return _internal_height();
}
inline void RSRgbJpeg::_internal_set_height(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000010u;
  height_ = value;
}
inline void RSRgbJpeg::set_height(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_height(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSRgbJpeg.height)
}

// optional bytes data = 4;
inline bool RSRgbJpeg::_internal_has_data() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool RSRgbJpeg::has_data() const {
  return _internal_has_data();
}
inline void RSRgbJpeg::clear_data() {
  data_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& RSRgbJpeg::data() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSRgbJpeg.data)
  return _internal_data();
}
inline void RSRgbJpeg::set_data(const std::string& value) {
  _internal_set_data(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSRgbJpeg.data)
}
inline std::string* RSRgbJpeg::mutable_data() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSRgbJpeg.data)
  return _internal_mutable_data();
}
inline const std::string& RSRgbJpeg::_internal_data() const {
  return data_.Get();
}
inline void RSRgbJpeg::_internal_set_data(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  data_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void RSRgbJpeg::set_data(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  data_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.acviewer_msgs.RSRgbJpeg.data)
}
inline void RSRgbJpeg::set_data(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  data_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.acviewer_msgs.RSRgbJpeg.data)
}
inline void RSRgbJpeg::set_data(const void* value,
    size_t size) {
  _has_bits_[0] |= 0x00000001u;
  data_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.acviewer_msgs.RSRgbJpeg.data)
}
inline std::string* RSRgbJpeg::_internal_mutable_data() {
  _has_bits_[0] |= 0x00000001u;
  return data_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* RSRgbJpeg::release_data() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RSRgbJpeg.data)
  if (!_internal_has_data()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return data_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void RSRgbJpeg::set_allocated_data(std::string* data) {
  if (data != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  data_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), data,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RSRgbJpeg.data)
}

// optional uint64 timestamp = 5;
inline bool RSRgbJpeg::_internal_has_timestamp() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool RSRgbJpeg::has_timestamp() const {
  return _internal_has_timestamp();
}
inline void RSRgbJpeg::clear_timestamp() {
  timestamp_ = PROTOBUF_ULONGLONG(0);
  _has_bits_[0] &= ~0x00000008u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 RSRgbJpeg::_internal_timestamp() const {
  return timestamp_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 RSRgbJpeg::timestamp() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSRgbJpeg.timestamp)
  return _internal_timestamp();
}
inline void RSRgbJpeg::_internal_set_timestamp(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _has_bits_[0] |= 0x00000008u;
  timestamp_ = value;
}
inline void RSRgbJpeg::set_timestamp(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_timestamp(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSRgbJpeg.timestamp)
}

// -------------------------------------------------------------------

// RSDepthImage

// optional uint32 width = 1;
inline bool RSDepthImage::_internal_has_width() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool RSDepthImage::has_width() const {
  return _internal_has_width();
}
inline void RSDepthImage::clear_width() {
  width_ = 0u;
  _has_bits_[0] &= ~0x00000002u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 RSDepthImage::_internal_width() const {
  return width_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 RSDepthImage::width() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSDepthImage.width)
  return _internal_width();
}
inline void RSDepthImage::_internal_set_width(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000002u;
  width_ = value;
}
inline void RSDepthImage::set_width(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_width(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSDepthImage.width)
}

// optional uint32 height = 2;
inline bool RSDepthImage::_internal_has_height() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool RSDepthImage::has_height() const {
  return _internal_has_height();
}
inline void RSDepthImage::clear_height() {
  height_ = 0u;
  _has_bits_[0] &= ~0x00000004u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 RSDepthImage::_internal_height() const {
  return height_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 RSDepthImage::height() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSDepthImage.height)
  return _internal_height();
}
inline void RSDepthImage::_internal_set_height(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000004u;
  height_ = value;
}
inline void RSDepthImage::set_height(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_height(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSDepthImage.height)
}

// optional uint64 timestamp = 3;
inline bool RSDepthImage::_internal_has_timestamp() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool RSDepthImage::has_timestamp() const {
  return _internal_has_timestamp();
}
inline void RSDepthImage::clear_timestamp() {
  timestamp_ = PROTOBUF_ULONGLONG(0);
  _has_bits_[0] &= ~0x00000008u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 RSDepthImage::_internal_timestamp() const {
  return timestamp_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 RSDepthImage::timestamp() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSDepthImage.timestamp)
  return _internal_timestamp();
}
inline void RSDepthImage::_internal_set_timestamp(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _has_bits_[0] |= 0x00000008u;
  timestamp_ = value;
}
inline void RSDepthImage::set_timestamp(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_timestamp(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSDepthImage.timestamp)
}

// optional bytes data = 4;
inline bool RSDepthImage::_internal_has_data() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool RSDepthImage::has_data() const {
  return _internal_has_data();
}
inline void RSDepthImage::clear_data() {
  data_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& RSDepthImage::data() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSDepthImage.data)
  return _internal_data();
}
inline void RSDepthImage::set_data(const std::string& value) {
  _internal_set_data(value);
  // @@protoc_insertion_point(field_set:robosense.acviewer_msgs.RSDepthImage.data)
}
inline std::string* RSDepthImage::mutable_data() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSDepthImage.data)
  return _internal_mutable_data();
}
inline const std::string& RSDepthImage::_internal_data() const {
  return data_.Get();
}
inline void RSDepthImage::_internal_set_data(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  data_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void RSDepthImage::set_data(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  data_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.acviewer_msgs.RSDepthImage.data)
}
inline void RSDepthImage::set_data(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  data_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.acviewer_msgs.RSDepthImage.data)
}
inline void RSDepthImage::set_data(const void* value,
    size_t size) {
  _has_bits_[0] |= 0x00000001u;
  data_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.acviewer_msgs.RSDepthImage.data)
}
inline std::string* RSDepthImage::_internal_mutable_data() {
  _has_bits_[0] |= 0x00000001u;
  return data_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* RSDepthImage::release_data() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RSDepthImage.data)
  if (!_internal_has_data()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return data_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void RSDepthImage::set_allocated_data(std::string* data) {
  if (data != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  data_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), data,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RSDepthImage.data)
}

// -------------------------------------------------------------------

// RSRender

// optional .robosense.acviewer_msgs.RSPositon position = 1;
inline bool RSRender::_internal_has_position() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || position_ != nullptr);
  return value;
}
inline bool RSRender::has_position() const {
  return _internal_has_position();
}
inline void RSRender::clear_position() {
  if (position_ != nullptr) position_->Clear();
  _has_bits_[0] &= ~0x00000001u;
}
inline const ::robosense::acviewer_msgs::RSPositon& RSRender::_internal_position() const {
  const ::robosense::acviewer_msgs::RSPositon* p = position_;
  return p != nullptr ? *p : reinterpret_cast<const ::robosense::acviewer_msgs::RSPositon&>(
      ::robosense::acviewer_msgs::_RSPositon_default_instance_);
}
inline const ::robosense::acviewer_msgs::RSPositon& RSRender::position() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSRender.position)
  return _internal_position();
}
inline void RSRender::unsafe_arena_set_allocated_position(
    ::robosense::acviewer_msgs::RSPositon* position) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(position_);
  }
  position_ = position;
  if (position) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:robosense.acviewer_msgs.RSRender.position)
}
inline ::robosense::acviewer_msgs::RSPositon* RSRender::release_position() {
  _has_bits_[0] &= ~0x00000001u;
  ::robosense::acviewer_msgs::RSPositon* temp = position_;
  position_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::robosense::acviewer_msgs::RSPositon* RSRender::unsafe_arena_release_position() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RSRender.position)
  _has_bits_[0] &= ~0x00000001u;
  ::robosense::acviewer_msgs::RSPositon* temp = position_;
  position_ = nullptr;
  return temp;
}
inline ::robosense::acviewer_msgs::RSPositon* RSRender::_internal_mutable_position() {
  _has_bits_[0] |= 0x00000001u;
  if (position_ == nullptr) {
    auto* p = CreateMaybeMessage<::robosense::acviewer_msgs::RSPositon>(GetArena());
    position_ = p;
  }
  return position_;
}
inline ::robosense::acviewer_msgs::RSPositon* RSRender::mutable_position() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSRender.position)
  return _internal_mutable_position();
}
inline void RSRender::set_allocated_position(::robosense::acviewer_msgs::RSPositon* position) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete position_;
  }
  if (position) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(position);
    if (message_arena != submessage_arena) {
      position = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, position, submessage_arena);
    }
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  position_ = position;
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RSRender.position)
}

// optional .robosense.acviewer_msgs.RSPointCloud point_cloud = 2;
inline bool RSRender::_internal_has_point_cloud() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  PROTOBUF_ASSUME(!value || point_cloud_ != nullptr);
  return value;
}
inline bool RSRender::has_point_cloud() const {
  return _internal_has_point_cloud();
}
inline void RSRender::clear_point_cloud() {
  if (point_cloud_ != nullptr) point_cloud_->Clear();
  _has_bits_[0] &= ~0x00000002u;
}
inline const ::robosense::acviewer_msgs::RSPointCloud& RSRender::_internal_point_cloud() const {
  const ::robosense::acviewer_msgs::RSPointCloud* p = point_cloud_;
  return p != nullptr ? *p : reinterpret_cast<const ::robosense::acviewer_msgs::RSPointCloud&>(
      ::robosense::acviewer_msgs::_RSPointCloud_default_instance_);
}
inline const ::robosense::acviewer_msgs::RSPointCloud& RSRender::point_cloud() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSRender.point_cloud)
  return _internal_point_cloud();
}
inline void RSRender::unsafe_arena_set_allocated_point_cloud(
    ::robosense::acviewer_msgs::RSPointCloud* point_cloud) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(point_cloud_);
  }
  point_cloud_ = point_cloud;
  if (point_cloud) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:robosense.acviewer_msgs.RSRender.point_cloud)
}
inline ::robosense::acviewer_msgs::RSPointCloud* RSRender::release_point_cloud() {
  _has_bits_[0] &= ~0x00000002u;
  ::robosense::acviewer_msgs::RSPointCloud* temp = point_cloud_;
  point_cloud_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::robosense::acviewer_msgs::RSPointCloud* RSRender::unsafe_arena_release_point_cloud() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RSRender.point_cloud)
  _has_bits_[0] &= ~0x00000002u;
  ::robosense::acviewer_msgs::RSPointCloud* temp = point_cloud_;
  point_cloud_ = nullptr;
  return temp;
}
inline ::robosense::acviewer_msgs::RSPointCloud* RSRender::_internal_mutable_point_cloud() {
  _has_bits_[0] |= 0x00000002u;
  if (point_cloud_ == nullptr) {
    auto* p = CreateMaybeMessage<::robosense::acviewer_msgs::RSPointCloud>(GetArena());
    point_cloud_ = p;
  }
  return point_cloud_;
}
inline ::robosense::acviewer_msgs::RSPointCloud* RSRender::mutable_point_cloud() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSRender.point_cloud)
  return _internal_mutable_point_cloud();
}
inline void RSRender::set_allocated_point_cloud(::robosense::acviewer_msgs::RSPointCloud* point_cloud) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete point_cloud_;
  }
  if (point_cloud) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(point_cloud);
    if (message_arena != submessage_arena) {
      point_cloud = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, point_cloud, submessage_arena);
    }
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  point_cloud_ = point_cloud;
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RSRender.point_cloud)
}

// optional .robosense.acviewer_msgs.RSPointCloud point_cloud_slam = 3;
inline bool RSRender::_internal_has_point_cloud_slam() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  PROTOBUF_ASSUME(!value || point_cloud_slam_ != nullptr);
  return value;
}
inline bool RSRender::has_point_cloud_slam() const {
  return _internal_has_point_cloud_slam();
}
inline void RSRender::clear_point_cloud_slam() {
  if (point_cloud_slam_ != nullptr) point_cloud_slam_->Clear();
  _has_bits_[0] &= ~0x00000004u;
}
inline const ::robosense::acviewer_msgs::RSPointCloud& RSRender::_internal_point_cloud_slam() const {
  const ::robosense::acviewer_msgs::RSPointCloud* p = point_cloud_slam_;
  return p != nullptr ? *p : reinterpret_cast<const ::robosense::acviewer_msgs::RSPointCloud&>(
      ::robosense::acviewer_msgs::_RSPointCloud_default_instance_);
}
inline const ::robosense::acviewer_msgs::RSPointCloud& RSRender::point_cloud_slam() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSRender.point_cloud_slam)
  return _internal_point_cloud_slam();
}
inline void RSRender::unsafe_arena_set_allocated_point_cloud_slam(
    ::robosense::acviewer_msgs::RSPointCloud* point_cloud_slam) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(point_cloud_slam_);
  }
  point_cloud_slam_ = point_cloud_slam;
  if (point_cloud_slam) {
    _has_bits_[0] |= 0x00000004u;
  } else {
    _has_bits_[0] &= ~0x00000004u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:robosense.acviewer_msgs.RSRender.point_cloud_slam)
}
inline ::robosense::acviewer_msgs::RSPointCloud* RSRender::release_point_cloud_slam() {
  _has_bits_[0] &= ~0x00000004u;
  ::robosense::acviewer_msgs::RSPointCloud* temp = point_cloud_slam_;
  point_cloud_slam_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::robosense::acviewer_msgs::RSPointCloud* RSRender::unsafe_arena_release_point_cloud_slam() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RSRender.point_cloud_slam)
  _has_bits_[0] &= ~0x00000004u;
  ::robosense::acviewer_msgs::RSPointCloud* temp = point_cloud_slam_;
  point_cloud_slam_ = nullptr;
  return temp;
}
inline ::robosense::acviewer_msgs::RSPointCloud* RSRender::_internal_mutable_point_cloud_slam() {
  _has_bits_[0] |= 0x00000004u;
  if (point_cloud_slam_ == nullptr) {
    auto* p = CreateMaybeMessage<::robosense::acviewer_msgs::RSPointCloud>(GetArena());
    point_cloud_slam_ = p;
  }
  return point_cloud_slam_;
}
inline ::robosense::acviewer_msgs::RSPointCloud* RSRender::mutable_point_cloud_slam() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSRender.point_cloud_slam)
  return _internal_mutable_point_cloud_slam();
}
inline void RSRender::set_allocated_point_cloud_slam(::robosense::acviewer_msgs::RSPointCloud* point_cloud_slam) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete point_cloud_slam_;
  }
  if (point_cloud_slam) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(point_cloud_slam);
    if (message_arena != submessage_arena) {
      point_cloud_slam = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, point_cloud_slam, submessage_arena);
    }
    _has_bits_[0] |= 0x00000004u;
  } else {
    _has_bits_[0] &= ~0x00000004u;
  }
  point_cloud_slam_ = point_cloud_slam;
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RSRender.point_cloud_slam)
}

// optional .robosense.acviewer_msgs.RSTrangleFacet triangle_list = 4;
inline bool RSRender::_internal_has_triangle_list() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  PROTOBUF_ASSUME(!value || triangle_list_ != nullptr);
  return value;
}
inline bool RSRender::has_triangle_list() const {
  return _internal_has_triangle_list();
}
inline void RSRender::clear_triangle_list() {
  if (triangle_list_ != nullptr) triangle_list_->Clear();
  _has_bits_[0] &= ~0x00000008u;
}
inline const ::robosense::acviewer_msgs::RSTrangleFacet& RSRender::_internal_triangle_list() const {
  const ::robosense::acviewer_msgs::RSTrangleFacet* p = triangle_list_;
  return p != nullptr ? *p : reinterpret_cast<const ::robosense::acviewer_msgs::RSTrangleFacet&>(
      ::robosense::acviewer_msgs::_RSTrangleFacet_default_instance_);
}
inline const ::robosense::acviewer_msgs::RSTrangleFacet& RSRender::triangle_list() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSRender.triangle_list)
  return _internal_triangle_list();
}
inline void RSRender::unsafe_arena_set_allocated_triangle_list(
    ::robosense::acviewer_msgs::RSTrangleFacet* triangle_list) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(triangle_list_);
  }
  triangle_list_ = triangle_list;
  if (triangle_list) {
    _has_bits_[0] |= 0x00000008u;
  } else {
    _has_bits_[0] &= ~0x00000008u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:robosense.acviewer_msgs.RSRender.triangle_list)
}
inline ::robosense::acviewer_msgs::RSTrangleFacet* RSRender::release_triangle_list() {
  _has_bits_[0] &= ~0x00000008u;
  ::robosense::acviewer_msgs::RSTrangleFacet* temp = triangle_list_;
  triangle_list_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::robosense::acviewer_msgs::RSTrangleFacet* RSRender::unsafe_arena_release_triangle_list() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RSRender.triangle_list)
  _has_bits_[0] &= ~0x00000008u;
  ::robosense::acviewer_msgs::RSTrangleFacet* temp = triangle_list_;
  triangle_list_ = nullptr;
  return temp;
}
inline ::robosense::acviewer_msgs::RSTrangleFacet* RSRender::_internal_mutable_triangle_list() {
  _has_bits_[0] |= 0x00000008u;
  if (triangle_list_ == nullptr) {
    auto* p = CreateMaybeMessage<::robosense::acviewer_msgs::RSTrangleFacet>(GetArena());
    triangle_list_ = p;
  }
  return triangle_list_;
}
inline ::robosense::acviewer_msgs::RSTrangleFacet* RSRender::mutable_triangle_list() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSRender.triangle_list)
  return _internal_mutable_triangle_list();
}
inline void RSRender::set_allocated_triangle_list(::robosense::acviewer_msgs::RSTrangleFacet* triangle_list) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete triangle_list_;
  }
  if (triangle_list) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(triangle_list);
    if (message_arena != submessage_arena) {
      triangle_list = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, triangle_list, submessage_arena);
    }
    _has_bits_[0] |= 0x00000008u;
  } else {
    _has_bits_[0] &= ~0x00000008u;
  }
  triangle_list_ = triangle_list;
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RSRender.triangle_list)
}

// optional .robosense.acviewer_msgs.RSRgbJpeg rgb_jpeg = 5;
inline bool RSRender::_internal_has_rgb_jpeg() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  PROTOBUF_ASSUME(!value || rgb_jpeg_ != nullptr);
  return value;
}
inline bool RSRender::has_rgb_jpeg() const {
  return _internal_has_rgb_jpeg();
}
inline void RSRender::clear_rgb_jpeg() {
  if (rgb_jpeg_ != nullptr) rgb_jpeg_->Clear();
  _has_bits_[0] &= ~0x00000010u;
}
inline const ::robosense::acviewer_msgs::RSRgbJpeg& RSRender::_internal_rgb_jpeg() const {
  const ::robosense::acviewer_msgs::RSRgbJpeg* p = rgb_jpeg_;
  return p != nullptr ? *p : reinterpret_cast<const ::robosense::acviewer_msgs::RSRgbJpeg&>(
      ::robosense::acviewer_msgs::_RSRgbJpeg_default_instance_);
}
inline const ::robosense::acviewer_msgs::RSRgbJpeg& RSRender::rgb_jpeg() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSRender.rgb_jpeg)
  return _internal_rgb_jpeg();
}
inline void RSRender::unsafe_arena_set_allocated_rgb_jpeg(
    ::robosense::acviewer_msgs::RSRgbJpeg* rgb_jpeg) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(rgb_jpeg_);
  }
  rgb_jpeg_ = rgb_jpeg;
  if (rgb_jpeg) {
    _has_bits_[0] |= 0x00000010u;
  } else {
    _has_bits_[0] &= ~0x00000010u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:robosense.acviewer_msgs.RSRender.rgb_jpeg)
}
inline ::robosense::acviewer_msgs::RSRgbJpeg* RSRender::release_rgb_jpeg() {
  _has_bits_[0] &= ~0x00000010u;
  ::robosense::acviewer_msgs::RSRgbJpeg* temp = rgb_jpeg_;
  rgb_jpeg_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::robosense::acviewer_msgs::RSRgbJpeg* RSRender::unsafe_arena_release_rgb_jpeg() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RSRender.rgb_jpeg)
  _has_bits_[0] &= ~0x00000010u;
  ::robosense::acviewer_msgs::RSRgbJpeg* temp = rgb_jpeg_;
  rgb_jpeg_ = nullptr;
  return temp;
}
inline ::robosense::acviewer_msgs::RSRgbJpeg* RSRender::_internal_mutable_rgb_jpeg() {
  _has_bits_[0] |= 0x00000010u;
  if (rgb_jpeg_ == nullptr) {
    auto* p = CreateMaybeMessage<::robosense::acviewer_msgs::RSRgbJpeg>(GetArena());
    rgb_jpeg_ = p;
  }
  return rgb_jpeg_;
}
inline ::robosense::acviewer_msgs::RSRgbJpeg* RSRender::mutable_rgb_jpeg() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSRender.rgb_jpeg)
  return _internal_mutable_rgb_jpeg();
}
inline void RSRender::set_allocated_rgb_jpeg(::robosense::acviewer_msgs::RSRgbJpeg* rgb_jpeg) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete rgb_jpeg_;
  }
  if (rgb_jpeg) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(rgb_jpeg);
    if (message_arena != submessage_arena) {
      rgb_jpeg = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, rgb_jpeg, submessage_arena);
    }
    _has_bits_[0] |= 0x00000010u;
  } else {
    _has_bits_[0] &= ~0x00000010u;
  }
  rgb_jpeg_ = rgb_jpeg;
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RSRender.rgb_jpeg)
}

// optional .robosense.acviewer_msgs.RSDepthImage depth_image = 6;
inline bool RSRender::_internal_has_depth_image() const {
  bool value = (_has_bits_[0] & 0x00000020u) != 0;
  PROTOBUF_ASSUME(!value || depth_image_ != nullptr);
  return value;
}
inline bool RSRender::has_depth_image() const {
  return _internal_has_depth_image();
}
inline void RSRender::clear_depth_image() {
  if (depth_image_ != nullptr) depth_image_->Clear();
  _has_bits_[0] &= ~0x00000020u;
}
inline const ::robosense::acviewer_msgs::RSDepthImage& RSRender::_internal_depth_image() const {
  const ::robosense::acviewer_msgs::RSDepthImage* p = depth_image_;
  return p != nullptr ? *p : reinterpret_cast<const ::robosense::acviewer_msgs::RSDepthImage&>(
      ::robosense::acviewer_msgs::_RSDepthImage_default_instance_);
}
inline const ::robosense::acviewer_msgs::RSDepthImage& RSRender::depth_image() const {
  // @@protoc_insertion_point(field_get:robosense.acviewer_msgs.RSRender.depth_image)
  return _internal_depth_image();
}
inline void RSRender::unsafe_arena_set_allocated_depth_image(
    ::robosense::acviewer_msgs::RSDepthImage* depth_image) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(depth_image_);
  }
  depth_image_ = depth_image;
  if (depth_image) {
    _has_bits_[0] |= 0x00000020u;
  } else {
    _has_bits_[0] &= ~0x00000020u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:robosense.acviewer_msgs.RSRender.depth_image)
}
inline ::robosense::acviewer_msgs::RSDepthImage* RSRender::release_depth_image() {
  _has_bits_[0] &= ~0x00000020u;
  ::robosense::acviewer_msgs::RSDepthImage* temp = depth_image_;
  depth_image_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::robosense::acviewer_msgs::RSDepthImage* RSRender::unsafe_arena_release_depth_image() {
  // @@protoc_insertion_point(field_release:robosense.acviewer_msgs.RSRender.depth_image)
  _has_bits_[0] &= ~0x00000020u;
  ::robosense::acviewer_msgs::RSDepthImage* temp = depth_image_;
  depth_image_ = nullptr;
  return temp;
}
inline ::robosense::acviewer_msgs::RSDepthImage* RSRender::_internal_mutable_depth_image() {
  _has_bits_[0] |= 0x00000020u;
  if (depth_image_ == nullptr) {
    auto* p = CreateMaybeMessage<::robosense::acviewer_msgs::RSDepthImage>(GetArena());
    depth_image_ = p;
  }
  return depth_image_;
}
inline ::robosense::acviewer_msgs::RSDepthImage* RSRender::mutable_depth_image() {
  // @@protoc_insertion_point(field_mutable:robosense.acviewer_msgs.RSRender.depth_image)
  return _internal_mutable_depth_image();
}
inline void RSRender::set_allocated_depth_image(::robosense::acviewer_msgs::RSDepthImage* depth_image) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete depth_image_;
  }
  if (depth_image) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(depth_image);
    if (message_arena != submessage_arena) {
      depth_image = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, depth_image, submessage_arena);
    }
    _has_bits_[0] |= 0x00000020u;
  } else {
    _has_bits_[0] &= ~0x00000020u;
  }
  depth_image_ = depth_image;
  // @@protoc_insertion_point(field_set_allocated:robosense.acviewer_msgs.RSRender.depth_image)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace acviewer_msgs
}  // namespace robosense

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::robosense::acviewer_msgs::RS_CMD_TYPE> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::robosense::acviewer_msgs::RS_CMD_TYPE>() {
  return ::robosense::acviewer_msgs::RS_CMD_TYPE_descriptor();
}
template <> struct is_proto_enum< ::robosense::acviewer_msgs::RS_RENDER_COMMUNICATION_TYPE> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::robosense::acviewer_msgs::RS_RENDER_COMMUNICATION_TYPE>() {
  return ::robosense::acviewer_msgs::RS_RENDER_COMMUNICATION_TYPE_descriptor();
}
template <> struct is_proto_enum< ::robosense::acviewer_msgs::RS_UDP_CONTROL_MODE_TYPE> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::robosense::acviewer_msgs::RS_UDP_CONTROL_MODE_TYPE>() {
  return ::robosense::acviewer_msgs::RS_UDP_CONTROL_MODE_TYPE_descriptor();
}
template <> struct is_proto_enum< ::robosense::acviewer_msgs::RS_UDP_BUFFER_CONTROL_MODE_TYPE> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::robosense::acviewer_msgs::RS_UDP_BUFFER_CONTROL_MODE_TYPE>() {
  return ::robosense::acviewer_msgs::RS_UDP_BUFFER_CONTROL_MODE_TYPE_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_acviewer_2eproto
