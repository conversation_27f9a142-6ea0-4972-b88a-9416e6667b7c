// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: timesync.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_timesync_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_timesync_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3014000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3014000 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_timesync_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_timesync_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_timesync_2eproto;
namespace robosense {
namespace timesync_msgs {
class TimeSync;
class TimeSyncDefaultTypeInternal;
extern TimeSyncDefaultTypeInternal _TimeSync_default_instance_;
}  // namespace timesync_msgs
}  // namespace robosense
PROTOBUF_NAMESPACE_OPEN
template<> ::robosense::timesync_msgs::TimeSync* Arena::CreateMaybeMessage<::robosense::timesync_msgs::TimeSync>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace robosense {
namespace timesync_msgs {

// ===================================================================

class TimeSync PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.timesync_msgs.TimeSync) */ {
 public:
  inline TimeSync() : TimeSync(nullptr) {}
  virtual ~TimeSync();

  TimeSync(const TimeSync& from);
  TimeSync(TimeSync&& from) noexcept
    : TimeSync() {
    *this = ::std::move(from);
  }

  inline TimeSync& operator=(const TimeSync& from) {
    CopyFrom(from);
    return *this;
  }
  inline TimeSync& operator=(TimeSync&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TimeSync& default_instance();

  static inline const TimeSync* internal_default_instance() {
    return reinterpret_cast<const TimeSync*>(
               &_TimeSync_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(TimeSync& a, TimeSync& b) {
    a.Swap(&b);
  }
  inline void Swap(TimeSync* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TimeSync* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TimeSync* New() const final {
    return CreateMaybeMessage<TimeSync>(nullptr);
  }

  TimeSync* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TimeSync>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TimeSync& from);
  void MergeFrom(const TimeSync& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TimeSync* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.timesync_msgs.TimeSync";
  }
  protected:
  explicit TimeSync(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_timesync_2eproto);
    return ::descriptor_table_timesync_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceIdFieldNumber = 1,
    kSourceTimestampNsFieldNumber = 2,
    kDestinationTimestampNsFieldNumber = 3,
  };
  // optional string device_id = 1 [default = ""];
  bool has_device_id() const;
  private:
  bool _internal_has_device_id() const;
  public:
  void clear_device_id();
  const std::string& device_id() const;
  void set_device_id(const std::string& value);
  void set_device_id(std::string&& value);
  void set_device_id(const char* value);
  void set_device_id(const char* value, size_t size);
  std::string* mutable_device_id();
  std::string* release_device_id();
  void set_allocated_device_id(std::string* device_id);
  private:
  const std::string& _internal_device_id() const;
  void _internal_set_device_id(const std::string& value);
  std::string* _internal_mutable_device_id();
  public:

  // optional uint64 source_timestamp_ns = 2 [default = 0];
  bool has_source_timestamp_ns() const;
  private:
  bool _internal_has_source_timestamp_ns() const;
  public:
  void clear_source_timestamp_ns();
  ::PROTOBUF_NAMESPACE_ID::uint64 source_timestamp_ns() const;
  void set_source_timestamp_ns(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_source_timestamp_ns() const;
  void _internal_set_source_timestamp_ns(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // optional uint64 destination_timestamp_ns = 3 [default = 0];
  bool has_destination_timestamp_ns() const;
  private:
  bool _internal_has_destination_timestamp_ns() const;
  public:
  void clear_destination_timestamp_ns();
  ::PROTOBUF_NAMESPACE_ID::uint64 destination_timestamp_ns() const;
  void set_destination_timestamp_ns(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_destination_timestamp_ns() const;
  void _internal_set_destination_timestamp_ns(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // @@protoc_insertion_point(class_scope:robosense.timesync_msgs.TimeSync)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_id_;
  ::PROTOBUF_NAMESPACE_ID::uint64 source_timestamp_ns_;
  ::PROTOBUF_NAMESPACE_ID::uint64 destination_timestamp_ns_;
  friend struct ::TableStruct_timesync_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TimeSync

// optional string device_id = 1 [default = ""];
inline bool TimeSync::_internal_has_device_id() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool TimeSync::has_device_id() const {
  return _internal_has_device_id();
}
inline void TimeSync::clear_device_id() {
  device_id_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& TimeSync::device_id() const {
  // @@protoc_insertion_point(field_get:robosense.timesync_msgs.TimeSync.device_id)
  return _internal_device_id();
}
inline void TimeSync::set_device_id(const std::string& value) {
  _internal_set_device_id(value);
  // @@protoc_insertion_point(field_set:robosense.timesync_msgs.TimeSync.device_id)
}
inline std::string* TimeSync::mutable_device_id() {
  // @@protoc_insertion_point(field_mutable:robosense.timesync_msgs.TimeSync.device_id)
  return _internal_mutable_device_id();
}
inline const std::string& TimeSync::_internal_device_id() const {
  return device_id_.Get();
}
inline void TimeSync::_internal_set_device_id(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  device_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void TimeSync::set_device_id(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  device_id_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.timesync_msgs.TimeSync.device_id)
}
inline void TimeSync::set_device_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  device_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.timesync_msgs.TimeSync.device_id)
}
inline void TimeSync::set_device_id(const char* value,
    size_t size) {
  _has_bits_[0] |= 0x00000001u;
  device_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.timesync_msgs.TimeSync.device_id)
}
inline std::string* TimeSync::_internal_mutable_device_id() {
  _has_bits_[0] |= 0x00000001u;
  return device_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* TimeSync::release_device_id() {
  // @@protoc_insertion_point(field_release:robosense.timesync_msgs.TimeSync.device_id)
  if (!_internal_has_device_id()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return device_id_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void TimeSync::set_allocated_device_id(std::string* device_id) {
  if (device_id != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  device_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), device_id,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.timesync_msgs.TimeSync.device_id)
}

// optional uint64 source_timestamp_ns = 2 [default = 0];
inline bool TimeSync::_internal_has_source_timestamp_ns() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool TimeSync::has_source_timestamp_ns() const {
  return _internal_has_source_timestamp_ns();
}
inline void TimeSync::clear_source_timestamp_ns() {
  source_timestamp_ns_ = PROTOBUF_ULONGLONG(0);
  _has_bits_[0] &= ~0x00000002u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 TimeSync::_internal_source_timestamp_ns() const {
  return source_timestamp_ns_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 TimeSync::source_timestamp_ns() const {
  // @@protoc_insertion_point(field_get:robosense.timesync_msgs.TimeSync.source_timestamp_ns)
  return _internal_source_timestamp_ns();
}
inline void TimeSync::_internal_set_source_timestamp_ns(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _has_bits_[0] |= 0x00000002u;
  source_timestamp_ns_ = value;
}
inline void TimeSync::set_source_timestamp_ns(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_source_timestamp_ns(value);
  // @@protoc_insertion_point(field_set:robosense.timesync_msgs.TimeSync.source_timestamp_ns)
}

// optional uint64 destination_timestamp_ns = 3 [default = 0];
inline bool TimeSync::_internal_has_destination_timestamp_ns() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool TimeSync::has_destination_timestamp_ns() const {
  return _internal_has_destination_timestamp_ns();
}
inline void TimeSync::clear_destination_timestamp_ns() {
  destination_timestamp_ns_ = PROTOBUF_ULONGLONG(0);
  _has_bits_[0] &= ~0x00000004u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 TimeSync::_internal_destination_timestamp_ns() const {
  return destination_timestamp_ns_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 TimeSync::destination_timestamp_ns() const {
  // @@protoc_insertion_point(field_get:robosense.timesync_msgs.TimeSync.destination_timestamp_ns)
  return _internal_destination_timestamp_ns();
}
inline void TimeSync::_internal_set_destination_timestamp_ns(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _has_bits_[0] |= 0x00000004u;
  destination_timestamp_ns_ = value;
}
inline void TimeSync::set_destination_timestamp_ns(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_destination_timestamp_ns(value);
  // @@protoc_insertion_point(field_set:robosense.timesync_msgs.TimeSync.destination_timestamp_ns)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace timesync_msgs
}  // namespace robosense

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_timesync_2eproto
