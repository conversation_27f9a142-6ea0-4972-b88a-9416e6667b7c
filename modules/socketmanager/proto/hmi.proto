syntax = "proto2"; 

import "proto_compress.proto"; 
package robosense.hmi_msgs; 

message SelfDescribingMessage{
  optional string   message_topic = 1;
  optional string   message_type  = 2;  
  optional bytes    message_data  = 3; 

  optional uint64 send_hmi_time        = 4;
  optional uint64 recv_hmi_time        = 5;
  optional uint64 deserialize_hmi_time = 6;

  optional robosense.proto_compress_msgs.RS_POST_DATA_COMPRESSION_FORMAT compress_format = 7; 
  optional uint32                                                        compress_level  = 8; 
  optional uint64                                                        uncompressed_size = 9[default = 0]; 
}
