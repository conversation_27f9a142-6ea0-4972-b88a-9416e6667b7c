// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: hmi.proto

#include "hmi.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
namespace robosense {
namespace hmi_msgs {
class SelfDescribingMessageDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<SelfDescribingMessage> _instance;
} _SelfDescribingMessage_default_instance_;
}  // namespace hmi_msgs
}  // namespace robosense
static void InitDefaultsscc_info_SelfDescribingMessage_hmi_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::hmi_msgs::_SelfDescribingMessage_default_instance_;
    new (ptr) ::robosense::hmi_msgs::SelfDescribingMessage();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_SelfDescribingMessage_hmi_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_SelfDescribingMessage_hmi_2eproto}, {}};

static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_hmi_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_hmi_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_hmi_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_hmi_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  PROTOBUF_FIELD_OFFSET(::robosense::hmi_msgs::SelfDescribingMessage, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::hmi_msgs::SelfDescribingMessage, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::hmi_msgs::SelfDescribingMessage, message_topic_),
  PROTOBUF_FIELD_OFFSET(::robosense::hmi_msgs::SelfDescribingMessage, message_type_),
  PROTOBUF_FIELD_OFFSET(::robosense::hmi_msgs::SelfDescribingMessage, message_data_),
  PROTOBUF_FIELD_OFFSET(::robosense::hmi_msgs::SelfDescribingMessage, send_hmi_time_),
  PROTOBUF_FIELD_OFFSET(::robosense::hmi_msgs::SelfDescribingMessage, recv_hmi_time_),
  PROTOBUF_FIELD_OFFSET(::robosense::hmi_msgs::SelfDescribingMessage, deserialize_hmi_time_),
  PROTOBUF_FIELD_OFFSET(::robosense::hmi_msgs::SelfDescribingMessage, compress_format_),
  PROTOBUF_FIELD_OFFSET(::robosense::hmi_msgs::SelfDescribingMessage, compress_level_),
  PROTOBUF_FIELD_OFFSET(::robosense::hmi_msgs::SelfDescribingMessage, uncompressed_size_),
  0,
  1,
  2,
  3,
  4,
  5,
  8,
  7,
  6,
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, 14, sizeof(::robosense::hmi_msgs::SelfDescribingMessage)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::hmi_msgs::_SelfDescribingMessage_default_instance_),
};

const char descriptor_table_protodef_hmi_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\thmi.proto\022\022robosense.hmi_msgs\032\024proto_c"
  "ompress.proto\"\265\002\n\025SelfDescribingMessage\022"
  "\025\n\rmessage_topic\030\001 \001(\t\022\024\n\014message_type\030\002"
  " \001(\t\022\024\n\014message_data\030\003 \001(\014\022\025\n\rsend_hmi_t"
  "ime\030\004 \001(\004\022\025\n\rrecv_hmi_time\030\005 \001(\004\022\034\n\024dese"
  "rialize_hmi_time\030\006 \001(\004\022W\n\017compress_forma"
  "t\030\007 \001(\0162>.robosense.proto_compress_msgs."
  "RS_POST_DATA_COMPRESSION_FORMAT\022\026\n\016compr"
  "ess_level\030\010 \001(\r\022\034\n\021uncompressed_size\030\t \001"
  "(\004:\0010"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_hmi_2eproto_deps[1] = {
  &::descriptor_table_proto_5fcompress_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase*const descriptor_table_hmi_2eproto_sccs[1] = {
  &scc_info_SelfDescribingMessage_hmi_2eproto.base,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_hmi_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_hmi_2eproto = {
  false, false, descriptor_table_protodef_hmi_2eproto, "hmi.proto", 365,
  &descriptor_table_hmi_2eproto_once, descriptor_table_hmi_2eproto_sccs, descriptor_table_hmi_2eproto_deps, 1, 1,
  schemas, file_default_instances, TableStruct_hmi_2eproto::offsets,
  file_level_metadata_hmi_2eproto, 1, file_level_enum_descriptors_hmi_2eproto, file_level_service_descriptors_hmi_2eproto,
};

// Force running AddDescriptors() at dynamic initialization time.
static bool dynamic_init_dummy_hmi_2eproto = (static_cast<void>(::PROTOBUF_NAMESPACE_ID::internal::AddDescriptors(&descriptor_table_hmi_2eproto)), true);
namespace robosense {
namespace hmi_msgs {

// ===================================================================

class SelfDescribingMessage::_Internal {
 public:
  using HasBits = decltype(std::declval<SelfDescribingMessage>()._has_bits_);
  static void set_has_message_topic(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_message_type(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_message_data(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_send_hmi_time(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_recv_hmi_time(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static void set_has_deserialize_hmi_time(HasBits* has_bits) {
    (*has_bits)[0] |= 32u;
  }
  static void set_has_compress_format(HasBits* has_bits) {
    (*has_bits)[0] |= 256u;
  }
  static void set_has_compress_level(HasBits* has_bits) {
    (*has_bits)[0] |= 128u;
  }
  static void set_has_uncompressed_size(HasBits* has_bits) {
    (*has_bits)[0] |= 64u;
  }
};

SelfDescribingMessage::SelfDescribingMessage(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.hmi_msgs.SelfDescribingMessage)
}
SelfDescribingMessage::SelfDescribingMessage(const SelfDescribingMessage& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  message_topic_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_message_topic()) {
    message_topic_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_message_topic(), 
      GetArena());
  }
  message_type_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_message_type()) {
    message_type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_message_type(), 
      GetArena());
  }
  message_data_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_message_data()) {
    message_data_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_message_data(), 
      GetArena());
  }
  ::memcpy(&send_hmi_time_, &from.send_hmi_time_,
    static_cast<size_t>(reinterpret_cast<char*>(&compress_format_) -
    reinterpret_cast<char*>(&send_hmi_time_)) + sizeof(compress_format_));
  // @@protoc_insertion_point(copy_constructor:robosense.hmi_msgs.SelfDescribingMessage)
}

void SelfDescribingMessage::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_SelfDescribingMessage_hmi_2eproto.base);
  message_topic_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  message_type_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  message_data_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&send_hmi_time_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&compress_level_) -
      reinterpret_cast<char*>(&send_hmi_time_)) + sizeof(compress_level_));
  compress_format_ = 1;
}

SelfDescribingMessage::~SelfDescribingMessage() {
  // @@protoc_insertion_point(destructor:robosense.hmi_msgs.SelfDescribingMessage)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void SelfDescribingMessage::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  message_topic_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  message_type_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  message_data_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void SelfDescribingMessage::ArenaDtor(void* object) {
  SelfDescribingMessage* _this = reinterpret_cast< SelfDescribingMessage* >(object);
  (void)_this;
}
void SelfDescribingMessage::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SelfDescribingMessage::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const SelfDescribingMessage& SelfDescribingMessage::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_SelfDescribingMessage_hmi_2eproto.base);
  return *internal_default_instance();
}


void SelfDescribingMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.hmi_msgs.SelfDescribingMessage)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    if (cached_has_bits & 0x00000001u) {
      message_topic_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      message_type_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000004u) {
      message_data_.ClearNonDefaultToEmpty();
    }
  }
  if (cached_has_bits & 0x000000f8u) {
    ::memset(&send_hmi_time_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&compress_level_) -
        reinterpret_cast<char*>(&send_hmi_time_)) + sizeof(compress_level_));
  }
  compress_format_ = 1;
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SelfDescribingMessage::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional string message_topic = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_message_topic();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.hmi_msgs.SelfDescribingMessage.message_topic");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string message_type = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_message_type();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.hmi_msgs.SelfDescribingMessage.message_type");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional bytes message_data = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          auto str = _internal_mutable_message_data();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint64 send_hmi_time = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          _Internal::set_has_send_hmi_time(&has_bits);
          send_hmi_time_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint64 recv_hmi_time = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          _Internal::set_has_recv_hmi_time(&has_bits);
          recv_hmi_time_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint64 deserialize_hmi_time = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          _Internal::set_has_deserialize_hmi_time(&has_bits);
          deserialize_hmi_time_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .robosense.proto_compress_msgs.RS_POST_DATA_COMPRESSION_FORMAT compress_format = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT_IsValid(val))) {
            _internal_set_compress_format(static_cast<::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(7, val, mutable_unknown_fields());
          }
        } else goto handle_unusual;
        continue;
      // optional uint32 compress_level = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64)) {
          _Internal::set_has_compress_level(&has_bits);
          compress_level_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint64 uncompressed_size = 9 [default = 0];
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 72)) {
          _Internal::set_has_uncompressed_size(&has_bits);
          uncompressed_size_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* SelfDescribingMessage::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.hmi_msgs.SelfDescribingMessage)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional string message_topic = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_message_topic().data(), static_cast<int>(this->_internal_message_topic().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.hmi_msgs.SelfDescribingMessage.message_topic");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_message_topic(), target);
  }

  // optional string message_type = 2;
  if (cached_has_bits & 0x00000002u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_message_type().data(), static_cast<int>(this->_internal_message_type().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.hmi_msgs.SelfDescribingMessage.message_type");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_message_type(), target);
  }

  // optional bytes message_data = 3;
  if (cached_has_bits & 0x00000004u) {
    target = stream->WriteBytesMaybeAliased(
        3, this->_internal_message_data(), target);
  }

  // optional uint64 send_hmi_time = 4;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(4, this->_internal_send_hmi_time(), target);
  }

  // optional uint64 recv_hmi_time = 5;
  if (cached_has_bits & 0x00000010u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(5, this->_internal_recv_hmi_time(), target);
  }

  // optional uint64 deserialize_hmi_time = 6;
  if (cached_has_bits & 0x00000020u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(6, this->_internal_deserialize_hmi_time(), target);
  }

  // optional .robosense.proto_compress_msgs.RS_POST_DATA_COMPRESSION_FORMAT compress_format = 7;
  if (cached_has_bits & 0x00000100u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      7, this->_internal_compress_format(), target);
  }

  // optional uint32 compress_level = 8;
  if (cached_has_bits & 0x00000080u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(8, this->_internal_compress_level(), target);
  }

  // optional uint64 uncompressed_size = 9 [default = 0];
  if (cached_has_bits & 0x00000040u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(9, this->_internal_uncompressed_size(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.hmi_msgs.SelfDescribingMessage)
  return target;
}

size_t SelfDescribingMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.hmi_msgs.SelfDescribingMessage)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    // optional string message_topic = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_message_topic());
    }

    // optional string message_type = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_message_type());
    }

    // optional bytes message_data = 3;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
          this->_internal_message_data());
    }

    // optional uint64 send_hmi_time = 4;
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
          this->_internal_send_hmi_time());
    }

    // optional uint64 recv_hmi_time = 5;
    if (cached_has_bits & 0x00000010u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
          this->_internal_recv_hmi_time());
    }

    // optional uint64 deserialize_hmi_time = 6;
    if (cached_has_bits & 0x00000020u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
          this->_internal_deserialize_hmi_time());
    }

    // optional uint64 uncompressed_size = 9 [default = 0];
    if (cached_has_bits & 0x00000040u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
          this->_internal_uncompressed_size());
    }

    // optional uint32 compress_level = 8;
    if (cached_has_bits & 0x00000080u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_compress_level());
    }

  }
  // optional .robosense.proto_compress_msgs.RS_POST_DATA_COMPRESSION_FORMAT compress_format = 7;
  if (cached_has_bits & 0x00000100u) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_compress_format());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SelfDescribingMessage::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.hmi_msgs.SelfDescribingMessage)
  GOOGLE_DCHECK_NE(&from, this);
  const SelfDescribingMessage* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<SelfDescribingMessage>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.hmi_msgs.SelfDescribingMessage)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.hmi_msgs.SelfDescribingMessage)
    MergeFrom(*source);
  }
}

void SelfDescribingMessage::MergeFrom(const SelfDescribingMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.hmi_msgs.SelfDescribingMessage)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_message_topic(from._internal_message_topic());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_set_message_type(from._internal_message_type());
    }
    if (cached_has_bits & 0x00000004u) {
      _internal_set_message_data(from._internal_message_data());
    }
    if (cached_has_bits & 0x00000008u) {
      send_hmi_time_ = from.send_hmi_time_;
    }
    if (cached_has_bits & 0x00000010u) {
      recv_hmi_time_ = from.recv_hmi_time_;
    }
    if (cached_has_bits & 0x00000020u) {
      deserialize_hmi_time_ = from.deserialize_hmi_time_;
    }
    if (cached_has_bits & 0x00000040u) {
      uncompressed_size_ = from.uncompressed_size_;
    }
    if (cached_has_bits & 0x00000080u) {
      compress_level_ = from.compress_level_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  if (cached_has_bits & 0x00000100u) {
    _internal_set_compress_format(from._internal_compress_format());
  }
}

void SelfDescribingMessage::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.hmi_msgs.SelfDescribingMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SelfDescribingMessage::CopyFrom(const SelfDescribingMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.hmi_msgs.SelfDescribingMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SelfDescribingMessage::IsInitialized() const {
  return true;
}

void SelfDescribingMessage::InternalSwap(SelfDescribingMessage* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  message_topic_.Swap(&other->message_topic_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  message_type_.Swap(&other->message_type_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  message_data_.Swap(&other->message_data_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SelfDescribingMessage, compress_level_)
      + sizeof(SelfDescribingMessage::compress_level_)
      - PROTOBUF_FIELD_OFFSET(SelfDescribingMessage, send_hmi_time_)>(
          reinterpret_cast<char*>(&send_hmi_time_),
          reinterpret_cast<char*>(&other->send_hmi_time_));
  swap(compress_format_, other->compress_format_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SelfDescribingMessage::GetMetadata() const {
  return GetMetadataStatic();
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace hmi_msgs
}  // namespace robosense
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::robosense::hmi_msgs::SelfDescribingMessage* Arena::CreateMaybeMessage< ::robosense::hmi_msgs::SelfDescribingMessage >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::hmi_msgs::SelfDescribingMessage >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
