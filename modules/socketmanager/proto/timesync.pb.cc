// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: timesync.proto

#include "timesync.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
namespace robosense {
namespace timesync_msgs {
class TimeSyncDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<TimeSync> _instance;
} _TimeSync_default_instance_;
}  // namespace timesync_msgs
}  // namespace robosense
static void InitDefaultsscc_info_TimeSync_timesync_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::timesync_msgs::_TimeSync_default_instance_;
    new (ptr) ::robosense::timesync_msgs::TimeSync();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_TimeSync_timesync_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_TimeSync_timesync_2eproto}, {}};

static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_timesync_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_timesync_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_timesync_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_timesync_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  PROTOBUF_FIELD_OFFSET(::robosense::timesync_msgs::TimeSync, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::timesync_msgs::TimeSync, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::timesync_msgs::TimeSync, device_id_),
  PROTOBUF_FIELD_OFFSET(::robosense::timesync_msgs::TimeSync, source_timestamp_ns_),
  PROTOBUF_FIELD_OFFSET(::robosense::timesync_msgs::TimeSync, destination_timestamp_ns_),
  0,
  1,
  2,
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, 8, sizeof(::robosense::timesync_msgs::TimeSync)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::timesync_msgs::_TimeSync_default_instance_),
};

const char descriptor_table_protodef_timesync_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\016timesync.proto\022\027robosense.timesync_msg"
  "s\"d\n\010TimeSync\022\023\n\tdevice_id\030\001 \001(\t:\000\022\036\n\023so"
  "urce_timestamp_ns\030\002 \001(\004:\0010\022#\n\030destinatio"
  "n_timestamp_ns\030\003 \001(\004:\0010"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_timesync_2eproto_deps[1] = {
};
static ::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase*const descriptor_table_timesync_2eproto_sccs[1] = {
  &scc_info_TimeSync_timesync_2eproto.base,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_timesync_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_timesync_2eproto = {
  false, false, descriptor_table_protodef_timesync_2eproto, "timesync.proto", 143,
  &descriptor_table_timesync_2eproto_once, descriptor_table_timesync_2eproto_sccs, descriptor_table_timesync_2eproto_deps, 1, 0,
  schemas, file_default_instances, TableStruct_timesync_2eproto::offsets,
  file_level_metadata_timesync_2eproto, 1, file_level_enum_descriptors_timesync_2eproto, file_level_service_descriptors_timesync_2eproto,
};

// Force running AddDescriptors() at dynamic initialization time.
static bool dynamic_init_dummy_timesync_2eproto = (static_cast<void>(::PROTOBUF_NAMESPACE_ID::internal::AddDescriptors(&descriptor_table_timesync_2eproto)), true);
namespace robosense {
namespace timesync_msgs {

// ===================================================================

class TimeSync::_Internal {
 public:
  using HasBits = decltype(std::declval<TimeSync>()._has_bits_);
  static void set_has_device_id(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_source_timestamp_ns(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_destination_timestamp_ns(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
};

TimeSync::TimeSync(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.timesync_msgs.TimeSync)
}
TimeSync::TimeSync(const TimeSync& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  device_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_device_id()) {
    device_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_device_id(), 
      GetArena());
  }
  ::memcpy(&source_timestamp_ns_, &from.source_timestamp_ns_,
    static_cast<size_t>(reinterpret_cast<char*>(&destination_timestamp_ns_) -
    reinterpret_cast<char*>(&source_timestamp_ns_)) + sizeof(destination_timestamp_ns_));
  // @@protoc_insertion_point(copy_constructor:robosense.timesync_msgs.TimeSync)
}

void TimeSync::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_TimeSync_timesync_2eproto.base);
  device_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&source_timestamp_ns_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&destination_timestamp_ns_) -
      reinterpret_cast<char*>(&source_timestamp_ns_)) + sizeof(destination_timestamp_ns_));
}

TimeSync::~TimeSync() {
  // @@protoc_insertion_point(destructor:robosense.timesync_msgs.TimeSync)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void TimeSync::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  device_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void TimeSync::ArenaDtor(void* object) {
  TimeSync* _this = reinterpret_cast< TimeSync* >(object);
  (void)_this;
}
void TimeSync::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TimeSync::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const TimeSync& TimeSync::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_TimeSync_timesync_2eproto.base);
  return *internal_default_instance();
}


void TimeSync::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.timesync_msgs.TimeSync)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    device_id_.ClearNonDefaultToEmpty();
  }
  if (cached_has_bits & 0x00000006u) {
    ::memset(&source_timestamp_ns_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&destination_timestamp_ns_) -
        reinterpret_cast<char*>(&source_timestamp_ns_)) + sizeof(destination_timestamp_ns_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TimeSync::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional string device_id = 1 [default = ""];
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_device_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.timesync_msgs.TimeSync.device_id");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint64 source_timestamp_ns = 2 [default = 0];
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          _Internal::set_has_source_timestamp_ns(&has_bits);
          source_timestamp_ns_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint64 destination_timestamp_ns = 3 [default = 0];
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          _Internal::set_has_destination_timestamp_ns(&has_bits);
          destination_timestamp_ns_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* TimeSync::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.timesync_msgs.TimeSync)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional string device_id = 1 [default = ""];
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_device_id().data(), static_cast<int>(this->_internal_device_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.timesync_msgs.TimeSync.device_id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_device_id(), target);
  }

  // optional uint64 source_timestamp_ns = 2 [default = 0];
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(2, this->_internal_source_timestamp_ns(), target);
  }

  // optional uint64 destination_timestamp_ns = 3 [default = 0];
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(3, this->_internal_destination_timestamp_ns(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.timesync_msgs.TimeSync)
  return target;
}

size_t TimeSync::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.timesync_msgs.TimeSync)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    // optional string device_id = 1 [default = ""];
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_device_id());
    }

    // optional uint64 source_timestamp_ns = 2 [default = 0];
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
          this->_internal_source_timestamp_ns());
    }

    // optional uint64 destination_timestamp_ns = 3 [default = 0];
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
          this->_internal_destination_timestamp_ns());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TimeSync::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.timesync_msgs.TimeSync)
  GOOGLE_DCHECK_NE(&from, this);
  const TimeSync* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<TimeSync>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.timesync_msgs.TimeSync)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.timesync_msgs.TimeSync)
    MergeFrom(*source);
  }
}

void TimeSync::MergeFrom(const TimeSync& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.timesync_msgs.TimeSync)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_device_id(from._internal_device_id());
    }
    if (cached_has_bits & 0x00000002u) {
      source_timestamp_ns_ = from.source_timestamp_ns_;
    }
    if (cached_has_bits & 0x00000004u) {
      destination_timestamp_ns_ = from.destination_timestamp_ns_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void TimeSync::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.timesync_msgs.TimeSync)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TimeSync::CopyFrom(const TimeSync& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.timesync_msgs.TimeSync)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TimeSync::IsInitialized() const {
  return true;
}

void TimeSync::InternalSwap(TimeSync* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  device_id_.Swap(&other->device_id_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(TimeSync, destination_timestamp_ns_)
      + sizeof(TimeSync::destination_timestamp_ns_)
      - PROTOBUF_FIELD_OFFSET(TimeSync, source_timestamp_ns_)>(
          reinterpret_cast<char*>(&source_timestamp_ns_),
          reinterpret_cast<char*>(&other->source_timestamp_ns_));
}

::PROTOBUF_NAMESPACE_ID::Metadata TimeSync::GetMetadata() const {
  return GetMetadataStatic();
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace timesync_msgs
}  // namespace robosense
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::robosense::timesync_msgs::TimeSync* Arena::CreateMaybeMessage< ::robosense::timesync_msgs::TimeSync >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::timesync_msgs::TimeSync >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
