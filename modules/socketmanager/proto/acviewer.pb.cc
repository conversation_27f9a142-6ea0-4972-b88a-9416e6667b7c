// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: acviewer.proto

#include "acviewer.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
extern PROTOBUF_INTERNAL_EXPORT_acviewer_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_RSDepthImage_acviewer_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_acviewer_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_RSDeviceInfo_acviewer_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_acviewer_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_RSOperatorDeviceData_acviewer_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_acviewer_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_RSPointCloud_acviewer_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_acviewer_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_RSPositon_acviewer_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_acviewer_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_RSReaderConfig_acviewer_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_acviewer_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_RSReaderProgress_acviewer_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_acviewer_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_RSReaderSetting_acviewer_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_acviewer_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<4> scc_info_RSRequestData_acviewer_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_acviewer_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<4> scc_info_RSResponseData_acviewer_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_acviewer_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_RSRgbJpeg_acviewer_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_acviewer_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_RSShowConfigData_acviewer_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_acviewer_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_RSTopicConfig_acviewer_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_acviewer_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_RSTopicSetting_acviewer_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_acviewer_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_RSTrangleFacet_acviewer_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_acviewer_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_RSWriterConfig_acviewer_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_acviewer_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_RSWriterSetting_acviewer_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_acviewer_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_UdpControlConfig_acviewer_2eproto;
namespace robosense {
namespace acviewer_msgs {
class RSRequestDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RSRequest> _instance;
} _RSRequest_default_instance_;
class RSShowConfigDataDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RSShowConfigData> _instance;
} _RSShowConfigData_default_instance_;
class RSOperatorDeviceDataDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RSOperatorDeviceData> _instance;
} _RSOperatorDeviceData_default_instance_;
class RSWriterSettingDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RSWriterSetting> _instance;
} _RSWriterSetting_default_instance_;
class RSTopicSettingDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RSTopicSetting> _instance;
} _RSTopicSetting_default_instance_;
class RSReaderSettingDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RSReaderSetting> _instance;
} _RSReaderSetting_default_instance_;
class RSRequestDataDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RSRequestData> _instance;
} _RSRequestData_default_instance_;
class RSResponseDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RSResponse> _instance;
} _RSResponse_default_instance_;
class RSDeviceInfoDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RSDeviceInfo> _instance;
} _RSDeviceInfo_default_instance_;
class RSWriterConfigDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RSWriterConfig> _instance;
} _RSWriterConfig_default_instance_;
class RSReaderConfigDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RSReaderConfig> _instance;
} _RSReaderConfig_default_instance_;
class RSTopicConfigDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RSTopicConfig> _instance;
} _RSTopicConfig_default_instance_;
class RSReaderProgressDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RSReaderProgress> _instance;
} _RSReaderProgress_default_instance_;
class RSResponseDataDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RSResponseData> _instance;
} _RSResponseData_default_instance_;
class UdpControlConfigDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<UdpControlConfig> _instance;
} _UdpControlConfig_default_instance_;
class RenderSwitchDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RenderSwitch> _instance;
} _RenderSwitch_default_instance_;
class RSPositonDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RSPositon> _instance;
} _RSPositon_default_instance_;
class RSPointCloudDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RSPointCloud> _instance;
} _RSPointCloud_default_instance_;
class RSTrangleFacetDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RSTrangleFacet> _instance;
} _RSTrangleFacet_default_instance_;
class RSRgbJpegDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RSRgbJpeg> _instance;
} _RSRgbJpeg_default_instance_;
class RSDepthImageDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RSDepthImage> _instance;
} _RSDepthImage_default_instance_;
class RSRenderDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RSRender> _instance;
} _RSRender_default_instance_;
}  // namespace acviewer_msgs
}  // namespace robosense
static void InitDefaultsscc_info_RSDepthImage_acviewer_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::acviewer_msgs::_RSDepthImage_default_instance_;
    new (ptr) ::robosense::acviewer_msgs::RSDepthImage();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_RSDepthImage_acviewer_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_RSDepthImage_acviewer_2eproto}, {}};

static void InitDefaultsscc_info_RSDeviceInfo_acviewer_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::acviewer_msgs::_RSDeviceInfo_default_instance_;
    new (ptr) ::robosense::acviewer_msgs::RSDeviceInfo();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_RSDeviceInfo_acviewer_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_RSDeviceInfo_acviewer_2eproto}, {}};

static void InitDefaultsscc_info_RSOperatorDeviceData_acviewer_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::acviewer_msgs::_RSOperatorDeviceData_default_instance_;
    new (ptr) ::robosense::acviewer_msgs::RSOperatorDeviceData();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_RSOperatorDeviceData_acviewer_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_RSOperatorDeviceData_acviewer_2eproto}, {}};

static void InitDefaultsscc_info_RSPointCloud_acviewer_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::acviewer_msgs::_RSPointCloud_default_instance_;
    new (ptr) ::robosense::acviewer_msgs::RSPointCloud();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_RSPointCloud_acviewer_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_RSPointCloud_acviewer_2eproto}, {}};

static void InitDefaultsscc_info_RSPositon_acviewer_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::acviewer_msgs::_RSPositon_default_instance_;
    new (ptr) ::robosense::acviewer_msgs::RSPositon();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_RSPositon_acviewer_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_RSPositon_acviewer_2eproto}, {}};

static void InitDefaultsscc_info_RSReaderConfig_acviewer_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::acviewer_msgs::_RSReaderConfig_default_instance_;
    new (ptr) ::robosense::acviewer_msgs::RSReaderConfig();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_RSReaderConfig_acviewer_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_RSReaderConfig_acviewer_2eproto}, {}};

static void InitDefaultsscc_info_RSReaderProgress_acviewer_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::acviewer_msgs::_RSReaderProgress_default_instance_;
    new (ptr) ::robosense::acviewer_msgs::RSReaderProgress();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_RSReaderProgress_acviewer_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_RSReaderProgress_acviewer_2eproto}, {}};

static void InitDefaultsscc_info_RSReaderSetting_acviewer_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::acviewer_msgs::_RSReaderSetting_default_instance_;
    new (ptr) ::robosense::acviewer_msgs::RSReaderSetting();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_RSReaderSetting_acviewer_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_RSReaderSetting_acviewer_2eproto}, {}};

static void InitDefaultsscc_info_RSRender_acviewer_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::acviewer_msgs::_RSRender_default_instance_;
    new (ptr) ::robosense::acviewer_msgs::RSRender();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<5> scc_info_RSRender_acviewer_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 5, 0, InitDefaultsscc_info_RSRender_acviewer_2eproto}, {
      &scc_info_RSPositon_acviewer_2eproto.base,
      &scc_info_RSPointCloud_acviewer_2eproto.base,
      &scc_info_RSTrangleFacet_acviewer_2eproto.base,
      &scc_info_RSRgbJpeg_acviewer_2eproto.base,
      &scc_info_RSDepthImage_acviewer_2eproto.base,}};

static void InitDefaultsscc_info_RSRequest_acviewer_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::acviewer_msgs::_RSRequest_default_instance_;
    new (ptr) ::robosense::acviewer_msgs::RSRequest();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_RSRequest_acviewer_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_RSRequest_acviewer_2eproto}, {
      &scc_info_RSRequestData_acviewer_2eproto.base,}};

static void InitDefaultsscc_info_RSRequestData_acviewer_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::acviewer_msgs::_RSRequestData_default_instance_;
    new (ptr) ::robosense::acviewer_msgs::RSRequestData();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<4> scc_info_RSRequestData_acviewer_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 4, 0, InitDefaultsscc_info_RSRequestData_acviewer_2eproto}, {
      &scc_info_RSShowConfigData_acviewer_2eproto.base,
      &scc_info_RSOperatorDeviceData_acviewer_2eproto.base,
      &scc_info_RSWriterSetting_acviewer_2eproto.base,
      &scc_info_RSReaderSetting_acviewer_2eproto.base,}};

static void InitDefaultsscc_info_RSResponse_acviewer_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::acviewer_msgs::_RSResponse_default_instance_;
    new (ptr) ::robosense::acviewer_msgs::RSResponse();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_RSResponse_acviewer_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_RSResponse_acviewer_2eproto}, {
      &scc_info_RSResponseData_acviewer_2eproto.base,}};

static void InitDefaultsscc_info_RSResponseData_acviewer_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::acviewer_msgs::_RSResponseData_default_instance_;
    new (ptr) ::robosense::acviewer_msgs::RSResponseData();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<4> scc_info_RSResponseData_acviewer_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 4, 0, InitDefaultsscc_info_RSResponseData_acviewer_2eproto}, {
      &scc_info_RSDeviceInfo_acviewer_2eproto.base,
      &scc_info_RSWriterConfig_acviewer_2eproto.base,
      &scc_info_RSReaderConfig_acviewer_2eproto.base,
      &scc_info_RSReaderProgress_acviewer_2eproto.base,}};

static void InitDefaultsscc_info_RSRgbJpeg_acviewer_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::acviewer_msgs::_RSRgbJpeg_default_instance_;
    new (ptr) ::robosense::acviewer_msgs::RSRgbJpeg();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_RSRgbJpeg_acviewer_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_RSRgbJpeg_acviewer_2eproto}, {}};

static void InitDefaultsscc_info_RSShowConfigData_acviewer_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::acviewer_msgs::_RSShowConfigData_default_instance_;
    new (ptr) ::robosense::acviewer_msgs::RSShowConfigData();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_RSShowConfigData_acviewer_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_RSShowConfigData_acviewer_2eproto}, {}};

static void InitDefaultsscc_info_RSTopicConfig_acviewer_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::acviewer_msgs::_RSTopicConfig_default_instance_;
    new (ptr) ::robosense::acviewer_msgs::RSTopicConfig();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_RSTopicConfig_acviewer_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_RSTopicConfig_acviewer_2eproto}, {}};

static void InitDefaultsscc_info_RSTopicSetting_acviewer_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::acviewer_msgs::_RSTopicSetting_default_instance_;
    new (ptr) ::robosense::acviewer_msgs::RSTopicSetting();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_RSTopicSetting_acviewer_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_RSTopicSetting_acviewer_2eproto}, {}};

static void InitDefaultsscc_info_RSTrangleFacet_acviewer_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::acviewer_msgs::_RSTrangleFacet_default_instance_;
    new (ptr) ::robosense::acviewer_msgs::RSTrangleFacet();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_RSTrangleFacet_acviewer_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_RSTrangleFacet_acviewer_2eproto}, {}};

static void InitDefaultsscc_info_RSWriterConfig_acviewer_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::acviewer_msgs::_RSWriterConfig_default_instance_;
    new (ptr) ::robosense::acviewer_msgs::RSWriterConfig();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_RSWriterConfig_acviewer_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_RSWriterConfig_acviewer_2eproto}, {
      &scc_info_RSTopicConfig_acviewer_2eproto.base,}};

static void InitDefaultsscc_info_RSWriterSetting_acviewer_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::acviewer_msgs::_RSWriterSetting_default_instance_;
    new (ptr) ::robosense::acviewer_msgs::RSWriterSetting();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_RSWriterSetting_acviewer_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_RSWriterSetting_acviewer_2eproto}, {
      &scc_info_RSTopicSetting_acviewer_2eproto.base,}};

static void InitDefaultsscc_info_RenderSwitch_acviewer_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::acviewer_msgs::_RenderSwitch_default_instance_;
    new (ptr) ::robosense::acviewer_msgs::RenderSwitch();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_RenderSwitch_acviewer_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_RenderSwitch_acviewer_2eproto}, {
      &scc_info_UdpControlConfig_acviewer_2eproto.base,}};

static void InitDefaultsscc_info_UdpControlConfig_acviewer_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::robosense::acviewer_msgs::_UdpControlConfig_default_instance_;
    new (ptr) ::robosense::acviewer_msgs::UdpControlConfig();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_UdpControlConfig_acviewer_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_UdpControlConfig_acviewer_2eproto}, {}};

static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_acviewer_2eproto[22];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_acviewer_2eproto[4];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_acviewer_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_acviewer_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSRequest, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSRequest, cmd_type_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSRequest, request_id_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSRequest, request_data_),
  2,
  1,
  0,
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSShowConfigData, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSShowConfigData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSShowConfigData, show_pos_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSShowConfigData, show_pc_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSShowConfigData, show_pc_slam_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSShowConfigData, show_tri_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSShowConfigData, show_rgb_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSShowConfigData, show_depth_),
  0,
  1,
  2,
  3,
  4,
  5,
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSOperatorDeviceData, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSOperatorDeviceData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSOperatorDeviceData, uuid_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSOperatorDeviceData, operator_type_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSWriterSetting, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSWriterSetting, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSWriterSetting, file_name_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSWriterSetting, file_format_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSWriterSetting, topic_setting_list_),
  0,
  1,
  ~0u,
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSTopicSetting, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSTopicSetting, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSTopicSetting, topic_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSTopicSetting, compressed_type_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSReaderSetting, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSReaderSetting, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSReaderSetting, file_name_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSReaderSetting, main_sync_topic_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSReaderSetting, pre_load_mains_sync_cnt_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSReaderSetting, read_topic_names_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSReaderSetting, read_start_timestamp_ns_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSReaderSetting, read_end_timestamp_ns_),
  0,
  1,
  2,
  ~0u,
  3,
  4,
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSRequestData, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSRequestData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSRequestData, runmode_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSRequestData, show_config_data_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSRequestData, operator_device_data_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSRequestData, writer_setting_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSRequestData, reader_setting_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSRequestData, topic_file_path_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSRequestData, skip_frame_),
  5,
  1,
  2,
  3,
  4,
  0,
  6,
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSResponse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSResponse, cmd_type_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSResponse, request_id_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSResponse, response_id_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSResponse, response_code_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSResponse, response_info_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSResponse, response_data_),
  3,
  2,
  5,
  4,
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSDeviceInfo, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSDeviceInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSDeviceInfo, uuid_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSDeviceInfo, event_type_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSWriterConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSWriterConfig, topic_config_list_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSWriterConfig, file_format_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSReaderConfig, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSReaderConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSReaderConfig, file_topics_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSReaderConfig, start_timestamp_ns_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSReaderConfig, end_timestamp_ns_),
  ~0u,
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSTopicConfig, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSTopicConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSTopicConfig, topic_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSTopicConfig, type_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSTopicConfig, process_types_),
  0,
  1,
  ~0u,
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSReaderProgress, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSReaderProgress, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSReaderProgress, total_frame_count_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSReaderProgress, is_playing_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSReaderProgress, current_frame_index_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSReaderProgress, current_timestamp_),
  0,
  1,
  3,
  2,
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSResponseData, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSResponseData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSResponseData, device_info_list_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSResponseData, writer_config_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSResponseData, file_list_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSResponseData, reader_config_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSResponseData, loading_progress_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSResponseData, reader_progress_),
  ~0u,
  0,
  ~0u,
  1,
  3,
  2,
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::UdpControlConfig, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::UdpControlConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::UdpControlConfig, udp_control_type_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::UdpControlConfig, udp_total_control_time_ms_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::UdpControlConfig, udp_total_control_single_time_ms_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::UdpControlConfig, udp_data_control_size_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::UdpControlConfig, udp_data_control_time_ms_),
  1,
  2,
  3,
  4,
  0,
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RenderSwitch, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RenderSwitch, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RenderSwitch, enable_render_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RenderSwitch, render_comm_type_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RenderSwitch, udp_p2p_carapp_ip_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RenderSwitch, udp_control_config_),
  2,
  3,
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSPositon, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSPositon, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSPositon, x_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSPositon, y_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSPositon, z_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSPositon, timestamp_),
  0,
  1,
  3,
  2,
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSPointCloud, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSPointCloud, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSPointCloud, data_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSPointCloud, size_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSTrangleFacet, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSTrangleFacet, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSTrangleFacet, data_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSTrangleFacet, size_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSRgbJpeg, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSRgbJpeg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSRgbJpeg, is_jpeg_compress_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSRgbJpeg, width_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSRgbJpeg, height_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSRgbJpeg, data_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSRgbJpeg, timestamp_),
  1,
  2,
  4,
  0,
  3,
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSDepthImage, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSDepthImage, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSDepthImage, width_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSDepthImage, height_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSDepthImage, timestamp_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSDepthImage, data_),
  1,
  2,
  3,
  0,
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSRender, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSRender, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSRender, position_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSRender, point_cloud_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSRender, point_cloud_slam_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSRender, triangle_list_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSRender, rgb_jpeg_),
  PROTOBUF_FIELD_OFFSET(::robosense::acviewer_msgs::RSRender, depth_image_),
  0,
  1,
  2,
  3,
  4,
  5,
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, 8, sizeof(::robosense::acviewer_msgs::RSRequest)},
  { 11, 22, sizeof(::robosense::acviewer_msgs::RSShowConfigData)},
  { 28, 35, sizeof(::robosense::acviewer_msgs::RSOperatorDeviceData)},
  { 37, 45, sizeof(::robosense::acviewer_msgs::RSWriterSetting)},
  { 48, 55, sizeof(::robosense::acviewer_msgs::RSTopicSetting)},
  { 57, 68, sizeof(::robosense::acviewer_msgs::RSReaderSetting)},
  { 74, 86, sizeof(::robosense::acviewer_msgs::RSRequestData)},
  { 93, 104, sizeof(::robosense::acviewer_msgs::RSResponse)},
  { 110, 117, sizeof(::robosense::acviewer_msgs::RSDeviceInfo)},
  { 119, -1, sizeof(::robosense::acviewer_msgs::RSWriterConfig)},
  { 126, 134, sizeof(::robosense::acviewer_msgs::RSReaderConfig)},
  { 137, 145, sizeof(::robosense::acviewer_msgs::RSTopicConfig)},
  { 148, 157, sizeof(::robosense::acviewer_msgs::RSReaderProgress)},
  { 161, 172, sizeof(::robosense::acviewer_msgs::RSResponseData)},
  { 178, 188, sizeof(::robosense::acviewer_msgs::UdpControlConfig)},
  { 193, 202, sizeof(::robosense::acviewer_msgs::RenderSwitch)},
  { 206, 215, sizeof(::robosense::acviewer_msgs::RSPositon)},
  { 219, 226, sizeof(::robosense::acviewer_msgs::RSPointCloud)},
  { 228, 235, sizeof(::robosense::acviewer_msgs::RSTrangleFacet)},
  { 237, 247, sizeof(::robosense::acviewer_msgs::RSRgbJpeg)},
  { 252, 261, sizeof(::robosense::acviewer_msgs::RSDepthImage)},
  { 265, 276, sizeof(::robosense::acviewer_msgs::RSRender)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::acviewer_msgs::_RSRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::acviewer_msgs::_RSShowConfigData_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::acviewer_msgs::_RSOperatorDeviceData_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::acviewer_msgs::_RSWriterSetting_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::acviewer_msgs::_RSTopicSetting_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::acviewer_msgs::_RSReaderSetting_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::acviewer_msgs::_RSRequestData_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::acviewer_msgs::_RSResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::acviewer_msgs::_RSDeviceInfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::acviewer_msgs::_RSWriterConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::acviewer_msgs::_RSReaderConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::acviewer_msgs::_RSTopicConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::acviewer_msgs::_RSReaderProgress_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::acviewer_msgs::_RSResponseData_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::acviewer_msgs::_UdpControlConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::acviewer_msgs::_RenderSwitch_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::acviewer_msgs::_RSPositon_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::acviewer_msgs::_RSPointCloud_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::acviewer_msgs::_RSTrangleFacet_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::acviewer_msgs::_RSRgbJpeg_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::acviewer_msgs::_RSDepthImage_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::robosense::acviewer_msgs::_RSRender_default_instance_),
};

const char descriptor_table_protodef_acviewer_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\016acviewer.proto\022\027robosense.acviewer_msg"
  "s\"\225\001\n\tRSRequest\0226\n\010cmd_type\030\001 \002(\0162$.robo"
  "sense.acviewer_msgs.RS_CMD_TYPE\022\022\n\nreque"
  "st_Id\030\002 \002(\004\022<\n\014request_data\030\003 \001(\0132&.robo"
  "sense.acviewer_msgs.RSRequestData\"\203\001\n\020RS"
  "ShowConfigData\022\020\n\010show_pos\030\001 \001(\010\022\017\n\007show"
  "_pc\030\002 \001(\010\022\024\n\014show_pc_slam\030\003 \001(\010\022\020\n\010show_"
  "tri\030\004 \001(\010\022\020\n\010show_rgb\030\005 \001(\010\022\022\n\nshow_dept"
  "h\030\006 \001(\010\";\n\024RSOperatorDeviceData\022\014\n\004uuid\030"
  "\001 \002(\t\022\025\n\roperator_type\030\002 \002(\r\"~\n\017RSWriter"
  "Setting\022\021\n\tfile_name\030\001 \001(\t\022\023\n\013file_forma"
  "t\030\002 \001(\t\022C\n\022topic_setting_list\030\003 \003(\0132\'.ro"
  "bosense.acviewer_msgs.RSTopicSetting\"8\n\016"
  "RSTopicSetting\022\r\n\005topic\030\001 \001(\t\022\027\n\017compres"
  "sed_type\030\002 \001(\t\"\300\001\n\017RSReaderSetting\022\021\n\tfi"
  "le_name\030\001 \001(\t\022\027\n\017main_sync_topic\030\002 \001(\t\022\037"
  "\n\027pre_load_mains_sync_cnt\030\003 \001(\r\022\030\n\020read_"
  "topic_names\030\004 \003(\t\022#\n\027read_start_timestam"
  "p_ns\030\005 \001(\003:\002-1\022!\n\025read_end_timestamp_ns\030"
  "\006 \001(\003:\002-1\"\343\002\n\rRSRequestData\022\017\n\007runMode\030\001"
  " \001(\r\022C\n\020show_config_data\030\002 \001(\0132).robosen"
  "se.acviewer_msgs.RSShowConfigData\022K\n\024ope"
  "rator_device_data\030\003 \001(\0132-.robosense.acvi"
  "ewer_msgs.RSOperatorDeviceData\022@\n\016writer"
  "_setting\030\004 \001(\0132(.robosense.acviewer_msgs"
  ".RSWriterSetting\022@\n\016reader_setting\030\005 \001(\013"
  "2(.robosense.acviewer_msgs.RSReaderSetti"
  "ng\022\027\n\017topic_file_path\030\006 \001(\t\022\022\n\nskip_fram"
  "e\030\007 \001(\005\"\333\001\n\nRSResponse\0226\n\010cmd_type\030\001 \002(\016"
  "2$.robosense.acviewer_msgs.RS_CMD_TYPE\022\022"
  "\n\nrequest_Id\030\002 \002(\004\022\023\n\013response_Id\030\003 \002(\004\022"
  "\025\n\rresponse_code\030\004 \002(\r\022\025\n\rresponse_info\030"
  "\005 \001(\t\022>\n\rresponse_data\030\006 \001(\0132\'.robosense"
  ".acviewer_msgs.RSResponseData\"0\n\014RSDevic"
  "eInfo\022\014\n\004uuid\030\001 \001(\t\022\022\n\nevent_type\030\002 \001(\r\""
  "h\n\016RSWriterConfig\022A\n\021topic_config_list\030\001"
  " \003(\0132&.robosense.acviewer_msgs.RSTopicCo"
  "nfig\022\023\n\013file_format\030\002 \003(\t\"[\n\016RSReaderCon"
  "fig\022\023\n\013file_topics\030\001 \003(\t\022\032\n\022start_timest"
  "amp_ns\030\002 \001(\003\022\030\n\020end_timestamp_ns\030\003 \001(\003\"C"
  "\n\rRSTopicConfig\022\r\n\005topic\030\001 \001(\t\022\014\n\004type\030\002"
  " \001(\t\022\025\n\rprocess_types\030\003 \003(\t\"y\n\020RSReaderP"
  "rogress\022\031\n\021total_frame_count\030\001 \001(\r\022\022\n\nis"
  "_playing\030\002 \001(\010\022\033\n\023current_frame_index\030\003 "
  "\001(\005\022\031\n\021current_timestamp\030\004 \001(\004\"\302\002\n\016RSRes"
  "ponseData\022\?\n\020device_info_list\030\001 \003(\0132%.ro"
  "bosense.acviewer_msgs.RSDeviceInfo\022>\n\rwr"
  "iter_config\030\002 \001(\0132\'.robosense.acviewer_m"
  "sgs.RSWriterConfig\022\021\n\tfile_list\030\003 \003(\t\022>\n"
  "\rreader_config\030\004 \001(\0132\'.robosense.acviewe"
  "r_msgs.RSReaderConfig\022\030\n\020loading_progres"
  "s\030\005 \001(\r\022B\n\017reader_progress\030\006 \001(\0132).robos"
  "ense.acviewer_msgs.RSReaderProgress\"\242\002\n\020"
  "UdpControlConfig\022n\n\020udp_control_type\030\001 \001"
  "(\01621.robosense.acviewer_msgs.RS_UDP_CONT"
  "ROL_MODE_TYPE:!RS_UDP_CONTROL_TOTAL_CONT"
  "ROL_TIME\022%\n\031udp_total_control_time_ms\030\002 "
  "\001(\r:\00260\022+\n udp_total_control_single_time"
  "_ms\030\003 \001(\r:\0012\022%\n\025udp_data_control_size\030\004 "
  "\001(\r:\006262144\022#\n\030udp_data_control_time_ms\030"
  "\005 \001(\r:\0012\"\370\001\n\014RenderSwitch\022\033\n\renable_rend"
  "er\030\001 \001(\010:\004true\022i\n\020render_comm_type\030\002 \001(\016"
  "25.robosense.acviewer_msgs.RS_RENDER_COM"
  "MUNICATION_TYPE:\030RS_RENDER_COMM_WEBSOCKE"
  "T\022\031\n\021udp_p2p_carapp_ip\030\003 \001(\t\022E\n\022udp_cont"
  "rol_config\030\004 \001(\0132).robosense.acviewer_ms"
  "gs.UdpControlConfig\"\?\n\tRSPositon\022\t\n\001x\030\001 "
  "\001(\002\022\t\n\001y\030\002 \001(\002\022\t\n\001z\030\003 \001(\002\022\021\n\ttimestamp\030\004"
  " \001(\004\"-\n\014RSPointCloud\022\014\n\004data\030\001 \001(\014\022\017\n\004si"
  "ze\030\002 \001(\r:\0010\"/\n\016RSTrangleFacet\022\014\n\004data\030\001 "
  "\001(\014\022\017\n\004size\030\002 \001(\r:\0010\"l\n\tRSRgbJpeg\022\037\n\020is_"
  "jpeg_compress\030\001 \001(\010:\005false\022\r\n\005width\030\002 \001("
  "\r\022\016\n\006height\030\003 \001(\r\022\014\n\004data\030\004 \001(\014\022\021\n\ttimes"
  "tamp\030\005 \001(\004\"N\n\014RSDepthImage\022\r\n\005width\030\001 \001("
  "\r\022\016\n\006height\030\002 \001(\r\022\021\n\ttimestamp\030\003 \001(\004\022\014\n\004"
  "data\030\004 \001(\014\"\357\002\n\010RSRender\0224\n\010position\030\001 \001("
  "\0132\".robosense.acviewer_msgs.RSPositon\022:\n"
  "\013point_cloud\030\002 \001(\0132%.robosense.acviewer_"
  "msgs.RSPointCloud\022\?\n\020point_cloud_slam\030\003 "
  "\001(\0132%.robosense.acviewer_msgs.RSPointClo"
  "ud\022>\n\rtriangle_list\030\004 \001(\0132\'.robosense.ac"
  "viewer_msgs.RSTrangleFacet\0224\n\010rgb_jpeg\030\005"
  " \001(\0132\".robosense.acviewer_msgs.RSRgbJpeg"
  "\022:\n\013depth_image\030\006 \001(\0132%.robosense.acview"
  "er_msgs.RSDepthImage*\372\002\n\013RS_CMD_TYPE\022\021\n\r"
  "RS_HEART_BEAT\020\000\022\023\n\017RS_SET_RUN_MODE\020\001\022\026\n\022"
  "RS_SET_SHOW_CONFIG\020\002\022\026\n\022RS_GET_DEVICE_IN"
  "FO\020\003\022\026\n\022RS_OPERATOR_DEVICE\020\004\022\030\n\024RS_GET_W"
  "RITER_CONFIG\020\005\022\023\n\017RS_START_WRITER\020\006\022\022\n\016R"
  "S_STOP_WRITER\020\007\022\024\n\020RS_GET_FILE_LIST\020\010\022\026\n"
  "\022RS_GET_FILE_TOPICS\020\t\022\023\n\017RS_START_READER"
  "\020\n\022\027\n\023RS_LOADING_PROGRESS\020\013\022\032\n\026RS_GET_RE"
  "ADER_PROGRESS\020\014\022\021\n\rRS_SKIP_FRAME\020\r\022\013\n\007RS"
  "_PLAY\020\016\022\014\n\010RS_PAUSE\020\017\022\022\n\016RS_STOP_READER\020"
  "\020*\243\001\n\034RS_RENDER_COMMUNICATION_TYPE\022\034\n\030RS"
  "_RENDER_COMM_WEBSOCKET\020\001\022 \n\034RS_RENDER_CO"
  "MM_UDP_MULTICAST\020\002\022\'\n#RS_RENDER_COMM_DOU"
  "BLE_UDP_MULTICAST\020\003\022\032\n\026RS_RENDER_COMM_UD"
  "P_P2P\020\004*\203\001\n\030RS_UDP_CONTROL_MODE_TYPE\022\032\n\026"
  "RS_UDP_CONTROL_NOTHING\020\001\022%\n!RS_UDP_CONTR"
  "OL_TOTAL_CONTROL_TIME\020\002\022$\n RS_UDP_CONTRO"
  "L_DATA_CONTROL_TIME\020\003*f\n\037RS_UDP_BUFFER_C"
  "ONTROL_MODE_TYPE\022!\n\035RS_UDP_BUFFER_CONTRO"
  "L_NOTHING\020\001\022 \n\034RS_UDP_BUFFER_CONTROL_ENA"
  "BLE\020\002"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_acviewer_2eproto_deps[1] = {
};
static ::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase*const descriptor_table_acviewer_2eproto_sccs[22] = {
  &scc_info_RSDepthImage_acviewer_2eproto.base,
  &scc_info_RSDeviceInfo_acviewer_2eproto.base,
  &scc_info_RSOperatorDeviceData_acviewer_2eproto.base,
  &scc_info_RSPointCloud_acviewer_2eproto.base,
  &scc_info_RSPositon_acviewer_2eproto.base,
  &scc_info_RSReaderConfig_acviewer_2eproto.base,
  &scc_info_RSReaderProgress_acviewer_2eproto.base,
  &scc_info_RSReaderSetting_acviewer_2eproto.base,
  &scc_info_RSRender_acviewer_2eproto.base,
  &scc_info_RSRequest_acviewer_2eproto.base,
  &scc_info_RSRequestData_acviewer_2eproto.base,
  &scc_info_RSResponse_acviewer_2eproto.base,
  &scc_info_RSResponseData_acviewer_2eproto.base,
  &scc_info_RSRgbJpeg_acviewer_2eproto.base,
  &scc_info_RSShowConfigData_acviewer_2eproto.base,
  &scc_info_RSTopicConfig_acviewer_2eproto.base,
  &scc_info_RSTopicSetting_acviewer_2eproto.base,
  &scc_info_RSTrangleFacet_acviewer_2eproto.base,
  &scc_info_RSWriterConfig_acviewer_2eproto.base,
  &scc_info_RSWriterSetting_acviewer_2eproto.base,
  &scc_info_RenderSwitch_acviewer_2eproto.base,
  &scc_info_UdpControlConfig_acviewer_2eproto.base,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_acviewer_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_acviewer_2eproto = {
  false, false, descriptor_table_protodef_acviewer_2eproto, "acviewer.proto", 4165,
  &descriptor_table_acviewer_2eproto_once, descriptor_table_acviewer_2eproto_sccs, descriptor_table_acviewer_2eproto_deps, 22, 0,
  schemas, file_default_instances, TableStruct_acviewer_2eproto::offsets,
  file_level_metadata_acviewer_2eproto, 22, file_level_enum_descriptors_acviewer_2eproto, file_level_service_descriptors_acviewer_2eproto,
};

// Force running AddDescriptors() at dynamic initialization time.
static bool dynamic_init_dummy_acviewer_2eproto = (static_cast<void>(::PROTOBUF_NAMESPACE_ID::internal::AddDescriptors(&descriptor_table_acviewer_2eproto)), true);
namespace robosense {
namespace acviewer_msgs {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RS_CMD_TYPE_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_acviewer_2eproto);
  return file_level_enum_descriptors_acviewer_2eproto[0];
}
bool RS_CMD_TYPE_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
    case 10:
    case 11:
    case 12:
    case 13:
    case 14:
    case 15:
    case 16:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RS_RENDER_COMMUNICATION_TYPE_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_acviewer_2eproto);
  return file_level_enum_descriptors_acviewer_2eproto[1];
}
bool RS_RENDER_COMMUNICATION_TYPE_IsValid(int value) {
  switch (value) {
    case 1:
    case 2:
    case 3:
    case 4:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RS_UDP_CONTROL_MODE_TYPE_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_acviewer_2eproto);
  return file_level_enum_descriptors_acviewer_2eproto[2];
}
bool RS_UDP_CONTROL_MODE_TYPE_IsValid(int value) {
  switch (value) {
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RS_UDP_BUFFER_CONTROL_MODE_TYPE_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_acviewer_2eproto);
  return file_level_enum_descriptors_acviewer_2eproto[3];
}
bool RS_UDP_BUFFER_CONTROL_MODE_TYPE_IsValid(int value) {
  switch (value) {
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class RSRequest::_Internal {
 public:
  using HasBits = decltype(std::declval<RSRequest>()._has_bits_);
  static void set_has_cmd_type(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_request_id(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static const ::robosense::acviewer_msgs::RSRequestData& request_data(const RSRequest* msg);
  static void set_has_request_data(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000006) ^ 0x00000006) != 0;
  }
};

const ::robosense::acviewer_msgs::RSRequestData&
RSRequest::_Internal::request_data(const RSRequest* msg) {
  return *msg->request_data_;
}
RSRequest::RSRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.acviewer_msgs.RSRequest)
}
RSRequest::RSRequest(const RSRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_request_data()) {
    request_data_ = new ::robosense::acviewer_msgs::RSRequestData(*from.request_data_);
  } else {
    request_data_ = nullptr;
  }
  ::memcpy(&request_id_, &from.request_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&cmd_type_) -
    reinterpret_cast<char*>(&request_id_)) + sizeof(cmd_type_));
  // @@protoc_insertion_point(copy_constructor:robosense.acviewer_msgs.RSRequest)
}

void RSRequest::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_RSRequest_acviewer_2eproto.base);
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&request_data_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&cmd_type_) -
      reinterpret_cast<char*>(&request_data_)) + sizeof(cmd_type_));
}

RSRequest::~RSRequest() {
  // @@protoc_insertion_point(destructor:robosense.acviewer_msgs.RSRequest)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void RSRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  if (this != internal_default_instance()) delete request_data_;
}

void RSRequest::ArenaDtor(void* object) {
  RSRequest* _this = reinterpret_cast< RSRequest* >(object);
  (void)_this;
}
void RSRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RSRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RSRequest& RSRequest::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RSRequest_acviewer_2eproto.base);
  return *internal_default_instance();
}


void RSRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.acviewer_msgs.RSRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    GOOGLE_DCHECK(request_data_ != nullptr);
    request_data_->Clear();
  }
  if (cached_has_bits & 0x00000006u) {
    ::memset(&request_id_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&cmd_type_) -
        reinterpret_cast<char*>(&request_id_)) + sizeof(cmd_type_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RSRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // required .robosense.acviewer_msgs.RS_CMD_TYPE cmd_type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::robosense::acviewer_msgs::RS_CMD_TYPE_IsValid(val))) {
            _internal_set_cmd_type(static_cast<::robosense::acviewer_msgs::RS_CMD_TYPE>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(1, val, mutable_unknown_fields());
          }
        } else goto handle_unusual;
        continue;
      // required uint64 request_Id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          _Internal::set_has_request_id(&has_bits);
          request_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .robosense.acviewer_msgs.RSRequestData request_data = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_request_data(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* RSRequest::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.acviewer_msgs.RSRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required .robosense.acviewer_msgs.RS_CMD_TYPE cmd_type = 1;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_cmd_type(), target);
  }

  // required uint64 request_Id = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(2, this->_internal_request_id(), target);
  }

  // optional .robosense.acviewer_msgs.RSRequestData request_data = 3;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::request_data(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.acviewer_msgs.RSRequest)
  return target;
}

size_t RSRequest::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:robosense.acviewer_msgs.RSRequest)
  size_t total_size = 0;

  if (_internal_has_request_id()) {
    // required uint64 request_Id = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_request_id());
  }

  if (_internal_has_cmd_type()) {
    // required .robosense.acviewer_msgs.RS_CMD_TYPE cmd_type = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_cmd_type());
  }

  return total_size;
}
size_t RSRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.acviewer_msgs.RSRequest)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x00000006) ^ 0x00000006) == 0) {  // All required fields are present.
    // required uint64 request_Id = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_request_id());

    // required .robosense.acviewer_msgs.RS_CMD_TYPE cmd_type = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_cmd_type());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // optional .robosense.acviewer_msgs.RSRequestData request_data = 3;
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *request_data_);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RSRequest::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.acviewer_msgs.RSRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const RSRequest* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RSRequest>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.acviewer_msgs.RSRequest)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.acviewer_msgs.RSRequest)
    MergeFrom(*source);
  }
}

void RSRequest::MergeFrom(const RSRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.acviewer_msgs.RSRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_mutable_request_data()->::robosense::acviewer_msgs::RSRequestData::MergeFrom(from._internal_request_data());
    }
    if (cached_has_bits & 0x00000002u) {
      request_id_ = from.request_id_;
    }
    if (cached_has_bits & 0x00000004u) {
      cmd_type_ = from.cmd_type_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void RSRequest::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.acviewer_msgs.RSRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RSRequest::CopyFrom(const RSRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.acviewer_msgs.RSRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RSRequest::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  if (_internal_has_request_data()) {
    if (!request_data_->IsInitialized()) return false;
  }
  return true;
}

void RSRequest::InternalSwap(RSRequest* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RSRequest, cmd_type_)
      + sizeof(RSRequest::cmd_type_)
      - PROTOBUF_FIELD_OFFSET(RSRequest, request_data_)>(
          reinterpret_cast<char*>(&request_data_),
          reinterpret_cast<char*>(&other->request_data_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RSRequest::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class RSShowConfigData::_Internal {
 public:
  using HasBits = decltype(std::declval<RSShowConfigData>()._has_bits_);
  static void set_has_show_pos(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_show_pc(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_show_pc_slam(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_show_tri(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_show_rgb(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static void set_has_show_depth(HasBits* has_bits) {
    (*has_bits)[0] |= 32u;
  }
};

RSShowConfigData::RSShowConfigData(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.acviewer_msgs.RSShowConfigData)
}
RSShowConfigData::RSShowConfigData(const RSShowConfigData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&show_pos_, &from.show_pos_,
    static_cast<size_t>(reinterpret_cast<char*>(&show_depth_) -
    reinterpret_cast<char*>(&show_pos_)) + sizeof(show_depth_));
  // @@protoc_insertion_point(copy_constructor:robosense.acviewer_msgs.RSShowConfigData)
}

void RSShowConfigData::SharedCtor() {
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&show_pos_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&show_depth_) -
      reinterpret_cast<char*>(&show_pos_)) + sizeof(show_depth_));
}

RSShowConfigData::~RSShowConfigData() {
  // @@protoc_insertion_point(destructor:robosense.acviewer_msgs.RSShowConfigData)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void RSShowConfigData::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void RSShowConfigData::ArenaDtor(void* object) {
  RSShowConfigData* _this = reinterpret_cast< RSShowConfigData* >(object);
  (void)_this;
}
void RSShowConfigData::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RSShowConfigData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RSShowConfigData& RSShowConfigData::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RSShowConfigData_acviewer_2eproto.base);
  return *internal_default_instance();
}


void RSShowConfigData::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.acviewer_msgs.RSShowConfigData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000003fu) {
    ::memset(&show_pos_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&show_depth_) -
        reinterpret_cast<char*>(&show_pos_)) + sizeof(show_depth_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RSShowConfigData::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional bool show_pos = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          _Internal::set_has_show_pos(&has_bits);
          show_pos_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional bool show_pc = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          _Internal::set_has_show_pc(&has_bits);
          show_pc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional bool show_pc_slam = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          _Internal::set_has_show_pc_slam(&has_bits);
          show_pc_slam_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional bool show_tri = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          _Internal::set_has_show_tri(&has_bits);
          show_tri_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional bool show_rgb = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          _Internal::set_has_show_rgb(&has_bits);
          show_rgb_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional bool show_depth = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          _Internal::set_has_show_depth(&has_bits);
          show_depth_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* RSShowConfigData::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.acviewer_msgs.RSShowConfigData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional bool show_pos = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_show_pos(), target);
  }

  // optional bool show_pc = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_show_pc(), target);
  }

  // optional bool show_pc_slam = 3;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_show_pc_slam(), target);
  }

  // optional bool show_tri = 4;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(4, this->_internal_show_tri(), target);
  }

  // optional bool show_rgb = 5;
  if (cached_has_bits & 0x00000010u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(5, this->_internal_show_rgb(), target);
  }

  // optional bool show_depth = 6;
  if (cached_has_bits & 0x00000020u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(6, this->_internal_show_depth(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.acviewer_msgs.RSShowConfigData)
  return target;
}

size_t RSShowConfigData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.acviewer_msgs.RSShowConfigData)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000003fu) {
    // optional bool show_pos = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 + 1;
    }

    // optional bool show_pc = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 + 1;
    }

    // optional bool show_pc_slam = 3;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 + 1;
    }

    // optional bool show_tri = 4;
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 + 1;
    }

    // optional bool show_rgb = 5;
    if (cached_has_bits & 0x00000010u) {
      total_size += 1 + 1;
    }

    // optional bool show_depth = 6;
    if (cached_has_bits & 0x00000020u) {
      total_size += 1 + 1;
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RSShowConfigData::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.acviewer_msgs.RSShowConfigData)
  GOOGLE_DCHECK_NE(&from, this);
  const RSShowConfigData* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RSShowConfigData>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.acviewer_msgs.RSShowConfigData)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.acviewer_msgs.RSShowConfigData)
    MergeFrom(*source);
  }
}

void RSShowConfigData::MergeFrom(const RSShowConfigData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.acviewer_msgs.RSShowConfigData)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000003fu) {
    if (cached_has_bits & 0x00000001u) {
      show_pos_ = from.show_pos_;
    }
    if (cached_has_bits & 0x00000002u) {
      show_pc_ = from.show_pc_;
    }
    if (cached_has_bits & 0x00000004u) {
      show_pc_slam_ = from.show_pc_slam_;
    }
    if (cached_has_bits & 0x00000008u) {
      show_tri_ = from.show_tri_;
    }
    if (cached_has_bits & 0x00000010u) {
      show_rgb_ = from.show_rgb_;
    }
    if (cached_has_bits & 0x00000020u) {
      show_depth_ = from.show_depth_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void RSShowConfigData::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.acviewer_msgs.RSShowConfigData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RSShowConfigData::CopyFrom(const RSShowConfigData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.acviewer_msgs.RSShowConfigData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RSShowConfigData::IsInitialized() const {
  return true;
}

void RSShowConfigData::InternalSwap(RSShowConfigData* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RSShowConfigData, show_depth_)
      + sizeof(RSShowConfigData::show_depth_)
      - PROTOBUF_FIELD_OFFSET(RSShowConfigData, show_pos_)>(
          reinterpret_cast<char*>(&show_pos_),
          reinterpret_cast<char*>(&other->show_pos_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RSShowConfigData::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class RSOperatorDeviceData::_Internal {
 public:
  using HasBits = decltype(std::declval<RSOperatorDeviceData>()._has_bits_);
  static void set_has_uuid(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_operator_type(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000003) ^ 0x00000003) != 0;
  }
};

RSOperatorDeviceData::RSOperatorDeviceData(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.acviewer_msgs.RSOperatorDeviceData)
}
RSOperatorDeviceData::RSOperatorDeviceData(const RSOperatorDeviceData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_uuid()) {
    uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_uuid(), 
      GetArena());
  }
  operator_type_ = from.operator_type_;
  // @@protoc_insertion_point(copy_constructor:robosense.acviewer_msgs.RSOperatorDeviceData)
}

void RSOperatorDeviceData::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_RSOperatorDeviceData_acviewer_2eproto.base);
  uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  operator_type_ = 0u;
}

RSOperatorDeviceData::~RSOperatorDeviceData() {
  // @@protoc_insertion_point(destructor:robosense.acviewer_msgs.RSOperatorDeviceData)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void RSOperatorDeviceData::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  uuid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void RSOperatorDeviceData::ArenaDtor(void* object) {
  RSOperatorDeviceData* _this = reinterpret_cast< RSOperatorDeviceData* >(object);
  (void)_this;
}
void RSOperatorDeviceData::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RSOperatorDeviceData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RSOperatorDeviceData& RSOperatorDeviceData::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RSOperatorDeviceData_acviewer_2eproto.base);
  return *internal_default_instance();
}


void RSOperatorDeviceData::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.acviewer_msgs.RSOperatorDeviceData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    uuid_.ClearNonDefaultToEmpty();
  }
  operator_type_ = 0u;
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RSOperatorDeviceData::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // required string uuid = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_uuid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.acviewer_msgs.RSOperatorDeviceData.uuid");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // required uint32 operator_type = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          _Internal::set_has_operator_type(&has_bits);
          operator_type_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* RSOperatorDeviceData::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.acviewer_msgs.RSOperatorDeviceData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required string uuid = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_uuid().data(), static_cast<int>(this->_internal_uuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.acviewer_msgs.RSOperatorDeviceData.uuid");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_uuid(), target);
  }

  // required uint32 operator_type = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_operator_type(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.acviewer_msgs.RSOperatorDeviceData)
  return target;
}

size_t RSOperatorDeviceData::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:robosense.acviewer_msgs.RSOperatorDeviceData)
  size_t total_size = 0;

  if (_internal_has_uuid()) {
    // required string uuid = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_uuid());
  }

  if (_internal_has_operator_type()) {
    // required uint32 operator_type = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_operator_type());
  }

  return total_size;
}
size_t RSOperatorDeviceData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.acviewer_msgs.RSOperatorDeviceData)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x00000003) ^ 0x00000003) == 0) {  // All required fields are present.
    // required string uuid = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_uuid());

    // required uint32 operator_type = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_operator_type());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RSOperatorDeviceData::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.acviewer_msgs.RSOperatorDeviceData)
  GOOGLE_DCHECK_NE(&from, this);
  const RSOperatorDeviceData* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RSOperatorDeviceData>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.acviewer_msgs.RSOperatorDeviceData)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.acviewer_msgs.RSOperatorDeviceData)
    MergeFrom(*source);
  }
}

void RSOperatorDeviceData::MergeFrom(const RSOperatorDeviceData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.acviewer_msgs.RSOperatorDeviceData)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_uuid(from._internal_uuid());
    }
    if (cached_has_bits & 0x00000002u) {
      operator_type_ = from.operator_type_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void RSOperatorDeviceData::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.acviewer_msgs.RSOperatorDeviceData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RSOperatorDeviceData::CopyFrom(const RSOperatorDeviceData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.acviewer_msgs.RSOperatorDeviceData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RSOperatorDeviceData::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  return true;
}

void RSOperatorDeviceData::InternalSwap(RSOperatorDeviceData* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  uuid_.Swap(&other->uuid_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  swap(operator_type_, other->operator_type_);
}

::PROTOBUF_NAMESPACE_ID::Metadata RSOperatorDeviceData::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class RSWriterSetting::_Internal {
 public:
  using HasBits = decltype(std::declval<RSWriterSetting>()._has_bits_);
  static void set_has_file_name(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_file_format(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

RSWriterSetting::RSWriterSetting(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  topic_setting_list_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.acviewer_msgs.RSWriterSetting)
}
RSWriterSetting::RSWriterSetting(const RSWriterSetting& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_),
      topic_setting_list_(from.topic_setting_list_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  file_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_file_name()) {
    file_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_file_name(), 
      GetArena());
  }
  file_format_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_file_format()) {
    file_format_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_file_format(), 
      GetArena());
  }
  // @@protoc_insertion_point(copy_constructor:robosense.acviewer_msgs.RSWriterSetting)
}

void RSWriterSetting::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_RSWriterSetting_acviewer_2eproto.base);
  file_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  file_format_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

RSWriterSetting::~RSWriterSetting() {
  // @@protoc_insertion_point(destructor:robosense.acviewer_msgs.RSWriterSetting)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void RSWriterSetting::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  file_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  file_format_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void RSWriterSetting::ArenaDtor(void* object) {
  RSWriterSetting* _this = reinterpret_cast< RSWriterSetting* >(object);
  (void)_this;
}
void RSWriterSetting::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RSWriterSetting::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RSWriterSetting& RSWriterSetting::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RSWriterSetting_acviewer_2eproto.base);
  return *internal_default_instance();
}


void RSWriterSetting::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.acviewer_msgs.RSWriterSetting)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  topic_setting_list_.Clear();
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      file_name_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      file_format_.ClearNonDefaultToEmpty();
    }
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RSWriterSetting::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional string file_name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_file_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.acviewer_msgs.RSWriterSetting.file_name");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string file_format = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_file_format();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.acviewer_msgs.RSWriterSetting.file_format");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .robosense.acviewer_msgs.RSTopicSetting topic_setting_list = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_topic_setting_list(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* RSWriterSetting::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.acviewer_msgs.RSWriterSetting)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional string file_name = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_file_name().data(), static_cast<int>(this->_internal_file_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.acviewer_msgs.RSWriterSetting.file_name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_file_name(), target);
  }

  // optional string file_format = 2;
  if (cached_has_bits & 0x00000002u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_file_format().data(), static_cast<int>(this->_internal_file_format().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.acviewer_msgs.RSWriterSetting.file_format");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_file_format(), target);
  }

  // repeated .robosense.acviewer_msgs.RSTopicSetting topic_setting_list = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_topic_setting_list_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, this->_internal_topic_setting_list(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.acviewer_msgs.RSWriterSetting)
  return target;
}

size_t RSWriterSetting::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.acviewer_msgs.RSWriterSetting)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .robosense.acviewer_msgs.RSTopicSetting topic_setting_list = 3;
  total_size += 1UL * this->_internal_topic_setting_list_size();
  for (const auto& msg : this->topic_setting_list_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional string file_name = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_file_name());
    }

    // optional string file_format = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_file_format());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RSWriterSetting::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.acviewer_msgs.RSWriterSetting)
  GOOGLE_DCHECK_NE(&from, this);
  const RSWriterSetting* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RSWriterSetting>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.acviewer_msgs.RSWriterSetting)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.acviewer_msgs.RSWriterSetting)
    MergeFrom(*source);
  }
}

void RSWriterSetting::MergeFrom(const RSWriterSetting& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.acviewer_msgs.RSWriterSetting)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  topic_setting_list_.MergeFrom(from.topic_setting_list_);
  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_file_name(from._internal_file_name());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_set_file_format(from._internal_file_format());
    }
  }
}

void RSWriterSetting::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.acviewer_msgs.RSWriterSetting)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RSWriterSetting::CopyFrom(const RSWriterSetting& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.acviewer_msgs.RSWriterSetting)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RSWriterSetting::IsInitialized() const {
  return true;
}

void RSWriterSetting::InternalSwap(RSWriterSetting* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  topic_setting_list_.InternalSwap(&other->topic_setting_list_);
  file_name_.Swap(&other->file_name_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  file_format_.Swap(&other->file_format_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}

::PROTOBUF_NAMESPACE_ID::Metadata RSWriterSetting::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class RSTopicSetting::_Internal {
 public:
  using HasBits = decltype(std::declval<RSTopicSetting>()._has_bits_);
  static void set_has_topic(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_compressed_type(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

RSTopicSetting::RSTopicSetting(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.acviewer_msgs.RSTopicSetting)
}
RSTopicSetting::RSTopicSetting(const RSTopicSetting& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  topic_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_topic()) {
    topic_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_topic(), 
      GetArena());
  }
  compressed_type_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_compressed_type()) {
    compressed_type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_compressed_type(), 
      GetArena());
  }
  // @@protoc_insertion_point(copy_constructor:robosense.acviewer_msgs.RSTopicSetting)
}

void RSTopicSetting::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_RSTopicSetting_acviewer_2eproto.base);
  topic_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  compressed_type_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

RSTopicSetting::~RSTopicSetting() {
  // @@protoc_insertion_point(destructor:robosense.acviewer_msgs.RSTopicSetting)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void RSTopicSetting::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  topic_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  compressed_type_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void RSTopicSetting::ArenaDtor(void* object) {
  RSTopicSetting* _this = reinterpret_cast< RSTopicSetting* >(object);
  (void)_this;
}
void RSTopicSetting::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RSTopicSetting::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RSTopicSetting& RSTopicSetting::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RSTopicSetting_acviewer_2eproto.base);
  return *internal_default_instance();
}


void RSTopicSetting::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.acviewer_msgs.RSTopicSetting)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      topic_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      compressed_type_.ClearNonDefaultToEmpty();
    }
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RSTopicSetting::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional string topic = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_topic();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.acviewer_msgs.RSTopicSetting.topic");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string compressed_type = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_compressed_type();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.acviewer_msgs.RSTopicSetting.compressed_type");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* RSTopicSetting::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.acviewer_msgs.RSTopicSetting)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional string topic = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_topic().data(), static_cast<int>(this->_internal_topic().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.acviewer_msgs.RSTopicSetting.topic");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_topic(), target);
  }

  // optional string compressed_type = 2;
  if (cached_has_bits & 0x00000002u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_compressed_type().data(), static_cast<int>(this->_internal_compressed_type().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.acviewer_msgs.RSTopicSetting.compressed_type");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_compressed_type(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.acviewer_msgs.RSTopicSetting)
  return target;
}

size_t RSTopicSetting::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.acviewer_msgs.RSTopicSetting)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional string topic = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_topic());
    }

    // optional string compressed_type = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_compressed_type());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RSTopicSetting::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.acviewer_msgs.RSTopicSetting)
  GOOGLE_DCHECK_NE(&from, this);
  const RSTopicSetting* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RSTopicSetting>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.acviewer_msgs.RSTopicSetting)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.acviewer_msgs.RSTopicSetting)
    MergeFrom(*source);
  }
}

void RSTopicSetting::MergeFrom(const RSTopicSetting& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.acviewer_msgs.RSTopicSetting)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_topic(from._internal_topic());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_set_compressed_type(from._internal_compressed_type());
    }
  }
}

void RSTopicSetting::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.acviewer_msgs.RSTopicSetting)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RSTopicSetting::CopyFrom(const RSTopicSetting& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.acviewer_msgs.RSTopicSetting)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RSTopicSetting::IsInitialized() const {
  return true;
}

void RSTopicSetting::InternalSwap(RSTopicSetting* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  topic_.Swap(&other->topic_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  compressed_type_.Swap(&other->compressed_type_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}

::PROTOBUF_NAMESPACE_ID::Metadata RSTopicSetting::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class RSReaderSetting::_Internal {
 public:
  using HasBits = decltype(std::declval<RSReaderSetting>()._has_bits_);
  static void set_has_file_name(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_main_sync_topic(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_pre_load_mains_sync_cnt(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_read_start_timestamp_ns(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_read_end_timestamp_ns(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
};

RSReaderSetting::RSReaderSetting(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  read_topic_names_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.acviewer_msgs.RSReaderSetting)
}
RSReaderSetting::RSReaderSetting(const RSReaderSetting& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_),
      read_topic_names_(from.read_topic_names_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  file_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_file_name()) {
    file_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_file_name(), 
      GetArena());
  }
  main_sync_topic_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_main_sync_topic()) {
    main_sync_topic_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_main_sync_topic(), 
      GetArena());
  }
  ::memcpy(&pre_load_mains_sync_cnt_, &from.pre_load_mains_sync_cnt_,
    static_cast<size_t>(reinterpret_cast<char*>(&read_end_timestamp_ns_) -
    reinterpret_cast<char*>(&pre_load_mains_sync_cnt_)) + sizeof(read_end_timestamp_ns_));
  // @@protoc_insertion_point(copy_constructor:robosense.acviewer_msgs.RSReaderSetting)
}

void RSReaderSetting::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_RSReaderSetting_acviewer_2eproto.base);
  file_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  main_sync_topic_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  pre_load_mains_sync_cnt_ = 0u;
  read_start_timestamp_ns_ = PROTOBUF_LONGLONG(-1);
  read_end_timestamp_ns_ = PROTOBUF_LONGLONG(-1);
}

RSReaderSetting::~RSReaderSetting() {
  // @@protoc_insertion_point(destructor:robosense.acviewer_msgs.RSReaderSetting)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void RSReaderSetting::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  file_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  main_sync_topic_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void RSReaderSetting::ArenaDtor(void* object) {
  RSReaderSetting* _this = reinterpret_cast< RSReaderSetting* >(object);
  (void)_this;
}
void RSReaderSetting::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RSReaderSetting::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RSReaderSetting& RSReaderSetting::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RSReaderSetting_acviewer_2eproto.base);
  return *internal_default_instance();
}


void RSReaderSetting::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.acviewer_msgs.RSReaderSetting)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  read_topic_names_.Clear();
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      file_name_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      main_sync_topic_.ClearNonDefaultToEmpty();
    }
  }
  if (cached_has_bits & 0x0000001cu) {
    pre_load_mains_sync_cnt_ = 0u;
    read_start_timestamp_ns_ = PROTOBUF_LONGLONG(-1);
    read_end_timestamp_ns_ = PROTOBUF_LONGLONG(-1);
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RSReaderSetting::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional string file_name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_file_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.acviewer_msgs.RSReaderSetting.file_name");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string main_sync_topic = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_main_sync_topic();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.acviewer_msgs.RSReaderSetting.main_sync_topic");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 pre_load_mains_sync_cnt = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          _Internal::set_has_pre_load_mains_sync_cnt(&has_bits);
          pre_load_mains_sync_cnt_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated string read_topic_names = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_read_topic_names();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            #ifndef NDEBUG
            ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.acviewer_msgs.RSReaderSetting.read_topic_names");
            #endif  // !NDEBUG
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else goto handle_unusual;
        continue;
      // optional int64 read_start_timestamp_ns = 5 [default = -1];
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          _Internal::set_has_read_start_timestamp_ns(&has_bits);
          read_start_timestamp_ns_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional int64 read_end_timestamp_ns = 6 [default = -1];
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          _Internal::set_has_read_end_timestamp_ns(&has_bits);
          read_end_timestamp_ns_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* RSReaderSetting::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.acviewer_msgs.RSReaderSetting)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional string file_name = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_file_name().data(), static_cast<int>(this->_internal_file_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.acviewer_msgs.RSReaderSetting.file_name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_file_name(), target);
  }

  // optional string main_sync_topic = 2;
  if (cached_has_bits & 0x00000002u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_main_sync_topic().data(), static_cast<int>(this->_internal_main_sync_topic().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.acviewer_msgs.RSReaderSetting.main_sync_topic");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_main_sync_topic(), target);
  }

  // optional uint32 pre_load_mains_sync_cnt = 3;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_pre_load_mains_sync_cnt(), target);
  }

  // repeated string read_topic_names = 4;
  for (int i = 0, n = this->_internal_read_topic_names_size(); i < n; i++) {
    const auto& s = this->_internal_read_topic_names(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.acviewer_msgs.RSReaderSetting.read_topic_names");
    target = stream->WriteString(4, s, target);
  }

  // optional int64 read_start_timestamp_ns = 5 [default = -1];
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(5, this->_internal_read_start_timestamp_ns(), target);
  }

  // optional int64 read_end_timestamp_ns = 6 [default = -1];
  if (cached_has_bits & 0x00000010u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(6, this->_internal_read_end_timestamp_ns(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.acviewer_msgs.RSReaderSetting)
  return target;
}

size_t RSReaderSetting::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.acviewer_msgs.RSReaderSetting)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string read_topic_names = 4;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(read_topic_names_.size());
  for (int i = 0, n = read_topic_names_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      read_topic_names_.Get(i));
  }

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000001fu) {
    // optional string file_name = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_file_name());
    }

    // optional string main_sync_topic = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_main_sync_topic());
    }

    // optional uint32 pre_load_mains_sync_cnt = 3;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_pre_load_mains_sync_cnt());
    }

    // optional int64 read_start_timestamp_ns = 5 [default = -1];
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64Size(
          this->_internal_read_start_timestamp_ns());
    }

    // optional int64 read_end_timestamp_ns = 6 [default = -1];
    if (cached_has_bits & 0x00000010u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64Size(
          this->_internal_read_end_timestamp_ns());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RSReaderSetting::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.acviewer_msgs.RSReaderSetting)
  GOOGLE_DCHECK_NE(&from, this);
  const RSReaderSetting* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RSReaderSetting>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.acviewer_msgs.RSReaderSetting)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.acviewer_msgs.RSReaderSetting)
    MergeFrom(*source);
  }
}

void RSReaderSetting::MergeFrom(const RSReaderSetting& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.acviewer_msgs.RSReaderSetting)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  read_topic_names_.MergeFrom(from.read_topic_names_);
  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000001fu) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_file_name(from._internal_file_name());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_set_main_sync_topic(from._internal_main_sync_topic());
    }
    if (cached_has_bits & 0x00000004u) {
      pre_load_mains_sync_cnt_ = from.pre_load_mains_sync_cnt_;
    }
    if (cached_has_bits & 0x00000008u) {
      read_start_timestamp_ns_ = from.read_start_timestamp_ns_;
    }
    if (cached_has_bits & 0x00000010u) {
      read_end_timestamp_ns_ = from.read_end_timestamp_ns_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void RSReaderSetting::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.acviewer_msgs.RSReaderSetting)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RSReaderSetting::CopyFrom(const RSReaderSetting& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.acviewer_msgs.RSReaderSetting)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RSReaderSetting::IsInitialized() const {
  return true;
}

void RSReaderSetting::InternalSwap(RSReaderSetting* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  read_topic_names_.InternalSwap(&other->read_topic_names_);
  file_name_.Swap(&other->file_name_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  main_sync_topic_.Swap(&other->main_sync_topic_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  swap(pre_load_mains_sync_cnt_, other->pre_load_mains_sync_cnt_);
  swap(read_start_timestamp_ns_, other->read_start_timestamp_ns_);
  swap(read_end_timestamp_ns_, other->read_end_timestamp_ns_);
}

::PROTOBUF_NAMESPACE_ID::Metadata RSReaderSetting::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class RSRequestData::_Internal {
 public:
  using HasBits = decltype(std::declval<RSRequestData>()._has_bits_);
  static void set_has_runmode(HasBits* has_bits) {
    (*has_bits)[0] |= 32u;
  }
  static const ::robosense::acviewer_msgs::RSShowConfigData& show_config_data(const RSRequestData* msg);
  static void set_has_show_config_data(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static const ::robosense::acviewer_msgs::RSOperatorDeviceData& operator_device_data(const RSRequestData* msg);
  static void set_has_operator_device_data(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static const ::robosense::acviewer_msgs::RSWriterSetting& writer_setting(const RSRequestData* msg);
  static void set_has_writer_setting(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static const ::robosense::acviewer_msgs::RSReaderSetting& reader_setting(const RSRequestData* msg);
  static void set_has_reader_setting(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static void set_has_topic_file_path(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_skip_frame(HasBits* has_bits) {
    (*has_bits)[0] |= 64u;
  }
};

const ::robosense::acviewer_msgs::RSShowConfigData&
RSRequestData::_Internal::show_config_data(const RSRequestData* msg) {
  return *msg->show_config_data_;
}
const ::robosense::acviewer_msgs::RSOperatorDeviceData&
RSRequestData::_Internal::operator_device_data(const RSRequestData* msg) {
  return *msg->operator_device_data_;
}
const ::robosense::acviewer_msgs::RSWriterSetting&
RSRequestData::_Internal::writer_setting(const RSRequestData* msg) {
  return *msg->writer_setting_;
}
const ::robosense::acviewer_msgs::RSReaderSetting&
RSRequestData::_Internal::reader_setting(const RSRequestData* msg) {
  return *msg->reader_setting_;
}
RSRequestData::RSRequestData(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.acviewer_msgs.RSRequestData)
}
RSRequestData::RSRequestData(const RSRequestData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  topic_file_path_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_topic_file_path()) {
    topic_file_path_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_topic_file_path(), 
      GetArena());
  }
  if (from._internal_has_show_config_data()) {
    show_config_data_ = new ::robosense::acviewer_msgs::RSShowConfigData(*from.show_config_data_);
  } else {
    show_config_data_ = nullptr;
  }
  if (from._internal_has_operator_device_data()) {
    operator_device_data_ = new ::robosense::acviewer_msgs::RSOperatorDeviceData(*from.operator_device_data_);
  } else {
    operator_device_data_ = nullptr;
  }
  if (from._internal_has_writer_setting()) {
    writer_setting_ = new ::robosense::acviewer_msgs::RSWriterSetting(*from.writer_setting_);
  } else {
    writer_setting_ = nullptr;
  }
  if (from._internal_has_reader_setting()) {
    reader_setting_ = new ::robosense::acviewer_msgs::RSReaderSetting(*from.reader_setting_);
  } else {
    reader_setting_ = nullptr;
  }
  ::memcpy(&runmode_, &from.runmode_,
    static_cast<size_t>(reinterpret_cast<char*>(&skip_frame_) -
    reinterpret_cast<char*>(&runmode_)) + sizeof(skip_frame_));
  // @@protoc_insertion_point(copy_constructor:robosense.acviewer_msgs.RSRequestData)
}

void RSRequestData::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_RSRequestData_acviewer_2eproto.base);
  topic_file_path_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&show_config_data_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&skip_frame_) -
      reinterpret_cast<char*>(&show_config_data_)) + sizeof(skip_frame_));
}

RSRequestData::~RSRequestData() {
  // @@protoc_insertion_point(destructor:robosense.acviewer_msgs.RSRequestData)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void RSRequestData::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  topic_file_path_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete show_config_data_;
  if (this != internal_default_instance()) delete operator_device_data_;
  if (this != internal_default_instance()) delete writer_setting_;
  if (this != internal_default_instance()) delete reader_setting_;
}

void RSRequestData::ArenaDtor(void* object) {
  RSRequestData* _this = reinterpret_cast< RSRequestData* >(object);
  (void)_this;
}
void RSRequestData::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RSRequestData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RSRequestData& RSRequestData::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RSRequestData_acviewer_2eproto.base);
  return *internal_default_instance();
}


void RSRequestData::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.acviewer_msgs.RSRequestData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000001fu) {
    if (cached_has_bits & 0x00000001u) {
      topic_file_path_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      GOOGLE_DCHECK(show_config_data_ != nullptr);
      show_config_data_->Clear();
    }
    if (cached_has_bits & 0x00000004u) {
      GOOGLE_DCHECK(operator_device_data_ != nullptr);
      operator_device_data_->Clear();
    }
    if (cached_has_bits & 0x00000008u) {
      GOOGLE_DCHECK(writer_setting_ != nullptr);
      writer_setting_->Clear();
    }
    if (cached_has_bits & 0x00000010u) {
      GOOGLE_DCHECK(reader_setting_ != nullptr);
      reader_setting_->Clear();
    }
  }
  if (cached_has_bits & 0x00000060u) {
    ::memset(&runmode_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&skip_frame_) -
        reinterpret_cast<char*>(&runmode_)) + sizeof(skip_frame_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RSRequestData::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional uint32 runMode = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          _Internal::set_has_runmode(&has_bits);
          runmode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .robosense.acviewer_msgs.RSShowConfigData show_config_data = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_show_config_data(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .robosense.acviewer_msgs.RSOperatorDeviceData operator_device_data = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_operator_device_data(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .robosense.acviewer_msgs.RSWriterSetting writer_setting = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_writer_setting(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .robosense.acviewer_msgs.RSReaderSetting reader_setting = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_reader_setting(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string topic_file_path = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          auto str = _internal_mutable_topic_file_path();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.acviewer_msgs.RSRequestData.topic_file_path");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional int32 skip_frame = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          _Internal::set_has_skip_frame(&has_bits);
          skip_frame_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* RSRequestData::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.acviewer_msgs.RSRequestData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional uint32 runMode = 1;
  if (cached_has_bits & 0x00000020u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_runmode(), target);
  }

  // optional .robosense.acviewer_msgs.RSShowConfigData show_config_data = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::show_config_data(this), target, stream);
  }

  // optional .robosense.acviewer_msgs.RSOperatorDeviceData operator_device_data = 3;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::operator_device_data(this), target, stream);
  }

  // optional .robosense.acviewer_msgs.RSWriterSetting writer_setting = 4;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::writer_setting(this), target, stream);
  }

  // optional .robosense.acviewer_msgs.RSReaderSetting reader_setting = 5;
  if (cached_has_bits & 0x00000010u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::reader_setting(this), target, stream);
  }

  // optional string topic_file_path = 6;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_topic_file_path().data(), static_cast<int>(this->_internal_topic_file_path().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.acviewer_msgs.RSRequestData.topic_file_path");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_topic_file_path(), target);
  }

  // optional int32 skip_frame = 7;
  if (cached_has_bits & 0x00000040u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(7, this->_internal_skip_frame(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.acviewer_msgs.RSRequestData)
  return target;
}

size_t RSRequestData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.acviewer_msgs.RSRequestData)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000007fu) {
    // optional string topic_file_path = 6;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_topic_file_path());
    }

    // optional .robosense.acviewer_msgs.RSShowConfigData show_config_data = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *show_config_data_);
    }

    // optional .robosense.acviewer_msgs.RSOperatorDeviceData operator_device_data = 3;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *operator_device_data_);
    }

    // optional .robosense.acviewer_msgs.RSWriterSetting writer_setting = 4;
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *writer_setting_);
    }

    // optional .robosense.acviewer_msgs.RSReaderSetting reader_setting = 5;
    if (cached_has_bits & 0x00000010u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *reader_setting_);
    }

    // optional uint32 runMode = 1;
    if (cached_has_bits & 0x00000020u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_runmode());
    }

    // optional int32 skip_frame = 7;
    if (cached_has_bits & 0x00000040u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
          this->_internal_skip_frame());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RSRequestData::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.acviewer_msgs.RSRequestData)
  GOOGLE_DCHECK_NE(&from, this);
  const RSRequestData* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RSRequestData>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.acviewer_msgs.RSRequestData)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.acviewer_msgs.RSRequestData)
    MergeFrom(*source);
  }
}

void RSRequestData::MergeFrom(const RSRequestData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.acviewer_msgs.RSRequestData)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000007fu) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_topic_file_path(from._internal_topic_file_path());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_mutable_show_config_data()->::robosense::acviewer_msgs::RSShowConfigData::MergeFrom(from._internal_show_config_data());
    }
    if (cached_has_bits & 0x00000004u) {
      _internal_mutable_operator_device_data()->::robosense::acviewer_msgs::RSOperatorDeviceData::MergeFrom(from._internal_operator_device_data());
    }
    if (cached_has_bits & 0x00000008u) {
      _internal_mutable_writer_setting()->::robosense::acviewer_msgs::RSWriterSetting::MergeFrom(from._internal_writer_setting());
    }
    if (cached_has_bits & 0x00000010u) {
      _internal_mutable_reader_setting()->::robosense::acviewer_msgs::RSReaderSetting::MergeFrom(from._internal_reader_setting());
    }
    if (cached_has_bits & 0x00000020u) {
      runmode_ = from.runmode_;
    }
    if (cached_has_bits & 0x00000040u) {
      skip_frame_ = from.skip_frame_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void RSRequestData::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.acviewer_msgs.RSRequestData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RSRequestData::CopyFrom(const RSRequestData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.acviewer_msgs.RSRequestData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RSRequestData::IsInitialized() const {
  if (_internal_has_operator_device_data()) {
    if (!operator_device_data_->IsInitialized()) return false;
  }
  return true;
}

void RSRequestData::InternalSwap(RSRequestData* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  topic_file_path_.Swap(&other->topic_file_path_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RSRequestData, skip_frame_)
      + sizeof(RSRequestData::skip_frame_)
      - PROTOBUF_FIELD_OFFSET(RSRequestData, show_config_data_)>(
          reinterpret_cast<char*>(&show_config_data_),
          reinterpret_cast<char*>(&other->show_config_data_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RSRequestData::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class RSResponse::_Internal {
 public:
  using HasBits = decltype(std::declval<RSResponse>()._has_bits_);
  static void set_has_cmd_type(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_request_id(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_response_id(HasBits* has_bits) {
    (*has_bits)[0] |= 32u;
  }
  static void set_has_response_code(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static void set_has_response_info(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static const ::robosense::acviewer_msgs::RSResponseData& response_data(const RSResponse* msg);
  static void set_has_response_data(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x0000003c) ^ 0x0000003c) != 0;
  }
};

const ::robosense::acviewer_msgs::RSResponseData&
RSResponse::_Internal::response_data(const RSResponse* msg) {
  return *msg->response_data_;
}
RSResponse::RSResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.acviewer_msgs.RSResponse)
}
RSResponse::RSResponse(const RSResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  response_info_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_response_info()) {
    response_info_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_response_info(), 
      GetArena());
  }
  if (from._internal_has_response_data()) {
    response_data_ = new ::robosense::acviewer_msgs::RSResponseData(*from.response_data_);
  } else {
    response_data_ = nullptr;
  }
  ::memcpy(&request_id_, &from.request_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&response_id_) -
    reinterpret_cast<char*>(&request_id_)) + sizeof(response_id_));
  // @@protoc_insertion_point(copy_constructor:robosense.acviewer_msgs.RSResponse)
}

void RSResponse::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_RSResponse_acviewer_2eproto.base);
  response_info_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&response_data_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&response_id_) -
      reinterpret_cast<char*>(&response_data_)) + sizeof(response_id_));
}

RSResponse::~RSResponse() {
  // @@protoc_insertion_point(destructor:robosense.acviewer_msgs.RSResponse)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void RSResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  response_info_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete response_data_;
}

void RSResponse::ArenaDtor(void* object) {
  RSResponse* _this = reinterpret_cast< RSResponse* >(object);
  (void)_this;
}
void RSResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RSResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RSResponse& RSResponse::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RSResponse_acviewer_2eproto.base);
  return *internal_default_instance();
}


void RSResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.acviewer_msgs.RSResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      response_info_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      GOOGLE_DCHECK(response_data_ != nullptr);
      response_data_->Clear();
    }
  }
  if (cached_has_bits & 0x0000003cu) {
    ::memset(&request_id_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&response_id_) -
        reinterpret_cast<char*>(&request_id_)) + sizeof(response_id_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RSResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // required .robosense.acviewer_msgs.RS_CMD_TYPE cmd_type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::robosense::acviewer_msgs::RS_CMD_TYPE_IsValid(val))) {
            _internal_set_cmd_type(static_cast<::robosense::acviewer_msgs::RS_CMD_TYPE>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(1, val, mutable_unknown_fields());
          }
        } else goto handle_unusual;
        continue;
      // required uint64 request_Id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          _Internal::set_has_request_id(&has_bits);
          request_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // required uint64 response_Id = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          _Internal::set_has_response_id(&has_bits);
          response_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // required uint32 response_code = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          _Internal::set_has_response_code(&has_bits);
          response_code_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string response_info = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          auto str = _internal_mutable_response_info();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.acviewer_msgs.RSResponse.response_info");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .robosense.acviewer_msgs.RSResponseData response_data = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_response_data(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* RSResponse::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.acviewer_msgs.RSResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required .robosense.acviewer_msgs.RS_CMD_TYPE cmd_type = 1;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_cmd_type(), target);
  }

  // required uint64 request_Id = 2;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(2, this->_internal_request_id(), target);
  }

  // required uint64 response_Id = 3;
  if (cached_has_bits & 0x00000020u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(3, this->_internal_response_id(), target);
  }

  // required uint32 response_code = 4;
  if (cached_has_bits & 0x00000010u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_response_code(), target);
  }

  // optional string response_info = 5;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_response_info().data(), static_cast<int>(this->_internal_response_info().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.acviewer_msgs.RSResponse.response_info");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_response_info(), target);
  }

  // optional .robosense.acviewer_msgs.RSResponseData response_data = 6;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::response_data(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.acviewer_msgs.RSResponse)
  return target;
}

size_t RSResponse::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:robosense.acviewer_msgs.RSResponse)
  size_t total_size = 0;

  if (_internal_has_request_id()) {
    // required uint64 request_Id = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_request_id());
  }

  if (_internal_has_cmd_type()) {
    // required .robosense.acviewer_msgs.RS_CMD_TYPE cmd_type = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_cmd_type());
  }

  if (_internal_has_response_code()) {
    // required uint32 response_code = 4;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_response_code());
  }

  if (_internal_has_response_id()) {
    // required uint64 response_Id = 3;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_response_id());
  }

  return total_size;
}
size_t RSResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.acviewer_msgs.RSResponse)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x0000003c) ^ 0x0000003c) == 0) {  // All required fields are present.
    // required uint64 request_Id = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_request_id());

    // required .robosense.acviewer_msgs.RS_CMD_TYPE cmd_type = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_cmd_type());

    // required uint32 response_code = 4;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_response_code());

    // required uint64 response_Id = 3;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_response_id());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional string response_info = 5;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_response_info());
    }

    // optional .robosense.acviewer_msgs.RSResponseData response_data = 6;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *response_data_);
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RSResponse::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.acviewer_msgs.RSResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const RSResponse* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RSResponse>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.acviewer_msgs.RSResponse)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.acviewer_msgs.RSResponse)
    MergeFrom(*source);
  }
}

void RSResponse::MergeFrom(const RSResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.acviewer_msgs.RSResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000003fu) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_response_info(from._internal_response_info());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_mutable_response_data()->::robosense::acviewer_msgs::RSResponseData::MergeFrom(from._internal_response_data());
    }
    if (cached_has_bits & 0x00000004u) {
      request_id_ = from.request_id_;
    }
    if (cached_has_bits & 0x00000008u) {
      cmd_type_ = from.cmd_type_;
    }
    if (cached_has_bits & 0x00000010u) {
      response_code_ = from.response_code_;
    }
    if (cached_has_bits & 0x00000020u) {
      response_id_ = from.response_id_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void RSResponse::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.acviewer_msgs.RSResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RSResponse::CopyFrom(const RSResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.acviewer_msgs.RSResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RSResponse::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  return true;
}

void RSResponse::InternalSwap(RSResponse* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  response_info_.Swap(&other->response_info_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RSResponse, response_id_)
      + sizeof(RSResponse::response_id_)
      - PROTOBUF_FIELD_OFFSET(RSResponse, response_data_)>(
          reinterpret_cast<char*>(&response_data_),
          reinterpret_cast<char*>(&other->response_data_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RSResponse::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class RSDeviceInfo::_Internal {
 public:
  using HasBits = decltype(std::declval<RSDeviceInfo>()._has_bits_);
  static void set_has_uuid(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_event_type(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

RSDeviceInfo::RSDeviceInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.acviewer_msgs.RSDeviceInfo)
}
RSDeviceInfo::RSDeviceInfo(const RSDeviceInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_uuid()) {
    uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_uuid(), 
      GetArena());
  }
  event_type_ = from.event_type_;
  // @@protoc_insertion_point(copy_constructor:robosense.acviewer_msgs.RSDeviceInfo)
}

void RSDeviceInfo::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_RSDeviceInfo_acviewer_2eproto.base);
  uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  event_type_ = 0u;
}

RSDeviceInfo::~RSDeviceInfo() {
  // @@protoc_insertion_point(destructor:robosense.acviewer_msgs.RSDeviceInfo)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void RSDeviceInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  uuid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void RSDeviceInfo::ArenaDtor(void* object) {
  RSDeviceInfo* _this = reinterpret_cast< RSDeviceInfo* >(object);
  (void)_this;
}
void RSDeviceInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RSDeviceInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RSDeviceInfo& RSDeviceInfo::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RSDeviceInfo_acviewer_2eproto.base);
  return *internal_default_instance();
}


void RSDeviceInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.acviewer_msgs.RSDeviceInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    uuid_.ClearNonDefaultToEmpty();
  }
  event_type_ = 0u;
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RSDeviceInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional string uuid = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_uuid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.acviewer_msgs.RSDeviceInfo.uuid");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 event_type = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          _Internal::set_has_event_type(&has_bits);
          event_type_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* RSDeviceInfo::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.acviewer_msgs.RSDeviceInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional string uuid = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_uuid().data(), static_cast<int>(this->_internal_uuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.acviewer_msgs.RSDeviceInfo.uuid");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_uuid(), target);
  }

  // optional uint32 event_type = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_event_type(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.acviewer_msgs.RSDeviceInfo)
  return target;
}

size_t RSDeviceInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.acviewer_msgs.RSDeviceInfo)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional string uuid = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_uuid());
    }

    // optional uint32 event_type = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_event_type());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RSDeviceInfo::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.acviewer_msgs.RSDeviceInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const RSDeviceInfo* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RSDeviceInfo>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.acviewer_msgs.RSDeviceInfo)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.acviewer_msgs.RSDeviceInfo)
    MergeFrom(*source);
  }
}

void RSDeviceInfo::MergeFrom(const RSDeviceInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.acviewer_msgs.RSDeviceInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_uuid(from._internal_uuid());
    }
    if (cached_has_bits & 0x00000002u) {
      event_type_ = from.event_type_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void RSDeviceInfo::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.acviewer_msgs.RSDeviceInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RSDeviceInfo::CopyFrom(const RSDeviceInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.acviewer_msgs.RSDeviceInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RSDeviceInfo::IsInitialized() const {
  return true;
}

void RSDeviceInfo::InternalSwap(RSDeviceInfo* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  uuid_.Swap(&other->uuid_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  swap(event_type_, other->event_type_);
}

::PROTOBUF_NAMESPACE_ID::Metadata RSDeviceInfo::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class RSWriterConfig::_Internal {
 public:
};

RSWriterConfig::RSWriterConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  topic_config_list_(arena),
  file_format_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.acviewer_msgs.RSWriterConfig)
}
RSWriterConfig::RSWriterConfig(const RSWriterConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      topic_config_list_(from.topic_config_list_),
      file_format_(from.file_format_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:robosense.acviewer_msgs.RSWriterConfig)
}

void RSWriterConfig::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_RSWriterConfig_acviewer_2eproto.base);
}

RSWriterConfig::~RSWriterConfig() {
  // @@protoc_insertion_point(destructor:robosense.acviewer_msgs.RSWriterConfig)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void RSWriterConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void RSWriterConfig::ArenaDtor(void* object) {
  RSWriterConfig* _this = reinterpret_cast< RSWriterConfig* >(object);
  (void)_this;
}
void RSWriterConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RSWriterConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RSWriterConfig& RSWriterConfig::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RSWriterConfig_acviewer_2eproto.base);
  return *internal_default_instance();
}


void RSWriterConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.acviewer_msgs.RSWriterConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  topic_config_list_.Clear();
  file_format_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RSWriterConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated .robosense.acviewer_msgs.RSTopicConfig topic_config_list = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_topic_config_list(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else goto handle_unusual;
        continue;
      // repeated string file_format = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_file_format();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            #ifndef NDEBUG
            ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.acviewer_msgs.RSWriterConfig.file_format");
            #endif  // !NDEBUG
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* RSWriterConfig::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.acviewer_msgs.RSWriterConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .robosense.acviewer_msgs.RSTopicConfig topic_config_list = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_topic_config_list_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_topic_config_list(i), target, stream);
  }

  // repeated string file_format = 2;
  for (int i = 0, n = this->_internal_file_format_size(); i < n; i++) {
    const auto& s = this->_internal_file_format(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.acviewer_msgs.RSWriterConfig.file_format");
    target = stream->WriteString(2, s, target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.acviewer_msgs.RSWriterConfig)
  return target;
}

size_t RSWriterConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.acviewer_msgs.RSWriterConfig)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .robosense.acviewer_msgs.RSTopicConfig topic_config_list = 1;
  total_size += 1UL * this->_internal_topic_config_list_size();
  for (const auto& msg : this->topic_config_list_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated string file_format = 2;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(file_format_.size());
  for (int i = 0, n = file_format_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      file_format_.Get(i));
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RSWriterConfig::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.acviewer_msgs.RSWriterConfig)
  GOOGLE_DCHECK_NE(&from, this);
  const RSWriterConfig* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RSWriterConfig>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.acviewer_msgs.RSWriterConfig)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.acviewer_msgs.RSWriterConfig)
    MergeFrom(*source);
  }
}

void RSWriterConfig::MergeFrom(const RSWriterConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.acviewer_msgs.RSWriterConfig)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  topic_config_list_.MergeFrom(from.topic_config_list_);
  file_format_.MergeFrom(from.file_format_);
}

void RSWriterConfig::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.acviewer_msgs.RSWriterConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RSWriterConfig::CopyFrom(const RSWriterConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.acviewer_msgs.RSWriterConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RSWriterConfig::IsInitialized() const {
  return true;
}

void RSWriterConfig::InternalSwap(RSWriterConfig* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  topic_config_list_.InternalSwap(&other->topic_config_list_);
  file_format_.InternalSwap(&other->file_format_);
}

::PROTOBUF_NAMESPACE_ID::Metadata RSWriterConfig::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class RSReaderConfig::_Internal {
 public:
  using HasBits = decltype(std::declval<RSReaderConfig>()._has_bits_);
  static void set_has_start_timestamp_ns(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_end_timestamp_ns(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

RSReaderConfig::RSReaderConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  file_topics_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.acviewer_msgs.RSReaderConfig)
}
RSReaderConfig::RSReaderConfig(const RSReaderConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_),
      file_topics_(from.file_topics_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&start_timestamp_ns_, &from.start_timestamp_ns_,
    static_cast<size_t>(reinterpret_cast<char*>(&end_timestamp_ns_) -
    reinterpret_cast<char*>(&start_timestamp_ns_)) + sizeof(end_timestamp_ns_));
  // @@protoc_insertion_point(copy_constructor:robosense.acviewer_msgs.RSReaderConfig)
}

void RSReaderConfig::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_RSReaderConfig_acviewer_2eproto.base);
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&start_timestamp_ns_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&end_timestamp_ns_) -
      reinterpret_cast<char*>(&start_timestamp_ns_)) + sizeof(end_timestamp_ns_));
}

RSReaderConfig::~RSReaderConfig() {
  // @@protoc_insertion_point(destructor:robosense.acviewer_msgs.RSReaderConfig)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void RSReaderConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void RSReaderConfig::ArenaDtor(void* object) {
  RSReaderConfig* _this = reinterpret_cast< RSReaderConfig* >(object);
  (void)_this;
}
void RSReaderConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RSReaderConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RSReaderConfig& RSReaderConfig::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RSReaderConfig_acviewer_2eproto.base);
  return *internal_default_instance();
}


void RSReaderConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.acviewer_msgs.RSReaderConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  file_topics_.Clear();
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    ::memset(&start_timestamp_ns_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&end_timestamp_ns_) -
        reinterpret_cast<char*>(&start_timestamp_ns_)) + sizeof(end_timestamp_ns_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RSReaderConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated string file_topics = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_file_topics();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            #ifndef NDEBUG
            ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.acviewer_msgs.RSReaderConfig.file_topics");
            #endif  // !NDEBUG
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else goto handle_unusual;
        continue;
      // optional int64 start_timestamp_ns = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          _Internal::set_has_start_timestamp_ns(&has_bits);
          start_timestamp_ns_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional int64 end_timestamp_ns = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          _Internal::set_has_end_timestamp_ns(&has_bits);
          end_timestamp_ns_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* RSReaderConfig::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.acviewer_msgs.RSReaderConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string file_topics = 1;
  for (int i = 0, n = this->_internal_file_topics_size(); i < n; i++) {
    const auto& s = this->_internal_file_topics(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.acviewer_msgs.RSReaderConfig.file_topics");
    target = stream->WriteString(1, s, target);
  }

  cached_has_bits = _has_bits_[0];
  // optional int64 start_timestamp_ns = 2;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(2, this->_internal_start_timestamp_ns(), target);
  }

  // optional int64 end_timestamp_ns = 3;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(3, this->_internal_end_timestamp_ns(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.acviewer_msgs.RSReaderConfig)
  return target;
}

size_t RSReaderConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.acviewer_msgs.RSReaderConfig)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string file_topics = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(file_topics_.size());
  for (int i = 0, n = file_topics_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      file_topics_.Get(i));
  }

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional int64 start_timestamp_ns = 2;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64Size(
          this->_internal_start_timestamp_ns());
    }

    // optional int64 end_timestamp_ns = 3;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64Size(
          this->_internal_end_timestamp_ns());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RSReaderConfig::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.acviewer_msgs.RSReaderConfig)
  GOOGLE_DCHECK_NE(&from, this);
  const RSReaderConfig* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RSReaderConfig>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.acviewer_msgs.RSReaderConfig)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.acviewer_msgs.RSReaderConfig)
    MergeFrom(*source);
  }
}

void RSReaderConfig::MergeFrom(const RSReaderConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.acviewer_msgs.RSReaderConfig)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  file_topics_.MergeFrom(from.file_topics_);
  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      start_timestamp_ns_ = from.start_timestamp_ns_;
    }
    if (cached_has_bits & 0x00000002u) {
      end_timestamp_ns_ = from.end_timestamp_ns_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void RSReaderConfig::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.acviewer_msgs.RSReaderConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RSReaderConfig::CopyFrom(const RSReaderConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.acviewer_msgs.RSReaderConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RSReaderConfig::IsInitialized() const {
  return true;
}

void RSReaderConfig::InternalSwap(RSReaderConfig* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  file_topics_.InternalSwap(&other->file_topics_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RSReaderConfig, end_timestamp_ns_)
      + sizeof(RSReaderConfig::end_timestamp_ns_)
      - PROTOBUF_FIELD_OFFSET(RSReaderConfig, start_timestamp_ns_)>(
          reinterpret_cast<char*>(&start_timestamp_ns_),
          reinterpret_cast<char*>(&other->start_timestamp_ns_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RSReaderConfig::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class RSTopicConfig::_Internal {
 public:
  using HasBits = decltype(std::declval<RSTopicConfig>()._has_bits_);
  static void set_has_topic(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_type(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

RSTopicConfig::RSTopicConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  process_types_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.acviewer_msgs.RSTopicConfig)
}
RSTopicConfig::RSTopicConfig(const RSTopicConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_),
      process_types_(from.process_types_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  topic_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_topic()) {
    topic_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_topic(), 
      GetArena());
  }
  type_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_type()) {
    type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_type(), 
      GetArena());
  }
  // @@protoc_insertion_point(copy_constructor:robosense.acviewer_msgs.RSTopicConfig)
}

void RSTopicConfig::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_RSTopicConfig_acviewer_2eproto.base);
  topic_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  type_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

RSTopicConfig::~RSTopicConfig() {
  // @@protoc_insertion_point(destructor:robosense.acviewer_msgs.RSTopicConfig)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void RSTopicConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  topic_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  type_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void RSTopicConfig::ArenaDtor(void* object) {
  RSTopicConfig* _this = reinterpret_cast< RSTopicConfig* >(object);
  (void)_this;
}
void RSTopicConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RSTopicConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RSTopicConfig& RSTopicConfig::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RSTopicConfig_acviewer_2eproto.base);
  return *internal_default_instance();
}


void RSTopicConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.acviewer_msgs.RSTopicConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  process_types_.Clear();
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      topic_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      type_.ClearNonDefaultToEmpty();
    }
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RSTopicConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional string topic = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_topic();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.acviewer_msgs.RSTopicConfig.topic");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string type = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_type();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.acviewer_msgs.RSTopicConfig.type");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated string process_types = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_process_types();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            #ifndef NDEBUG
            ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.acviewer_msgs.RSTopicConfig.process_types");
            #endif  // !NDEBUG
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* RSTopicConfig::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.acviewer_msgs.RSTopicConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional string topic = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_topic().data(), static_cast<int>(this->_internal_topic().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.acviewer_msgs.RSTopicConfig.topic");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_topic(), target);
  }

  // optional string type = 2;
  if (cached_has_bits & 0x00000002u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_type().data(), static_cast<int>(this->_internal_type().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.acviewer_msgs.RSTopicConfig.type");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_type(), target);
  }

  // repeated string process_types = 3;
  for (int i = 0, n = this->_internal_process_types_size(); i < n; i++) {
    const auto& s = this->_internal_process_types(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.acviewer_msgs.RSTopicConfig.process_types");
    target = stream->WriteString(3, s, target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.acviewer_msgs.RSTopicConfig)
  return target;
}

size_t RSTopicConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.acviewer_msgs.RSTopicConfig)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string process_types = 3;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(process_types_.size());
  for (int i = 0, n = process_types_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      process_types_.Get(i));
  }

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional string topic = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_topic());
    }

    // optional string type = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_type());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RSTopicConfig::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.acviewer_msgs.RSTopicConfig)
  GOOGLE_DCHECK_NE(&from, this);
  const RSTopicConfig* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RSTopicConfig>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.acviewer_msgs.RSTopicConfig)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.acviewer_msgs.RSTopicConfig)
    MergeFrom(*source);
  }
}

void RSTopicConfig::MergeFrom(const RSTopicConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.acviewer_msgs.RSTopicConfig)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  process_types_.MergeFrom(from.process_types_);
  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_topic(from._internal_topic());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_set_type(from._internal_type());
    }
  }
}

void RSTopicConfig::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.acviewer_msgs.RSTopicConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RSTopicConfig::CopyFrom(const RSTopicConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.acviewer_msgs.RSTopicConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RSTopicConfig::IsInitialized() const {
  return true;
}

void RSTopicConfig::InternalSwap(RSTopicConfig* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  process_types_.InternalSwap(&other->process_types_);
  topic_.Swap(&other->topic_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  type_.Swap(&other->type_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}

::PROTOBUF_NAMESPACE_ID::Metadata RSTopicConfig::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class RSReaderProgress::_Internal {
 public:
  using HasBits = decltype(std::declval<RSReaderProgress>()._has_bits_);
  static void set_has_total_frame_count(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_is_playing(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_current_frame_index(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_current_timestamp(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
};

RSReaderProgress::RSReaderProgress(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.acviewer_msgs.RSReaderProgress)
}
RSReaderProgress::RSReaderProgress(const RSReaderProgress& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&total_frame_count_, &from.total_frame_count_,
    static_cast<size_t>(reinterpret_cast<char*>(&current_frame_index_) -
    reinterpret_cast<char*>(&total_frame_count_)) + sizeof(current_frame_index_));
  // @@protoc_insertion_point(copy_constructor:robosense.acviewer_msgs.RSReaderProgress)
}

void RSReaderProgress::SharedCtor() {
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&total_frame_count_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&current_frame_index_) -
      reinterpret_cast<char*>(&total_frame_count_)) + sizeof(current_frame_index_));
}

RSReaderProgress::~RSReaderProgress() {
  // @@protoc_insertion_point(destructor:robosense.acviewer_msgs.RSReaderProgress)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void RSReaderProgress::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void RSReaderProgress::ArenaDtor(void* object) {
  RSReaderProgress* _this = reinterpret_cast< RSReaderProgress* >(object);
  (void)_this;
}
void RSReaderProgress::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RSReaderProgress::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RSReaderProgress& RSReaderProgress::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RSReaderProgress_acviewer_2eproto.base);
  return *internal_default_instance();
}


void RSReaderProgress::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.acviewer_msgs.RSReaderProgress)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    ::memset(&total_frame_count_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&current_frame_index_) -
        reinterpret_cast<char*>(&total_frame_count_)) + sizeof(current_frame_index_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RSReaderProgress::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional uint32 total_frame_count = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          _Internal::set_has_total_frame_count(&has_bits);
          total_frame_count_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional bool is_playing = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          _Internal::set_has_is_playing(&has_bits);
          is_playing_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional int32 current_frame_index = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          _Internal::set_has_current_frame_index(&has_bits);
          current_frame_index_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint64 current_timestamp = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          _Internal::set_has_current_timestamp(&has_bits);
          current_timestamp_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* RSReaderProgress::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.acviewer_msgs.RSReaderProgress)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional uint32 total_frame_count = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_total_frame_count(), target);
  }

  // optional bool is_playing = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_is_playing(), target);
  }

  // optional int32 current_frame_index = 3;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_current_frame_index(), target);
  }

  // optional uint64 current_timestamp = 4;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(4, this->_internal_current_timestamp(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.acviewer_msgs.RSReaderProgress)
  return target;
}

size_t RSReaderProgress::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.acviewer_msgs.RSReaderProgress)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    // optional uint32 total_frame_count = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_total_frame_count());
    }

    // optional bool is_playing = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 + 1;
    }

    // optional uint64 current_timestamp = 4;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
          this->_internal_current_timestamp());
    }

    // optional int32 current_frame_index = 3;
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
          this->_internal_current_frame_index());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RSReaderProgress::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.acviewer_msgs.RSReaderProgress)
  GOOGLE_DCHECK_NE(&from, this);
  const RSReaderProgress* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RSReaderProgress>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.acviewer_msgs.RSReaderProgress)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.acviewer_msgs.RSReaderProgress)
    MergeFrom(*source);
  }
}

void RSReaderProgress::MergeFrom(const RSReaderProgress& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.acviewer_msgs.RSReaderProgress)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    if (cached_has_bits & 0x00000001u) {
      total_frame_count_ = from.total_frame_count_;
    }
    if (cached_has_bits & 0x00000002u) {
      is_playing_ = from.is_playing_;
    }
    if (cached_has_bits & 0x00000004u) {
      current_timestamp_ = from.current_timestamp_;
    }
    if (cached_has_bits & 0x00000008u) {
      current_frame_index_ = from.current_frame_index_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void RSReaderProgress::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.acviewer_msgs.RSReaderProgress)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RSReaderProgress::CopyFrom(const RSReaderProgress& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.acviewer_msgs.RSReaderProgress)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RSReaderProgress::IsInitialized() const {
  return true;
}

void RSReaderProgress::InternalSwap(RSReaderProgress* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RSReaderProgress, current_frame_index_)
      + sizeof(RSReaderProgress::current_frame_index_)
      - PROTOBUF_FIELD_OFFSET(RSReaderProgress, total_frame_count_)>(
          reinterpret_cast<char*>(&total_frame_count_),
          reinterpret_cast<char*>(&other->total_frame_count_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RSReaderProgress::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class RSResponseData::_Internal {
 public:
  using HasBits = decltype(std::declval<RSResponseData>()._has_bits_);
  static const ::robosense::acviewer_msgs::RSWriterConfig& writer_config(const RSResponseData* msg);
  static void set_has_writer_config(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static const ::robosense::acviewer_msgs::RSReaderConfig& reader_config(const RSResponseData* msg);
  static void set_has_reader_config(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_loading_progress(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static const ::robosense::acviewer_msgs::RSReaderProgress& reader_progress(const RSResponseData* msg);
  static void set_has_reader_progress(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
};

const ::robosense::acviewer_msgs::RSWriterConfig&
RSResponseData::_Internal::writer_config(const RSResponseData* msg) {
  return *msg->writer_config_;
}
const ::robosense::acviewer_msgs::RSReaderConfig&
RSResponseData::_Internal::reader_config(const RSResponseData* msg) {
  return *msg->reader_config_;
}
const ::robosense::acviewer_msgs::RSReaderProgress&
RSResponseData::_Internal::reader_progress(const RSResponseData* msg) {
  return *msg->reader_progress_;
}
RSResponseData::RSResponseData(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  device_info_list_(arena),
  file_list_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.acviewer_msgs.RSResponseData)
}
RSResponseData::RSResponseData(const RSResponseData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_),
      device_info_list_(from.device_info_list_),
      file_list_(from.file_list_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_writer_config()) {
    writer_config_ = new ::robosense::acviewer_msgs::RSWriterConfig(*from.writer_config_);
  } else {
    writer_config_ = nullptr;
  }
  if (from._internal_has_reader_config()) {
    reader_config_ = new ::robosense::acviewer_msgs::RSReaderConfig(*from.reader_config_);
  } else {
    reader_config_ = nullptr;
  }
  if (from._internal_has_reader_progress()) {
    reader_progress_ = new ::robosense::acviewer_msgs::RSReaderProgress(*from.reader_progress_);
  } else {
    reader_progress_ = nullptr;
  }
  loading_progress_ = from.loading_progress_;
  // @@protoc_insertion_point(copy_constructor:robosense.acviewer_msgs.RSResponseData)
}

void RSResponseData::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_RSResponseData_acviewer_2eproto.base);
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&writer_config_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&loading_progress_) -
      reinterpret_cast<char*>(&writer_config_)) + sizeof(loading_progress_));
}

RSResponseData::~RSResponseData() {
  // @@protoc_insertion_point(destructor:robosense.acviewer_msgs.RSResponseData)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void RSResponseData::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  if (this != internal_default_instance()) delete writer_config_;
  if (this != internal_default_instance()) delete reader_config_;
  if (this != internal_default_instance()) delete reader_progress_;
}

void RSResponseData::ArenaDtor(void* object) {
  RSResponseData* _this = reinterpret_cast< RSResponseData* >(object);
  (void)_this;
}
void RSResponseData::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RSResponseData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RSResponseData& RSResponseData::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RSResponseData_acviewer_2eproto.base);
  return *internal_default_instance();
}


void RSResponseData::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.acviewer_msgs.RSResponseData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  device_info_list_.Clear();
  file_list_.Clear();
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    if (cached_has_bits & 0x00000001u) {
      GOOGLE_DCHECK(writer_config_ != nullptr);
      writer_config_->Clear();
    }
    if (cached_has_bits & 0x00000002u) {
      GOOGLE_DCHECK(reader_config_ != nullptr);
      reader_config_->Clear();
    }
    if (cached_has_bits & 0x00000004u) {
      GOOGLE_DCHECK(reader_progress_ != nullptr);
      reader_progress_->Clear();
    }
  }
  loading_progress_ = 0u;
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RSResponseData::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated .robosense.acviewer_msgs.RSDeviceInfo device_info_list = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_device_info_list(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else goto handle_unusual;
        continue;
      // optional .robosense.acviewer_msgs.RSWriterConfig writer_config = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_writer_config(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated string file_list = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_file_list();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            #ifndef NDEBUG
            ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.acviewer_msgs.RSResponseData.file_list");
            #endif  // !NDEBUG
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else goto handle_unusual;
        continue;
      // optional .robosense.acviewer_msgs.RSReaderConfig reader_config = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_reader_config(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 loading_progress = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          _Internal::set_has_loading_progress(&has_bits);
          loading_progress_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .robosense.acviewer_msgs.RSReaderProgress reader_progress = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_reader_progress(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* RSResponseData::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.acviewer_msgs.RSResponseData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .robosense.acviewer_msgs.RSDeviceInfo device_info_list = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_device_info_list_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_device_info_list(i), target, stream);
  }

  cached_has_bits = _has_bits_[0];
  // optional .robosense.acviewer_msgs.RSWriterConfig writer_config = 2;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::writer_config(this), target, stream);
  }

  // repeated string file_list = 3;
  for (int i = 0, n = this->_internal_file_list_size(); i < n; i++) {
    const auto& s = this->_internal_file_list(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.acviewer_msgs.RSResponseData.file_list");
    target = stream->WriteString(3, s, target);
  }

  // optional .robosense.acviewer_msgs.RSReaderConfig reader_config = 4;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::reader_config(this), target, stream);
  }

  // optional uint32 loading_progress = 5;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_loading_progress(), target);
  }

  // optional .robosense.acviewer_msgs.RSReaderProgress reader_progress = 6;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::reader_progress(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.acviewer_msgs.RSResponseData)
  return target;
}

size_t RSResponseData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.acviewer_msgs.RSResponseData)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .robosense.acviewer_msgs.RSDeviceInfo device_info_list = 1;
  total_size += 1UL * this->_internal_device_info_list_size();
  for (const auto& msg : this->device_info_list_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated string file_list = 3;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(file_list_.size());
  for (int i = 0, n = file_list_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      file_list_.Get(i));
  }

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    // optional .robosense.acviewer_msgs.RSWriterConfig writer_config = 2;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *writer_config_);
    }

    // optional .robosense.acviewer_msgs.RSReaderConfig reader_config = 4;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *reader_config_);
    }

    // optional .robosense.acviewer_msgs.RSReaderProgress reader_progress = 6;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *reader_progress_);
    }

    // optional uint32 loading_progress = 5;
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_loading_progress());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RSResponseData::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.acviewer_msgs.RSResponseData)
  GOOGLE_DCHECK_NE(&from, this);
  const RSResponseData* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RSResponseData>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.acviewer_msgs.RSResponseData)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.acviewer_msgs.RSResponseData)
    MergeFrom(*source);
  }
}

void RSResponseData::MergeFrom(const RSResponseData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.acviewer_msgs.RSResponseData)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  device_info_list_.MergeFrom(from.device_info_list_);
  file_list_.MergeFrom(from.file_list_);
  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    if (cached_has_bits & 0x00000001u) {
      _internal_mutable_writer_config()->::robosense::acviewer_msgs::RSWriterConfig::MergeFrom(from._internal_writer_config());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_mutable_reader_config()->::robosense::acviewer_msgs::RSReaderConfig::MergeFrom(from._internal_reader_config());
    }
    if (cached_has_bits & 0x00000004u) {
      _internal_mutable_reader_progress()->::robosense::acviewer_msgs::RSReaderProgress::MergeFrom(from._internal_reader_progress());
    }
    if (cached_has_bits & 0x00000008u) {
      loading_progress_ = from.loading_progress_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void RSResponseData::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.acviewer_msgs.RSResponseData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RSResponseData::CopyFrom(const RSResponseData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.acviewer_msgs.RSResponseData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RSResponseData::IsInitialized() const {
  return true;
}

void RSResponseData::InternalSwap(RSResponseData* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  device_info_list_.InternalSwap(&other->device_info_list_);
  file_list_.InternalSwap(&other->file_list_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RSResponseData, loading_progress_)
      + sizeof(RSResponseData::loading_progress_)
      - PROTOBUF_FIELD_OFFSET(RSResponseData, writer_config_)>(
          reinterpret_cast<char*>(&writer_config_),
          reinterpret_cast<char*>(&other->writer_config_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RSResponseData::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class UdpControlConfig::_Internal {
 public:
  using HasBits = decltype(std::declval<UdpControlConfig>()._has_bits_);
  static void set_has_udp_control_type(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_udp_total_control_time_ms(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_udp_total_control_single_time_ms(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_udp_data_control_size(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static void set_has_udp_data_control_time_ms(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

UdpControlConfig::UdpControlConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.acviewer_msgs.UdpControlConfig)
}
UdpControlConfig::UdpControlConfig(const UdpControlConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&udp_data_control_time_ms_, &from.udp_data_control_time_ms_,
    static_cast<size_t>(reinterpret_cast<char*>(&udp_data_control_size_) -
    reinterpret_cast<char*>(&udp_data_control_time_ms_)) + sizeof(udp_data_control_size_));
  // @@protoc_insertion_point(copy_constructor:robosense.acviewer_msgs.UdpControlConfig)
}

void UdpControlConfig::SharedCtor() {
  udp_data_control_time_ms_ = 2u;
  udp_control_type_ = 2;
  udp_total_control_time_ms_ = 60u;
  udp_total_control_single_time_ms_ = 2u;
  udp_data_control_size_ = 262144u;
}

UdpControlConfig::~UdpControlConfig() {
  // @@protoc_insertion_point(destructor:robosense.acviewer_msgs.UdpControlConfig)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void UdpControlConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void UdpControlConfig::ArenaDtor(void* object) {
  UdpControlConfig* _this = reinterpret_cast< UdpControlConfig* >(object);
  (void)_this;
}
void UdpControlConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UdpControlConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const UdpControlConfig& UdpControlConfig::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_UdpControlConfig_acviewer_2eproto.base);
  return *internal_default_instance();
}


void UdpControlConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.acviewer_msgs.UdpControlConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000001fu) {
    udp_data_control_time_ms_ = 2u;
    udp_control_type_ = 2;
    udp_total_control_time_ms_ = 60u;
    udp_total_control_single_time_ms_ = 2u;
    udp_data_control_size_ = 262144u;
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* UdpControlConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional .robosense.acviewer_msgs.RS_UDP_CONTROL_MODE_TYPE udp_control_type = 1 [default = RS_UDP_CONTROL_TOTAL_CONTROL_TIME];
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::robosense::acviewer_msgs::RS_UDP_CONTROL_MODE_TYPE_IsValid(val))) {
            _internal_set_udp_control_type(static_cast<::robosense::acviewer_msgs::RS_UDP_CONTROL_MODE_TYPE>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(1, val, mutable_unknown_fields());
          }
        } else goto handle_unusual;
        continue;
      // optional uint32 udp_total_control_time_ms = 2 [default = 60];
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          _Internal::set_has_udp_total_control_time_ms(&has_bits);
          udp_total_control_time_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 udp_total_control_single_time_ms = 3 [default = 2];
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          _Internal::set_has_udp_total_control_single_time_ms(&has_bits);
          udp_total_control_single_time_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 udp_data_control_size = 4 [default = 262144];
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          _Internal::set_has_udp_data_control_size(&has_bits);
          udp_data_control_size_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 udp_data_control_time_ms = 5 [default = 2];
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          _Internal::set_has_udp_data_control_time_ms(&has_bits);
          udp_data_control_time_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* UdpControlConfig::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.acviewer_msgs.UdpControlConfig)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional .robosense.acviewer_msgs.RS_UDP_CONTROL_MODE_TYPE udp_control_type = 1 [default = RS_UDP_CONTROL_TOTAL_CONTROL_TIME];
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_udp_control_type(), target);
  }

  // optional uint32 udp_total_control_time_ms = 2 [default = 60];
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_udp_total_control_time_ms(), target);
  }

  // optional uint32 udp_total_control_single_time_ms = 3 [default = 2];
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_udp_total_control_single_time_ms(), target);
  }

  // optional uint32 udp_data_control_size = 4 [default = 262144];
  if (cached_has_bits & 0x00000010u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_udp_data_control_size(), target);
  }

  // optional uint32 udp_data_control_time_ms = 5 [default = 2];
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_udp_data_control_time_ms(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.acviewer_msgs.UdpControlConfig)
  return target;
}

size_t UdpControlConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.acviewer_msgs.UdpControlConfig)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000001fu) {
    // optional uint32 udp_data_control_time_ms = 5 [default = 2];
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_udp_data_control_time_ms());
    }

    // optional .robosense.acviewer_msgs.RS_UDP_CONTROL_MODE_TYPE udp_control_type = 1 [default = RS_UDP_CONTROL_TOTAL_CONTROL_TIME];
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_udp_control_type());
    }

    // optional uint32 udp_total_control_time_ms = 2 [default = 60];
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_udp_total_control_time_ms());
    }

    // optional uint32 udp_total_control_single_time_ms = 3 [default = 2];
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_udp_total_control_single_time_ms());
    }

    // optional uint32 udp_data_control_size = 4 [default = 262144];
    if (cached_has_bits & 0x00000010u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_udp_data_control_size());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void UdpControlConfig::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.acviewer_msgs.UdpControlConfig)
  GOOGLE_DCHECK_NE(&from, this);
  const UdpControlConfig* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<UdpControlConfig>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.acviewer_msgs.UdpControlConfig)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.acviewer_msgs.UdpControlConfig)
    MergeFrom(*source);
  }
}

void UdpControlConfig::MergeFrom(const UdpControlConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.acviewer_msgs.UdpControlConfig)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000001fu) {
    if (cached_has_bits & 0x00000001u) {
      udp_data_control_time_ms_ = from.udp_data_control_time_ms_;
    }
    if (cached_has_bits & 0x00000002u) {
      udp_control_type_ = from.udp_control_type_;
    }
    if (cached_has_bits & 0x00000004u) {
      udp_total_control_time_ms_ = from.udp_total_control_time_ms_;
    }
    if (cached_has_bits & 0x00000008u) {
      udp_total_control_single_time_ms_ = from.udp_total_control_single_time_ms_;
    }
    if (cached_has_bits & 0x00000010u) {
      udp_data_control_size_ = from.udp_data_control_size_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void UdpControlConfig::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.acviewer_msgs.UdpControlConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void UdpControlConfig::CopyFrom(const UdpControlConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.acviewer_msgs.UdpControlConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UdpControlConfig::IsInitialized() const {
  return true;
}

void UdpControlConfig::InternalSwap(UdpControlConfig* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  swap(udp_data_control_time_ms_, other->udp_data_control_time_ms_);
  swap(udp_control_type_, other->udp_control_type_);
  swap(udp_total_control_time_ms_, other->udp_total_control_time_ms_);
  swap(udp_total_control_single_time_ms_, other->udp_total_control_single_time_ms_);
  swap(udp_data_control_size_, other->udp_data_control_size_);
}

::PROTOBUF_NAMESPACE_ID::Metadata UdpControlConfig::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class RenderSwitch::_Internal {
 public:
  using HasBits = decltype(std::declval<RenderSwitch>()._has_bits_);
  static void set_has_enable_render(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_render_comm_type(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_udp_p2p_carapp_ip(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static const ::robosense::acviewer_msgs::UdpControlConfig& udp_control_config(const RenderSwitch* msg);
  static void set_has_udp_control_config(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

const ::robosense::acviewer_msgs::UdpControlConfig&
RenderSwitch::_Internal::udp_control_config(const RenderSwitch* msg) {
  return *msg->udp_control_config_;
}
RenderSwitch::RenderSwitch(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.acviewer_msgs.RenderSwitch)
}
RenderSwitch::RenderSwitch(const RenderSwitch& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  udp_p2p_carapp_ip_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_udp_p2p_carapp_ip()) {
    udp_p2p_carapp_ip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_udp_p2p_carapp_ip(), 
      GetArena());
  }
  if (from._internal_has_udp_control_config()) {
    udp_control_config_ = new ::robosense::acviewer_msgs::UdpControlConfig(*from.udp_control_config_);
  } else {
    udp_control_config_ = nullptr;
  }
  ::memcpy(&enable_render_, &from.enable_render_,
    static_cast<size_t>(reinterpret_cast<char*>(&render_comm_type_) -
    reinterpret_cast<char*>(&enable_render_)) + sizeof(render_comm_type_));
  // @@protoc_insertion_point(copy_constructor:robosense.acviewer_msgs.RenderSwitch)
}

void RenderSwitch::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_RenderSwitch_acviewer_2eproto.base);
  udp_p2p_carapp_ip_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  udp_control_config_ = nullptr;
  enable_render_ = true;
  render_comm_type_ = 1;
}

RenderSwitch::~RenderSwitch() {
  // @@protoc_insertion_point(destructor:robosense.acviewer_msgs.RenderSwitch)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void RenderSwitch::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  udp_p2p_carapp_ip_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete udp_control_config_;
}

void RenderSwitch::ArenaDtor(void* object) {
  RenderSwitch* _this = reinterpret_cast< RenderSwitch* >(object);
  (void)_this;
}
void RenderSwitch::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RenderSwitch::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RenderSwitch& RenderSwitch::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RenderSwitch_acviewer_2eproto.base);
  return *internal_default_instance();
}


void RenderSwitch::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.acviewer_msgs.RenderSwitch)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    if (cached_has_bits & 0x00000001u) {
      udp_p2p_carapp_ip_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      GOOGLE_DCHECK(udp_control_config_ != nullptr);
      udp_control_config_->Clear();
    }
    enable_render_ = true;
    render_comm_type_ = 1;
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RenderSwitch::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional bool enable_render = 1 [default = true];
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          _Internal::set_has_enable_render(&has_bits);
          enable_render_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .robosense.acviewer_msgs.RS_RENDER_COMMUNICATION_TYPE render_comm_type = 2 [default = RS_RENDER_COMM_WEBSOCKET];
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::robosense::acviewer_msgs::RS_RENDER_COMMUNICATION_TYPE_IsValid(val))) {
            _internal_set_render_comm_type(static_cast<::robosense::acviewer_msgs::RS_RENDER_COMMUNICATION_TYPE>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(2, val, mutable_unknown_fields());
          }
        } else goto handle_unusual;
        continue;
      // optional string udp_p2p_carapp_ip = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          auto str = _internal_mutable_udp_p2p_carapp_ip();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "robosense.acviewer_msgs.RenderSwitch.udp_p2p_carapp_ip");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .robosense.acviewer_msgs.UdpControlConfig udp_control_config = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_udp_control_config(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* RenderSwitch::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.acviewer_msgs.RenderSwitch)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional bool enable_render = 1 [default = true];
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_enable_render(), target);
  }

  // optional .robosense.acviewer_msgs.RS_RENDER_COMMUNICATION_TYPE render_comm_type = 2 [default = RS_RENDER_COMM_WEBSOCKET];
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_render_comm_type(), target);
  }

  // optional string udp_p2p_carapp_ip = 3;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_udp_p2p_carapp_ip().data(), static_cast<int>(this->_internal_udp_p2p_carapp_ip().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "robosense.acviewer_msgs.RenderSwitch.udp_p2p_carapp_ip");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_udp_p2p_carapp_ip(), target);
  }

  // optional .robosense.acviewer_msgs.UdpControlConfig udp_control_config = 4;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::udp_control_config(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.acviewer_msgs.RenderSwitch)
  return target;
}

size_t RenderSwitch::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.acviewer_msgs.RenderSwitch)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    // optional string udp_p2p_carapp_ip = 3;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_udp_p2p_carapp_ip());
    }

    // optional .robosense.acviewer_msgs.UdpControlConfig udp_control_config = 4;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *udp_control_config_);
    }

    // optional bool enable_render = 1 [default = true];
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 + 1;
    }

    // optional .robosense.acviewer_msgs.RS_RENDER_COMMUNICATION_TYPE render_comm_type = 2 [default = RS_RENDER_COMM_WEBSOCKET];
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_render_comm_type());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RenderSwitch::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.acviewer_msgs.RenderSwitch)
  GOOGLE_DCHECK_NE(&from, this);
  const RenderSwitch* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RenderSwitch>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.acviewer_msgs.RenderSwitch)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.acviewer_msgs.RenderSwitch)
    MergeFrom(*source);
  }
}

void RenderSwitch::MergeFrom(const RenderSwitch& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.acviewer_msgs.RenderSwitch)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_udp_p2p_carapp_ip(from._internal_udp_p2p_carapp_ip());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_mutable_udp_control_config()->::robosense::acviewer_msgs::UdpControlConfig::MergeFrom(from._internal_udp_control_config());
    }
    if (cached_has_bits & 0x00000004u) {
      enable_render_ = from.enable_render_;
    }
    if (cached_has_bits & 0x00000008u) {
      render_comm_type_ = from.render_comm_type_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void RenderSwitch::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.acviewer_msgs.RenderSwitch)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RenderSwitch::CopyFrom(const RenderSwitch& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.acviewer_msgs.RenderSwitch)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RenderSwitch::IsInitialized() const {
  return true;
}

void RenderSwitch::InternalSwap(RenderSwitch* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  udp_p2p_carapp_ip_.Swap(&other->udp_p2p_carapp_ip_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  swap(udp_control_config_, other->udp_control_config_);
  swap(enable_render_, other->enable_render_);
  swap(render_comm_type_, other->render_comm_type_);
}

::PROTOBUF_NAMESPACE_ID::Metadata RenderSwitch::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class RSPositon::_Internal {
 public:
  using HasBits = decltype(std::declval<RSPositon>()._has_bits_);
  static void set_has_x(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_y(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_z(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_timestamp(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
};

RSPositon::RSPositon(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.acviewer_msgs.RSPositon)
}
RSPositon::RSPositon(const RSPositon& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&x_, &from.x_,
    static_cast<size_t>(reinterpret_cast<char*>(&z_) -
    reinterpret_cast<char*>(&x_)) + sizeof(z_));
  // @@protoc_insertion_point(copy_constructor:robosense.acviewer_msgs.RSPositon)
}

void RSPositon::SharedCtor() {
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&x_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&z_) -
      reinterpret_cast<char*>(&x_)) + sizeof(z_));
}

RSPositon::~RSPositon() {
  // @@protoc_insertion_point(destructor:robosense.acviewer_msgs.RSPositon)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void RSPositon::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void RSPositon::ArenaDtor(void* object) {
  RSPositon* _this = reinterpret_cast< RSPositon* >(object);
  (void)_this;
}
void RSPositon::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RSPositon::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RSPositon& RSPositon::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RSPositon_acviewer_2eproto.base);
  return *internal_default_instance();
}


void RSPositon::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.acviewer_msgs.RSPositon)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    ::memset(&x_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&z_) -
        reinterpret_cast<char*>(&x_)) + sizeof(z_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RSPositon::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional float x = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 13)) {
          _Internal::set_has_x(&has_bits);
          x_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // optional float y = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 21)) {
          _Internal::set_has_y(&has_bits);
          y_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // optional float z = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 29)) {
          _Internal::set_has_z(&has_bits);
          z_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // optional uint64 timestamp = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          _Internal::set_has_timestamp(&has_bits);
          timestamp_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* RSPositon::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.acviewer_msgs.RSPositon)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional float x = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_x(), target);
  }

  // optional float y = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_y(), target);
  }

  // optional float z = 3;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_z(), target);
  }

  // optional uint64 timestamp = 4;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(4, this->_internal_timestamp(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.acviewer_msgs.RSPositon)
  return target;
}

size_t RSPositon::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.acviewer_msgs.RSPositon)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    // optional float x = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 + 4;
    }

    // optional float y = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 + 4;
    }

    // optional uint64 timestamp = 4;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
          this->_internal_timestamp());
    }

    // optional float z = 3;
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 + 4;
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RSPositon::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.acviewer_msgs.RSPositon)
  GOOGLE_DCHECK_NE(&from, this);
  const RSPositon* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RSPositon>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.acviewer_msgs.RSPositon)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.acviewer_msgs.RSPositon)
    MergeFrom(*source);
  }
}

void RSPositon::MergeFrom(const RSPositon& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.acviewer_msgs.RSPositon)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    if (cached_has_bits & 0x00000001u) {
      x_ = from.x_;
    }
    if (cached_has_bits & 0x00000002u) {
      y_ = from.y_;
    }
    if (cached_has_bits & 0x00000004u) {
      timestamp_ = from.timestamp_;
    }
    if (cached_has_bits & 0x00000008u) {
      z_ = from.z_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void RSPositon::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.acviewer_msgs.RSPositon)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RSPositon::CopyFrom(const RSPositon& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.acviewer_msgs.RSPositon)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RSPositon::IsInitialized() const {
  return true;
}

void RSPositon::InternalSwap(RSPositon* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RSPositon, z_)
      + sizeof(RSPositon::z_)
      - PROTOBUF_FIELD_OFFSET(RSPositon, x_)>(
          reinterpret_cast<char*>(&x_),
          reinterpret_cast<char*>(&other->x_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RSPositon::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class RSPointCloud::_Internal {
 public:
  using HasBits = decltype(std::declval<RSPointCloud>()._has_bits_);
  static void set_has_data(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_size(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

RSPointCloud::RSPointCloud(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.acviewer_msgs.RSPointCloud)
}
RSPointCloud::RSPointCloud(const RSPointCloud& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  data_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_data()) {
    data_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_data(), 
      GetArena());
  }
  size_ = from.size_;
  // @@protoc_insertion_point(copy_constructor:robosense.acviewer_msgs.RSPointCloud)
}

void RSPointCloud::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_RSPointCloud_acviewer_2eproto.base);
  data_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  size_ = 0u;
}

RSPointCloud::~RSPointCloud() {
  // @@protoc_insertion_point(destructor:robosense.acviewer_msgs.RSPointCloud)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void RSPointCloud::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  data_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void RSPointCloud::ArenaDtor(void* object) {
  RSPointCloud* _this = reinterpret_cast< RSPointCloud* >(object);
  (void)_this;
}
void RSPointCloud::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RSPointCloud::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RSPointCloud& RSPointCloud::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RSPointCloud_acviewer_2eproto.base);
  return *internal_default_instance();
}


void RSPointCloud::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.acviewer_msgs.RSPointCloud)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    data_.ClearNonDefaultToEmpty();
  }
  size_ = 0u;
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RSPointCloud::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional bytes data = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_data();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 size = 2 [default = 0];
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          _Internal::set_has_size(&has_bits);
          size_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* RSPointCloud::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.acviewer_msgs.RSPointCloud)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional bytes data = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->WriteBytesMaybeAliased(
        1, this->_internal_data(), target);
  }

  // optional uint32 size = 2 [default = 0];
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_size(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.acviewer_msgs.RSPointCloud)
  return target;
}

size_t RSPointCloud::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.acviewer_msgs.RSPointCloud)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional bytes data = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
          this->_internal_data());
    }

    // optional uint32 size = 2 [default = 0];
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_size());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RSPointCloud::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.acviewer_msgs.RSPointCloud)
  GOOGLE_DCHECK_NE(&from, this);
  const RSPointCloud* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RSPointCloud>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.acviewer_msgs.RSPointCloud)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.acviewer_msgs.RSPointCloud)
    MergeFrom(*source);
  }
}

void RSPointCloud::MergeFrom(const RSPointCloud& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.acviewer_msgs.RSPointCloud)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_data(from._internal_data());
    }
    if (cached_has_bits & 0x00000002u) {
      size_ = from.size_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void RSPointCloud::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.acviewer_msgs.RSPointCloud)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RSPointCloud::CopyFrom(const RSPointCloud& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.acviewer_msgs.RSPointCloud)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RSPointCloud::IsInitialized() const {
  return true;
}

void RSPointCloud::InternalSwap(RSPointCloud* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  data_.Swap(&other->data_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  swap(size_, other->size_);
}

::PROTOBUF_NAMESPACE_ID::Metadata RSPointCloud::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class RSTrangleFacet::_Internal {
 public:
  using HasBits = decltype(std::declval<RSTrangleFacet>()._has_bits_);
  static void set_has_data(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_size(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

RSTrangleFacet::RSTrangleFacet(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.acviewer_msgs.RSTrangleFacet)
}
RSTrangleFacet::RSTrangleFacet(const RSTrangleFacet& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  data_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_data()) {
    data_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_data(), 
      GetArena());
  }
  size_ = from.size_;
  // @@protoc_insertion_point(copy_constructor:robosense.acviewer_msgs.RSTrangleFacet)
}

void RSTrangleFacet::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_RSTrangleFacet_acviewer_2eproto.base);
  data_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  size_ = 0u;
}

RSTrangleFacet::~RSTrangleFacet() {
  // @@protoc_insertion_point(destructor:robosense.acviewer_msgs.RSTrangleFacet)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void RSTrangleFacet::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  data_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void RSTrangleFacet::ArenaDtor(void* object) {
  RSTrangleFacet* _this = reinterpret_cast< RSTrangleFacet* >(object);
  (void)_this;
}
void RSTrangleFacet::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RSTrangleFacet::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RSTrangleFacet& RSTrangleFacet::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RSTrangleFacet_acviewer_2eproto.base);
  return *internal_default_instance();
}


void RSTrangleFacet::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.acviewer_msgs.RSTrangleFacet)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    data_.ClearNonDefaultToEmpty();
  }
  size_ = 0u;
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RSTrangleFacet::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional bytes data = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_data();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 size = 2 [default = 0];
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          _Internal::set_has_size(&has_bits);
          size_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* RSTrangleFacet::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.acviewer_msgs.RSTrangleFacet)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional bytes data = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->WriteBytesMaybeAliased(
        1, this->_internal_data(), target);
  }

  // optional uint32 size = 2 [default = 0];
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_size(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.acviewer_msgs.RSTrangleFacet)
  return target;
}

size_t RSTrangleFacet::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.acviewer_msgs.RSTrangleFacet)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional bytes data = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
          this->_internal_data());
    }

    // optional uint32 size = 2 [default = 0];
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_size());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RSTrangleFacet::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.acviewer_msgs.RSTrangleFacet)
  GOOGLE_DCHECK_NE(&from, this);
  const RSTrangleFacet* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RSTrangleFacet>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.acviewer_msgs.RSTrangleFacet)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.acviewer_msgs.RSTrangleFacet)
    MergeFrom(*source);
  }
}

void RSTrangleFacet::MergeFrom(const RSTrangleFacet& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.acviewer_msgs.RSTrangleFacet)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_data(from._internal_data());
    }
    if (cached_has_bits & 0x00000002u) {
      size_ = from.size_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void RSTrangleFacet::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.acviewer_msgs.RSTrangleFacet)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RSTrangleFacet::CopyFrom(const RSTrangleFacet& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.acviewer_msgs.RSTrangleFacet)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RSTrangleFacet::IsInitialized() const {
  return true;
}

void RSTrangleFacet::InternalSwap(RSTrangleFacet* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  data_.Swap(&other->data_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  swap(size_, other->size_);
}

::PROTOBUF_NAMESPACE_ID::Metadata RSTrangleFacet::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class RSRgbJpeg::_Internal {
 public:
  using HasBits = decltype(std::declval<RSRgbJpeg>()._has_bits_);
  static void set_has_is_jpeg_compress(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_width(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_height(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static void set_has_data(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_timestamp(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
};

RSRgbJpeg::RSRgbJpeg(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.acviewer_msgs.RSRgbJpeg)
}
RSRgbJpeg::RSRgbJpeg(const RSRgbJpeg& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  data_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_data()) {
    data_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_data(), 
      GetArena());
  }
  ::memcpy(&is_jpeg_compress_, &from.is_jpeg_compress_,
    static_cast<size_t>(reinterpret_cast<char*>(&height_) -
    reinterpret_cast<char*>(&is_jpeg_compress_)) + sizeof(height_));
  // @@protoc_insertion_point(copy_constructor:robosense.acviewer_msgs.RSRgbJpeg)
}

void RSRgbJpeg::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_RSRgbJpeg_acviewer_2eproto.base);
  data_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&is_jpeg_compress_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&height_) -
      reinterpret_cast<char*>(&is_jpeg_compress_)) + sizeof(height_));
}

RSRgbJpeg::~RSRgbJpeg() {
  // @@protoc_insertion_point(destructor:robosense.acviewer_msgs.RSRgbJpeg)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void RSRgbJpeg::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  data_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void RSRgbJpeg::ArenaDtor(void* object) {
  RSRgbJpeg* _this = reinterpret_cast< RSRgbJpeg* >(object);
  (void)_this;
}
void RSRgbJpeg::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RSRgbJpeg::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RSRgbJpeg& RSRgbJpeg::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RSRgbJpeg_acviewer_2eproto.base);
  return *internal_default_instance();
}


void RSRgbJpeg::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.acviewer_msgs.RSRgbJpeg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    data_.ClearNonDefaultToEmpty();
  }
  if (cached_has_bits & 0x0000001eu) {
    ::memset(&is_jpeg_compress_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&height_) -
        reinterpret_cast<char*>(&is_jpeg_compress_)) + sizeof(height_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RSRgbJpeg::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional bool is_jpeg_compress = 1 [default = false];
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          _Internal::set_has_is_jpeg_compress(&has_bits);
          is_jpeg_compress_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 width = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          _Internal::set_has_width(&has_bits);
          width_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 height = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          _Internal::set_has_height(&has_bits);
          height_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional bytes data = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          auto str = _internal_mutable_data();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint64 timestamp = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          _Internal::set_has_timestamp(&has_bits);
          timestamp_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* RSRgbJpeg::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.acviewer_msgs.RSRgbJpeg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional bool is_jpeg_compress = 1 [default = false];
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_is_jpeg_compress(), target);
  }

  // optional uint32 width = 2;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_width(), target);
  }

  // optional uint32 height = 3;
  if (cached_has_bits & 0x00000010u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_height(), target);
  }

  // optional bytes data = 4;
  if (cached_has_bits & 0x00000001u) {
    target = stream->WriteBytesMaybeAliased(
        4, this->_internal_data(), target);
  }

  // optional uint64 timestamp = 5;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(5, this->_internal_timestamp(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.acviewer_msgs.RSRgbJpeg)
  return target;
}

size_t RSRgbJpeg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.acviewer_msgs.RSRgbJpeg)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000001fu) {
    // optional bytes data = 4;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
          this->_internal_data());
    }

    // optional bool is_jpeg_compress = 1 [default = false];
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 + 1;
    }

    // optional uint32 width = 2;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_width());
    }

    // optional uint64 timestamp = 5;
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
          this->_internal_timestamp());
    }

    // optional uint32 height = 3;
    if (cached_has_bits & 0x00000010u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_height());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RSRgbJpeg::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.acviewer_msgs.RSRgbJpeg)
  GOOGLE_DCHECK_NE(&from, this);
  const RSRgbJpeg* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RSRgbJpeg>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.acviewer_msgs.RSRgbJpeg)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.acviewer_msgs.RSRgbJpeg)
    MergeFrom(*source);
  }
}

void RSRgbJpeg::MergeFrom(const RSRgbJpeg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.acviewer_msgs.RSRgbJpeg)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000001fu) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_data(from._internal_data());
    }
    if (cached_has_bits & 0x00000002u) {
      is_jpeg_compress_ = from.is_jpeg_compress_;
    }
    if (cached_has_bits & 0x00000004u) {
      width_ = from.width_;
    }
    if (cached_has_bits & 0x00000008u) {
      timestamp_ = from.timestamp_;
    }
    if (cached_has_bits & 0x00000010u) {
      height_ = from.height_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void RSRgbJpeg::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.acviewer_msgs.RSRgbJpeg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RSRgbJpeg::CopyFrom(const RSRgbJpeg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.acviewer_msgs.RSRgbJpeg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RSRgbJpeg::IsInitialized() const {
  return true;
}

void RSRgbJpeg::InternalSwap(RSRgbJpeg* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  data_.Swap(&other->data_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RSRgbJpeg, height_)
      + sizeof(RSRgbJpeg::height_)
      - PROTOBUF_FIELD_OFFSET(RSRgbJpeg, is_jpeg_compress_)>(
          reinterpret_cast<char*>(&is_jpeg_compress_),
          reinterpret_cast<char*>(&other->is_jpeg_compress_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RSRgbJpeg::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class RSDepthImage::_Internal {
 public:
  using HasBits = decltype(std::declval<RSDepthImage>()._has_bits_);
  static void set_has_width(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_height(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_timestamp(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_data(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

RSDepthImage::RSDepthImage(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.acviewer_msgs.RSDepthImage)
}
RSDepthImage::RSDepthImage(const RSDepthImage& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  data_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_data()) {
    data_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_data(), 
      GetArena());
  }
  ::memcpy(&width_, &from.width_,
    static_cast<size_t>(reinterpret_cast<char*>(&timestamp_) -
    reinterpret_cast<char*>(&width_)) + sizeof(timestamp_));
  // @@protoc_insertion_point(copy_constructor:robosense.acviewer_msgs.RSDepthImage)
}

void RSDepthImage::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_RSDepthImage_acviewer_2eproto.base);
  data_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&width_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&timestamp_) -
      reinterpret_cast<char*>(&width_)) + sizeof(timestamp_));
}

RSDepthImage::~RSDepthImage() {
  // @@protoc_insertion_point(destructor:robosense.acviewer_msgs.RSDepthImage)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void RSDepthImage::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  data_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void RSDepthImage::ArenaDtor(void* object) {
  RSDepthImage* _this = reinterpret_cast< RSDepthImage* >(object);
  (void)_this;
}
void RSDepthImage::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RSDepthImage::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RSDepthImage& RSDepthImage::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RSDepthImage_acviewer_2eproto.base);
  return *internal_default_instance();
}


void RSDepthImage::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.acviewer_msgs.RSDepthImage)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    data_.ClearNonDefaultToEmpty();
  }
  if (cached_has_bits & 0x0000000eu) {
    ::memset(&width_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&timestamp_) -
        reinterpret_cast<char*>(&width_)) + sizeof(timestamp_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RSDepthImage::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional uint32 width = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          _Internal::set_has_width(&has_bits);
          width_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint32 height = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          _Internal::set_has_height(&has_bits);
          height_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional uint64 timestamp = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          _Internal::set_has_timestamp(&has_bits);
          timestamp_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional bytes data = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          auto str = _internal_mutable_data();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* RSDepthImage::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.acviewer_msgs.RSDepthImage)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional uint32 width = 1;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_width(), target);
  }

  // optional uint32 height = 2;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_height(), target);
  }

  // optional uint64 timestamp = 3;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(3, this->_internal_timestamp(), target);
  }

  // optional bytes data = 4;
  if (cached_has_bits & 0x00000001u) {
    target = stream->WriteBytesMaybeAliased(
        4, this->_internal_data(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.acviewer_msgs.RSDepthImage)
  return target;
}

size_t RSDepthImage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.acviewer_msgs.RSDepthImage)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    // optional bytes data = 4;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
          this->_internal_data());
    }

    // optional uint32 width = 1;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_width());
    }

    // optional uint32 height = 2;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
          this->_internal_height());
    }

    // optional uint64 timestamp = 3;
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
          this->_internal_timestamp());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RSDepthImage::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.acviewer_msgs.RSDepthImage)
  GOOGLE_DCHECK_NE(&from, this);
  const RSDepthImage* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RSDepthImage>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.acviewer_msgs.RSDepthImage)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.acviewer_msgs.RSDepthImage)
    MergeFrom(*source);
  }
}

void RSDepthImage::MergeFrom(const RSDepthImage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.acviewer_msgs.RSDepthImage)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_data(from._internal_data());
    }
    if (cached_has_bits & 0x00000002u) {
      width_ = from.width_;
    }
    if (cached_has_bits & 0x00000004u) {
      height_ = from.height_;
    }
    if (cached_has_bits & 0x00000008u) {
      timestamp_ = from.timestamp_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void RSDepthImage::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.acviewer_msgs.RSDepthImage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RSDepthImage::CopyFrom(const RSDepthImage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.acviewer_msgs.RSDepthImage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RSDepthImage::IsInitialized() const {
  return true;
}

void RSDepthImage::InternalSwap(RSDepthImage* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  data_.Swap(&other->data_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RSDepthImage, timestamp_)
      + sizeof(RSDepthImage::timestamp_)
      - PROTOBUF_FIELD_OFFSET(RSDepthImage, width_)>(
          reinterpret_cast<char*>(&width_),
          reinterpret_cast<char*>(&other->width_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RSDepthImage::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class RSRender::_Internal {
 public:
  using HasBits = decltype(std::declval<RSRender>()._has_bits_);
  static const ::robosense::acviewer_msgs::RSPositon& position(const RSRender* msg);
  static void set_has_position(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static const ::robosense::acviewer_msgs::RSPointCloud& point_cloud(const RSRender* msg);
  static void set_has_point_cloud(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static const ::robosense::acviewer_msgs::RSPointCloud& point_cloud_slam(const RSRender* msg);
  static void set_has_point_cloud_slam(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static const ::robosense::acviewer_msgs::RSTrangleFacet& triangle_list(const RSRender* msg);
  static void set_has_triangle_list(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static const ::robosense::acviewer_msgs::RSRgbJpeg& rgb_jpeg(const RSRender* msg);
  static void set_has_rgb_jpeg(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static const ::robosense::acviewer_msgs::RSDepthImage& depth_image(const RSRender* msg);
  static void set_has_depth_image(HasBits* has_bits) {
    (*has_bits)[0] |= 32u;
  }
};

const ::robosense::acviewer_msgs::RSPositon&
RSRender::_Internal::position(const RSRender* msg) {
  return *msg->position_;
}
const ::robosense::acviewer_msgs::RSPointCloud&
RSRender::_Internal::point_cloud(const RSRender* msg) {
  return *msg->point_cloud_;
}
const ::robosense::acviewer_msgs::RSPointCloud&
RSRender::_Internal::point_cloud_slam(const RSRender* msg) {
  return *msg->point_cloud_slam_;
}
const ::robosense::acviewer_msgs::RSTrangleFacet&
RSRender::_Internal::triangle_list(const RSRender* msg) {
  return *msg->triangle_list_;
}
const ::robosense::acviewer_msgs::RSRgbJpeg&
RSRender::_Internal::rgb_jpeg(const RSRender* msg) {
  return *msg->rgb_jpeg_;
}
const ::robosense::acviewer_msgs::RSDepthImage&
RSRender::_Internal::depth_image(const RSRender* msg) {
  return *msg->depth_image_;
}
RSRender::RSRender(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:robosense.acviewer_msgs.RSRender)
}
RSRender::RSRender(const RSRender& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_position()) {
    position_ = new ::robosense::acviewer_msgs::RSPositon(*from.position_);
  } else {
    position_ = nullptr;
  }
  if (from._internal_has_point_cloud()) {
    point_cloud_ = new ::robosense::acviewer_msgs::RSPointCloud(*from.point_cloud_);
  } else {
    point_cloud_ = nullptr;
  }
  if (from._internal_has_point_cloud_slam()) {
    point_cloud_slam_ = new ::robosense::acviewer_msgs::RSPointCloud(*from.point_cloud_slam_);
  } else {
    point_cloud_slam_ = nullptr;
  }
  if (from._internal_has_triangle_list()) {
    triangle_list_ = new ::robosense::acviewer_msgs::RSTrangleFacet(*from.triangle_list_);
  } else {
    triangle_list_ = nullptr;
  }
  if (from._internal_has_rgb_jpeg()) {
    rgb_jpeg_ = new ::robosense::acviewer_msgs::RSRgbJpeg(*from.rgb_jpeg_);
  } else {
    rgb_jpeg_ = nullptr;
  }
  if (from._internal_has_depth_image()) {
    depth_image_ = new ::robosense::acviewer_msgs::RSDepthImage(*from.depth_image_);
  } else {
    depth_image_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:robosense.acviewer_msgs.RSRender)
}

void RSRender::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_RSRender_acviewer_2eproto.base);
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&position_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&depth_image_) -
      reinterpret_cast<char*>(&position_)) + sizeof(depth_image_));
}

RSRender::~RSRender() {
  // @@protoc_insertion_point(destructor:robosense.acviewer_msgs.RSRender)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void RSRender::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  if (this != internal_default_instance()) delete position_;
  if (this != internal_default_instance()) delete point_cloud_;
  if (this != internal_default_instance()) delete point_cloud_slam_;
  if (this != internal_default_instance()) delete triangle_list_;
  if (this != internal_default_instance()) delete rgb_jpeg_;
  if (this != internal_default_instance()) delete depth_image_;
}

void RSRender::ArenaDtor(void* object) {
  RSRender* _this = reinterpret_cast< RSRender* >(object);
  (void)_this;
}
void RSRender::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RSRender::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RSRender& RSRender::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RSRender_acviewer_2eproto.base);
  return *internal_default_instance();
}


void RSRender::Clear() {
// @@protoc_insertion_point(message_clear_start:robosense.acviewer_msgs.RSRender)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000003fu) {
    if (cached_has_bits & 0x00000001u) {
      GOOGLE_DCHECK(position_ != nullptr);
      position_->Clear();
    }
    if (cached_has_bits & 0x00000002u) {
      GOOGLE_DCHECK(point_cloud_ != nullptr);
      point_cloud_->Clear();
    }
    if (cached_has_bits & 0x00000004u) {
      GOOGLE_DCHECK(point_cloud_slam_ != nullptr);
      point_cloud_slam_->Clear();
    }
    if (cached_has_bits & 0x00000008u) {
      GOOGLE_DCHECK(triangle_list_ != nullptr);
      triangle_list_->Clear();
    }
    if (cached_has_bits & 0x00000010u) {
      GOOGLE_DCHECK(rgb_jpeg_ != nullptr);
      rgb_jpeg_->Clear();
    }
    if (cached_has_bits & 0x00000020u) {
      GOOGLE_DCHECK(depth_image_ != nullptr);
      depth_image_->Clear();
    }
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RSRender::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional .robosense.acviewer_msgs.RSPositon position = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_position(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .robosense.acviewer_msgs.RSPointCloud point_cloud = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_point_cloud(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .robosense.acviewer_msgs.RSPointCloud point_cloud_slam = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_point_cloud_slam(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .robosense.acviewer_msgs.RSTrangleFacet triangle_list = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_triangle_list(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .robosense.acviewer_msgs.RSRgbJpeg rgb_jpeg = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_rgb_jpeg(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .robosense.acviewer_msgs.RSDepthImage depth_image = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_depth_image(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* RSRender::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:robosense.acviewer_msgs.RSRender)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional .robosense.acviewer_msgs.RSPositon position = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::position(this), target, stream);
  }

  // optional .robosense.acviewer_msgs.RSPointCloud point_cloud = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::point_cloud(this), target, stream);
  }

  // optional .robosense.acviewer_msgs.RSPointCloud point_cloud_slam = 3;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::point_cloud_slam(this), target, stream);
  }

  // optional .robosense.acviewer_msgs.RSTrangleFacet triangle_list = 4;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::triangle_list(this), target, stream);
  }

  // optional .robosense.acviewer_msgs.RSRgbJpeg rgb_jpeg = 5;
  if (cached_has_bits & 0x00000010u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::rgb_jpeg(this), target, stream);
  }

  // optional .robosense.acviewer_msgs.RSDepthImage depth_image = 6;
  if (cached_has_bits & 0x00000020u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::depth_image(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:robosense.acviewer_msgs.RSRender)
  return target;
}

size_t RSRender::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:robosense.acviewer_msgs.RSRender)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000003fu) {
    // optional .robosense.acviewer_msgs.RSPositon position = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *position_);
    }

    // optional .robosense.acviewer_msgs.RSPointCloud point_cloud = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *point_cloud_);
    }

    // optional .robosense.acviewer_msgs.RSPointCloud point_cloud_slam = 3;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *point_cloud_slam_);
    }

    // optional .robosense.acviewer_msgs.RSTrangleFacet triangle_list = 4;
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *triangle_list_);
    }

    // optional .robosense.acviewer_msgs.RSRgbJpeg rgb_jpeg = 5;
    if (cached_has_bits & 0x00000010u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *rgb_jpeg_);
    }

    // optional .robosense.acviewer_msgs.RSDepthImage depth_image = 6;
    if (cached_has_bits & 0x00000020u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *depth_image_);
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RSRender::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:robosense.acviewer_msgs.RSRender)
  GOOGLE_DCHECK_NE(&from, this);
  const RSRender* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RSRender>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:robosense.acviewer_msgs.RSRender)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:robosense.acviewer_msgs.RSRender)
    MergeFrom(*source);
  }
}

void RSRender::MergeFrom(const RSRender& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:robosense.acviewer_msgs.RSRender)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000003fu) {
    if (cached_has_bits & 0x00000001u) {
      _internal_mutable_position()->::robosense::acviewer_msgs::RSPositon::MergeFrom(from._internal_position());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_mutable_point_cloud()->::robosense::acviewer_msgs::RSPointCloud::MergeFrom(from._internal_point_cloud());
    }
    if (cached_has_bits & 0x00000004u) {
      _internal_mutable_point_cloud_slam()->::robosense::acviewer_msgs::RSPointCloud::MergeFrom(from._internal_point_cloud_slam());
    }
    if (cached_has_bits & 0x00000008u) {
      _internal_mutable_triangle_list()->::robosense::acviewer_msgs::RSTrangleFacet::MergeFrom(from._internal_triangle_list());
    }
    if (cached_has_bits & 0x00000010u) {
      _internal_mutable_rgb_jpeg()->::robosense::acviewer_msgs::RSRgbJpeg::MergeFrom(from._internal_rgb_jpeg());
    }
    if (cached_has_bits & 0x00000020u) {
      _internal_mutable_depth_image()->::robosense::acviewer_msgs::RSDepthImage::MergeFrom(from._internal_depth_image());
    }
  }
}

void RSRender::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:robosense.acviewer_msgs.RSRender)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RSRender::CopyFrom(const RSRender& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:robosense.acviewer_msgs.RSRender)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RSRender::IsInitialized() const {
  return true;
}

void RSRender::InternalSwap(RSRender* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RSRender, depth_image_)
      + sizeof(RSRender::depth_image_)
      - PROTOBUF_FIELD_OFFSET(RSRender, position_)>(
          reinterpret_cast<char*>(&position_),
          reinterpret_cast<char*>(&other->position_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RSRender::GetMetadata() const {
  return GetMetadataStatic();
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace acviewer_msgs
}  // namespace robosense
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::robosense::acviewer_msgs::RSRequest* Arena::CreateMaybeMessage< ::robosense::acviewer_msgs::RSRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::acviewer_msgs::RSRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::acviewer_msgs::RSShowConfigData* Arena::CreateMaybeMessage< ::robosense::acviewer_msgs::RSShowConfigData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::acviewer_msgs::RSShowConfigData >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::acviewer_msgs::RSOperatorDeviceData* Arena::CreateMaybeMessage< ::robosense::acviewer_msgs::RSOperatorDeviceData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::acviewer_msgs::RSOperatorDeviceData >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::acviewer_msgs::RSWriterSetting* Arena::CreateMaybeMessage< ::robosense::acviewer_msgs::RSWriterSetting >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::acviewer_msgs::RSWriterSetting >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::acviewer_msgs::RSTopicSetting* Arena::CreateMaybeMessage< ::robosense::acviewer_msgs::RSTopicSetting >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::acviewer_msgs::RSTopicSetting >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::acviewer_msgs::RSReaderSetting* Arena::CreateMaybeMessage< ::robosense::acviewer_msgs::RSReaderSetting >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::acviewer_msgs::RSReaderSetting >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::acviewer_msgs::RSRequestData* Arena::CreateMaybeMessage< ::robosense::acviewer_msgs::RSRequestData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::acviewer_msgs::RSRequestData >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::acviewer_msgs::RSResponse* Arena::CreateMaybeMessage< ::robosense::acviewer_msgs::RSResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::acviewer_msgs::RSResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::acviewer_msgs::RSDeviceInfo* Arena::CreateMaybeMessage< ::robosense::acviewer_msgs::RSDeviceInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::acviewer_msgs::RSDeviceInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::acviewer_msgs::RSWriterConfig* Arena::CreateMaybeMessage< ::robosense::acviewer_msgs::RSWriterConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::acviewer_msgs::RSWriterConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::acviewer_msgs::RSReaderConfig* Arena::CreateMaybeMessage< ::robosense::acviewer_msgs::RSReaderConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::acviewer_msgs::RSReaderConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::acviewer_msgs::RSTopicConfig* Arena::CreateMaybeMessage< ::robosense::acviewer_msgs::RSTopicConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::acviewer_msgs::RSTopicConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::acviewer_msgs::RSReaderProgress* Arena::CreateMaybeMessage< ::robosense::acviewer_msgs::RSReaderProgress >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::acviewer_msgs::RSReaderProgress >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::acviewer_msgs::RSResponseData* Arena::CreateMaybeMessage< ::robosense::acviewer_msgs::RSResponseData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::acviewer_msgs::RSResponseData >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::acviewer_msgs::UdpControlConfig* Arena::CreateMaybeMessage< ::robosense::acviewer_msgs::UdpControlConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::acviewer_msgs::UdpControlConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::acviewer_msgs::RenderSwitch* Arena::CreateMaybeMessage< ::robosense::acviewer_msgs::RenderSwitch >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::acviewer_msgs::RenderSwitch >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::acviewer_msgs::RSPositon* Arena::CreateMaybeMessage< ::robosense::acviewer_msgs::RSPositon >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::acviewer_msgs::RSPositon >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::acviewer_msgs::RSPointCloud* Arena::CreateMaybeMessage< ::robosense::acviewer_msgs::RSPointCloud >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::acviewer_msgs::RSPointCloud >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::acviewer_msgs::RSTrangleFacet* Arena::CreateMaybeMessage< ::robosense::acviewer_msgs::RSTrangleFacet >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::acviewer_msgs::RSTrangleFacet >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::acviewer_msgs::RSRgbJpeg* Arena::CreateMaybeMessage< ::robosense::acviewer_msgs::RSRgbJpeg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::acviewer_msgs::RSRgbJpeg >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::acviewer_msgs::RSDepthImage* Arena::CreateMaybeMessage< ::robosense::acviewer_msgs::RSDepthImage >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::acviewer_msgs::RSDepthImage >(arena);
}
template<> PROTOBUF_NOINLINE ::robosense::acviewer_msgs::RSRender* Arena::CreateMaybeMessage< ::robosense::acviewer_msgs::RSRender >(Arena* arena) {
  return Arena::CreateMessageInternal< ::robosense::acviewer_msgs::RSRender >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
