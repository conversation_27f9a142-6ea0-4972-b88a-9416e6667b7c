// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: hmi.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_hmi_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_hmi_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3014000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3014000 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "proto_compress.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_hmi_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_hmi_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_hmi_2eproto;
namespace robosense {
namespace hmi_msgs {
class SelfDescribingMessage;
class SelfDescribingMessageDefaultTypeInternal;
extern SelfDescribingMessageDefaultTypeInternal _SelfDescribingMessage_default_instance_;
}  // namespace hmi_msgs
}  // namespace robosense
PROTOBUF_NAMESPACE_OPEN
template<> ::robosense::hmi_msgs::SelfDescribingMessage* Arena::CreateMaybeMessage<::robosense::hmi_msgs::SelfDescribingMessage>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace robosense {
namespace hmi_msgs {

// ===================================================================

class SelfDescribingMessage PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:robosense.hmi_msgs.SelfDescribingMessage) */ {
 public:
  inline SelfDescribingMessage() : SelfDescribingMessage(nullptr) {}
  virtual ~SelfDescribingMessage();

  SelfDescribingMessage(const SelfDescribingMessage& from);
  SelfDescribingMessage(SelfDescribingMessage&& from) noexcept
    : SelfDescribingMessage() {
    *this = ::std::move(from);
  }

  inline SelfDescribingMessage& operator=(const SelfDescribingMessage& from) {
    CopyFrom(from);
    return *this;
  }
  inline SelfDescribingMessage& operator=(SelfDescribingMessage&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SelfDescribingMessage& default_instance();

  static inline const SelfDescribingMessage* internal_default_instance() {
    return reinterpret_cast<const SelfDescribingMessage*>(
               &_SelfDescribingMessage_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(SelfDescribingMessage& a, SelfDescribingMessage& b) {
    a.Swap(&b);
  }
  inline void Swap(SelfDescribingMessage* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SelfDescribingMessage* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SelfDescribingMessage* New() const final {
    return CreateMaybeMessage<SelfDescribingMessage>(nullptr);
  }

  SelfDescribingMessage* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SelfDescribingMessage>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SelfDescribingMessage& from);
  void MergeFrom(const SelfDescribingMessage& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SelfDescribingMessage* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "robosense.hmi_msgs.SelfDescribingMessage";
  }
  protected:
  explicit SelfDescribingMessage(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_hmi_2eproto);
    return ::descriptor_table_hmi_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMessageTopicFieldNumber = 1,
    kMessageTypeFieldNumber = 2,
    kMessageDataFieldNumber = 3,
    kSendHmiTimeFieldNumber = 4,
    kRecvHmiTimeFieldNumber = 5,
    kDeserializeHmiTimeFieldNumber = 6,
    kUncompressedSizeFieldNumber = 9,
    kCompressLevelFieldNumber = 8,
    kCompressFormatFieldNumber = 7,
  };
  // optional string message_topic = 1;
  bool has_message_topic() const;
  private:
  bool _internal_has_message_topic() const;
  public:
  void clear_message_topic();
  const std::string& message_topic() const;
  void set_message_topic(const std::string& value);
  void set_message_topic(std::string&& value);
  void set_message_topic(const char* value);
  void set_message_topic(const char* value, size_t size);
  std::string* mutable_message_topic();
  std::string* release_message_topic();
  void set_allocated_message_topic(std::string* message_topic);
  private:
  const std::string& _internal_message_topic() const;
  void _internal_set_message_topic(const std::string& value);
  std::string* _internal_mutable_message_topic();
  public:

  // optional string message_type = 2;
  bool has_message_type() const;
  private:
  bool _internal_has_message_type() const;
  public:
  void clear_message_type();
  const std::string& message_type() const;
  void set_message_type(const std::string& value);
  void set_message_type(std::string&& value);
  void set_message_type(const char* value);
  void set_message_type(const char* value, size_t size);
  std::string* mutable_message_type();
  std::string* release_message_type();
  void set_allocated_message_type(std::string* message_type);
  private:
  const std::string& _internal_message_type() const;
  void _internal_set_message_type(const std::string& value);
  std::string* _internal_mutable_message_type();
  public:

  // optional bytes message_data = 3;
  bool has_message_data() const;
  private:
  bool _internal_has_message_data() const;
  public:
  void clear_message_data();
  const std::string& message_data() const;
  void set_message_data(const std::string& value);
  void set_message_data(std::string&& value);
  void set_message_data(const char* value);
  void set_message_data(const void* value, size_t size);
  std::string* mutable_message_data();
  std::string* release_message_data();
  void set_allocated_message_data(std::string* message_data);
  private:
  const std::string& _internal_message_data() const;
  void _internal_set_message_data(const std::string& value);
  std::string* _internal_mutable_message_data();
  public:

  // optional uint64 send_hmi_time = 4;
  bool has_send_hmi_time() const;
  private:
  bool _internal_has_send_hmi_time() const;
  public:
  void clear_send_hmi_time();
  ::PROTOBUF_NAMESPACE_ID::uint64 send_hmi_time() const;
  void set_send_hmi_time(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_send_hmi_time() const;
  void _internal_set_send_hmi_time(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // optional uint64 recv_hmi_time = 5;
  bool has_recv_hmi_time() const;
  private:
  bool _internal_has_recv_hmi_time() const;
  public:
  void clear_recv_hmi_time();
  ::PROTOBUF_NAMESPACE_ID::uint64 recv_hmi_time() const;
  void set_recv_hmi_time(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_recv_hmi_time() const;
  void _internal_set_recv_hmi_time(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // optional uint64 deserialize_hmi_time = 6;
  bool has_deserialize_hmi_time() const;
  private:
  bool _internal_has_deserialize_hmi_time() const;
  public:
  void clear_deserialize_hmi_time();
  ::PROTOBUF_NAMESPACE_ID::uint64 deserialize_hmi_time() const;
  void set_deserialize_hmi_time(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_deserialize_hmi_time() const;
  void _internal_set_deserialize_hmi_time(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // optional uint64 uncompressed_size = 9 [default = 0];
  bool has_uncompressed_size() const;
  private:
  bool _internal_has_uncompressed_size() const;
  public:
  void clear_uncompressed_size();
  ::PROTOBUF_NAMESPACE_ID::uint64 uncompressed_size() const;
  void set_uncompressed_size(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_uncompressed_size() const;
  void _internal_set_uncompressed_size(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // optional uint32 compress_level = 8;
  bool has_compress_level() const;
  private:
  bool _internal_has_compress_level() const;
  public:
  void clear_compress_level();
  ::PROTOBUF_NAMESPACE_ID::uint32 compress_level() const;
  void set_compress_level(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_compress_level() const;
  void _internal_set_compress_level(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // optional .robosense.proto_compress_msgs.RS_POST_DATA_COMPRESSION_FORMAT compress_format = 7;
  bool has_compress_format() const;
  private:
  bool _internal_has_compress_format() const;
  public:
  void clear_compress_format();
  ::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT compress_format() const;
  void set_compress_format(::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT value);
  private:
  ::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT _internal_compress_format() const;
  void _internal_set_compress_format(::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT value);
  public:

  // @@protoc_insertion_point(class_scope:robosense.hmi_msgs.SelfDescribingMessage)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr message_topic_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr message_type_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr message_data_;
  ::PROTOBUF_NAMESPACE_ID::uint64 send_hmi_time_;
  ::PROTOBUF_NAMESPACE_ID::uint64 recv_hmi_time_;
  ::PROTOBUF_NAMESPACE_ID::uint64 deserialize_hmi_time_;
  ::PROTOBUF_NAMESPACE_ID::uint64 uncompressed_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 compress_level_;
  int compress_format_;
  friend struct ::TableStruct_hmi_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// SelfDescribingMessage

// optional string message_topic = 1;
inline bool SelfDescribingMessage::_internal_has_message_topic() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool SelfDescribingMessage::has_message_topic() const {
  return _internal_has_message_topic();
}
inline void SelfDescribingMessage::clear_message_topic() {
  message_topic_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& SelfDescribingMessage::message_topic() const {
  // @@protoc_insertion_point(field_get:robosense.hmi_msgs.SelfDescribingMessage.message_topic)
  return _internal_message_topic();
}
inline void SelfDescribingMessage::set_message_topic(const std::string& value) {
  _internal_set_message_topic(value);
  // @@protoc_insertion_point(field_set:robosense.hmi_msgs.SelfDescribingMessage.message_topic)
}
inline std::string* SelfDescribingMessage::mutable_message_topic() {
  // @@protoc_insertion_point(field_mutable:robosense.hmi_msgs.SelfDescribingMessage.message_topic)
  return _internal_mutable_message_topic();
}
inline const std::string& SelfDescribingMessage::_internal_message_topic() const {
  return message_topic_.Get();
}
inline void SelfDescribingMessage::_internal_set_message_topic(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  message_topic_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void SelfDescribingMessage::set_message_topic(std::string&& value) {
  _has_bits_[0] |= 0x00000001u;
  message_topic_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.hmi_msgs.SelfDescribingMessage.message_topic)
}
inline void SelfDescribingMessage::set_message_topic(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000001u;
  message_topic_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.hmi_msgs.SelfDescribingMessage.message_topic)
}
inline void SelfDescribingMessage::set_message_topic(const char* value,
    size_t size) {
  _has_bits_[0] |= 0x00000001u;
  message_topic_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.hmi_msgs.SelfDescribingMessage.message_topic)
}
inline std::string* SelfDescribingMessage::_internal_mutable_message_topic() {
  _has_bits_[0] |= 0x00000001u;
  return message_topic_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* SelfDescribingMessage::release_message_topic() {
  // @@protoc_insertion_point(field_release:robosense.hmi_msgs.SelfDescribingMessage.message_topic)
  if (!_internal_has_message_topic()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  return message_topic_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void SelfDescribingMessage::set_allocated_message_topic(std::string* message_topic) {
  if (message_topic != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  message_topic_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), message_topic,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.hmi_msgs.SelfDescribingMessage.message_topic)
}

// optional string message_type = 2;
inline bool SelfDescribingMessage::_internal_has_message_type() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool SelfDescribingMessage::has_message_type() const {
  return _internal_has_message_type();
}
inline void SelfDescribingMessage::clear_message_type() {
  message_type_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000002u;
}
inline const std::string& SelfDescribingMessage::message_type() const {
  // @@protoc_insertion_point(field_get:robosense.hmi_msgs.SelfDescribingMessage.message_type)
  return _internal_message_type();
}
inline void SelfDescribingMessage::set_message_type(const std::string& value) {
  _internal_set_message_type(value);
  // @@protoc_insertion_point(field_set:robosense.hmi_msgs.SelfDescribingMessage.message_type)
}
inline std::string* SelfDescribingMessage::mutable_message_type() {
  // @@protoc_insertion_point(field_mutable:robosense.hmi_msgs.SelfDescribingMessage.message_type)
  return _internal_mutable_message_type();
}
inline const std::string& SelfDescribingMessage::_internal_message_type() const {
  return message_type_.Get();
}
inline void SelfDescribingMessage::_internal_set_message_type(const std::string& value) {
  _has_bits_[0] |= 0x00000002u;
  message_type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void SelfDescribingMessage::set_message_type(std::string&& value) {
  _has_bits_[0] |= 0x00000002u;
  message_type_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.hmi_msgs.SelfDescribingMessage.message_type)
}
inline void SelfDescribingMessage::set_message_type(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000002u;
  message_type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.hmi_msgs.SelfDescribingMessage.message_type)
}
inline void SelfDescribingMessage::set_message_type(const char* value,
    size_t size) {
  _has_bits_[0] |= 0x00000002u;
  message_type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.hmi_msgs.SelfDescribingMessage.message_type)
}
inline std::string* SelfDescribingMessage::_internal_mutable_message_type() {
  _has_bits_[0] |= 0x00000002u;
  return message_type_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* SelfDescribingMessage::release_message_type() {
  // @@protoc_insertion_point(field_release:robosense.hmi_msgs.SelfDescribingMessage.message_type)
  if (!_internal_has_message_type()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000002u;
  return message_type_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void SelfDescribingMessage::set_allocated_message_type(std::string* message_type) {
  if (message_type != nullptr) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  message_type_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), message_type,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.hmi_msgs.SelfDescribingMessage.message_type)
}

// optional bytes message_data = 3;
inline bool SelfDescribingMessage::_internal_has_message_data() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool SelfDescribingMessage::has_message_data() const {
  return _internal_has_message_data();
}
inline void SelfDescribingMessage::clear_message_data() {
  message_data_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000004u;
}
inline const std::string& SelfDescribingMessage::message_data() const {
  // @@protoc_insertion_point(field_get:robosense.hmi_msgs.SelfDescribingMessage.message_data)
  return _internal_message_data();
}
inline void SelfDescribingMessage::set_message_data(const std::string& value) {
  _internal_set_message_data(value);
  // @@protoc_insertion_point(field_set:robosense.hmi_msgs.SelfDescribingMessage.message_data)
}
inline std::string* SelfDescribingMessage::mutable_message_data() {
  // @@protoc_insertion_point(field_mutable:robosense.hmi_msgs.SelfDescribingMessage.message_data)
  return _internal_mutable_message_data();
}
inline const std::string& SelfDescribingMessage::_internal_message_data() const {
  return message_data_.Get();
}
inline void SelfDescribingMessage::_internal_set_message_data(const std::string& value) {
  _has_bits_[0] |= 0x00000004u;
  message_data_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void SelfDescribingMessage::set_message_data(std::string&& value) {
  _has_bits_[0] |= 0x00000004u;
  message_data_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:robosense.hmi_msgs.SelfDescribingMessage.message_data)
}
inline void SelfDescribingMessage::set_message_data(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _has_bits_[0] |= 0x00000004u;
  message_data_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:robosense.hmi_msgs.SelfDescribingMessage.message_data)
}
inline void SelfDescribingMessage::set_message_data(const void* value,
    size_t size) {
  _has_bits_[0] |= 0x00000004u;
  message_data_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:robosense.hmi_msgs.SelfDescribingMessage.message_data)
}
inline std::string* SelfDescribingMessage::_internal_mutable_message_data() {
  _has_bits_[0] |= 0x00000004u;
  return message_data_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* SelfDescribingMessage::release_message_data() {
  // @@protoc_insertion_point(field_release:robosense.hmi_msgs.SelfDescribingMessage.message_data)
  if (!_internal_has_message_data()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000004u;
  return message_data_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void SelfDescribingMessage::set_allocated_message_data(std::string* message_data) {
  if (message_data != nullptr) {
    _has_bits_[0] |= 0x00000004u;
  } else {
    _has_bits_[0] &= ~0x00000004u;
  }
  message_data_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), message_data,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:robosense.hmi_msgs.SelfDescribingMessage.message_data)
}

// optional uint64 send_hmi_time = 4;
inline bool SelfDescribingMessage::_internal_has_send_hmi_time() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool SelfDescribingMessage::has_send_hmi_time() const {
  return _internal_has_send_hmi_time();
}
inline void SelfDescribingMessage::clear_send_hmi_time() {
  send_hmi_time_ = PROTOBUF_ULONGLONG(0);
  _has_bits_[0] &= ~0x00000008u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 SelfDescribingMessage::_internal_send_hmi_time() const {
  return send_hmi_time_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 SelfDescribingMessage::send_hmi_time() const {
  // @@protoc_insertion_point(field_get:robosense.hmi_msgs.SelfDescribingMessage.send_hmi_time)
  return _internal_send_hmi_time();
}
inline void SelfDescribingMessage::_internal_set_send_hmi_time(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _has_bits_[0] |= 0x00000008u;
  send_hmi_time_ = value;
}
inline void SelfDescribingMessage::set_send_hmi_time(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_send_hmi_time(value);
  // @@protoc_insertion_point(field_set:robosense.hmi_msgs.SelfDescribingMessage.send_hmi_time)
}

// optional uint64 recv_hmi_time = 5;
inline bool SelfDescribingMessage::_internal_has_recv_hmi_time() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool SelfDescribingMessage::has_recv_hmi_time() const {
  return _internal_has_recv_hmi_time();
}
inline void SelfDescribingMessage::clear_recv_hmi_time() {
  recv_hmi_time_ = PROTOBUF_ULONGLONG(0);
  _has_bits_[0] &= ~0x00000010u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 SelfDescribingMessage::_internal_recv_hmi_time() const {
  return recv_hmi_time_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 SelfDescribingMessage::recv_hmi_time() const {
  // @@protoc_insertion_point(field_get:robosense.hmi_msgs.SelfDescribingMessage.recv_hmi_time)
  return _internal_recv_hmi_time();
}
inline void SelfDescribingMessage::_internal_set_recv_hmi_time(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _has_bits_[0] |= 0x00000010u;
  recv_hmi_time_ = value;
}
inline void SelfDescribingMessage::set_recv_hmi_time(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_recv_hmi_time(value);
  // @@protoc_insertion_point(field_set:robosense.hmi_msgs.SelfDescribingMessage.recv_hmi_time)
}

// optional uint64 deserialize_hmi_time = 6;
inline bool SelfDescribingMessage::_internal_has_deserialize_hmi_time() const {
  bool value = (_has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool SelfDescribingMessage::has_deserialize_hmi_time() const {
  return _internal_has_deserialize_hmi_time();
}
inline void SelfDescribingMessage::clear_deserialize_hmi_time() {
  deserialize_hmi_time_ = PROTOBUF_ULONGLONG(0);
  _has_bits_[0] &= ~0x00000020u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 SelfDescribingMessage::_internal_deserialize_hmi_time() const {
  return deserialize_hmi_time_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 SelfDescribingMessage::deserialize_hmi_time() const {
  // @@protoc_insertion_point(field_get:robosense.hmi_msgs.SelfDescribingMessage.deserialize_hmi_time)
  return _internal_deserialize_hmi_time();
}
inline void SelfDescribingMessage::_internal_set_deserialize_hmi_time(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _has_bits_[0] |= 0x00000020u;
  deserialize_hmi_time_ = value;
}
inline void SelfDescribingMessage::set_deserialize_hmi_time(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_deserialize_hmi_time(value);
  // @@protoc_insertion_point(field_set:robosense.hmi_msgs.SelfDescribingMessage.deserialize_hmi_time)
}

// optional .robosense.proto_compress_msgs.RS_POST_DATA_COMPRESSION_FORMAT compress_format = 7;
inline bool SelfDescribingMessage::_internal_has_compress_format() const {
  bool value = (_has_bits_[0] & 0x00000100u) != 0;
  return value;
}
inline bool SelfDescribingMessage::has_compress_format() const {
  return _internal_has_compress_format();
}
inline void SelfDescribingMessage::clear_compress_format() {
  compress_format_ = 1;
  _has_bits_[0] &= ~0x00000100u;
}
inline ::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT SelfDescribingMessage::_internal_compress_format() const {
  return static_cast< ::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT >(compress_format_);
}
inline ::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT SelfDescribingMessage::compress_format() const {
  // @@protoc_insertion_point(field_get:robosense.hmi_msgs.SelfDescribingMessage.compress_format)
  return _internal_compress_format();
}
inline void SelfDescribingMessage::_internal_set_compress_format(::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT value) {
  assert(::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT_IsValid(value));
  _has_bits_[0] |= 0x00000100u;
  compress_format_ = value;
}
inline void SelfDescribingMessage::set_compress_format(::robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT value) {
  _internal_set_compress_format(value);
  // @@protoc_insertion_point(field_set:robosense.hmi_msgs.SelfDescribingMessage.compress_format)
}

// optional uint32 compress_level = 8;
inline bool SelfDescribingMessage::_internal_has_compress_level() const {
  bool value = (_has_bits_[0] & 0x00000080u) != 0;
  return value;
}
inline bool SelfDescribingMessage::has_compress_level() const {
  return _internal_has_compress_level();
}
inline void SelfDescribingMessage::clear_compress_level() {
  compress_level_ = 0u;
  _has_bits_[0] &= ~0x00000080u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 SelfDescribingMessage::_internal_compress_level() const {
  return compress_level_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 SelfDescribingMessage::compress_level() const {
  // @@protoc_insertion_point(field_get:robosense.hmi_msgs.SelfDescribingMessage.compress_level)
  return _internal_compress_level();
}
inline void SelfDescribingMessage::_internal_set_compress_level(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _has_bits_[0] |= 0x00000080u;
  compress_level_ = value;
}
inline void SelfDescribingMessage::set_compress_level(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_compress_level(value);
  // @@protoc_insertion_point(field_set:robosense.hmi_msgs.SelfDescribingMessage.compress_level)
}

// optional uint64 uncompressed_size = 9 [default = 0];
inline bool SelfDescribingMessage::_internal_has_uncompressed_size() const {
  bool value = (_has_bits_[0] & 0x00000040u) != 0;
  return value;
}
inline bool SelfDescribingMessage::has_uncompressed_size() const {
  return _internal_has_uncompressed_size();
}
inline void SelfDescribingMessage::clear_uncompressed_size() {
  uncompressed_size_ = PROTOBUF_ULONGLONG(0);
  _has_bits_[0] &= ~0x00000040u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 SelfDescribingMessage::_internal_uncompressed_size() const {
  return uncompressed_size_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 SelfDescribingMessage::uncompressed_size() const {
  // @@protoc_insertion_point(field_get:robosense.hmi_msgs.SelfDescribingMessage.uncompressed_size)
  return _internal_uncompressed_size();
}
inline void SelfDescribingMessage::_internal_set_uncompressed_size(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _has_bits_[0] |= 0x00000040u;
  uncompressed_size_ = value;
}
inline void SelfDescribingMessage::set_uncompressed_size(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_uncompressed_size(value);
  // @@protoc_insertion_point(field_set:robosense.hmi_msgs.SelfDescribingMessage.uncompressed_size)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace hmi_msgs
}  // namespace robosense

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_hmi_2eproto
