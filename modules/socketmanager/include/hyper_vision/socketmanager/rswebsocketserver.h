#pragma once

#include "hyper_vision/socketmanager/rswebsocketrole.h"

namespace robosense {
namespace rs_hmi {
namespace hmi {

class RSWebsocketServer : public RSWebsocketRole {
public:
  using RS_WEBSOCKET_SERVER_ROLE =
      websocketpp::server<websocketpp::config::asio>;

  enum class RS_CONNECT_STATUE : int {
    RS_CONNECT_CLOSE = 0,
    RS_CONNECT_OPEN,
  };

  struct RSConnectHdlInfo {
    unsigned long long int hdlId;
    websocketpp::connection_hdl hdl;

    bool operator<(const RSConnectHdlInfo &val) const {
      return (hdlId < val.hdlId);
    }

    bool operator==(const RSConnectHdlInfo &val) const {
      return (hdlId == val.hdlId);
    }
  };

public:
  RSWebsocketServer();
  ~RSWebsocketServer();

public:
  int init(const robosense::rs_hmi::config::SocketTaskConfig &config) override;

  int start() override;

  int stop() override;

  int outgoingMsgSize(
      size_t &totalOutgoingMsgSize,
      std::map<std::string, size_t> &connectionOutgoingSizes) override;

protected:
  int sendProtoMessage(const std::string &message) override;

private:
  void openHandle(websocketpp::connection_hdl hld);
  void failHandle(websocketpp::connection_hdl hld);
  void closeHandle(websocketpp::connection_hdl hld);

  void websocketWorkThread();
  void websocketTimeoutCheckThread();
  void recvProtoMessage(websocketpp::connection_hdl hdl,
                        RS_WEBSOCKET_SERVER_ROLE::message_ptr msg);

private:
  RS_WEBSOCKET_SERVER_ROLE server_;
  uint64_t maxConnectHdlId_ = 0;

private:
  class RSClientConnectHdlInfo {
  public:
    RSClientConnectHdlInfo() { reset(); }

    ~RSClientConnectHdlInfo() {}

  public:
    void reset() {
      status = RS_CONNECT_STATUE::RS_CONNECT_CLOSE;
      start_timestamp_ns = 0;
      last_timestamp_ns = 0;
    }

  public:
    RS_CONNECT_STATUE status;
    uint64_t start_timestamp_ns;
    uint64_t last_timestamp_ns;
  };
  std::map<RSConnectHdlInfo, RSClientConnectHdlInfo> connectHdls_;

  bool is_running_timeout_checker_;
  uint64_t timeout_checker_timeout_th_ms_ = 20000; // 连接超时的阈值
  uint64_t timeout_checker_th_ms_ = 3000;          // 检查的周期
  std::shared_ptr<std::thread> timeout_checker_thread_;
};

} // namespace hmi
} // namespace rs_hmi
} // namespace robosense
