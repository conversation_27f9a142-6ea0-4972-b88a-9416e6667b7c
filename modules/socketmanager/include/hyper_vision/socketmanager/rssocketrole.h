#ifndef RSSOCKETROLE_H
#define RSSOCKETROLE_H

/***
 * HMI 通信
 *                                      RSSocketRole
 *                                   /                   \
 *              RSWebsocketRole                                 RSUdpRole
 *               /         \                                   /            \
 *  RSWebsocketClient   RSWebsocketServer            RSMulticastComm RSDgramComm
 *
 *
 * RSWebsocketClient: Websocket Client
 * RSWebsocketServer: Websocket Server
 * RSMulticastComm: UDP Multicast
 * RSDgramComm: UDP P2P
 *
 */

#include <condition_variable>
#include <functional>
#include <iostream>
#include <mutex>
#include <queue>
#include <set>
#include <string>
#include <thread>
#include <vector>

#include <boost/filesystem.hpp>

// #include "cyber/common/log.h"
#include "hyper_vision/socketmanager/rsprotoparser.h"
#include "proto/proto_compress.pb.h"

namespace robosense {
namespace rs_hmi {
namespace hmi {

using RS_SOCKET_RECV_CALLBACK =
    std::function<void(const RSWebsocketProtoMsg::Ptr &)>;

using RS_SOCKET_EXCEPTION_CALLBACK = std::function<void(
    const int, const std::string &,
    const robosense::rs_hmi::config::WebsocketEndPointConfig &)>;

class RSSocketRole {
public:
  using Ptr = std::shared_ptr<RSSocketRole>;
  using ConstPtr = std::shared_ptr<const RSSocketRole>;

public:
  RSSocketRole() = default;

  virtual ~RSSocketRole() = default;

public:
  virtual int
  init(const robosense::rs_hmi::config::SocketTaskConfig &config) = 0;

  virtual int start() = 0;

  virtual int stop() = 0;

  virtual int
  registerExceptCallback(const RS_SOCKET_EXCEPTION_CALLBACK &callback);

  virtual int registerRecvCallback(const std::set<std::string> &messageTopics,
                                   const RS_SOCKET_RECV_CALLBACK &callback);

  virtual int sendProtoMessage(const RSWebsocketProtoMsg::Ptr &msgPtr) = 0;

  virtual int
  outgoingMsgSize(size_t &totalOutgoingMsgSize,
                  std::map<std::string, size_t> &connectionOutgoingSizes) = 0;

  virtual uint32_t getUdpSendBufferSize() = 0;

  virtual void updateUdpControlConfig(
      const robosense::rs_hmi::config::UdpControlConfig &udpControlConfig) = 0;

protected:
  void runLocalRecvCallback(const RSWebsocketProtoMsg::Ptr &protoMsgPtr);
  virtual int sendProtoMessage(const std::string &protoMsgString) = 0;

protected:
  bool is_running_;

protected:
  std::condition_variable send_buffer_cond_;
  std::mutex send_buffer_mtx_;
  std::queue<RSWebsocketProtoMsg::Ptr> send_buffer_;
  std::vector<std::shared_ptr<std::thread>> send_threads_;

protected:
  std::condition_variable recv_buffer_cond_;
  std::mutex recv_buffer_mtx_;
  std::queue<RSWebsocketProtoMsg::Ptr> recv_buffer_;
  std::shared_ptr<std::thread> recv_thread_;

protected:
  std::mutex recv_callback_mtx_;
  std::map<std::string, std::vector<RS_SOCKET_RECV_CALLBACK>> recv_callbacks_;
  std::mutex exception_callback_mtx_;
  std::vector<RS_SOCKET_EXCEPTION_CALLBACK> exception_callbacks_;

protected:
  robosense::rs_hmi::config::SocketTaskConfig socket_task_config_;
};

} // namespace hmi
} // namespace rs_hmi
} // namespace robosense

#endif // RSSOCKETROLE_H