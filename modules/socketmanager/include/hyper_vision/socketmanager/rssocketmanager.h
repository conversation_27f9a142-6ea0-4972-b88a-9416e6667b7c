#ifndef RSWEBSOCKETMANAGER_H
#define RSWEBSOCKETMANAGER_H

#include "hyper_vision/socketmanager/rsdgramcomm.h"
#include "hyper_vision/socketmanager/rsmulticastcomm.h"
#include "hyper_vision/socketmanager/rswebsocketclient.h"
#include "hyper_vision/socketmanager/rswebsocketserver.h"

namespace robosense {
namespace rs_hmi {
namespace hmi {

class RSSocketManager {
public:
  using Ptr = std::shared_ptr<RSSocketManager>;
  using ConstPtr = std::shared_ptr<const RSSocketManager>;

public:
  using RS_WEBSOCKET_BUFFERED_SIZE_CALLBACK =
      std::function<void(const std::map<RSSocketRole::Ptr, size_t> &)>;

public:
  RSSocketManager();

  ~RSSocketManager();

public:
  int init(const robosense::rs_hmi::config::Config &config,
           const RS_WEBSOCKET_BUFFERED_SIZE_CALLBACK &callback);

  int start();

  int stop();

  RSSocketRole::Ptr getWebsocketRole(const std::string &task_socket_key);

private:
  int init();

  int updateEnableSocketRoleKeys();

  int updateEnableSocketRoleKeys(
      const robosense::rs_hmi::config::HmiTaskSocketConfig
          &hmiTaskSocketConfig);

  int addWebsocketRole(
      const robosense::rs_hmi::config::SocketTaskConfig &taskConfig);

  int addUdpP2PRole(
      const robosense::rs_hmi::config::SocketTaskConfig &taskConfig);

  int addUdpMulticastRole(
      const robosense::rs_hmi::config::SocketTaskConfig &taskConfig);

  void websocketCallback(
      const int error_code, const std::string &error_info,
      const robosense::rs_hmi::config::WebsocketEndPointConfig &endPointConfig);

  void websocketBufferCheckWorkThread();

private:
  robosense::rs_hmi::config::Config config_;
  std::map<std::string, RSSocketRole::Ptr> socketRoles_;

  std::set<std::string> websocketRoleKeys_;
  std::set<std::string> udpMulticastRoleKeys_;
  std::set<std::string> udpP2PRoleKeys_;

  std::set<std::string> enableSocketRoleKeys_;

private:
  bool is_websocket_buffered_size_check_running_ = false;
  std::shared_ptr<std::thread> websocket_buffered_size_check_thread_ = nullptr;
  RS_WEBSOCKET_BUFFERED_SIZE_CALLBACK callback_;
};

} // namespace hmi
} // namespace rs_hmi
} // namespace robosense

#endif // RSWEBSOCKETMANAGER_H