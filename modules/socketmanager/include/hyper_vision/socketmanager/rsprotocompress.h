#ifndef RSPROTOCOMPRESS_H
#define RSPROTOCOMPRESS_H

/***
 * ModuleName: RSProtoCompress
 * Description: Support Protobuffer Message Compress/Decompress
 * Author: hsw
 * Date: 2024-07-30
 *
 * Support Compress Method:
 * - GZIP
 * - ZLIB
 * - LZ4
 * - ZSTD
 *
 */

#include <fstream>
#include <iostream>
#include <memory>
#include <string>

#include <google/protobuf/message.h>

#include "proto/proto_compress.pb.h"

namespace robosense {
namespace proto {
namespace compress {

class RSProtoCompress {
public:
  using Ptr = std::shared_ptr<RSProtoCompress>;
  using ConstPtr = std::shared_ptr<const RSProtoCompress>;

public:
  RSProtoCompress() = default;
  ~RSProtoCompress() = default;

public:
  //压缩
  static bool compressedToFile(
      const google::protobuf::Message &protoMsg, const std::string &outputFile,
      size_t &unCompressMsgSize,
      const robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT
          compressFormat,
      const int compressLevel = 9);

  static bool compressedToFile(
      const std::shared_ptr<google::protobuf::Message> &protoMsgPtr,
      const std::string &outputFile, size_t &unCompressMsgSize,
      const robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT
          compressFormat,
      const int compressLevel = 9);

  static bool compressedToFile(
      const google::protobuf::Message *protoMsgPtr,
      const std::string &outputFile, size_t &unCompressMsgSize,
      const robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT
          compressFormat,
      const int compressLevel = 9);

  static bool compressedToString(
      const google::protobuf::Message &protoMsg, std::string &outputString,
      size_t &unCompressMsgSize,
      const robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT
          compressFormat,
      const int compressLevel = 9);

  static bool compressedToString(
      const std::shared_ptr<google::protobuf::Message> &protoMsgPtr,
      std::string &outputString, size_t &unCompressMsgSize,
      const robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT
          compressFormat,
      const int compressLevel = 9);

  static bool compressedToString(
      const google::protobuf::Message *protoMsgPtr, std::string &outputString,
      size_t &unCompressMsgSize,
      const robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT
          compressFormat,
      const int compressLevel = 9);

public:
  // 解压缩
  static bool decompressedFromFile(
      const std::string &inputFile,
      const robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT
          compressFormat,
      const size_t unCompressMsgSize, google::protobuf::Message &protoMsg);

  static bool decompressedFromFile(
      const std::string &inputFile,
      const robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT
          compressFormat,
      const size_t unCompressMsgSize,
      std::shared_ptr<google::protobuf::Message> &protoMsgPtr);

  static bool decompressedFromFile(
      const std::string &inputFile,
      const robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT
          compressFormat,
      const size_t unCompressMsgSize, google::protobuf::Message *protoMsgPtr);

  static bool decompressedFromString(
      const std::string &inputString,
      const robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT
          compressFormat,
      const size_t unCompressMsgSize, google::protobuf::Message &protoMsg);

  static bool decompressedFromString(
      const std::string &inputString,
      const robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT
          compressFormat,
      const size_t unCompressMsgSize,
      std::shared_ptr<google::protobuf::Message> &protoMsgPtr);

  static bool decompressedFromString(
      const std::string &inputString,
      const robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT
          compressFormat,
      const size_t unCompressMsgSize, google::protobuf::Message *protoMsgPtr);
};

} // namespace compress
} // namespace proto
} // namespace robosense

#endif // RSPROTOCOMPRESS_H
