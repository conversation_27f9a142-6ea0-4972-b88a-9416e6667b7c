
#ifndef RSUDPROLE_H
#define RSUDPROLE_H

#include "hyper_vision/socketmanager/rssocketrole.h"
#include "hyper_vision/socketmanager/rsudpcommon.h"

namespace robosense {
namespace rs_hmi {
namespace hmi {

class RSUdpRole : public RSSocketRole {
public:
  using Ptr = std::shared_ptr<RSUdpRole>;
  using ConstPtr = std::shared_ptr<const RSUdpRole>;

public:
  RSUdpRole() = default;

  virtual ~RSUdpRole() = default;

public:
  virtual int
  init(const robosense::rs_hmi::config::SocketTaskConfig &config) = 0;

  virtual int start() = 0;

  virtual int stop() = 0;

  virtual int sendProtoMessage(const RSWebsocketProtoMsg::Ptr &msgPtr);

  virtual void updateUdpControlConfig(
      const robosense::rs_hmi::config::UdpControlConfig &udpControlConfig) {
    if (_udpControlUtil) {
      _udpControlUtil->updateUdpControlConfig(udpControlConfig);
    }
  }

  virtual int
  outgoingMsgSize(size_t &totalOutgoingMsgSize,
                  std::map<std::string, size_t> &connectionOutgoingSizes) {
    totalOutgoingMsgSize = 0;
    connectionOutgoingSizes.clear();
    return 0;
  }

  virtual uint32_t getUdpSendBufferSize() {
    std::lock_guard<std::mutex> lg(send_buffer_mtx_);
    return send_buffer_.size();
  }

protected:
  int initSndComm();

  int initRcvComm();

  int initBothComm();

  void updateRcvAvailIdx(const int idx);

  RSBinaryBuffer::Ptr getRcvBinaryCell(int idx);

  int sendMessage(const RSRoboMsgHeader &msgHeader, const char *data,
                  const int length);

  int sendMessage(const RSRoboMsgHeader &msgHeader, const char *data,
                  const int length, const std::string &remote_endpoint);

  int availIdxSize() const { return _sndAvailIdxs.size(); }

protected:
  void sendHandle(error_code ec, int snd_byte,
                  RSBinaryBuffer::Ptr &selectBufferPtr);

  void recvHandle(error_code ec, int rcv_byte,
                  RSBinaryBuffer::Ptr &selectBufferPtr);

  void recvMessage();

  virtual int initSocket() = 0;

  virtual int initDgramBuffer() = 0;

  virtual int initAsynComm() = 0;

  virtual int initAsynCommSnd() = 0;

  virtual int initAsynCommRcv() = 0;

  void processWorkThread();

  void recvMessage(const int recvIdx);

  void ioServiceWorker();

  void udpSendWorkThread();

  void udpRecvWorkThread();

  virtual int sendMessageUtil(const RSWebsocketProtoMsg::Ptr &sendMsg) = 0;

  virtual int sendProtoMessage(const std::string &protoMsgString) = 0;

  RSRoboMsgHeader parseRoboMsgHeader(const char *data, const int length);

protected:
  IOServicePtr _ioServicePtr;
  IOServiceWorkPtr _ioServiceWorkPtr;
  std::vector<std::shared_ptr<std::thread>> _ioServiceThreads;
  unsigned int _frameID;
  unsigned int _curMaxFrameId;
  unsigned int _curOutputMaxFrameId;
  std::unordered_map<unsigned int, RSReceiveMsgBuffer::Ptr> _recvMessages;
  std::mutex _msgMutex;

protected:
  robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT
      _compressFormat;
  uint32_t _compressLevel;

protected:
  uint32_t _udpMaxMsgSize;     // udp最大消息大小
  uint32_t _udpAvailMsgSize;   // udp有效消息大小
  uint32_t _udpBufferCellSize; //

protected:
  uint64_t _udpSendCnt_ = 0;
  uint64_t _udpSendMsgCnt_ = 0;
  uint64_t _udpSendErrorCnt_ = 0;
  uint64_t _udpSendTotalSize_ = 0;

protected:
  uint64_t _udpReceiveCnt_ = 0;
  uint64_t _udpReceiveMsgCnt_ = 0;
  uint64_t _udpReceiveTimeoutCnt_ = 0;
  uint64_t _udpReceiveParseErrorCnt_ = 0;
  uint64_t _udpReceiveTotalSize_ = 0;

protected:
  uint64_t _udpStartTimestampNs_ = 0;

protected:
  uint32_t _udpRecvTimeoutThMs_ = 200;

  uint32_t _udpSndRetryCnt_ = 10;
  uint32_t _udpSndRetryThMs_ = 2;

protected:
  COMMUNICATION_IO_TYPE _type;
  std::mutex _mutexSocket;
  UdpSocketPtr _udpSocketPtr;
  udp_endpoint _defaultRemoteEndPoint;

protected:
  // 组播发送/接收 + UDP P2P 接收使用的缓冲区
  std::mutex _recvBufferMtx;
  std::vector<int> _recvAvailIdxs;
  std::vector<int> _recvProcessIdxs;
  std::condition_variable _recvAvailIdxsCond;
  std::condition_variable _recvProcessIdxsCond;
  std::vector<udp_endpoint> _recvRemoteEndPoints;
  std::vector<RSBinaryBuffer::Ptr> _recvBinaryBuffers;
  std::mutex _mutexProcess;
  std::vector<std::shared_ptr<std::thread>> _recvProcessThreads;
  unsigned int _recvMsgFrameCnt;

protected:
  // UDP 2P2 发送使用的缓冲区
  std::mutex _sndBufferMtx;
  std::vector<int> _sndAvailIdxs;
  std::condition_variable _sndAvailIdxsCond;
  std::vector<RSBinaryBuffer::Ptr> _sndBinaryBuffers;
  unsigned int _sndMsgFrameCnt;

protected:
  RSUdpControlUtil::Ptr _udpControlUtil;
};

} // namespace hmi
} // namespace rs_hmi
} // namespace robosense

#endif // RSUDPROLE_H