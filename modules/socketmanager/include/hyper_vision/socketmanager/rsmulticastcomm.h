#ifndef RSMULTICASTCOMM_H
#define RSMULTICASTCOMM_H

#include "hyper_vision/socketmanager/rsudprole.h"

namespace robosense {
namespace rs_hmi {
namespace hmi {

class RSMulticastComm : public RSUdpRole {
public:
  using Ptr = std::shared_ptr<RSMulticastComm>;
  using ConstPtr = std::shared_ptr<const RSMulticastComm>;

public:
  RSMulticastComm();

  virtual ~RSMulticastComm();

public:
  int init(const robosense::rs_hmi::config::SocketTaskConfig &config) override;

  int start() override;

  int stop() override;

protected:
  virtual int initSocket() override;

  virtual int initDgramBuffer() override;

  virtual int initAsynComm() override;

  virtual int initAsynCommSnd() override;

  virtual int initAsynCommRcv() override;

  virtual int sendMessageUtil(const RSWebsocketProtoMsg::Ptr &sendMsg) override;

  virtual int sendProtoMessage(const std::string &protoMsgString) override;

protected:
  robosense::rs_hmi::config::UdpMulticastEndPointConfig _commConfig;
};

} // namespace hmi
} // namespace rs_hmi
} // namespace robosense

#endif // RSMULTICASTCOMM_H
