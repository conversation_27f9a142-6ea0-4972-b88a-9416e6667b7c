#ifndef RSDGRAMCOMM_H
#define RSDGRAMCOMM_H

#include "hyper_vision/socketmanager/rsudprole.h"

namespace robosense {
namespace rs_hmi {
namespace hmi {

class RSDgramComm : public RSUdpRole {
public:
  using Ptr = std::shared_ptr<RSDgramComm>;
  using ConstPtr = std::shared_ptr<const RSDgramComm>;

public:
  RSDgramComm();

  virtual ~RSDgramComm();

public:
  int init(const robosense::rs_hmi::config::SocketTaskConfig &config) override;

  int start() override;

  int stop() override;

protected:
  int initSocket() override;

  int initDgramBuffer() override;

  int initAsynComm() override;

  int initAsynCommSnd() override;

  int initAsynCommRcv() override;

  virtual int sendMessageUtil(const RSWebsocketProtoMsg::Ptr &sendMsg) override;

  virtual int sendProtoMessage(const std::string &protoMsgString) override;

  int sendProtoMessage(const std::string &protoMsgString,
                       const std::vector<std::string> &udp_remote_ips);

protected:
  robosense::rs_hmi::config::UdpP2PEndPointConfig _commConfig;
};

} // namespace hmi
} // namespace rs_hmi
} // namespace robosense

#endif // RSDGRAMCOMM_H
