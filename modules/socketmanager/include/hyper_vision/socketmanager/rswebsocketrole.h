#ifndef RSWEBSOCKETROLE_H
#define RSWEBSOCKETROLE_H

#include "hyper_vision/socketmanager/rssocketrole.h"
#include "websocketpp/client.hpp"
#include "websocketpp/config/asio_client.hpp"
#include "websocketpp/config/asio_no_tls.hpp"
#include "websocketpp/server.hpp"

namespace robosense {
namespace rs_hmi {
namespace hmi {

class RSWebsocketRole : public RSSocketRole {
public:
  using Ptr = std::shared_ptr<RSWebsocketRole>;
  using ConstPtr = std::shared_ptr<const RSWebsocketRole>;

public:
  RSWebsocketRole();

  virtual ~RSWebsocketRole();

public:
  virtual int
  init(const robosense::rs_hmi::config::SocketTaskConfig &config) = 0;

  virtual int start() = 0;

  virtual int stop() = 0;

  virtual int sendProtoMessage(const RSWebsocketProtoMsg::Ptr &msgPtr);

  virtual int
  addWebsocketControl(const RSWebsocketSendControl &websocketSendControl);

  virtual int
  outgoingMsgSize(size_t &totalOutgoingMsgSize,
                  std::map<std::string, size_t> &connectionOutgoingSizes);

  virtual uint32_t getUdpSendBufferSize() { return 0; }

  virtual void updateUdpControlConfig(
      const robosense::rs_hmi::config::UdpControlConfig &udpControlConfig) {
    (void)(udpControlConfig);
  }

protected:
  void runLocalExceptCallback(const int error_code,
                              const std::string &error_info);
  void websocketRecvWorkThread();
  void websocketSendWorkThread();
  virtual int sendProtoMessage(const std::string &protoMsgString);

protected:
  std::mutex handle_mtx_;

protected:
  std::vector<std::shared_ptr<std::thread>> running_threads_;

protected:
  robosense::rs_hmi::config::WebsocketEndPointConfig websocket_endpoint_config_;
  std::map<std::string, RSWebsocketSendControl> websocket_send_controls_;
};

} // namespace hmi
} // namespace rs_hmi
} // namespace robosense

#endif // RSWEBSOCKETROLE_H