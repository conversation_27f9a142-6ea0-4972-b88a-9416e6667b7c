#ifndef RSPROTOPARSER_H
#define RSPROTOPARSER_H

#include "hyper_vision/socketmanager/rsprotocompress.h"
#include "proto/acviewer.pb.h"
#include "proto/config.pb.h"
#include "proto/hmi.pb.h"
#include "rally/utils/logger/logger.h"

// 是否进行数据调试
#define ENABLE_SERIALIZE_COMPRESS_DEBUG (0)
#define UINT64_T_TIMESTAMP_NS                                                  \
  static_cast<uint64_t>(                                                       \
      std::chrono::time_point_cast<std::chrono::nanoseconds>(                  \
          std::chrono::system_clock::now())                                    \
          .time_since_epoch()                                                  \
          .count())

namespace robosense {
namespace rs_hmi {
namespace hmi {

enum class RS_WEBSOCKET_MESSAGE_IO_TYPE : int {
  RS_WEBSOCKET_UNKNOWN = -1,
  RS_WEBSOCKET_RECEIVE = 0,
  RS_WEBSOCKET_SEND = 1,
};

class RSWebsocketProtoMsg {
public:
  using Ptr = std::shared_ptr<RSWebsocketProtoMsg>;
  using ConstPtr = std::shared_ptr<const RSWebsocketProtoMsg>;

public:
  RSWebsocketProtoMsg() { reset(); }

  void reset() {
    message_topic_ = "";
    message_type_ = "";
    message_io_type_ = RS_WEBSOCKET_MESSAGE_IO_TYPE::RS_WEBSOCKET_UNKNOWN;
    message_ = nullptr;
    udp_p2p_remote_ips_.clear();
  }

public:
  std::string message_topic_;
  std::string message_type_;
  RS_WEBSOCKET_MESSAGE_IO_TYPE message_io_type_;
  std::shared_ptr<::google::protobuf::Message> message_;
  std::vector<std::string> udp_p2p_remote_ips_;
};

class RSWebsocketSendControl {
public:
  using Ptr = std::shared_ptr<RSWebsocketSendControl>;
  using ConstPtr = std::shared_ptr<const RSWebsocketSendControl>;

public:
  enum class RS_WEBSOCKET_SEND_CONTROL_OP {
    RS_WEBSOCKET_SEND_CONTROL_NON = 0,
    RS_WEBSOCKET_SEND_CONTROL_CLEAR,
    RS_WEBSOCKET_SEND_CONTROL_LOSS,
    RS_WEBSOCKET_SEND_CONTROL_LOSS2,
  };

public:
  RSWebsocketSendControl() { reset(); }

  RSWebsocketSendControl(
      const robosense::rs_hmi::config::WebsocketSendControlConfig &config) {
    reset();
    message_type_ = config.message_type();
    enable_loss_ = config.enable_loss();
    loss_buffer_size_ = config.loss_buffer_size();
    enable_loss2_ = config.enable_loss2();
    loss_buffer_size2_ = config.loss_buffer_size2();
    enable_clear_ = config.enable_clear();
    clear_buffer_size_ = config.clear_buffer_size();
    loss_gap_size_ = config.loss_gap_size();
    loss_gap_size2_ = config.loss_gap_size2();
  }

  void reset() {
    message_type_ = "";
    enable_loss_ = false;
    loss_buffer_size_ = 0;
    enable_loss2_ = false;
    loss_buffer_size2_ = 0;
    enable_clear_ = false;
    clear_buffer_size_ = 0;
    cur_buffer_size_ = 0;
    loss_gap_size_ = 0;
    loss_gap_size2_ = 0;
    loss_cal_ = 0;
    pre_op_ = RS_WEBSOCKET_SEND_CONTROL_OP::RS_WEBSOCKET_SEND_CONTROL_NON;
  }

  void printWebsocketSendControl() const {
    AINFO << "websocket send control: message_type = " << message_type_
          << ", enable_loss = " << enable_loss_
          << ", enable_loss2 = " << enable_loss2_
          << ", loss_buffer_size = " << loss_buffer_size_
          << ", loss_buffer_size2 = " << loss_buffer_size2_
          << ", enable_clear = " << enable_clear_
          << ", clear_buffer_size = " << clear_buffer_size_
          << ", loss_gap_size = " << loss_gap_size_
          << ", loss_gap_size2 = " << loss_gap_size2_;
  }

  RS_WEBSOCKET_SEND_CONTROL_OP checkWebsocketSend() {
    RS_WEBSOCKET_SEND_CONTROL_OP op =
        RS_WEBSOCKET_SEND_CONTROL_OP::RS_WEBSOCKET_SEND_CONTROL_NON;
    if (enable_clear_ || enable_loss2_ || enable_loss_) {
      if (enable_clear_ && cur_buffer_size_ >= clear_buffer_size_) {
        op = RS_WEBSOCKET_SEND_CONTROL_OP::RS_WEBSOCKET_SEND_CONTROL_CLEAR;
        loss_cal_ = 0;
        pre_op_ = op;
      } else if (enable_loss2_ && cur_buffer_size_ >= loss_buffer_size2_) {
        if (pre_op_ !=
            RS_WEBSOCKET_SEND_CONTROL_OP::RS_WEBSOCKET_SEND_CONTROL_LOSS2) {
          loss_cal_ = 0;
        }
        if (loss_cal_ % loss_gap_size2_ == 0) {
          op = RS_WEBSOCKET_SEND_CONTROL_OP::RS_WEBSOCKET_SEND_CONTROL_NON;
        } else {
          op = RS_WEBSOCKET_SEND_CONTROL_OP::RS_WEBSOCKET_SEND_CONTROL_LOSS2;
        }
        ++loss_cal_;
        pre_op_ = RS_WEBSOCKET_SEND_CONTROL_OP::RS_WEBSOCKET_SEND_CONTROL_LOSS2;
      } else if (enable_loss_ && cur_buffer_size_ >= loss_buffer_size_) {
        if (pre_op_ !=
            RS_WEBSOCKET_SEND_CONTROL_OP::RS_WEBSOCKET_SEND_CONTROL_LOSS) {
          loss_cal_ = 0;
        }
        if (loss_cal_ % loss_gap_size_ == 0) {
          op = RS_WEBSOCKET_SEND_CONTROL_OP::RS_WEBSOCKET_SEND_CONTROL_NON;
        } else {
          op = RS_WEBSOCKET_SEND_CONTROL_OP::RS_WEBSOCKET_SEND_CONTROL_LOSS;
        }
        ++loss_cal_;
        pre_op_ = RS_WEBSOCKET_SEND_CONTROL_OP::RS_WEBSOCKET_SEND_CONTROL_LOSS;
      } else {
        loss_cal_ = 0;
        pre_op_ = op;
      }
    }

    return op;
  }

  void minusBufferSize() { --cur_buffer_size_; }

  void addBufferSize() { ++cur_buffer_size_; }

  void clearBufferSize() { cur_buffer_size_ = 0; }

public:
  RS_WEBSOCKET_SEND_CONTROL_OP pre_op_;
  std::string message_type_;
  bool enable_loss_;
  uint32_t loss_buffer_size_;
  bool enable_loss2_;
  uint32_t loss_buffer_size2_;
  bool enable_clear_;
  uint32_t clear_buffer_size_;
  uint32_t cur_buffer_size_;
  uint32_t loss_gap_size_;
  uint32_t loss_gap_size2_;
  uint32_t loss_cal_;
};

class RSProtoParser {
public:
  using Ptr = std::shared_ptr<RSProtoParser>;
  using ConstPtr = std::shared_ptr<const RSProtoParser>;

public:
  RSProtoParser() = default;

  ~RSProtoParser() = default;

public:
  // HMI消息反序列化
  static RSWebsocketProtoMsg::Ptr
  protoParseMessage(const std::string &protoData);

  // HMI消息序列化
  static bool protoPackageMessage(
      const RSWebsocketProtoMsg::Ptr &msgPtr,
      const robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT
          compressFormat,
      const uint32_t compressLevel, std::string &msgProtoString);
};

} // namespace hmi
} // namespace rs_hmi
} // namespace robosense

#endif // RSPROTOPARSER_H
