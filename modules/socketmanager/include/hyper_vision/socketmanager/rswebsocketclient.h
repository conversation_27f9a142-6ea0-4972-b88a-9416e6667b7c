#pragma once

#include "hyper_vision/socketmanager/rswebsocketrole.h"

namespace robosense {
namespace rs_hmi {
namespace hmi {

class RSWebsocketClient : public RSWebsocketRole {
public:
  using RS_WEBSOCKET_CLIENT_ROLE =
      websocketpp::client<websocketpp::config::asio_client>;

public:
  using Ptr = std::shared_ptr<RSWebsocketClient>;
  using ConstPtr = std::shared_ptr<const RSWebsocketClient>;

public:
  RSWebsocketClient();
  ~RSWebsocketClient();

public:
  int init(const robosense::rs_hmi::config::SocketTaskConfig &config) override;

  int start() override;

  int stop() override;

  int outgoingMsgSize(
      size_t &totalOutgoingMsgSize,
      std::map<std::string, size_t> &connectionOutgoingSizes) override;

protected:
  int sendProtoMessage(const std::string &message) override;

private:
  void websocketWorkThread();
  void websocketConnectionThread();

private:
  void openHandle(websocketpp::connection_hdl hld);
  void failHandle(websocketpp::connection_hdl hld);
  void closeHandle(websocketpp::connection_hdl hld);
  void recvProtoMessage(websocketpp::connection_hdl hdl,
                        RS_WEBSOCKET_CLIENT_ROLE::message_ptr msg);

private:
  RS_WEBSOCKET_CLIENT_ROLE::connection_ptr connection_;
  RS_WEBSOCKET_CLIENT_ROLE client_;
  std::shared_ptr<std::thread> connectWorkThread_;
  bool is_connection_ = false;
};

} // namespace hmi
} // namespace rs_hmi
} // namespace robosense
