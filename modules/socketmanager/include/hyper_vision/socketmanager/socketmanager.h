#ifndef SOCKETMANAGER_H
#define SOCKETMANAGER_H

#include "hyper_vision/codec/jpegcoder.h"
#include "hyper_vision/common/common.h"
#include "hyper_vision/socketmanager/rssocketmanager.h"

namespace robosense {
namespace socket {

using namespace robosense::rs_hmi::hmi;

class SocketManager {
public:
  using Ptr = std::shared_ptr<SocketManager>;
  using ConstPtr = std::shared_ptr<const SocketManager>;

public:
  using RS_SOCKET_REQUEST_CALLBACK = std::function<int(
      const std::shared_ptr<robosense::acviewer_msgs::RSRequest> &reqPtr,
      std::shared_ptr<robosense::acviewer_msgs::RSResponse> &respPtr)>;

public:
  SocketManager();
  ~SocketManager();

public:
  int init(const std::string &pbConfigFilePath,
           const RS_SOCKET_REQUEST_CALLBACK &requestCallback);
  int start();
  int stop();
  int sendRenderData(const TagPosition *pPose, const TagPointCloud *pCloud,
                     const TagPointCloud *pSlamCloud,
                     const TagTriangleFacet *pTriangle,
                     const Tag_RgbImage *pRgbImage,
                     const Tag_DepthImage *pDepthImage);

private:
  int init();
  int loadPdConfigFile();
  int initSocketManager();
  int initSockets();
  int initSendThread();
  void websocketBufferCallback(
      const std::map<RSSocketRole::Ptr, size_t> &socketBufferMapper);
  void websocketCallback(const RSWebsocketProtoMsg::Ptr &websocketMsgPtr);
  int registerSocketCallback(
      const robosense::rs_hmi::config::HmiTaskSocketConfig &hmiTaskSocketConfig,
      const RS_SOCKET_RECV_CALLBACK &socketRecvCallback);
  bool updateUdpControlConfig(
      const robosense::acviewer_msgs::UdpControlConfig &udpControlConfig);
  bool checkIsNewUdpControlConfig(
      const robosense::acviewer_msgs::UdpControlConfig &udpControlConfig);
  void sendRenderWorkThread();

private:
  int toProtoMessage(const Tag_Position &msg,
                     robosense::acviewer_msgs::RSPositon &protoMsg);

  int toProtoMessage(const Tag_PointCloud &msg,
                     robosense::acviewer_msgs::RSPointCloud &protoMsg);

  int toProtoMessage_Short(const Tag_PointCloud &msg,
                           const bool isSlamPointCloud,
                           const bool isUseShortIPoint,
                           robosense::acviewer_msgs::RSPointCloud &protoMsg);

  int toProtoMessage(const Tag_TriangleFacet &msg,
                     robosense::acviewer_msgs::RSTrangleFacet &protoMsg);

  int toProtoMessage(const Tag_RgbImage &msg,
                     robosense::acviewer_msgs::RSRgbJpeg &protoMsg);

  int toProtoMessage(const Tag_DepthImage &msg,
                     robosense::acviewer_msgs::RSDepthImage &protoMsg);

private:
  robosense::rs_hmi::config::Config config_;
  std::string pd_config_file_path_;
  RS_SOCKET_REQUEST_CALLBACK request_callback_;
  RSSocketManager::Ptr socket_manager_ptr_;
  bool is_init_ = false;
  bool is_start_ = false;

private:
  std::map<std::string, RSSocketRole::Ptr> socketRoles_;
  // Websocket CMD
  RSSocketRole::Ptr websocketCmdPtr_ = nullptr;
  std::string websocket_cmd_socket_key_;

  // Websocket
  RSSocketRole::Ptr websocketPtr_ = nullptr;
  std::string websocket_socket_key_;

  // UDP Multicast（Double UDP Multicast）
  RSSocketRole::Ptr multicastPtr = nullptr;
  std::string udp_multicast_socket_key_;

  RSSocketRole::Ptr double_multicastPtr = nullptr;
  std::string udp_double_multicast_socket_key_;

  // UDP P2P
  RSSocketRole::Ptr dgramManagerPtr = nullptr;
  std::string udp_p2p_socket_key_;
  robosense::rs_hmi::config::UdpControlConfig cur_udp_control_config_;

  // Communication Type
  robosense::acviewer_msgs::RS_RENDER_COMMUNICATION_TYPE render_comm_type_ =
      robosense::acviewer_msgs::RS_RENDER_COMM_WEBSOCKET;

private:
  std::atomic<bool> is_enable_render_ = true;
  robosense::jpeg::JpegCoder::Ptr jpeg_coder_ptr_ = nullptr;
  std::vector<unsigned char> jpeg_encode_buffer_;

  bool is_send_running_ = false;
  std::shared_ptr<std::thread> send_thread_ptr_ = nullptr;
  std::condition_variable send_cond_;
  std::mutex send_mtx_;
  std::queue<std::shared_ptr<robosense::acviewer_msgs::RSRender>> send_buffer_;

private:
  // 优化通信带宽的优化配置
  std::vector<Tag_PointShort> slam_pointcloud_short_buffer_;
  std::vector<Tag_PointShort> pointcloud_short_buffer_;
  std::vector<Tag_PointShortI> pointcloud_shorti_buffer_;
  const bool RS_ENABLE_SEND_POINT_SHORT = true; // 是否使用简化的点云格式
  const bool RS_ENABLE_SEND_POINT_SHORTI = true; // 是否使用简化的整型点云格式
  const int32_t RS_CAMERA_DOWNSAMPLE_FACTOR = 3; // 图像采样率
  const float RS_CROP_X_BOTTOM = 0; // 使用简化的整型点云时的过滤范围
  const float RS_CROP_X_TOP = 70;
  const float RS_CROP_Y_BOTTOM = -70;
  const float RS_CROP_Y_TOP = 70;
  const float RS_CROP_Z_BOTTOM = -70;
  const float RS_CROP_Z_TOP = 70;

private:
  bool enable_websocket_buffer_size_ = false;
  uint32_t websocket_buffer_size_ = 2097152;
  uint32_t low_websocket_buffer_size_ = 524288;
  std::atomic<uint64_t> current_websocket_buffer_size_ = 0;
  std::atomic<bool> current_websocket_loss_ = false;

  uint32_t udp_2_sample_1_buffer_size_ = 3;
  uint32_t udp_3_sample_1_buffer_size_ = 5;
  uint32_t udp_loss_all_buffer_size_ = 10;
  uint32_t udp_send_frame_cnt_ = 0;

  uint32_t max_send_render_buffer_size_ = 15;

private:
  const std::string RS_WEBSOCKET_TOPIC_REQUEST = "/hmi/request";
  const std::string RS_WEBSOCKET_TOPIC_RESPONSE = "/hmi/response";
  const std::string RS_WEBSOCKET_TOPIC_RENDERSWITCH = "/hmi/renderswitch";
  const std::string RS_WEBSOCKET_TOPIC_RENDER = "/hmi/render";

private:
  // RGB Image是否 JPEG压缩
  const bool RS_RENDER_RGBIMAGE_COMPRESS = true;
  const int32_t RS_RENDER_RGBIMAGE_JPEG_QUALITY = 70;
};

} // namespace socket
} // namespace robosense

#endif // SOCKETMANAGER_H