#ifndef RSUDPCOMMON_H
#define RSUDPCOMMON_H

#include "hyper_vision/socketmanager/rsprotoparser.h"
#include <boost/asio.hpp>

namespace robosense {
namespace rs_hmi {
namespace hmi {

// 通信相关的类型和宏
typedef boost::asio::ip::udp::socket udp_socket;
typedef boost::asio::ip::udp::endpoint udp_endpoint;
typedef boost::asio::ip::tcp::socket tcp_socket;
typedef boost::asio::ip::tcp::endpoint tcp_endpoint;
typedef boost::asio::ip::tcp::acceptor tcp_acceptor;
typedef boost::asio::deadline_timer deadline_timer;
typedef boost::asio::io_service io_service;
typedef io_service::work io_service_work;
typedef boost::system::error_code error_code;
typedef std::shared_ptr<io_service> IOServicePtr;
typedef std::shared_ptr<deadline_timer> IOServiceTimerPtr;
typedef std::shared_ptr<io_service_work> IOServiceWorkPtr;
typedef std::shared_ptr<udp_socket> UdpSocketPtr;
typedef std::shared_ptr<tcp_socket> TcpSocketPtr;
typedef std::shared_ptr<tcp_acceptor> TcpAcceptorPtr;
// 定义函数宏
#define ASIO_BUFFER(X, Y) boost::asio::buffer(X, Y)
#define ASIO_UDP_V4_EMPTY_ENDPOINT boost::asio::ip::udp::v4()
#define ASIO_TCP_V4_EMPTY_ENDPOINT boost::asio::ip::tcp::v4()
#define ASIO_IP_ADDRESS_FROM_STRING(X) boost::asio::ip::address::from_string(X)
#define ASIO_UDP_SOCKET_REUSE_ADDRESS(X)                                       \
  boost::asio::ip::udp::socket::reuse_address(X)
#define ASIO_UDP_SOCKET_SEND_BUFFER_SIZE(X)                                    \
  boost::asio::ip::udp::socket::send_buffer_size(X)
#define ASIO_UDP_SOCKET_RECEIVE_BUFFER_SIZE(X)                                 \
  boost::asio::ip::udp::socket::receive_buffer_size(X)
#define ASIO_TCP_NO_DELAY(X) boost::asio::ip::tcp::no_delay(X)
#define ASIO_TCP_SOCKET_REUSE_ADDRESS(X)                                       \
  boost::asio::ip::tcp::socket::reuse_address(X)
#define ASIO_TCP_SOCKET_SEND_BUFFER_SIZE(X)                                    \
  boost::asio::ip::tcp::socket::send_buffer_size(X)
#define ASIO_TCP_SOCKET_RECEIVE_BUFFER_SIZE(X)                                 \
  boost::asio::ip::tcp::socket::receive_buffer_size(X)

/**
 *
 * Robosense Data Endian Type
 *
 */
enum class RS_DATA_ENDIAN_TYPE : int {
  RS_DATA_BIG_ENDIAN = 0,
  RS_DATA_LITTLE_ENDIAN
};

/**
 *
 * Robosense Communicater Type
 *
 */
enum class COMMUNICATION_IO_TYPE : int {
  COMMUNICATION_IO_SEND = 0,
  COMMUNICATION_IO_RECV,
  COMMUNICATION_IO_BOTH,
};

// 数据大小端处理
template <typename T> class RSEndian {
public:
  typedef typename std::shared_ptr<RSEndian> Ptr;
  typedef typename std::shared_ptr<const RSEndian> ConstPtr;

public:
  RSEndian() {
    unsigned char *pChar = (unsigned char *)(&magicData);

    if (*pChar == magicBigEndian[0] && (*(pChar + 1)) == magicBigEndian[1] &&
        (*(pChar + 2)) == magicBigEndian[2] &&
        (*(pChar + 3)) == magicBigEndian[3]) {
      m_hostEndianType = RS_DATA_ENDIAN_TYPE::RS_DATA_BIG_ENDIAN;
    } else if (*pChar == magicLittleEndian[0] &&
               (*(pChar + 1)) == magicLittleEndian[1] &&
               (*(pChar + 2)) == magicLittleEndian[2] &&
               (*(pChar + 3)) == magicLittleEndian[3]) {
      m_hostEndianType = RS_DATA_ENDIAN_TYPE::RS_DATA_LITTLE_ENDIAN;
    }
  }

public:
  RS_DATA_ENDIAN_TYPE getHostEndian() { return m_hostEndianType; }

  int toTargetEndianArray(T t, void *pArray, unsigned int maxSize,
                          RS_DATA_ENDIAN_TYPE dstEndianType) {
    unsigned int tSize = sizeof(T);

    if (maxSize < tSize || pArray == nullptr) {
      return -1;
    }

    // 保存副本
    T val = t;

    if (m_hostEndianType != dstEndianType) {
      char *pData = (char *)(&val);
      unsigned int tSize_h = tSize / 2;

      for (unsigned int idx = 0; idx < tSize_h; ++idx) {
        char tmp = pData[idx];
        unsigned int swapIdx = tSize - idx - 1;
        pData[idx] = pData[swapIdx];
        pData[swapIdx] = tmp;
      }
    }

    memcpy(pArray, &val, tSize);

    return 0;
  }

  int toHostEndianValue(T &t, const void *pArray, unsigned int maxSize,
                        RS_DATA_ENDIAN_TYPE srcEndianType) {
    unsigned int tSize = sizeof(T);
    if (maxSize < tSize || pArray == nullptr || tSize > 128) {
      return -1;
    }

    if (srcEndianType != m_hostEndianType) {
      // 保存副本
      memcpy(byteBuffer, pArray, tSize);

      char *pData = (char *)(byteBuffer);
      unsigned int tSize_h = tSize / 2;

      for (unsigned int idx = 0; idx < tSize_h; ++idx) {
        char tmp = pData[idx];
        unsigned int swapIdx = tSize - idx - 1;
        pData[idx] = pData[swapIdx];
        pData[swapIdx] = tmp;
      }

      memcpy(&t, byteBuffer, tSize);
    } else {
      memcpy(&t, pArray, tSize);
    }

    return 0;
  }

private:
  RS_DATA_ENDIAN_TYPE m_hostEndianType;

private:
  const unsigned int magicData = 0xA1B2C3D4;
  const unsigned char magicBigEndian[4] = {
      (unsigned char)0xA1, (unsigned char)0xB2, (unsigned char)0xC3,
      (unsigned char)0xD4};
  const unsigned char magicLittleEndian[4] = {
      (unsigned char)0xD4, (unsigned char)0xC3, (unsigned char)0xB2,
      (unsigned char)0xA1};
  // 对于内建类型足够大
  char byteBuffer[128];
};

// 发送消息头
struct alignas(8) RSRoboMsgHeader {
public:
  unsigned int msgFrameId;
  unsigned int msgTotalLen;
  unsigned int msgLocalLen;
  unsigned int msgOffset; // 消息索引，用于分割消息的归属标识

public:
  RSRoboMsgHeader() { reset(); }

  int toTargetEndianArray(unsigned char *data, const int maxLen,
                          RS_DATA_ENDIAN_TYPE targetType) {
    if ((size_t)(maxLen) < sizeof(RSRoboMsgHeader)) {
      return -1;
    }

    int offset = 0;

    RSEndian<unsigned int> uint32Endian;

    uint32Endian.toTargetEndianArray(msgFrameId, data + offset, maxLen - offset,
                                     targetType);
    offset += sizeof(unsigned int);

    uint32Endian.toTargetEndianArray(msgTotalLen, data + offset,
                                     maxLen - offset, targetType);
    offset += sizeof(unsigned int);

    uint32Endian.toTargetEndianArray(msgLocalLen, data + offset,
                                     maxLen - offset, targetType);
    offset += sizeof(unsigned int);

    uint32Endian.toTargetEndianArray(msgOffset, data + offset, maxLen - offset,
                                     targetType);
    offset += sizeof(unsigned int);

    return offset;
  }

  int toHostEndianValue(const char *data, const int dataLen,
                        RS_DATA_ENDIAN_TYPE srcType) {
    if ((size_t)(dataLen) < sizeof(RSRoboMsgHeader)) {
      return -1;
    }

    // Re-initial
    reset();

    int offset = 0;

    RSEndian<unsigned int> uint32Endian;

    uint32Endian.toHostEndianValue(msgFrameId, data + offset, dataLen - offset,
                                   srcType);
    offset += sizeof(unsigned int);

    uint32Endian.toHostEndianValue(msgTotalLen, data + offset, dataLen - offset,
                                   srcType);
    offset += sizeof(unsigned int);

    uint32Endian.toHostEndianValue(msgLocalLen, data + offset, dataLen - offset,
                                   srcType);
    offset += sizeof(unsigned int);

    uint32Endian.toHostEndianValue(msgOffset, data + offset, dataLen - offset,
                                   srcType);
    offset += sizeof(unsigned int);

    return 0;
  }

  void reset() {
    msgFrameId = 0;
    msgTotalLen = 0;
    msgLocalLen = 0;
    msgOffset = 0;
  }

  void printInfo(std::ostream &ofstr) {
    ofstr << "Robosense Message Header ======> " << std::endl;
    ofstr << "msgFrameId       = " << msgFrameId << std::endl;
    ofstr << "msgTotalCnt      = " << msgTotalLen << std::endl;
    ofstr << "msgLocalLen      = " << msgLocalLen << std::endl;
    ofstr << "msgOffset        = " << msgOffset << std::endl;
  }
};

// 缓冲区
class RSBinaryBuffer {
public:
  typedef std::shared_ptr<RSBinaryBuffer> Ptr;
  typedef std::shared_ptr<const RSBinaryBuffer> ConstPtr;

public:
  RSBinaryBuffer(const int index, const int siz)
      : idx(index), content(0), send(0), size(siz) {
    buffer.resize(size, '\0');
  }

  void initWithData(const int cont, const int snd, const char *data,
                    const RSRoboMsgHeader &msgHeader) {
    // std::cout << "initWidthData cont = " << cont << std::endl;
    content = cont + sizeof(msgHeader);
    send = snd;

    if (content > size) {
      size = (content / 16 + 1) * 16;
      buffer.resize(size, '\0');
    }

    memcpy(buffer.data(), &msgHeader, sizeof(RSRoboMsgHeader));

    if (cont > 0 && data != nullptr) {
      memcpy(buffer.data() + sizeof(msgHeader), data, cont);
    }
  }

  void initWithData(const int cont, const int snd, const char *data) {
    content = cont;
    send = snd;

    if (content > size) {
      size = (content / 16 + 1) * 16;
      buffer.resize(size, '\0');
    }

    if (cont > 0 && data != nullptr) {
      memcpy(buffer.data(), data, cont);
    }
  }

  void initWithoutData(const int cont, const int snd) {
    content = cont;
    send = snd;
  }

  void update(const int snd) { send += snd; }

  int totalSize() { return size; }

  bool isSndSuccess() { return (send == content); }

  int index() { return idx; }

  int contentSize() { return content; }

  int sndGap() { return (content - send); }

  char *dataOffset() { return (buffer.data() + send); }

  char *data() { return buffer.data(); }

private:
  int idx;
  int content;
  int send;
  int size;
  std::vector<char> buffer;
};

// 接收消息的缓冲区
class RSReceiveMsgBuffer {
public:
  using Ptr = std::shared_ptr<RSReceiveMsgBuffer>;
  using ConstPtr = std::shared_ptr<const RSReceiveMsgBuffer>;

public:
  RSReceiveMsgBuffer(const uint32_t totalBufferSize,
                     const uint64_t timestampMs) {
    curMsgSize_ = 0;
    totalMsgSize_ = totalBufferSize;
    receiveBuffer_.resize(totalMsgSize_);
    startTimestampMs_ = timestampMs;
  }

  int AddReceiveData(const unsigned char *pData, const uint32_t dataSize,
                     const uint32_t dataOffset) {
    if (dataOffset + dataSize > totalMsgSize_) {
      return -1;
    }

    memcpy(receiveBuffer_.data() + dataOffset, pData, dataSize);
    curMsgSize_ += dataSize;

    return 0;
  }

  bool checkIsComplete() const { return curMsgSize_ == totalMsgSize_; }

  bool checkIsTimeout(const uint64_t curTimestampMs,
                      const uint64_t timeoutMsTh) const {
    if (curTimestampMs >= startTimestampMs_) {
      return ((curTimestampMs - startTimestampMs_) >= timeoutMsTh);
    }
    return false;
  }

public:
  uint64_t startTimestampMs_;
  uint32_t curMsgSize_;
  uint32_t totalMsgSize_;
  std::vector<unsigned char> receiveBuffer_;
};

enum class RS_COMM_ERROR_TYPE : int {
  RS_COMM_ERROR_DEBUG = 0,
  RS_COMM_ERROR_INFO = 0,
  RS_COMM_ERROR_WARNNING,
  RS_COMM_ERROR_ERROR,
  RS_COMM_ERROR_FATAL,
};

using FuncStatusCallback =
    std::function<void(const RS_COMM_ERROR_TYPE, const std::string &)>;

// UDP 发送流量控制
class RSUdpControlUtil {
public:
  using Ptr = std::shared_ptr<RSUdpControlUtil>;
  using ConstPtr = std::shared_ptr<const RSUdpControlUtil>;

public:
  RSUdpControlUtil(
      const robosense::rs_hmi::config::UdpControlConfig &udpControlConfig) {
    _udpControlConfig = udpControlConfig;
  }

public:
  void updateUdpControlConfig(
      const robosense::rs_hmi::config::UdpControlConfig &udpControlConfig) {
    std::lock_guard<std::mutex> lg(_udpControlConfigMtx);
    _udpControlConfig = udpControlConfig;
  }

  void updateDataControl(const uint32_t &totalDataSize) {
    data_size_control_points_.clear();

    robosense::rs_hmi::config::RS_UDP_CONTROL_MODE_TYPE control_mode_type;
    uint32_t udp_total_control_time_ms = 0;
    uint32_t udp_total_control_single_time_ms = 0;
    uint32_t udp_data_control_size = 0;
    {
      std::lock_guard<std::mutex> lg(_udpControlConfigMtx);
      control_mode_type = _udpControlConfig.udp_control_type();
      udp_total_control_time_ms = _udpControlConfig.udp_total_control_time_ms();
      udp_total_control_single_time_ms =
          _udpControlConfig.udp_total_control_single_time_ms();
      udp_data_control_size = _udpControlConfig.udp_data_control_size();
    }

    switch (control_mode_type) {
    case robosense::rs_hmi::config::RS_UDP_CONTROL_TOTAL_CONTROL_TIME: {
      int count = udp_total_control_time_ms / udp_total_control_single_time_ms;
      if (count > 0) {
        data_size_control_points_.resize(count, 0);
        int size = totalDataSize / count;
        for (int i = 1; i <= count; ++i) {
          data_size_control_points_[i - 1] = (i * size);
        }
      }
      break;
    }
    case robosense::rs_hmi::config::RS_UDP_CONTROL_DATA_CONTROL_TIME: {
      int count = totalDataSize / udp_data_control_size;
      if (count > 0) {
        data_size_control_points_.resize(count, 0);
        int pointsCnt = 0;
        for (int i = 1; i * udp_data_control_size <= totalDataSize; ++i) {
          data_size_control_points_[i - 1] = i * udp_data_control_size;
          ++pointsCnt;
        }
        data_size_control_points_.resize(pointsCnt);
      }
      break;
    }
    default: {
      break;
    }
    }
  }

  bool checkDataControl(const uint32_t &dataOffsetSize) {
    bool isEnableControl = false;
    if (data_size_control_points_.empty()) {
      return isEnableControl;
    }

    robosense::rs_hmi::config::RS_UDP_CONTROL_MODE_TYPE control_mode_type;
    uint32_t udp_total_control_single_time_ms = 0;
    uint32_t udp_data_control_time_ms = 0;
    {
      std::lock_guard<std::mutex> lg(_udpControlConfigMtx);
      control_mode_type = _udpControlConfig.udp_control_type();
      udp_total_control_single_time_ms =
          _udpControlConfig.udp_total_control_single_time_ms();
      udp_data_control_time_ms = _udpControlConfig.udp_data_control_time_ms();
    }

    switch (control_mode_type) {
    case robosense::rs_hmi::config::RS_UDP_CONTROL_TOTAL_CONTROL_TIME: {
      if (data_size_control_points_[0] <= dataOffsetSize) {
        std::this_thread::sleep_for(
            std::chrono::milliseconds(udp_total_control_single_time_ms));
        // AINFO_EVERY(10) << "Total Udp Control Sleep: " << dataOffsetSize;
        data_size_control_points_.erase(data_size_control_points_.begin());
        isEnableControl = true;
      }
      break;
    }
    case robosense::rs_hmi::config::RS_UDP_CONTROL_DATA_CONTROL_TIME: {
      if (data_size_control_points_[0] <= dataOffsetSize) {
        std::this_thread::sleep_for(
            std::chrono::milliseconds(udp_data_control_time_ms));
        // AINFO_EVERY(10) << "Data Udp Control Sleep: " << dataOffsetSize;
        data_size_control_points_.erase(data_size_control_points_.begin());
        isEnableControl = true;
      }
      break;
    }
    default: {
      break;
    }
    }
    return isEnableControl;
  }

private:
  std::mutex _udpControlConfigMtx;
  robosense::rs_hmi::config::UdpControlConfig _udpControlConfig;
  std::vector<uint32_t> data_size_control_points_;
};

} // namespace hmi
} // namespace rs_hmi
} // namespace robosense

#endif // RSUDPCOMMON_H