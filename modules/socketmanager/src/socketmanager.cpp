#include "hyper_vision/socketmanager/socketmanager.h"
#include <fstream>
#include <google/protobuf/io/zero_copy_stream.h>
#include <google/protobuf/io/zero_copy_stream_impl.h>
#include <google/protobuf/text_format.h>
#if defined(WIN32) || defined(_WIN32) || defined(__WIN32__)
#include <io.h>
#endif // _WIN32

namespace robosense {
namespace socket {

SocketManager::SocketManager() {
  is_init_ = false;
  is_start_ = false;
}

SocketManager::~SocketManager() { stop(); }

int SocketManager::init(const std::string &pbConfigFilePath,
                        const RS_SOCKET_REQUEST_CALLBACK &requestCallback) {
  if (is_init_) {
    return 0;
  }

  pd_config_file_path_ = pbConfigFilePath;
  if (requestCallback == nullptr) {
    RERROR << "SocketManager: requestCallback is Nullptr !";
    return -1;
  }
  request_callback_ = requestCallback;

  int ret = init();
  if (ret != 0) {
    RERROR << "SocketManager: initial Failed: ret = " << ret;
    return -2;
  }

  return 0;
}

int SocketManager::start() {
  if (!is_init_) {
    RERROR << "SocketManager: Start But Not Initial !";
    return -1;
  } else if (is_start_) {
    return 0;
  }

  if (socket_manager_ptr_ != nullptr) {
    int ret = socket_manager_ptr_->start();
    if (ret != 0) {
      RERROR << "SocketManager: RSSocketManager Start Failed: ret = " << ret;
      return -2;
    }
  } else {
    RERROR << "SocketManager: RSSocketManager is Nullptr !";
    return -3;
  }

  is_start_ = true;

  return 0;
}

int SocketManager::stop() {
  // 释放发送线程
  if (is_send_running_) {
    std::lock_guard<std::mutex> lg(send_mtx_);
    is_send_running_ = false;
    send_cond_.notify_all();
  }
  if (send_thread_ptr_ != nullptr) {
    if (send_thread_ptr_->joinable()) {
      send_thread_ptr_->join();
    }
    send_thread_ptr_.reset();
  }

  // 释放SocketManager
  if (socket_manager_ptr_ != nullptr) {
    int ret = socket_manager_ptr_->stop();
    if (ret != 0) {
      RERROR << "SocketManager: RSSocketManager Stop Failed: ret = " << ret;
      return -1;
    }
    socket_manager_ptr_.reset();
  }

  is_init_ = false;
  is_start_ = false;

  return 0;
}

int SocketManager::sendRenderData(const TagPosition *pPose,
                                  const TagPointCloud *pCloud,
                                  const TagPointCloud *pSlamCloud,
                                  const TagTriangleFacet *pTriangle,
                                  const Tag_RgbImage *pRgbImage,
                                  const Tag_DepthImage *pDepthImage) {
  // 无数据需要发送时
  if (!is_enable_render_.load()) {
    RINFO << "SocketManager: Disable Render !";
    return 0;
  } else if (!pPose && !pCloud && !pSlamCloud && !pTriangle && !pRgbImage &&
             !pDepthImage) {
    RINFO << "SocketManager: No Data Need Send !";
    return 0;
  }

  // 转为proto消息
  std::shared_ptr<robosense::acviewer_msgs::RSRender> renderMsgPtr;
  try {
    renderMsgPtr.reset(new robosense::acviewer_msgs::RSRender());
  } catch (...) {
    RERROR << "SocketManager: Malloc Proto Render Message Failed !";
    return -1;
  }

  if (pPose != nullptr) {
    toProtoMessage(*pPose, *renderMsgPtr->mutable_position());
  }
  // 是否发送删减的点
  if (RS_ENABLE_SEND_POINT_SHORT) {
    if (pCloud != nullptr) {
      toProtoMessage_Short(*pCloud, false, RS_ENABLE_SEND_POINT_SHORTI,
                           *renderMsgPtr->mutable_point_cloud());
    }
    if (pSlamCloud != nullptr) {
      toProtoMessage_Short(*pSlamCloud, true, false,
                           *renderMsgPtr->mutable_point_cloud_slam());
    }
  } else {
    if (pCloud != nullptr) {
      toProtoMessage(*pCloud, *renderMsgPtr->mutable_point_cloud());
    }
    if (pSlamCloud != nullptr) {
      toProtoMessage(*pSlamCloud, *renderMsgPtr->mutable_point_cloud_slam());
    }
  }

  if (pTriangle != nullptr) {
    toProtoMessage(*pTriangle, *renderMsgPtr->mutable_triangle_list());
  }
  if (pRgbImage != nullptr) {
    toProtoMessage(*pRgbImage, *renderMsgPtr->mutable_rgb_jpeg());
  }
  if (pDepthImage != nullptr) {
    toProtoMessage(*pDepthImage, *renderMsgPtr->mutable_depth_image());
  }

  // 加入缓冲区
  if (renderMsgPtr) {
    std::lock_guard<std::mutex> lg(send_mtx_);
    send_buffer_.push(renderMsgPtr);
    // RERROR << "send_buffer_ size = " << send_buffer_.size();
    // 最大缓冲消息数量
    while (send_buffer_.size() >= max_send_render_buffer_size_) {
      send_buffer_.pop();
    }
    send_cond_.notify_one();
  }

  return 0;
}

int SocketManager::init() {
  is_enable_render_.store(true);
  int ret = loadPdConfigFile();
  if (ret != 0) {
    RERROR << "SocketManager: load Pd Config File Failed: ret = " << ret;
    return -1;
  }

  ret = initSocketManager();
  if (ret != 0) {
    RERROR << "SocketManager: Initial Socket Manager Failed: ret = " << ret;
    return -2;
  }

  ret = initSockets();
  if (ret != 0) {
    RERROR << "SocketManager: Initial Sockets Failed: ret = " << ret;
    return -3;
  }

  ret = initSendThread();
  if (ret != 0) {
    RERROR << "SocketManager: Initial Send Thread Failed: ret = " << ret;
    return -4;
  }

  is_init_ = true;

  return 0;
}

int SocketManager::loadPdConfigFile() {
  using google::protobuf::TextFormat;
  using google::protobuf::io::FileInputStream;
  using google::protobuf::io::ZeroCopyInputStream;
  int file_descriptor = open(pd_config_file_path_.c_str(), 0);
  if (file_descriptor < 0) {
    RERROR << "Failed to open file " << pd_config_file_path_
           << " in text mode.";
    return -1;
  }

  ZeroCopyInputStream *input = new FileInputStream(file_descriptor);
  bool success = TextFormat::Parse(input, &config_);
  if (!success) {
    delete input;
    RERROR << "Failed to parse file " << pd_config_file_path_
           << " as text proto.";
    return -2;
  }
  delete input;
  close(file_descriptor);

  RINFO << "SocketManager: config_ = " << config_.DebugString();

  return 0;
}

int SocketManager::initSocketManager() {
  try {
    socket_manager_ptr_.reset(new RSSocketManager());
  } catch (...) {
    RERROR << "SocketManager: Malloc RSSocketManager Failed !";
    return -1;
  }

  const auto &callback = std::bind(&SocketManager::websocketBufferCallback,
                                   this, std::placeholders::_1);
  int ret = socket_manager_ptr_->init(config_, callback);
  if (ret != 0) {
    RERROR << "SocketManager: RSSocketManager Initial Failed !";
    return -2;
  }

  return 0;
}

int SocketManager::initSockets() {
  const auto &render_config = config_.render_config();
  if (!render_config.websocket_task().enable_task()) {
    RWARN << "SocketManager: Render Config Socket Disable !";
    return 0;
  }

  enable_websocket_buffer_size_ = render_config.enable_websocket_buffer();
  websocket_buffer_size_ = render_config.websocket_buffer_size();
  low_websocket_buffer_size_ = render_config.low_websocket_buffer_size();

  current_websocket_buffer_size_.store(0);
  current_websocket_loss_.store(false);

  max_send_render_buffer_size_ = render_config.max_send_render_buffer_size();

  udp_2_sample_1_buffer_size_ = render_config.udp_2_sample_1_buffer_size();
  udp_3_sample_1_buffer_size_ = render_config.udp_3_sample_1_buffer_size();
  udp_loss_all_buffer_size_ = render_config.udp_loss_all_buffer_size();

  websocket_cmd_socket_key_ = render_config.websocket_cmd_socket_key();
  websocketCmdPtr_ =
      socket_manager_ptr_->getWebsocketRole(websocket_cmd_socket_key_);
  if (websocketCmdPtr_ == nullptr) {
    RERROR << "SocketManager: websocket_cmd_socket_key_ not match any socket: "
           << websocket_cmd_socket_key_;
    return -1;
  }
  socketRoles_.insert({websocket_cmd_socket_key_, websocketCmdPtr_});

  websocket_socket_key_ = render_config.websocket_socket_key();
  websocketPtr_ = socket_manager_ptr_->getWebsocketRole(websocket_socket_key_);
  if (websocketPtr_ == nullptr) {
    RERROR << "SocketManager: websocket_socket_key_ not match any socket: "
           << websocket_socket_key_;
    return -2;
  }
  socketRoles_.insert({websocket_socket_key_, websocketPtr_});

  udp_multicast_socket_key_ = render_config.udp_multicast_socket_key();
  multicastPtr =
      socket_manager_ptr_->getWebsocketRole(udp_multicast_socket_key_);
  if (multicastPtr == nullptr) {
    RERROR << "SocketManager: udp_multicast_socket_key_ not match any socket: "
           << udp_multicast_socket_key_;
    return -3;
  }
  socketRoles_.insert({udp_multicast_socket_key_, multicastPtr});

  udp_double_multicast_socket_key_ =
      render_config.udp_double_multicast_socket_key();
  double_multicastPtr =
      socket_manager_ptr_->getWebsocketRole(udp_double_multicast_socket_key_);
  if (double_multicastPtr == nullptr) {
    RERROR << "SocketManager: udp_double_multicast_socket_key_ not match any "
              "socket: "
           << udp_double_multicast_socket_key_;
    return -4;
  }
  socketRoles_.insert({udp_double_multicast_socket_key_, double_multicastPtr});

  udp_p2p_socket_key_ = render_config.udp_p2p_socket_key();
  dgramManagerPtr = socket_manager_ptr_->getWebsocketRole(udp_p2p_socket_key_);
  if (dgramManagerPtr == nullptr) {
    RERROR << "SocketManager: udp_p2p_socket_key_ not match any socket: "
           << udp_p2p_socket_key_;
    return -5;
  }
  socketRoles_.insert({udp_p2p_socket_key_, dgramManagerPtr});

  int ret = registerSocketCallback(render_config.websocket_task(),
                                   std::bind(&SocketManager::websocketCallback,
                                             this, std::placeholders::_1));
  if (ret != 0) {
    RERROR << "SocketManager: Register Socket Callback Failed: ret = " << ret;
    return -6;
  }

  return 0;
}

int SocketManager::initSendThread() {
  try {
    is_send_running_ = true;
    send_thread_ptr_.reset(
        new std::thread(&SocketManager::sendRenderWorkThread, this));
  } catch (...) {
    is_send_running_ = false;
    RERROR << "SocketManager: Malloc Send Render Work Thread Failed !";
    return -1;
  }

  return 0;
}

void SocketManager::websocketBufferCallback(
    const std::map<RSSocketRole::Ptr, size_t> &socketBufferMapper) {
  // for (auto iterMap = socketBufferMapper.begin();
  //      iterMap != socketBufferMapper.end(); ++iterMap) {
  //   RINFO << "Websocket Buffer Size = "
  //         << std::to_string(iterMap->second / 1024.0 / 1024.0);
  // }
}

void SocketManager::websocketCallback(
    const RSWebsocketProtoMsg::Ptr &websocketMsgPtr) {
  if (websocketMsgPtr != nullptr &&
      websocketMsgPtr->message_io_type_ ==
          RS_WEBSOCKET_MESSAGE_IO_TYPE::RS_WEBSOCKET_RECEIVE) {
    const std::string &message_topic = websocketMsgPtr->message_topic_;
    const std::string &message_type = websocketMsgPtr->message_type_;

    if (message_topic == RS_WEBSOCKET_TOPIC_REQUEST) {
      RINFO << "RUN HERE: RS_WEBSOCKET_TOPIC_REQUEST";
      std::shared_ptr<robosense::acviewer_msgs::RSRequest> requestPtr =
          std::dynamic_pointer_cast<robosense::acviewer_msgs::RSRequest>(
              websocketMsgPtr->message_);

      std::shared_ptr<robosense::acviewer_msgs::RSResponse> responsePtr(
          new robosense::acviewer_msgs::RSResponse());
      if (request_callback_) {
        int ret = request_callback_(requestPtr, responsePtr);
        if (ret != 0) {
          RERROR << "SocketManager: Process Websocket Request Proto Message "
                    "Failed: ret = "
                 << ret << ", requestType = "
                 << robosense::acviewer_msgs::RS_CMD_TYPE_Name(
                        requestPtr->cmd_type());
          if (ret < 0) {
            responsePtr->set_response_code(ret);
            responsePtr->set_response_info("Prepare To Process Failed !");
          } else if (ret > 0) {
            responsePtr->set_response_code(ret);
            responsePtr->set_response_info("Request Data Not Include Value !");
          }
        } else {
          RINFO << "SocketManager: Process Websocket Request Proto Message "
                   "Successed: requestType = "
                << robosense::acviewer_msgs::RS_CMD_TYPE_Name(
                       requestPtr->cmd_type());
        }

        // 进行响应
        RSWebsocketProtoMsg::Ptr sendWebsocketMsgPtr(new RSWebsocketProtoMsg());
        sendWebsocketMsgPtr->message_io_type_ =
            RS_WEBSOCKET_MESSAGE_IO_TYPE::RS_WEBSOCKET_SEND;
        sendWebsocketMsgPtr->message_topic_ = RS_WEBSOCKET_TOPIC_RESPONSE;
        sendWebsocketMsgPtr->message_type_ = responsePtr->GetTypeName();
        sendWebsocketMsgPtr->message_ = responsePtr;

        if (websocketCmdPtr_) {
          int ret = websocketCmdPtr_->sendProtoMessage(sendWebsocketMsgPtr);
          if (ret != 0) {
            RERROR << "SocketManager: Send Websocket Response Proto Message "
                      "Failed: ret = "
                   << ret << ", requestType = "
                   << robosense::acviewer_msgs::RS_CMD_TYPE_Name(
                          requestPtr->cmd_type());
          } else {
            RINFO << "SocketManager: Send Websocket Response Proto Message "
                     "Successed, requestType = "
                  << robosense::acviewer_msgs::RS_CMD_TYPE_Name(
                         requestPtr->cmd_type());
          }
        }
      } else {
        RERROR << "SocketManager: Not Register Request CallBack !";
      }
    } else if (message_topic == RS_WEBSOCKET_TOPIC_RENDERSWITCH) {
      RINFO << "RUN HERE: RS_WEBSOCKET_TOPIC_RENDERSWITCH";
      std::shared_ptr<robosense::acviewer_msgs::RenderSwitch> renderSwitchPtr =
          std::dynamic_pointer_cast<robosense::acviewer_msgs::RenderSwitch>(
              websocketMsgPtr->message_);
      if (renderSwitchPtr == nullptr) {
        RERROR << "SocketManager: RenderSwitch Topic Receive Proto Message Is "
                  "Nullptr !";
      } else {
        RERROR << "renderSwitchPtr = " << renderSwitchPtr->DebugString();
        if (renderSwitchPtr->has_enable_render()) {
          is_enable_render_.store(renderSwitchPtr->enable_render());
          // RERROR << "RUN HERE: ===> " << renderSwitchPtr->enable_render();
        }
        if (renderSwitchPtr->has_render_comm_type()) {
          if (renderSwitchPtr->render_comm_type() != render_comm_type_) {
            render_comm_type_ = renderSwitchPtr->render_comm_type();
          }
        }
        if (renderSwitchPtr->has_udp_p2p_carapp_ip() &&
            render_comm_type_ ==
                robosense::acviewer_msgs::RS_RENDER_COMM_UDP_P2P) {
          config_.mutable_render_config()
              ->mutable_udp_p2p_render_config()
              ->set_remote_ip(renderSwitchPtr->udp_p2p_carapp_ip());
        }
        if (renderSwitchPtr->has_udp_control_config()) {
          if (checkIsNewUdpControlConfig(
                  renderSwitchPtr->udp_control_config())) {
            updateUdpControlConfig(renderSwitchPtr->udp_control_config());
            if (multicastPtr) {
              multicastPtr->updateUdpControlConfig(cur_udp_control_config_);
            }
            if (double_multicastPtr) {
              double_multicastPtr->updateUdpControlConfig(
                  cur_udp_control_config_);
            }
            if (dgramManagerPtr) {
              dgramManagerPtr->updateUdpControlConfig(cur_udp_control_config_);
            }
          }
        }
      }
    } else if (message_topic == RS_WEBSOCKET_TOPIC_RESPONSE) {
      RWARN << "SocketManager: Not Support !";
    } else {
      RWARN << "SocketManager: Not Support Message Topic = " << message_topic
            << ", Message Type = " << message_type;
    }
  } else {
    RWARN << "SocketManager: Websocket Receive Message is Nullptr Or Not "
             "Support Message IO Type !";
  }
}

int SocketManager::registerSocketCallback(
    const robosense::rs_hmi::config::HmiTaskSocketConfig &hmiTaskSocketConfig,
    const RS_SOCKET_RECV_CALLBACK &socketRecvCallback) {
  for (int i = 0; i < hmiTaskSocketConfig.task_socket_configs_size(); ++i) {
    const auto &task_socket_config = hmiTaskSocketConfig.task_socket_configs(i);

    std::set<std::string> support_message_topics_;
    for (int j = 0; j < task_socket_config.support_message_topics_size(); ++j) {
      support_message_topics_.insert(
          task_socket_config.support_message_topics(j));
    }

    const std::string &task_socket_key = task_socket_config.task_socket_key();
    if (socketRoles_.find(task_socket_key) == socketRoles_.end()) {
      RERROR << "task_socket_key not match any socket: " << task_socket_key;
      return -1;
    }

    if (!support_message_topics_.empty()) {
      int ret = socketRoles_[task_socket_key]->registerRecvCallback(
          support_message_topics_, socketRecvCallback);
      if (ret != 0) {
        RERROR << "task_socket_key = " << task_socket_key
               << " match socket register callback failed: ret = " << ret;
        return -2;
      }
    }
  }

  return 0;
}

bool SocketManager::updateUdpControlConfig(
    const robosense::acviewer_msgs::UdpControlConfig &udpControlConfig) {
  if (!checkIsNewUdpControlConfig(udpControlConfig)) {
    switch (udpControlConfig.udp_control_type()) {
    case robosense::rs_hmi::config::RS_UDP_CONTROL_NOTHING: {
      cur_udp_control_config_.set_udp_control_type(
          robosense::rs_hmi::config::RS_UDP_CONTROL_NOTHING);
      break;
    }
    case robosense::rs_hmi::config::RS_UDP_CONTROL_TOTAL_CONTROL_TIME: {
      cur_udp_control_config_.set_udp_control_type(
          robosense::rs_hmi::config::RS_UDP_CONTROL_TOTAL_CONTROL_TIME);
      break;
    }
    case robosense::rs_hmi::config::RS_UDP_CONTROL_DATA_CONTROL_TIME: {
      cur_udp_control_config_.set_udp_control_type(
          robosense::rs_hmi::config::RS_UDP_CONTROL_DATA_CONTROL_TIME);
      break;
    }
    }
    cur_udp_control_config_.set_udp_total_control_time_ms(
        udpControlConfig.udp_total_control_time_ms());
    cur_udp_control_config_.set_udp_total_control_single_time_ms(
        udpControlConfig.udp_total_control_single_time_ms());
    cur_udp_control_config_.set_udp_data_control_size(
        udpControlConfig.udp_data_control_size());
    cur_udp_control_config_.set_udp_data_control_time_ms(
        udpControlConfig.udp_data_control_time_ms());

    return true;
  } else {
    return false;
  }
}

bool SocketManager::checkIsNewUdpControlConfig(
    const robosense::acviewer_msgs::UdpControlConfig &udpControlConfig) {
  return (static_cast<int>(cur_udp_control_config_.udp_control_type()) ==
          static_cast<int>(udpControlConfig.udp_control_type())) &&
         (cur_udp_control_config_.udp_total_control_time_ms() ==
          udpControlConfig.udp_total_control_time_ms()) &&
         (cur_udp_control_config_.udp_total_control_single_time_ms() ==
          udpControlConfig.udp_total_control_single_time_ms()) &&
         (cur_udp_control_config_.udp_data_control_size() ==
          udpControlConfig.udp_data_control_size()) &&
         (cur_udp_control_config_.udp_data_control_time_ms() ==
          udpControlConfig.udp_data_control_time_ms());
}

void SocketManager::sendRenderWorkThread() {
  while (is_send_running_) {
    std::shared_ptr<robosense::acviewer_msgs::RSRender> renderMsgPtr = nullptr;
    {
      std::unique_lock<std::mutex> lg(send_mtx_);
      send_cond_.wait(
          lg, [this] { return !send_buffer_.empty() || !is_send_running_; });

      if (!is_send_running_) {
        break;
      }

      renderMsgPtr = send_buffer_.front();
      send_buffer_.pop();

      // RINFO << "send_buffer_ SIZE = " << send_buffer_.size();
    }
    if (renderMsgPtr == nullptr) {
      continue;
    }

    // check loss frame by buffer size
    if (enable_websocket_buffer_size_) {
      switch (render_comm_type_) {
      case robosense::acviewer_msgs::RS_RENDER_COMM_WEBSOCKET: {
        size_t totalOutgoingMsgSize = 0;
        std::map<std::string, size_t> connectionOutgoingSizes;
        if (websocketPtr_) {
          int ret = websocketPtr_->outgoingMsgSize(totalOutgoingMsgSize,
                                                   connectionOutgoingSizes);
          if (ret != 0) {
            RERROR << "SocketManager: Get Websocket outgoing Message Size "
                      "Failed: ret = "
                   << ret;
          } else {
            if (totalOutgoingMsgSize > websocket_buffer_size_) {
              current_websocket_loss_.store(true);
              AWARN << "Websocket Buffer Size = " << totalOutgoingMsgSize
                    << " Loss Frame !";
            } else if (current_websocket_loss_.load() &&
                       totalOutgoingMsgSize < low_websocket_buffer_size_) {
              current_websocket_loss_.store(false);
            }
          }
        }
        break;
      }
      case robosense::acviewer_msgs::RS_RENDER_COMM_DOUBLE_UDP_MULTICAST:
      case robosense::acviewer_msgs::RS_RENDER_COMM_UDP_MULTICAST:
      case robosense::acviewer_msgs::RS_RENDER_COMM_UDP_P2P: {
        const auto udpBufferSize = multicastPtr->getUdpSendBufferSize();
        ++udp_send_frame_cnt_;
        if (udpBufferSize >= udp_loss_all_buffer_size_) {
          AWARN << "UdpBufferSize = " << udpBufferSize << " All Loss Frame !";
          current_websocket_loss_.store(true);
        } else if (udpBufferSize >= udp_3_sample_1_buffer_size_ &&
                   udp_send_frame_cnt_ % 3 != 0) {
          AWARN << "UdpBufferSize = " << udpBufferSize
                << " 3 Sample 1 Loss Frame !";
          current_websocket_loss_.store(true);
        } else if (udpBufferSize >= udp_2_sample_1_buffer_size_ &&
                   udp_send_frame_cnt_ % 2 != 0) {
          AWARN << "UdpBufferSize = " << udpBufferSize
                << " 2 Sample 1 Loss Frame !";
          current_websocket_loss_.store(true);
        } else {
          current_websocket_loss_.store(false);
        }
        break;
      }
      }
    }

    if (current_websocket_loss_.load()) {
      continue;
    }

    // 对于RGBImage 检查是否需要压缩为JPEG
    if (RS_RENDER_RGBIMAGE_COMPRESS && renderMsgPtr->has_rgb_jpeg()) {
      if (jpeg_coder_ptr_ == nullptr) {
        try {
          jpeg_coder_ptr_.reset(new robosense::jpeg::JpegCoder());
        } catch (...) {
          RERROR << "SocketManager: Malloc Jpeg Coder Failed !";
          continue;
        }

        robosense::jpeg::JpegCodesConfig jpegCodesConfig;
        jpegCodesConfig.coderType =
            robosense::jpeg::JPEG_CODER_TYPE::RS_JPEG_CODER_ENCODE;
        jpegCodesConfig.imageFrameFormat =
            robosense::common::FRAME_FORMAT_RGB24;
        jpegCodesConfig.imageWidth = renderMsgPtr->rgb_jpeg().width();
        jpegCodesConfig.imageHeight = renderMsgPtr->rgb_jpeg().height();
        jpegCodesConfig.jpegQuality = RS_RENDER_RGBIMAGE_JPEG_QUALITY;
        jpegCodesConfig.sampleFactor = RS_CAMERA_DOWNSAMPLE_FACTOR;

        int ret = jpeg_coder_ptr_->init(jpegCodesConfig);
        if (ret == 0) {
          RINFO << "SocketManager: Initial Jpeg Coder Successed !";
        } else {
          RERROR << "SocketManager: Initial Jpeg Coder Failed: ret = " << ret;
          jpeg_coder_ptr_.reset();
        }

        jpeg_encode_buffer_.resize(jpegCodesConfig.imageWidth *
                                   jpegCodesConfig.imageHeight * 3);
      }

      // 进行JPEG压缩
      if (jpeg_coder_ptr_) {
        size_t jpegBufferLen = jpeg_encode_buffer_.size();
        int ret = jpeg_coder_ptr_->encode(
            reinterpret_cast<unsigned char *>(
                const_cast<char *>(renderMsgPtr->rgb_jpeg().data().data())),
            renderMsgPtr->rgb_jpeg().data().size(), jpeg_encode_buffer_.data(),
            jpegBufferLen);

        if (ret == 0) {
          // 更新为JPEG消息
          renderMsgPtr->mutable_rgb_jpeg()->set_is_jpeg_compress(true);
          renderMsgPtr->mutable_rgb_jpeg()->set_data(jpeg_encode_buffer_.data(),
                                                     jpegBufferLen);
        } else {
          RWARN << "SocketManager: Jpeg Encode Failed !";
        }
      }
    }

    // 数据发送
    RSWebsocketProtoMsg::Ptr websocketProtoMsgPtr(new RSWebsocketProtoMsg());
    websocketProtoMsgPtr->message_io_type_ =
        robosense::rs_hmi::hmi::RS_WEBSOCKET_MESSAGE_IO_TYPE::RS_WEBSOCKET_SEND;
    websocketProtoMsgPtr->message_type_ = renderMsgPtr->GetTypeName();
    websocketProtoMsgPtr->message_topic_ = RS_WEBSOCKET_TOPIC_RENDER;
    websocketProtoMsgPtr->message_ = renderMsgPtr;

    switch (render_comm_type_) {
    case robosense::acviewer_msgs::RS_RENDER_COMM_WEBSOCKET: {
      if (websocketPtr_) {
        int ret = websocketPtr_->sendProtoMessage(websocketProtoMsgPtr);
        if (ret != 0) {
          RERROR << "SocketManager: Send Websocket Render Proto Message "
                    "Failed: ret = "
                 << ret;
        } else {
          RINFO << "SocketManager: Send Websocket Render Proto Message "
                   "Successed !";
        }
      }
      break;
    }
    case robosense::acviewer_msgs::RS_RENDER_COMM_DOUBLE_UDP_MULTICAST:
    case robosense::acviewer_msgs::RS_RENDER_COMM_UDP_MULTICAST: {
      if (multicastPtr) {
        int ret = multicastPtr->sendProtoMessage(websocketProtoMsgPtr);
        if (ret != 0) {
          RERROR << "SocketManager: Send Udp Multicast Render Proto Message "
                    "Failed: "
                    "ret = "
                 << ret;
        } else {
          RINFO << "SocketManager: Send Websocket Render Proto Message "
                   "Successed !";
        }
      }
      break;
    }
    case robosense::acviewer_msgs::RS_RENDER_COMM_UDP_P2P: {
      if (dgramManagerPtr) {
        websocketProtoMsgPtr->udp_p2p_remote_ips_.push_back(
            config_.render_config().udp_p2p_render_config().remote_ip());
        int ret = dgramManagerPtr->sendProtoMessage(websocketProtoMsgPtr);
        if (ret != 0) {
          RERROR << "SocketManager: Send Udp P2P Render Proto Message Failed: "
                    "ret = "
                 << ret;
        } else {
          RINFO << "SocketManager: Send Websocket Render Proto Message "
                   "Successed !";
        }
      }
      break;
    }
    }
  }
}

int SocketManager::toProtoMessage(
    const Tag_Position &msg, robosense::acviewer_msgs::RSPositon &protoMsg) {
  protoMsg.set_x(msg.x);
  protoMsg.set_y(msg.y);
  protoMsg.set_z(msg.z);
  protoMsg.set_timestamp(msg.timeStamp);

  return 0;
}

int SocketManager::toProtoMessage(
    const Tag_PointCloud &msg,
    robosense::acviewer_msgs::RSPointCloud &protoMsg) {
  protoMsg.set_size(msg.size);
  protoMsg.set_data(msg.data, msg.size * sizeof(Tag_Point));

  return 0;
}

int SocketManager::toProtoMessage_Short(
    const Tag_PointCloud &msg, const bool isSlamPointCloud,
    const bool isUseShortIPoint,
    robosense::acviewer_msgs::RSPointCloud &protoMsg) {
  const uint32_t totalPointCnt = msg.size;
  if (isSlamPointCloud) {
    slam_pointcloud_short_buffer_.resize(totalPointCnt);

    int j = 0;
    for (uint32_t i = 0; i < totalPointCnt; ++i) {
      const auto &point = msg.data[i];

      // 过滤nan
      if (std::isnan(point.x) || std::isnan(point.y) || std::isnan(point.z) ||
          std::isnan(point.intensity)) {
        continue;
      }

      auto &point_short = slam_pointcloud_short_buffer_[j];

      point_short.x = point.x;
      point_short.y = point.y;
      point_short.z = point.z;
      point_short.intensity = point.intensity;
      point_short.r = point.r;
      point_short.g = point.g;
      point_short.b = point.b;
      ++j;
    }

    protoMsg.set_size(j);
    protoMsg.set_data(slam_pointcloud_short_buffer_.data(),
                      j * sizeof(Tag_PointShort));
  } else {

    if (isUseShortIPoint) {
      pointcloud_shorti_buffer_.resize(totalPointCnt);

      // 确保不超过65535
      const int32_t factor_x =
          std::floor(65535 / (RS_CROP_X_TOP - RS_CROP_X_BOTTOM));
      const int32_t factor_y =
          std::floor(65535 / (RS_CROP_Y_TOP - RS_CROP_Y_BOTTOM));
      const int32_t factor_z =
          std::floor(65535 / (RS_CROP_Z_TOP - RS_CROP_Z_BOTTOM));

      const float offset_x =
          RS_CROP_X_BOTTOM >= 0 ? 0 : std::abs(RS_CROP_X_BOTTOM);
      const float offset_y =
          RS_CROP_Y_BOTTOM >= 0 ? 0 : std::abs(RS_CROP_Y_BOTTOM);
      const float offset_z =
          RS_CROP_Z_BOTTOM >= 0 ? 0 : std::abs(RS_CROP_Z_BOTTOM);

      int j = 0;
      for (uint32_t i = 0; i < totalPointCnt; ++i) {
        const auto &point = msg.data[i];

        // 过滤nan
        if (std::isnan(point.x) || std::isnan(point.y) || std::isnan(point.z) ||
            std::isnan(point.intensity)) {
          continue;
        } else if (point.x < RS_CROP_X_BOTTOM || point.x > RS_CROP_X_TOP ||
                   point.y < RS_CROP_Y_BOTTOM || point.y > RS_CROP_Y_TOP ||
                   point.z < RS_CROP_Z_BOTTOM || point.z > RS_CROP_Z_TOP) {
          continue;
        }

        auto &point_short = pointcloud_shorti_buffer_[j];
        point_short.x = (point.x + offset_x) * factor_x;
        point_short.y = (point.y + offset_y) * factor_y;
        point_short.z = (point.z + offset_z) * factor_z;
        point_short.intensity = point.intensity;
        point_short.r = point.r;
        point_short.g = point.g;
        point_short.b = point.b;
        ++j;
      }

      protoMsg.set_size(j);
      protoMsg.set_data(pointcloud_shorti_buffer_.data(),
                        j * sizeof(Tag_PointShortI));
    } else {
      pointcloud_short_buffer_.resize(totalPointCnt);
      int j = 0;
      for (uint32_t i = 0; i < totalPointCnt; ++i) {
        const auto &point = msg.data[i];

        // 过滤nan
        if (std::isnan(point.x) || std::isnan(point.y) || std::isnan(point.z) ||
            std::isnan(point.intensity)) {
          continue;
        }

        auto &point_short = pointcloud_short_buffer_[j];

        point_short.x = point.x;
        point_short.y = point.y;
        point_short.z = point.z;
        point_short.intensity = point.intensity;
        point_short.r = point.r;
        point_short.g = point.g;
        point_short.b = point.b;
        ++j;
      }

      protoMsg.set_size(j);
      protoMsg.set_data(pointcloud_short_buffer_.data(),
                        j * sizeof(Tag_PointShort));
    }
    // RINFO << "j = " << j;
  }

  return 0;
}

int SocketManager::toProtoMessage(
    const Tag_TriangleFacet &msg,
    robosense::acviewer_msgs::RSTrangleFacet &protoMsg) {
  protoMsg.set_size(msg.size);
  protoMsg.set_data(msg.data, msg.size * sizeof(Tag_Triangle));

  return 0;
}

int SocketManager::toProtoMessage(
    const Tag_RgbImage &msg, robosense::acviewer_msgs::RSRgbJpeg &protoMsg) {
  protoMsg.set_is_jpeg_compress(false);
  protoMsg.set_width(msg.width);
  protoMsg.set_height(msg.height);
  protoMsg.set_timestamp(msg.timeStamp);
  protoMsg.set_data(msg.data, msg.width * msg.height * 3);
  return 0;
}

int SocketManager::toProtoMessage(
    const Tag_DepthImage &msg,
    robosense::acviewer_msgs::RSDepthImage &protoMsg) {
  protoMsg.set_width(msg.width);
  protoMsg.set_height(msg.height);
  protoMsg.set_timestamp(msg.timeStamp);
  protoMsg.set_data(msg.data, msg.width * msg.height * sizeof(float));

  return 0;
}

} // namespace socket
} // namespace robosense