#include "proto/timesync.pb.h"

#include "hyper_vision/socketmanager/rswebsocketserver.h"

namespace robosense {
namespace rs_hmi {
namespace hmi {

RSWebsocketServer::RSWebsocketServer() { is_running_ = false; }

RSWebsocketServer::~RSWebsocketServer() { stop(); }

int RSWebsocketServer::init(
    const robosense::rs_hmi::config::SocketTaskConfig &config) {
  socket_task_config_ = config;
  if (socket_task_config_.has_websocket_task()) {
    websocket_endpoint_config_ =
        socket_task_config_.websocket_task().websocket_endpoint();
  } else {
    AERROR << "socket_task_config not include websocket_task() !";
    return -1;
  }

  server_.set_access_channels(websocketpp::log::alevel::none);
  server_.set_access_channels(websocketpp::log::alevel::connect);
  server_.set_access_channels(websocketpp::log::alevel::disconnect);
  server_.set_error_channels(websocketpp::log::elevel::rerror);
  server_.set_error_channels(websocketpp::log::elevel::fatal);
  server_.set_reuse_addr(true);
  server_.init_asio();

  server_.set_open_handler(
      std::bind(&RSWebsocketServer::openHandle, this, std::placeholders::_1));

  server_.set_fail_handler(
      std::bind(&RSWebsocketServer::failHandle, this, std::placeholders::_1));

  server_.set_close_handler(
      std::bind(&RSWebsocketServer::closeHandle, this, std::placeholders::_1));

  server_.set_message_handler(std::bind(&RSWebsocketServer::recvProtoMessage,
                                        this, std::placeholders::_1,
                                        std::placeholders::_2));

  is_running_ = false;
  is_running_timeout_checker_ = false;

  return 0;
}

int RSWebsocketServer::start() {
  is_running_ = true;
  const std::string websocket_ip_addr = websocket_endpoint_config_.server_ip();
  const uint16_t websocket_port = websocket_endpoint_config_.server_port();
  try {
    timeout_checker_timeout_th_ms_ =
        websocket_endpoint_config_.server_client_timeout_ms();
    timeout_checker_th_ms_ =
        websocket_endpoint_config_.server_check_timeout_ms();
    is_running_timeout_checker_ = true;
    timeout_checker_thread_.reset(
        new std::thread(&RSWebsocketServer::websocketTimeoutCheckThread, this));
  } catch (const std::exception &e) {
    AERROR << "Malloc websocket client check work thread failed !";
    goto FAILED_HANNDLE;
  }

  try {
    recv_thread_.reset(
        new std::thread(&RSWebsocketServer::websocketRecvWorkThread, this));
  } catch (const std::exception &e) {
    AERROR << "Malloc websocket recv work thread failed !";
    goto FAILED_HANNDLE;
  }

  for (int i = 0; i < websocket_endpoint_config_.send_thread_cnt(); ++i) {
    try {
      std::shared_ptr<std::thread> send_thread(
          new std::thread(&RSWebsocketServer::websocketSendWorkThread, this));
      send_threads_.push_back(send_thread);
    } catch (const std::exception &e) {
      AERROR << "Malloc websocket send work thread failed !";
      goto FAILED_HANNDLE;
    }
  }

  server_.listen(boost::asio::ip::address::from_string(websocket_ip_addr),
                 websocket_port);
  server_.start_accept();

  for (int i = 0; i < websocket_endpoint_config_.run_thread_cnt(); ++i) {
    try {
      std::shared_ptr<std::thread> running_thread(
          new std::thread(&RSWebsocketServer::websocketWorkThread, this));
      running_threads_.push_back(running_thread);
    } catch (const std::exception &e) {
      AERROR << "Malloc websocket work thread failed !";
      goto FAILED_HANNDLE;
    }
  }

  return 0;

FAILED_HANNDLE:
  stop();
  return -1;
}

int RSWebsocketServer::stop() {
  if (is_running_) {
    is_running_ = false;
  } else {
    return 0;
  }

  websocketpp::lib::error_code ec;
  server_.stop_listening(ec);
  if (ec.value() != 0) {
    AERROR << "Websocket Server Stop Listening Failed: ec = " << ec.value()
           << ", info = " << ec.message();
  }

  for (auto hdl : connectHdls_) {
    if (hdl.second.status == RS_CONNECT_STATUE::RS_CONNECT_OPEN) {
      server_.close(hdl.first.hdl, websocketpp::close::status::going_away,
                    "Server destructor called", ec);
      if (ec.value() != 0) {
        AERROR << "Websocket Server Close Client Connection Failed: ec = "
               << ec.value() << ", info = " << ec.message();
      }
      hdl.second.status = RS_CONNECT_STATUE::RS_CONNECT_CLOSE;
    }
  }
  server_.stop_perpetual();
  server_.stop();

  for (int i = 0; i < running_threads_.size(); ++i) {
    if (running_threads_[i] != nullptr) {
      if (running_threads_[i]->joinable()) {
        running_threads_[i]->join();
      }
    }
  }

  if (recv_thread_ != nullptr) {
    recv_buffer_cond_.notify_all();
    if (recv_thread_->joinable()) {
      recv_thread_->join();
    }
  }

  send_buffer_cond_.notify_all();
  for (int i = 0; i < send_threads_.size(); ++i) {
    if (send_threads_[i] != nullptr) {
      if (send_threads_[i]->joinable()) {
        send_threads_[i]->join();
      }
    }
  }

  if (timeout_checker_thread_) {
    is_running_timeout_checker_ = false;
    if (timeout_checker_thread_->joinable()) {
      timeout_checker_thread_->join();
    }
  }

  return 0;
}

int RSWebsocketServer::outgoingMsgSize(
    size_t &totalOutgoingMsgSize,
    std::map<std::string, size_t> &connectionOutgoingSizes) {
  totalOutgoingMsgSize = 0;
  connectionOutgoingSizes.clear();
  {
    std::lock_guard<std::mutex> lg(handle_mtx_);
    for (auto iterMap = connectHdls_.begin(); iterMap != connectHdls_.end();
         ++iterMap) {
      const auto &hdlInfo = iterMap->first;
      if (iterMap->second.status == RS_CONNECT_STATUE::RS_CONNECT_CLOSE) {
        continue;
      }
      websocketpp::lib::error_code ec;
      auto conn = server_.get_con_from_hdl(hdlInfo.hdl, ec);
      if (ec.value() != 0) {
        continue;
      } else if (conn == nullptr) {
        continue;
      }
      const std::string remote_address = conn->get_remote_endpoint();
      const size_t buffer_mount = conn->get_buffered_amount();
      connectionOutgoingSizes[remote_address] = buffer_mount;
      totalOutgoingMsgSize += buffer_mount;
    }
  }

  return 0;
}

void RSWebsocketServer::websocketWorkThread() { server_.run(); }

void RSWebsocketServer::websocketTimeoutCheckThread() {
  const int64_t timeout_th_ns = timeout_checker_timeout_th_ms_ * 1000000;
  while (is_running_timeout_checker_) {
    const uint64_t timestampNs = UINT64_T_TIMESTAMP_NS;
    {
      std::lock_guard<std::mutex> lg(handle_mtx_);
      for (auto &hdls : connectHdls_) {
        if (hdls.second.status == RS_CONNECT_STATUE::RS_CONNECT_CLOSE) {
          continue;
        } else if (timestampNs < hdls.second.last_timestamp_ns) {
          continue;
        }

        uint64_t diff_timestamp = timestampNs - hdls.second.last_timestamp_ns;
        if (diff_timestamp > timeout_th_ns) {
          if (hdls.first.hdl.lock()) {
            websocketpp::lib::error_code ec;
            const std::string err_info =
                "websocket server close client not any data translate: timeout "
                "threshold = " +
                std::to_string(timeout_th_ns) +
                "(ns), diff_timestamp = " + std::to_string(diff_timestamp) +
                "(ns), last_timestamp = " +
                std::to_string(hdls.second.last_timestamp_ns) + "(ns)";
            server_.close(hdls.first.hdl, 0, err_info, ec);
            if (ec.value() != 0) {
              AERROR
                  << "Websocket Server Try Close Client Connect Failed: ec = "
                  << ec.value() << ", info = " << ec.message();
            } else {
              AWARN << "Websocket Server Close Client Connect Successed !";
              hdls.second.status = RS_CONNECT_STATUE::RS_CONNECT_CLOSE;
            }
            AINFO << err_info;
          }
        }
      }

      // 删除消息
      for (auto iterMap = connectHdls_.begin();
           iterMap != connectHdls_.end();) {
        if (iterMap->second.status == RS_CONNECT_STATUE::RS_CONNECT_CLOSE) {
          iterMap = connectHdls_.erase(iterMap);
        } else {
          ++iterMap;
        }
      }
    }
    std::this_thread::sleep_for(
        std::chrono::milliseconds(timeout_checker_th_ms_));
  }
}

void RSWebsocketServer::recvProtoMessage(
    websocketpp::connection_hdl hdl,
    RS_WEBSOCKET_SERVER_ROLE::message_ptr msg) {
  if (is_running_ && msg) {
    {
      std::lock_guard<std::mutex> lg(handle_mtx_);
      for (auto &hdls : connectHdls_) {
        if (hdls.first.hdl.lock() == hdl.lock()) {
          if (hdls.second.status == RS_CONNECT_STATUE::RS_CONNECT_CLOSE) {
            return;
          } else {
            // AINFO << "Update last Timestamp";
            hdls.second.last_timestamp_ns = UINT64_T_TIMESTAMP_NS;
          }
        }
      }
    }

    // AERROR << "msg->get_payload() = " << msg->get_payload();
    // RSWebsocketProtoMsg::Ptr protoMsgPtr =
    //     proto_parser_->protoParseMessage(msg->get_payload());
    RSWebsocketProtoMsg::Ptr protoMsgPtr =
        RSProtoParser::protoParseMessage(msg->get_payload());
    if (protoMsgPtr != nullptr && protoMsgPtr->message_ != nullptr) {
      if (protoMsgPtr->message_topic_ != "/hmi/time_sync") {
        protoMsgPtr->message_io_type_ =
            RS_WEBSOCKET_MESSAGE_IO_TYPE::RS_WEBSOCKET_RECEIVE;
        std::lock_guard<std::mutex> lg(recv_buffer_mtx_);
        recv_buffer_.push(protoMsgPtr);
        recv_buffer_cond_.notify_one();
      } else {
        if (protoMsgPtr->message_type_ ==
            robosense::timesync_msgs::TimeSync().GetTypeName()) {
          // CarApp 时间同步消息
          std::shared_ptr<robosense::timesync_msgs::TimeSync> timeSyncPtr =
              std::dynamic_pointer_cast<robosense::timesync_msgs::TimeSync>(
                  protoMsgPtr->message_);
          if (timeSyncPtr != nullptr) {
            timeSyncPtr->set_destination_timestamp_ns(UINT64_T_TIMESTAMP_NS);
            protoMsgPtr->message_io_type_ =
                RS_WEBSOCKET_MESSAGE_IO_TYPE::RS_WEBSOCKET_SEND;
            protoMsgPtr->message_ = timeSyncPtr;

            int ret = RSWebsocketRole::sendProtoMessage(protoMsgPtr);
            if (ret != 0) {
              AERROR << "Send Timesync Message Failed: ret = " << ret;
            } else {
              const uint64_t source_timestamp_ns =
                  timeSyncPtr->source_timestamp_ns();
              const uint64_t destination_timestamp_ns =
                  timeSyncPtr->destination_timestamp_ns();
              AINFO << "DeviceId = " << timeSyncPtr->device_id()
                    << ", source_timestamp_ns = " << source_timestamp_ns
                    << ", destination_timestamp_ns = "
                    << destination_timestamp_ns << ", diff_timestamp_ns = "
                    << (source_timestamp_ns > destination_timestamp_ns
                            ? (source_timestamp_ns - destination_timestamp_ns)
                            : (destination_timestamp_ns - source_timestamp_ns));
            }
          } else {
            AERROR << "Dynamic Cast Timesync Message Failed !";
          }
        } else {
          AERROR << "Timesync/CarAppSyncMsg Message Seem Error !";
        }
      }

      // std::string str;
      // protoMsgPtr->message_->SerializeToString(&str);
      // AERROR << "Receive Msg: " << str;
    } else {
      AERROR << "Recv Proto Messge Parser Failed !";
    }
  }
}

void RSWebsocketServer::openHandle(websocketpp::connection_hdl hdl) {
  RSConnectHdlInfo connectHdl;
  connectHdl.hdlId = maxConnectHdlId_;
  connectHdl.hdl = hdl;

  ++maxConnectHdlId_;

  RSClientConnectHdlInfo clientConnectHdlInfo;
  clientConnectHdlInfo.status = RS_CONNECT_STATUE::RS_CONNECT_OPEN;
  clientConnectHdlInfo.start_timestamp_ns = UINT64_T_TIMESTAMP_NS;
  clientConnectHdlInfo.last_timestamp_ns = UINT64_T_TIMESTAMP_NS;

  std::lock_guard<std::mutex> lg(handle_mtx_);
  connectHdls_.insert(std::pair<RSConnectHdlInfo, RSClientConnectHdlInfo>(
      connectHdl, clientConnectHdlInfo));
}

void RSWebsocketServer::failHandle(websocketpp::connection_hdl hld) {
  // AERROR << "RUN HERE failHandle";
  std::lock_guard<std::mutex> lg(handle_mtx_);
  for (auto iterMap = connectHdls_.begin(); iterMap != connectHdls_.end();
       ++iterMap) {
    if (iterMap->first.hdl.lock() == hld.lock()) {
      // iterMap->second.status = RS_CONNECT_STATUE::RS_CONNECT_CLOSE;
      connectHdls_.erase(iterMap);
      break;
    }
  }
}

void RSWebsocketServer::closeHandle(websocketpp::connection_hdl hld) {
  // AERROR << "RUN HERE closeHandle";
  std::lock_guard<std::mutex> lg(handle_mtx_);
  for (auto iterMap = connectHdls_.begin(); iterMap != connectHdls_.end();
       ++iterMap) {
    if (iterMap->first.hdl.lock() == hld.lock()) {
      // iterMap->second.status = RS_CONNECT_STATUE::RS_CONNECT_CLOSE;
      connectHdls_.erase(iterMap);
      break;
    }
  }
}

int RSWebsocketServer::sendProtoMessage(const std::string &message) {
  if (is_running_) {
    std::lock_guard<std::mutex> lg(handle_mtx_);
    for (auto iterMap = connectHdls_.begin(); iterMap != connectHdls_.end();
         ++iterMap) {
      auto &hdlInfo = iterMap->second;
      const auto &hdl = iterMap->first;
      websocketpp::lib::error_code ec;
      if (hdlInfo.status == RS_CONNECT_STATUE::RS_CONNECT_OPEN &&
          hdl.hdl.lock()) {
        server_.send(hdl.hdl, message, websocketpp::frame::opcode::binary, ec);
        if (ec.value() != 0) {
          AERROR << "Websocket Send Failed: ec = " << ec.value()
                 << ", info = " << ec.message();
          continue;
        }
      }
    }
  }

  return 0;
}

} // namespace hmi
} // namespace rs_hmi
} // namespace robosense
