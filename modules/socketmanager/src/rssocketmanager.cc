#include "hyper_vision/socketmanager/rssocketmanager.h"

namespace robosense {
namespace rs_hmi {
namespace hmi {

RSSocketManager::RSSocketManager() {
  // NOTHING TODO...
}

RSSocketManager::~RSSocketManager() { stop(); }

int RSSocketManager::init(const robosense::rs_hmi::config::Config &config,
                          const RS_WEBSOCKET_BUFFERED_SIZE_CALLBACK &callback) {
  if (callback == nullptr) {
    AERROR << "Websocket Buffered Size Callback is Nullptr !";
    return -1;
  }
  config_ = config;
  callback_ = callback;

  int ret = init();
  if (ret != 0) {
    AERROR << "Initial SocketManager Failed: ret = " << ret;
    return -2;
  }

  return 0;
}

int RSSocketManager::start() {
  for (auto iterMap = socketRoles_.begin(); iterMap != socketRoles_.end();
       ++iterMap) {
    auto &socketRolePtr = iterMap->second;
    if (socketRolePtr != nullptr) {
      int ret = socketRolePtr->start();
      if (ret != 0) {
        return -1;
      }
    }
  }

  if (config_.enable_websocket_buffer_check()) {
    try {
      is_websocket_buffered_size_check_running_ = true;
      websocket_buffered_size_check_thread_.reset(new std::thread(
          &RSSocketManager::websocketBufferCheckWorkThread, this));
    } catch (...) {
      is_websocket_buffered_size_check_running_ = false;
      AERROR << "Initial Websocket Buffer Check Thread Failed !";
      return -2;
    }
    AINFO << "Initial Websocket Buffer Check Thread Successed !";
  }

  return 0;
}

int RSSocketManager::stop() {
  if (is_websocket_buffered_size_check_running_) {
    is_websocket_buffered_size_check_running_ = false;
    if (websocket_buffered_size_check_thread_ != nullptr) {
      if (websocket_buffered_size_check_thread_->joinable()) {
        websocket_buffered_size_check_thread_->join();
      }
    }
  }

  for (auto iterMap = socketRoles_.begin(); iterMap != socketRoles_.end();
       ++iterMap) {
    auto &socketRolePtr = iterMap->second;
    if (socketRolePtr != nullptr) {
      int ret = socketRolePtr->stop();
      if (ret != 0) {
        return -1;
      }
    }
  }
  socketRoles_.clear();

  return 0;
}

RSSocketRole::Ptr
RSSocketManager::getWebsocketRole(const std::string &task_socket_key) {
  auto iterMap = socketRoles_.find(task_socket_key);
  if (iterMap != socketRoles_.end()) {
    return iterMap->second;
  }

  return nullptr;
}

int RSSocketManager::init() {
  int ret = 0;
  // 更新SocketRole Key(s)
  ret = updateEnableSocketRoleKeys();
  if (ret != 0) {
    return -1;
  }

  const auto &hmi_socket_config = config_.hmi_socket_task_config();
  for (int i = 0; i < hmi_socket_config.socket_task_configs_size(); ++i) {
    const auto &socket_task_config = hmi_socket_config.socket_task_configs(i);
    if (socket_task_config.has_websocket_task()) {
      ret = addWebsocketRole(socket_task_config);
    } else if (socket_task_config.has_udp_multicast_task()) {
      ret = addUdpMulticastRole(socket_task_config);
    } else if (socket_task_config.has_udp_p2p_task()) {
      ret = addUdpP2PRole(socket_task_config);
    }
    if (ret != 0) {
      return -2;
    }
  }

  return 0;
}

int RSSocketManager::updateEnableSocketRoleKeys() {
  enableSocketRoleKeys_.clear();
  updateEnableSocketRoleKeys(config_.render_config().websocket_task());
  
  return 0;
}

int RSSocketManager::updateEnableSocketRoleKeys(
    const robosense::rs_hmi::config::HmiTaskSocketConfig &hmiTaskSocketConfig) {
  if (!hmiTaskSocketConfig.enable_task()) {
    return 0;
  }

  // 获取使用到的Socket Role Key(s)
  for (int i = 0; i < hmiTaskSocketConfig.task_socket_configs_size(); ++i) {
    const auto &task_socket_config = hmiTaskSocketConfig.task_socket_configs(i);
    enableSocketRoleKeys_.insert(task_socket_config.task_socket_key());
  }

  return 0;
}

int RSSocketManager::addWebsocketRole(
    const robosense::rs_hmi::config::SocketTaskConfig &taskConfig) {
  const std::string &task_socket_key =
      taskConfig.websocket_task().task_socket_key();

  if (enableSocketRoleKeys_.find(task_socket_key) ==
      enableSocketRoleKeys_.end()) {
    AWARN << "Not Enable Socket Role Key: " << task_socket_key;
    return 0;
  }

  RSSocketRole::Ptr socketRolePtr = nullptr;
  auto iterMap = socketRoles_.find(task_socket_key);
  if (iterMap != socketRoles_.end()) {
    socketRolePtr = iterMap->second;
  }

  if (socketRolePtr == nullptr) {
    const auto &endPointConfig =
        taskConfig.websocket_task().websocket_endpoint();
    switch (endPointConfig.role_type()) {
    case robosense::rs_hmi::config::RS_WEBSOCKET_SERVER_ROLE: {
      socketRolePtr.reset(new RSWebsocketServer());
      break;
    }
    case robosense::rs_hmi::config::RS_WEBSOCKET_CLIENT_ROLE: {
      socketRolePtr.reset(new RSWebsocketClient());
      break;
    }
    default: {
      break;
    }
    }

    if (socketRolePtr == nullptr) {
      AERROR << "Malloc Websocket Role is Nullptr !";
      return -2;
    }

    int ret = socketRolePtr->init(taskConfig);
    if (ret != 0) {
      AERROR << "Initial Webosocket Role Failed: ret = " << ret;
      return -3;
    }

    socketRoles_[task_socket_key] = socketRolePtr;

    AINFO << "Add Websocket EndPoint: " << task_socket_key
          << ", socketRoles_ Size: " << socketRoles_.size();
  } else {
    AINFO << "Already Add Websocket EndPoint: " << task_socket_key
          << ", socketRoles_ Size: " << socketRoles_.size();
  }

  // AINFO << "endPointConfig = " << endPointConfig.DebugString();
  // AERROR << "websocket_send_controls_size = "
  //        << taskConfig.websocket_send_controls_size();

  RSWebsocketRole::Ptr websocketRolePtr =
      std::dynamic_pointer_cast<RSWebsocketRole>(socketRolePtr);
  if (websocketRolePtr != nullptr) {
    const int websocket_send_controls_size =
        taskConfig.websocket_task().websocket_send_controls_size();
    for (int i = 0; i < websocket_send_controls_size; ++i) {
      const auto &websocket_send_control =
          taskConfig.websocket_task().websocket_send_controls(i);
      RSWebsocketSendControl sendControl(websocket_send_control);

      websocketRolePtr->addWebsocketControl(sendControl);
    }
  }

  websocketRoleKeys_.insert(task_socket_key);

  // AINFO << "===========================================";

  return 0;
}

int RSSocketManager::addUdpP2PRole(
    const robosense::rs_hmi::config::SocketTaskConfig &taskConfig) {
  const std::string &task_socket_key =
      taskConfig.udp_p2p_task().task_socket_key();

  if (enableSocketRoleKeys_.find(task_socket_key) ==
      enableSocketRoleKeys_.end()) {
    AWARN << "Not Enable Socket Role Key: " << task_socket_key;
    return 0;
  }

  RSSocketRole::Ptr socketRolePtr = nullptr;
  auto iterMap = socketRoles_.find(task_socket_key);
  if (iterMap != socketRoles_.end()) {
    socketRolePtr = iterMap->second;
  }

  if (socketRolePtr == nullptr) {
    const auto &endPointConfig = taskConfig.udp_p2p_task().udp_p2p_endpoint();
    switch (endPointConfig.role_type()) {
    case robosense::rs_hmi::config::RS_UDP_P2P_SENDER_ROLE:
    case robosense::rs_hmi::config::RS_UDP_P2P_RECEIVER_ROLE: {
      socketRolePtr.reset(new RSDgramComm());
      break;
    }
    default: {
      break;
    }
    }

    if (socketRolePtr == nullptr) {
      AERROR << "Malloc UDP P2P Role is Nullptr !";
      return -2;
    }

    int ret = socketRolePtr->init(taskConfig);
    if (ret != 0) {
      AERROR << "Initial UDP P2P Role Failed: ret = " << ret;
      return -3;
    }

    socketRoles_[task_socket_key] = socketRolePtr;

    AINFO << "Add UDP P2P  EndPoint: " << task_socket_key
          << ", socketRoles_ Size: " << socketRoles_.size();
  } else {
    AINFO << "Already Add UDP P2P EndPoint: " << task_socket_key
          << ", socketRoles_ Size: " << socketRoles_.size();
  }

  udpP2PRoleKeys_.insert(task_socket_key);

  return 0;
}

int RSSocketManager::addUdpMulticastRole(
    const robosense::rs_hmi::config::SocketTaskConfig &taskConfig) {
  const std::string &task_socket_key =
      taskConfig.udp_multicast_task().task_socket_key();

  if (enableSocketRoleKeys_.find(task_socket_key) ==
      enableSocketRoleKeys_.end()) {
    AWARN << "Not Enable Socket Role Key: " << task_socket_key;
    return 0;
  }

  RSSocketRole::Ptr socketRolePtr = nullptr;
  auto iterMap = socketRoles_.find(task_socket_key);
  if (iterMap != socketRoles_.end()) {
    socketRolePtr = iterMap->second;
  }

  if (socketRolePtr == nullptr) {
    const auto &endPointConfig =
        taskConfig.udp_multicast_task().udp_multicast_endpoint();
    switch (endPointConfig.role_type()) {
    case robosense::rs_hmi::config::RS_UDP_MULTICAST_SENDER_ROLE:
    case robosense::rs_hmi::config::RS_UDP_MULTICAST_RECEIVER_ROLE: {
      socketRolePtr.reset(new RSMulticastComm());
      break;
    }
    default: {
      break;
    }
    }

    if (socketRolePtr == nullptr) {
      AERROR << "Malloc UDP MULTICAST Role is Nullptr !";
      return -2;
    }

    int ret = socketRolePtr->init(taskConfig);
    if (ret != 0) {
      AERROR << "Initial UDP MULTICAST Role Failed: ret = " << ret;
      return -3;
    }

    socketRoles_[task_socket_key] = socketRolePtr;

    AINFO << "Add UDP MULTICAST  EndPoint: " << task_socket_key
          << ", socketRoles_ Size: " << socketRoles_.size();
  } else {
    AINFO << "Already Add UDP MULTICAST EndPoint: " << task_socket_key
          << ", socketRoles_ Size: " << socketRoles_.size();
  }

  udpMulticastRoleKeys_.insert(task_socket_key);

  return 0;
}

void RSSocketManager::websocketCallback(
    const int error_code, const std::string &error_info,
    const robosense::rs_hmi::config::WebsocketEndPointConfig &endPointConfig) {
  (void)(error_code);
  (void)(error_info);
  (void)(endPointConfig);
}

void RSSocketManager::websocketBufferCheckWorkThread() {
  while (is_websocket_buffered_size_check_running_) {
    size_t allTotalWebsocketBufferSize = 0;
    std::map<RSSocketRole::Ptr, size_t> websocket_sizes;
    for (auto iterMap = socketRoles_.begin(); iterMap != socketRoles_.end();
         ++iterMap) {
      if (websocketRoleKeys_.find(iterMap->first) == websocketRoleKeys_.end()) {
        continue;
      }
      if (iterMap->second != nullptr) {
        size_t totalWebsocketBufferSize = 0;
        std::map<std::string, size_t> websocket_connections;

        int ret = iterMap->second->outgoingMsgSize(totalWebsocketBufferSize,
                                                   websocket_connections);
        if (ret == 0) {
          websocket_sizes[iterMap->second] = totalWebsocketBufferSize;
          allTotalWebsocketBufferSize += totalWebsocketBufferSize;
        } else {
          AWARN << "Get Websocket Buffer Check Failed: ret = " << ret;
        }
      }
    }

    if (callback_ && !socketRoles_.empty()) {
      callback_(websocket_sizes);
    }

    if (allTotalWebsocketBufferSize > 4 * 1024 * 1024) {
      AINFO << "Websocket Buffered Size: "
            << std::to_string(allTotalWebsocketBufferSize);
    }

    std::this_thread::sleep_for(std::chrono::milliseconds(
        config_.websocket_buffer_check_timeout_th_ms()));
  }
}

} // namespace hmi
} // namespace rs_hmi
} // namespace robosense
