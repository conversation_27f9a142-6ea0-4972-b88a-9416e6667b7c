#include "hyper_vision/socketmanager/rsdgramcomm.h"

namespace robosense {
namespace rs_hmi {
namespace hmi {

RSDgramComm::RSDgramComm() {
  _frameID = 1;
  _curMaxFrameId = 0;
  _curOutputMaxFrameId = 0;
}

RSDgramComm::~RSDgramComm() { stop(); }

int RSDgramComm::init(
    const robosense::rs_hmi::config::SocketTaskConfig &config) {
  socket_task_config_ = config;
  if (socket_task_config_.has_udp_p2p_task()) {
    _commConfig = socket_task_config_.udp_p2p_task().udp_p2p_endpoint();
  } else {
    AERROR << "socket_task_config not include udp_p2p_task() !";
    return -1;
  }

  return 0;
}

int RSDgramComm::start() {
  int ret = 0;
  is_running_ = true;
  switch (_commConfig.role_type()) {
  case robosense::rs_hmi::config::RS_UDP_P2P_SENDER_ROLE: {
    ret = initSndComm();
    break;
  }
  case robosense::rs_hmi::config::RS_UDP_P2P_RECEIVER_ROLE: {
    ret = initRcvComm();
    break;
  }
  case robosense::rs_hmi::config::RS_UDP_P2P_BOTH_ROLE: {
    ret = initBothComm();
    break;
  }
  default: {
    AERROR << "not udp p2p support role_type: " << _commConfig.role_type();
    return -1;
  }
  }

  if (ret != 0) {
    AERROR << "initial udp multicast failed: ret = " << ret;
    return -2;
  }

  AINFO << "initial udp multicast successed !";

  return 0;
}

int RSDgramComm::stop() {
  if (is_running_ == false) {
    return 0;
  }
  is_running_ = false;

  // udp套接字关闭
  {
    std::lock_guard<std::mutex> lg(_mutexSocket);
    if (_udpSocketPtr->is_open()) {
      _udpSocketPtr->close();
    }
  }

  // Step2: 接收处理线程关闭
  {
    {
      std::lock_guard<std::mutex> lg(_mutexProcess);
      _recvProcessIdxsCond.notify_all();
    }

    for (size_t i = 0; i < _recvProcessThreads.size(); ++i) {
      if (_recvProcessThreads[i]) {
        if (_recvProcessThreads[i]->joinable()) {
          _recvProcessThreads[i]->join();
        }
      }
    }
  }

  if (recv_thread_ != nullptr) {
    recv_buffer_cond_.notify_all();
    if (recv_thread_->joinable()) {
      recv_thread_->join();
    }
  }

  send_buffer_cond_.notify_all();
  for (int i = 0; i < send_threads_.size(); ++i) {
    if (send_threads_[i] != nullptr) {
      if (send_threads_[i]->joinable()) {
        send_threads_[i]->join();
      }
    }
  }

  // Step2: 停止IO Service
  if (_ioServicePtr != nullptr) {
    _ioServicePtr->stop();
  }

  // 退出线程
  for (size_t i = 0; i < _ioServiceThreads.size(); ++i) {
    if (_ioServiceThreads[i]) {
      if (_ioServiceThreads[i]->joinable()) {
        _ioServiceThreads[i]->join();
      }
    }
  }

  return 0;
}

int RSDgramComm::initSocket() {
  if (_type == COMMUNICATION_IO_TYPE::COMMUNICATION_IO_SEND) {
    try {
      _udpSocketPtr.reset(new udp_socket(*_ioServicePtr));
      _udpSocketPtr->open(ASIO_UDP_V4_EMPTY_ENDPOINT);
      _udpSocketPtr->set_option(ASIO_UDP_SOCKET_REUSE_ADDRESS(true));
      _udpSocketPtr->set_option(
          ASIO_UDP_SOCKET_SEND_BUFFER_SIZE(_commConfig.socket_buffer_size()));
      _defaultRemoteEndPoint =
          udp_endpoint(ASIO_IP_ADDRESS_FROM_STRING(_commConfig.remote_ip()),
                       _commConfig.remote_port());

      boost::asio::socket_base::send_buffer_size option;
      boost::system::error_code ec;
      _udpSocketPtr->get_option(option, ec);
      if (ec.value() != 0) {
        AWARN << "get udp socket snd buffer size failed: err_code = "
              << ec.value() << ", err_info = " << ec.message();
      } else {
        AINFO << "get udp socket snd buffer size success: " << option.value();
      }
    } catch (std::exception &e) {
      return -1;
    }
  } else if (_type == COMMUNICATION_IO_TYPE::COMMUNICATION_IO_RECV ||
             _type == COMMUNICATION_IO_TYPE::COMMUNICATION_IO_BOTH) {
    try {
      _udpSocketPtr.reset(new udp_socket(
          *_ioServicePtr,
          udp_endpoint(ASIO_UDP_V4_EMPTY_ENDPOINT, _commConfig.remote_port())));
      _udpSocketPtr->set_option(ASIO_UDP_SOCKET_REUSE_ADDRESS(true));
      _udpSocketPtr->set_option(ASIO_UDP_SOCKET_RECEIVE_BUFFER_SIZE(
          _commConfig.socket_buffer_size()));

      boost::asio::socket_base::receive_buffer_size option;
      boost::system::error_code ec;
      _udpSocketPtr->get_option(option, ec);
      if (ec.value() != 0) {
        AWARN << "get udp socket recv buffer size failed: err_code = "
              << ec.value() << ", err_info = " << ec.message();
      } else {
        AINFO << "get udp socket recv buffer size success: " << option.value();
      }
    } catch (std::exception &e) {
      return -1;
    }
  }

  return 0;
}

int RSDgramComm::initDgramBuffer() {
  if (_type == COMMUNICATION_IO_TYPE::COMMUNICATION_IO_SEND ||
      _type == COMMUNICATION_IO_TYPE::COMMUNICATION_IO_BOTH) {
    for (size_t index = 0; index < _udpBufferCellSize; ++index) {
      try {
        RSBinaryBuffer::Ptr cell(new RSBinaryBuffer(index, _udpMaxMsgSize));
        _sndBinaryBuffers.push_back(cell);
        _sndAvailIdxs.push_back(index);
      } catch (std::exception &e) {
        return -1;
      }
    }
  }

  if (_type == COMMUNICATION_IO_TYPE::COMMUNICATION_IO_RECV ||
      _type == COMMUNICATION_IO_TYPE::COMMUNICATION_IO_BOTH) {
    for (size_t index = 0; index < _udpBufferCellSize; ++index) {
      try {
        RSBinaryBuffer::Ptr cell(new RSBinaryBuffer(index, _udpMaxMsgSize));
        _recvBinaryBuffers.push_back(cell);
        _recvAvailIdxs.push_back(index);
      } catch (std::exception &e) {
        return -1;
      }
    }
    _recvRemoteEndPoints.resize(_udpBufferCellSize);
  }

  return 0;
}

int RSDgramComm::initAsynComm() {
  int ret;

  _udpStartTimestampNs_ = UINT64_T_TIMESTAMP_NS;
  _udpRecvTimeoutThMs_ = _commConfig.msg_timeout_th_ms();

  // 发送消息的大小
  _udpMaxMsgSize = _commConfig.max_msg_size();
  _udpAvailMsgSize = _udpMaxMsgSize - sizeof(RSRoboMsgHeader);
  _udpBufferCellSize = _commConfig.buffer_cell_size();

  _udpSndRetryCnt_ = _commConfig.asio_snd_retry_cnt();
  _udpSndRetryThMs_ = _commConfig.asio_snd_retry_th_ms();

  _compressLevel = _commConfig.compress_level();
  _compressFormat = _commConfig.compress_format();

  AINFO << "_udpSndRetryCnt_ = " << _udpSndRetryCnt_
        << ", _udpSndRetryThMs_ = " << _udpSndRetryThMs_;

  try {
    _ioServicePtr.reset(new io_service());
    _ioServiceWorkPtr.reset(new io_service::work(*_ioServicePtr));
  } catch (std::exception &e) {
    AERROR << "Create io_service/io_service::work Failed !";
    return -1;
  }

  try {
    _udpControlUtil.reset(
        new RSUdpControlUtil(_commConfig.udp_control_config()));
  } catch (...) {
    AERROR << "Create Udp Control Util Failed !";
    return -2;
  }

  try {
    for (int i = 0; i < _commConfig.asio_run_thread_cnt(); ++i) {
      std::shared_ptr<std::thread> runThread(
          new std::thread(&RSDgramComm::ioServiceWorker, this));
      _ioServiceThreads.push_back(runThread);
    }
  } catch (std::exception &e) {
    AERROR << "Create io_service running thread(s) Failed !";
    return -3;
  }

  if (_type == COMMUNICATION_IO_TYPE::COMMUNICATION_IO_SEND) {
    ret = initAsynCommSnd();
    if (ret != 0) {
      AERROR << "Multicast Role Sender Initial Failed: ret = " << ret;
      return -4;
    }

  } else if (_type == COMMUNICATION_IO_TYPE::COMMUNICATION_IO_RECV) {
    ret = initAsynCommRcv();
    if (ret != 0) {
      AERROR << "Multicast Role Receiver Initial Failed: ret = " << ret;
      return -6;
    }
  }

  if (_type == COMMUNICATION_IO_TYPE::COMMUNICATION_IO_SEND ||
      _type == COMMUNICATION_IO_TYPE::COMMUNICATION_IO_BOTH) {
    try {
      is_running_ = true;
      for (int i = 0; i < _commConfig.asio_snd_thread_cnt(); ++i) {
        std::shared_ptr<std::thread> sndMsgThread(
            new std::thread(&RSDgramComm::udpSendWorkThread, this));
        send_threads_.push_back(sndMsgThread);
      }
    } catch (...) {
      AERROR << "Create Sender Work Thread(s) Failed !";
      is_running_ = false;
      return -5;
    }
  }

  if (_type == COMMUNICATION_IO_TYPE::COMMUNICATION_IO_RECV ||
      _type == COMMUNICATION_IO_TYPE::COMMUNICATION_IO_BOTH) {
    // Start Recv Process Thread
    try {
      for (int i = 0; i < _commConfig.asio_recv_thread_cnt(); ++i) {
        std::shared_ptr<std::thread> recvThreadPtr(
            new std::thread(&RSDgramComm::processWorkThread, this));
        _recvProcessThreads.push_back(recvThreadPtr);
      }
    } catch (const std::exception &e) {
      AERROR << "Multicast Receiver Role Create Receive Thread(s) Failed !";
      return -7;
    }

    try {
      recv_thread_.reset(
          new std::thread(&RSDgramComm::udpRecvWorkThread, this));
    } catch (const std::exception &e) {
      AERROR << "Malloc websocket recv work thread failed !";
      return -8;
    }

    // Start Recv Message
    int preRecvHandleCnt = std::max((size_t)1, _recvAvailIdxs.size() / 4);
    for (int i = 0; i < preRecvHandleCnt; ++i) {
      recvMessage();
    }
  }

  return 0;
}

int RSDgramComm::initAsynCommSnd() {
  int ret = initSocket();
  if (ret != 0) {
    AERROR << "Multicast Sender Role Initial Socket Failed: ret = " << ret;
    return -1;
  }

  ret = initDgramBuffer();
  if (ret != 0) {
    AERROR << "Multicast Sender Role Initial Dgram Buffer Failed: ret = "
           << ret;
    return -2;
  }

  return 0;
}

int RSDgramComm::initAsynCommRcv() {
  int ret = initSocket();
  if (ret != 0) {
    AERROR << "Multicast Receiver Role Initial Socket Failed: ret = " << ret;
    return -1;
  }

  ret = initDgramBuffer();
  if (ret != 0) {
    AERROR << "Multicast Receiver Role Initial Dgram Buffer Failed: ret = "
           << ret;
    return -2;
  }

  return 0;
}

int RSDgramComm::sendMessageUtil(const RSWebsocketProtoMsg::Ptr &protoMsgPtr) {
  int ret = 0;
  std::string protoMsgString;
  if (!RSProtoParser::protoPackageMessage(protoMsgPtr, _compressFormat,
                                          _compressLevel, protoMsgString)) {
    AERROR << "Dgram Proto Message Serialize Failed !";
    return -1;
  }

  if (!protoMsgPtr->udp_p2p_remote_ips_.empty()) {
    return sendProtoMessage(protoMsgString, protoMsgPtr->udp_p2p_remote_ips_);
  } else {
    return sendProtoMessage(protoMsgString);
  }
}

int RSDgramComm::sendProtoMessage(const std::string &protoMsgString) {
  return sendProtoMessage(protoMsgString,
                          std::vector<std::string>{_commConfig.remote_ip()});
}

int RSDgramComm::sendProtoMessage(
    const std::string &protoMsgString,
    const std::vector<std::string> &udp_p2p_remote_ips) {
  int ret = 0;
  size_t totalSndMsgSize = protoMsgString.size();

  // 缓冲区都满足时才发送
  int needBufferIdxCnt =
      (totalSndMsgSize / _udpAvailMsgSize + 1) * udp_p2p_remote_ips.size();
  int availSize = availIdxSize();
  if (availSize < needBufferIdxCnt) {
    int count = 0;
    do {
      if (count % 1000) {
        AWARN << "No Full Available Buffer: availSize = " << availSize
              << ", needBufferIdxCnt = " << needBufferIdxCnt
              << ", count = " << count << ", _frameID = " << _frameID;
      }
      std::this_thread::sleep_for(std::chrono::milliseconds(_udpSndRetryThMs_));
      ++count;
      availSize = availIdxSize();
    } while (availSize < needBufferIdxCnt);
  }

  // 更新Udp流量控制
  if (_udpControlUtil) {
    _udpControlUtil->updateDataControl(totalSndMsgSize);
  }

  RSRoboMsgHeader msgHeader;
  for (int msgOffset = 0; msgOffset < totalSndMsgSize;) {
    // Make Message Header
    msgHeader.msgTotalLen = totalSndMsgSize;
    msgHeader.msgLocalLen = std::min(
        static_cast<uint32_t>(totalSndMsgSize - msgOffset), _udpAvailMsgSize);
    msgHeader.msgFrameId = _frameID;
    msgHeader.msgOffset = msgOffset;

    RSRoboMsgHeader littleEndianMsgHeader;
    msgHeader.toTargetEndianArray((unsigned char *)(&littleEndianMsgHeader),
                                  sizeof(RSRoboMsgHeader),
                                  RS_DATA_ENDIAN_TYPE::RS_DATA_LITTLE_ENDIAN);
    // Send Message
    for (size_t j = 0; j < udp_p2p_remote_ips.size(); ++j) {
      ret =
          sendMessage(littleEndianMsgHeader, protoMsgString.data() + msgOffset,
                      littleEndianMsgHeader.msgLocalLen, udp_p2p_remote_ips[j]);

      if (ret != 0) {
        std::string errMsg = std::string(__FUNCTION__) + std::string(": ");
        RS_COMM_ERROR_TYPE errorType =
            RS_COMM_ERROR_TYPE::RS_COMM_ERROR_WARNNING;
        if (ret == 1) {
          errMsg += "Send Socket Buffer Full !";
          ++_udpSendErrorCnt_;
          int count = _udpSndRetryCnt_;
          do {
            std::this_thread::sleep_for(
                std::chrono::milliseconds(_udpSndRetryThMs_));
            ret = sendMessage(
                littleEndianMsgHeader, protoMsgString.data() + msgOffset,
                littleEndianMsgHeader.msgLocalLen, udp_p2p_remote_ips[j]);
            --count;
          } while (ret == 1 && count >= 0);
          errMsg += "retry count = " + std::to_string(count);
          if (ret == 0) {
            --_udpSendErrorCnt_;
          }
        } else if (ret == 2) {
          errMsg += "Speceial Parameter for SendMessage(...) Invalid !";
        } else if (ret == -1) {
          errMsg += "Send Message Too Big !";
        } else if (ret == -2) {
          errorType = RS_COMM_ERROR_TYPE::RS_COMM_ERROR_FATAL;
          errMsg += "Send Message Socket Close And Try Open Failed !";
        }
        AERROR << "Send Error: " << errMsg
               << ", RS_COMM_ERROR_TYPE = " << static_cast<int>(errorType);
      }
    }

    ++_udpSendCnt_;
    msgOffset += msgHeader.msgLocalLen;

    // 判断是否需要控制
    if (_udpControlUtil) {
      bool isEnableControl = false;
      do {
        isEnableControl = _udpControlUtil->checkDataControl(msgOffset);
      } while (isEnableControl);
    }
  }
  ++_frameID;

  _udpSendTotalSize_ += protoMsgString.size();
  ++_udpSendMsgCnt_;
  if (_udpSendMsgCnt_ % 10 == 0) {
    auto endTimestampNs = UINT64_T_TIMESTAMP_NS;
    double diffTime = (endTimestampNs - _udpStartTimestampNs_) * 1e-9;
    double dataSize = (_udpSendTotalSize_ * 1.0 / 1024 / 1024);

    AINFO << "_udpSendMsgCnt_ = " << _udpSendMsgCnt_
          << ", _udpSendCnt_ = " << _udpSendCnt_
          << ", _udpSendErrorCnt_ = " << _udpSendErrorCnt_
          << ", _udpSendErrorCnt_(%) = "
          << (_udpSendErrorCnt_ * 100.0) / _udpSendCnt_
          << "(%),  _udpSendTotalSize_(MB) = " << dataSize
          << "(MB), Send Time(s) = " << diffTime
          << "(s), Send bandWidth(Mbps) = " << (dataSize * 8 / diffTime)
          << "(Mbps)";
  }

  return 0;
}

} // namespace hmi
} // namespace rs_hmi
} // namespace robosense