#include "hyper_vision/socketmanager/rswebsocketclient.h"

namespace robosense {
namespace rs_hmi {
namespace hmi {

RSWebsocketClient::RSWebsocketClient() { is_running_ = false; }

RSWebsocketClient::~RSWebsocketClient() { stop(); }

int RSWebsocketClient::init(
    const robosense::rs_hmi::config::SocketTaskConfig &config) {
  socket_task_config_ = config;
  if (socket_task_config_.has_websocket_task()) {
    websocket_endpoint_config_ =
        socket_task_config_.websocket_task().websocket_endpoint();
  } else {
    AERROR << "socket_task_config not include websocket_task() !";
    return -1;
  }

  // client_.set_access_channels(websocketpp::log::alevel::all);
  // client_.clear_access_channels(websocketpp::log::alevel::frame_payload);
  client_.set_access_channels(websocketpp::log::alevel::none); 
  client_.init_asio();

  client_.set_open_handler(
      std::bind(&RSWebsocketClient::openHandle, this, std::placeholders::_1));

  client_.set_fail_handler(
      std::bind(&RSWebsocketClient::failHandle, this, std::placeholders::_1));

  client_.set_close_handler(
      std::bind(&RSWebsocketClient::closeHandle, this, std::placeholders::_1));

  client_.set_message_handler(std::bind(&RSWebsocketClient::recvProtoMessage,
                                        this, std::placeholders::_1,
                                        std::placeholders::_2));
  return 0;
}

int RSWebsocketClient::start() {
  is_connection_ = false;
  is_running_ = true;

  client_.start_perpetual();

  for (int i = 0; i < websocket_endpoint_config_.run_thread_cnt(); ++i) {
    try {
      std::shared_ptr<std::thread> running_thread(
          new std::thread(&RSWebsocketClient::websocketWorkThread, this));
      running_threads_.push_back(running_thread);
    } catch (const std::exception &e) {
      AERROR << "Malloc websocket work thread failed !";
      goto FAILED_HANDLE;
    }
  }

  try {
    recv_thread_.reset(
        new std::thread(&RSWebsocketClient::websocketRecvWorkThread, this));
  } catch (const std::exception &e) {
    AERROR << "Malloc websocket recv work thread failed !";
    return -2;
  }

  for (int i = 0; i < websocket_endpoint_config_.send_thread_cnt(); ++i) {
    try {
      std::shared_ptr<std::thread> send_thread(
          new std::thread(&RSWebsocketClient::websocketSendWorkThread, this));
      send_threads_.push_back(send_thread);
    } catch (const std::exception &e) {
      AERROR << "Malloc websocket send work thread failed !";
      goto FAILED_HANDLE;
    }
  }

  try {
    connectWorkThread_.reset(
        new std::thread(&RSWebsocketClient::websocketConnectionThread, this));
  } catch (...) {
    AERROR << "Malloc websocket connect work thread failed !";
    return -1;
  }

  return 0;
FAILED_HANDLE:
  stop();

  return -1;
}

int RSWebsocketClient::stop() {
  if (is_running_) {
    is_running_ = false;
  } else {
    return 0;
  }

  if (connectWorkThread_ != nullptr) {
    if (connectWorkThread_->joinable()) {
      connectWorkThread_->join();
    }
  }

  {
    std::lock_guard<std::mutex> lg(handle_mtx_);
    try {
      if (connection_ && is_connection_) {
        client_.close(connection_->get_handle(),
                      websocketpp::close::status::going_away,
                      "Client destructor called");
      }
      client_.stop();
    } catch (const std::exception &e) {
      std::cout << "Exception during WebSocketClient destruction: " << e.what()
                << std::endl;
      return -1;
    }
    is_connection_ = false;
  }

  for (int i = 0; i < running_threads_.size(); ++i) {
    if (running_threads_[i] != nullptr) {
      if (running_threads_[i]->joinable()) {
        running_threads_[i]->join();
      }
    }
  }

  if (recv_thread_ != nullptr) {
    recv_buffer_cond_.notify_all();
    if (recv_thread_->joinable()) {
      recv_thread_->join();
    }
  }

  send_buffer_cond_.notify_all();
  for (int i = 0; i < send_threads_.size(); ++i) {
    if (send_threads_[i] != nullptr) {
      if (send_threads_[i]->joinable()) {
        send_threads_[i]->join();
      }
    }
  }

  return 0;
}

int RSWebsocketClient::outgoingMsgSize(
    size_t &totalOutgoingMsgSize,
    std::map<std::string, size_t> &connectionOutgoingSizes) {
  connectionOutgoingSizes.clear();
  {
    std::lock_guard<std::mutex> lg(handle_mtx_);
    if (connection_ == nullptr || !is_connection_) {
      return -1;
    }
    totalOutgoingMsgSize = connection_->get_buffered_amount();
  }

  return 0;
}

void RSWebsocketClient::websocketWorkThread() {
  // AERROR << "RUN HERE";
  client_.run();
}

void RSWebsocketClient::websocketConnectionThread() {
  const std::string websocket_ip_addr = websocket_endpoint_config_.server_ip();
  const uint16_t websocket_port = websocket_endpoint_config_.server_port();
  // AINFO << "websocket_ip_addr = " << websocket_ip_addr
  //        << ", websocket_port = " << websocket_port;
  while (is_running_) {
    {
      std::lock_guard<std::mutex> lg(handle_mtx_);
      if (!is_connection_) {
        websocketpp::lib::error_code ec;
        connection_ = client_.get_connection("ws://" + websocket_ip_addr + ":" +
                                                 std::to_string(websocket_port),
                                             ec);
        if (ec) {
          AERROR << "Create websocket connect failed !";
          continue;
        }
        client_.connect(connection_);
        AWARN << "try connection to: [ip:port] = [" << websocket_ip_addr << ":"
              << websocket_port << "]";
        // AERROR << "RUN HERE";
      }
    }
    std::this_thread::sleep_for(std::chrono::seconds(10));
  }
}

void RSWebsocketClient::openHandle(websocketpp::connection_hdl hld) {
  AINFO << "Websocket Client: openHandle";
  std::lock_guard<std::mutex> lg(handle_mtx_);
  is_connection_ = true;
}

void RSWebsocketClient::failHandle(websocketpp::connection_hdl hld) {
  AINFO << "Websocket Client: failHandle";
  std::lock_guard<std::mutex> lg(handle_mtx_);
  is_connection_ = false;
}

void RSWebsocketClient::closeHandle(websocketpp::connection_hdl hld) {
  AINFO << "Websocket Client: closeHandle";
  std::lock_guard<std::mutex> lg(handle_mtx_);
  is_connection_ = false;
}

void RSWebsocketClient::recvProtoMessage(
    websocketpp::connection_hdl hdl,
    RS_WEBSOCKET_CLIENT_ROLE::message_ptr msg) {
  if (is_running_ && msg) {
    // RSWebsocketProtoMsg::Ptr protoMsgPtr =
    //     proto_parser_->protoParseMessage(msg->get_payload());

    RSWebsocketProtoMsg::Ptr protoMsgPtr =
        RSProtoParser::protoParseMessage(msg->get_payload());
    if (protoMsgPtr != nullptr) {
      protoMsgPtr->message_io_type_ =
          RS_WEBSOCKET_MESSAGE_IO_TYPE::RS_WEBSOCKET_RECEIVE;
      std::lock_guard<std::mutex> lg(recv_buffer_mtx_);
      recv_buffer_.push(protoMsgPtr);
      recv_buffer_cond_.notify_one();
    } else {
      AERROR << "Rect Proto Messge Parser Failed !";
    }
  }
}

int RSWebsocketClient::sendProtoMessage(const std::string &msg) {
  std::lock_guard<std::mutex> lg(handle_mtx_);
  if (is_running_ && is_connection_ && connection_) {
    websocketpp::lib::error_code ec;
    client_.send(connection_->get_handle(), msg,
                 websocketpp::frame::opcode::BINARY, ec);
    if (ec.value() != 0) {
      AERROR << "Websocket Client Send Failed: ec = " << ec.value()
             << ", info = " << ec.message();
      return -1;
    }
  }
  return 0;
}

} // namespace hmi
} // namespace rs_hmi
} // namespace robosense
