#include "hyper_vision/socketmanager/rssocketrole.h"

namespace robosense {
namespace rs_hmi {
namespace hmi {

int RSSocketRole::registerExceptCallback(
    const RS_SOCKET_EXCEPTION_CALLBACK &callback) {
  if (callback == nullptr) {
    return -1;
  } else {
    std::lock_guard<std::mutex> lg(exception_callback_mtx_);
    exception_callbacks_.push_back(callback);
  }

  return 0;
}

int RSSocketRole::registerRecvCallback(
    const std::set<std::string> &messageTopics,
    const RS_SOCKET_RECV_CALLBACK &callback) {
  if (callback == nullptr) {
    return -1;
  } else {
    std::lock_guard<std::mutex> lg(recv_callback_mtx_);
    for (auto iterSet = messageTopics.begin(); iterSet != messageTopics.end();
         ++iterSet) {
      const std::string &messageTopic = *iterSet;
      if (recv_callbacks_.find(messageTopic) != recv_callbacks_.end()) {
        recv_callbacks_[messageTopic].push_back(callback);
      } else {
        recv_callbacks_.insert(
            {messageTopic, std::vector<RS_SOCKET_RECV_CALLBACK>{callback}});
      }
    }
  }

  return 0;
}

void RSSocketRole::runLocalRecvCallback(
    const RSWebsocketProtoMsg::Ptr &protoMsgPtr) {
  if (protoMsgPtr != nullptr) {
    std::lock_guard<std::mutex> lg(recv_callback_mtx_);
    const std::string &message_topic = protoMsgPtr->message_topic_;
    auto iterMap = recv_callbacks_.find(message_topic);
    if (iterMap == recv_callbacks_.end()) {
      return;
    }
    const auto &recv_callbacks = iterMap->second;
    for (const auto &callback : recv_callbacks) {
      // AERROR << "RUN HERE";
      if (callback != nullptr) {
        // AERROR << "RUN HERE";
        callback(protoMsgPtr);
      }
    }
  }
}

} // namespace hmi
} // namespace rs_hmi
} // namespace robosense
