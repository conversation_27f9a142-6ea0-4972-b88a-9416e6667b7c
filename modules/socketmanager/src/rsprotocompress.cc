#include <zstd.h>

#include <google/protobuf/io/gzip_stream.h>
#include <google/protobuf/io/zero_copy_stream_impl.h>

#include "cyber/common/log.h"
#include "hyper_vision/socketmanager/rsprotocompress.h"
#include "lz4.h"

namespace robosense {
namespace proto {
namespace compress {

////////////////////////////////////
// 压缩
////////////////////////////////////
bool RSProtoCompress::compressedToFile(
    const google::protobuf::Message &protoMsg, const std::string &outputFile,
    size_t &unCompressMsgSize,
    const robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT
        compressFormat,
    const int compressLevel) {
  unCompressMsgSize = protoMsg.ByteSizeLong();
  bool isSuccessed = false;
  switch (compressFormat) {
  case robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_GZIP:
  case robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_ZLIB: {
    std::ofstream output(outputFile,
                         std::ios_base::out | std::ios_base::binary);
    if (output.is_open()) {
      google::protobuf::io::OstreamOutputStream outputFileStream(&output);
      google::protobuf::io::GzipOutputStream::Options options;
      if (compressFormat ==
          robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_GZIP) {
        options.format = google::protobuf::io::GzipOutputStream::GZIP;
      } else {
        options.format = google::protobuf::io::GzipOutputStream::ZLIB;
      }
      options.compression_level = compressLevel;

      google::protobuf::io::GzipOutputStream gzipOutputStream(&outputFileStream,
                                                              options);

      isSuccessed = protoMsg.SerializeToZeroCopyStream(&gzipOutputStream);
    }
    break;
  }
  case robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_LZ4: {
    std::string protoMsgString;
    if (protoMsg.SerializeToString(&protoMsgString)) {
      if (protoMsgString.size() > std::numeric_limits<int>::max()) {
        AERROR << "UnCompress Message Data Too Big: "
               << std::to_string(protoMsgString.size()) << " Not Support !";
        return false;
      }

      const int content_size = protoMsgString.size();
      const int max_dst_size = LZ4_compressBound(content_size);
      std::vector<char> compress(max_dst_size, '\0');
      const int compressed_data_size = LZ4_compress_default(
          protoMsgString.data(), static_cast<char *>(compress.data()),
          content_size, max_dst_size);
      if (compressed_data_size > 0) {
        std::ofstream output(outputFile,
                             std::ios_base::out | std::ios_base::binary);

        if (output.is_open()) {
          if (output.write(compress.data(), compressed_data_size)) {
            isSuccessed = true;
          }
        }
      }
    }
    break;
  }
  case robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_ZSTD: {
    std::string protoMsgString;
    if (protoMsg.SerializeToString(&protoMsgString)) {
      if (protoMsgString.size() > std::numeric_limits<int>::max()) {
        AERROR << "UnCompress Message Data Too Big: "
               << std::to_string(protoMsgString.size()) << " Not Support !";
        return false;
      }

      const int content_size = protoMsgString.size();
      const int max_dst_size = ZSTD_compressBound(content_size);
      std::vector<char> compress(max_dst_size, '\0');
      const int compressed_data_size =
          ZSTD_compress(compress.data(), max_dst_size,
                        const_cast<char *>(protoMsgString.data()), content_size,
                        compressLevel);
      const auto errCode = ZSTD_isError(compressed_data_size);
      if (errCode) {
        const char *errInfo = ZSTD_getErrorName(compressed_data_size);
        AERROR << "ZSTD Compressed Failed: errorCode = " << errCode
               << ", errInfo = " << std::string(errInfo);
      } else {
        std::ofstream output(outputFile,
                             std::ios_base::out | std::ios_base::binary);

        if (output.is_open()) {
          if (output.write(compress.data(), compressed_data_size)) {
            isSuccessed = true;
          }
          // AERROR << "zstd compressed_data_size = " << compressed_data_size;
        }
      }
    }
    break;
  }
  default: {
    break;
  }
  }

  return isSuccessed;
}

bool RSProtoCompress::compressedToFile(
    const std::shared_ptr<google::protobuf::Message> &protoMsgPtr,
    const std::string &outputFile, size_t &unCompressMsgSize,
    const robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT
        compressFormat,
    const int compressLevel) {
  if (protoMsgPtr == nullptr) {
    return false;
  }
  return RSProtoCompress::compressedToFile(*protoMsgPtr, outputFile,
                                           unCompressMsgSize, compressFormat,
                                           compressLevel);
}

bool RSProtoCompress::compressedToFile(
    const google::protobuf::Message *protoMsgPtr, const std::string &outputFile,
    size_t &unCompressMsgSize,
    const robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT
        compressFormat,
    const int compressLevel) {
  if (protoMsgPtr == nullptr) {
    return false;
  }
  return RSProtoCompress::compressedToFile(*protoMsgPtr, outputFile,
                                           unCompressMsgSize, compressFormat,
                                           compressLevel);
}

bool RSProtoCompress::compressedToString(
    const google::protobuf::Message &protoMsg, std::string &outputString,
    size_t &unCompressMsgSize,
    const robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT
        compressFormat,
    const int compressLevel) {
  outputString.clear();
  unCompressMsgSize = protoMsg.ByteSizeLong();
  bool isSuccessed = false;
  switch (compressFormat) {
  case robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_GZIP:
  case robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_ZLIB: {
    google::protobuf::io::GzipOutputStream::Options options;
    if (compressFormat ==
        robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_GZIP) {
      options.format = google::protobuf::io::GzipOutputStream::GZIP;
    } else {
      options.format = google::protobuf::io::GzipOutputStream::ZLIB;
    }
    options.compression_level = compressLevel;

    google::protobuf::io::StringOutputStream outputStream(&outputString);
    google::protobuf::io::GzipOutputStream gzipStream(&outputStream, options);

    isSuccessed = protoMsg.SerializeToZeroCopyStream(&gzipStream);
    if (isSuccessed) {
      isSuccessed = gzipStream.Flush();
    }
    break;
  }
  case robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_LZ4: {
    std::string protoMsgString;
    if (protoMsg.SerializeToString(&protoMsgString)) {
      if (protoMsgString.size() > std::numeric_limits<int>::max()) {
        AERROR << "UnCompress Message Data Too Big: "
               << std::to_string(protoMsgString.size()) << " Not Support !";
        return false;
      }

      const int content_size = protoMsgString.size();
      const int max_dst_size = LZ4_compressBound(content_size);
      std::vector<char> compress(max_dst_size, '\0');
      const int compressed_data_size = LZ4_compress_default(
          protoMsgString.data(), static_cast<char *>(compress.data()),
          content_size, max_dst_size);
      if (compressed_data_size > 0) {
        outputString.assign(compress.data(), compressed_data_size);
        isSuccessed = true;
      } else {
        AERROR << "LZ4 Compressed Failed: errorCode = " << compressed_data_size;
      }
    }
    break;
  }
  case robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_ZSTD: {
    std::string protoMsgString;
    if (protoMsg.SerializeToString(&protoMsgString)) {
      if (protoMsgString.size() > std::numeric_limits<int>::max()) {
        AERROR << "UnCompress Message Data Too Big: "
               << std::to_string(protoMsgString.size()) << " Not Support !";
        return false;
      }

      const int content_size = protoMsgString.size();
      const int max_dst_size = ZSTD_compressBound(content_size);
      std::vector<char> compress(max_dst_size, '\0');
      const size_t compressed_data_size =
          ZSTD_compress(compress.data(), max_dst_size, protoMsgString.data(),
                        content_size, compressLevel);
      const auto errCode = ZSTD_isError(compressed_data_size);
      if (errCode) {
        const char *errInfo = ZSTD_getErrorName(compressed_data_size);
        AERROR << "ZSTD Compressed Failed: errorCode = " << errCode
               << ", errInfo = " << std::string(errInfo);
      } else {
        outputString.assign(compress.data(), compressed_data_size);
        isSuccessed = true;
      }
    }
    break;
  }
  default: {
    break;
  }
  }

  return isSuccessed;
}

bool RSProtoCompress::compressedToString(
    const std::shared_ptr<google::protobuf::Message> &protoMsgPtr,
    std::string &outputString, size_t &unCompressMsgSize,
    const robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT
        compressFormat,
    const int compressLevel) {
  if (protoMsgPtr == nullptr) {
    return false;
  }

  return RSProtoCompress::compressedToString(*protoMsgPtr, outputString,
                                             unCompressMsgSize, compressFormat,
                                             compressLevel);
}

bool RSProtoCompress::compressedToString(
    const google::protobuf::Message *protoMsgPtr, std::string &outputString,
    size_t &unCompressMsgSize,
    const robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT
        compressFormat,
    const int compressLevel) {
  if (protoMsgPtr == nullptr) {
    return false;
  }

  return RSProtoCompress::compressedToString(*protoMsgPtr, outputString,
                                             unCompressMsgSize, compressFormat,
                                             compressLevel);
}

////////////////////////////////////
// 解压缩
////////////////////////////////////
bool RSProtoCompress::decompressedFromFile(
    const std::string &inputFile,
    const robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT
        compressFormat,
    const size_t unCompressMsgSize, google::protobuf::Message &protoMsg) {
  protoMsg.Clear();

  bool isSuccessed = false;
  switch (compressFormat) {
  case robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_GZIP:
  case robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_ZLIB: {
    std::ifstream input(inputFile, std::ifstream::in | std::ifstream::binary);
    if (input.is_open()) {
      google::protobuf::io::IstreamInputStream inputFileStream(&input);
      google::protobuf::io::GzipInputStream GzipInputStream(&inputFileStream);
      isSuccessed = protoMsg.ParseFromZeroCopyStream(&GzipInputStream);
    }
    break;
  }
  case robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_LZ4: {
    // 防止数据越界
    // 检查解压缩数据量长度是否支持
    int maxUnCompresssBufferSize = 0;
    if (unCompressMsgSize > std::numeric_limits<int>::max() - 16 &&
        unCompressMsgSize <= std::numeric_limits<int>::max()) {
      maxUnCompresssBufferSize = std::numeric_limits<int>::max();
    } else if (unCompressMsgSize <= std::numeric_limits<int>::max() - 16) {
      maxUnCompresssBufferSize = (unCompressMsgSize / 16 + 1) * 16;
    } else {
      AERROR << "UnCompressed Message Size Too Big, Not Support: "
             << std::to_string(unCompressMsgSize);
      return false;
    }

    std::ifstream input(inputFile, std::ifstream::in | std::ifstream::binary);
    if (input.is_open()) {
      std::streampos data_size = 0;
      input.seekg(0, std::ios::end); // 移动到文件末尾
      data_size = input.tellg();     // 获取文件大小
      input.seekg(0, std::ios::beg); // 移动回文件开头

      // 检查压缩数据量长度是否支持
      if (data_size > std::numeric_limits<int>::max()) {
        AERROR << "Compress Message Size Too Big, Not Support: "
               << std::to_string(data_size);
        return false;
      }

      std::vector<char> compressBuffer(data_size, 0);
      if (input.read(compressBuffer.data(), data_size)) {
        std::vector<char> uncompressBuffer(maxUnCompresssBufferSize, 0);
        const int decompressed_size = LZ4_decompress_safe(
            static_cast<const char *>(compressBuffer.data()),
            uncompressBuffer.data(), data_size, maxUnCompresssBufferSize);
        if (decompressed_size > 0 &&
            decompressed_size <= maxUnCompresssBufferSize) {
          uncompressBuffer.resize(decompressed_size);
          if (protoMsg.ParseFromArray(uncompressBuffer.data(),
                                      uncompressBuffer.size())) {
            isSuccessed = true;
          }
        }
      }
    }
    break;
  }
  case robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_ZSTD: {
    // 防止数据越界
    // 检查解压缩数据量长度是否支持
    int maxUnCompresssBufferSize = 0;
    if (unCompressMsgSize > std::numeric_limits<int>::max() - 16 &&
        unCompressMsgSize <= std::numeric_limits<int>::max()) {
      maxUnCompresssBufferSize = std::numeric_limits<int>::max();
    } else if (unCompressMsgSize <= std::numeric_limits<int>::max() - 16) {
      maxUnCompresssBufferSize = (unCompressMsgSize / 16 + 1) * 16;
    } else {
      AERROR << "UnCompressed Message Size Too Big, Not Support: "
             << std::to_string(unCompressMsgSize);
      return false;
    }

    std::ifstream input(inputFile, std::ifstream::in | std::ifstream::binary);
    if (input.is_open()) {
      std::streampos data_size = 0;
      input.seekg(0, std::ios::end); // 移动到文件末尾
      data_size = input.tellg();     // 获取文件大小
      input.seekg(0, std::ios::beg); // 移动回文件开头

      // 检查压缩数据量长度是否支持
      if (data_size > std::numeric_limits<int>::max()) {
        AERROR << "Compress Message Size Too Big, Not Support: "
               << std::to_string(data_size);
        return false;
      }

      std::vector<char> compressBuffer(data_size, 0);
      if (input.read(compressBuffer.data(), data_size)) {
        // AERROR << "zstd data_size = " << data_size;
        std::vector<char> uncompressBuffer(maxUnCompresssBufferSize, 0);
        size_t decompressed_size =
            ZSTD_decompress(uncompressBuffer.data(), maxUnCompresssBufferSize,
                            compressBuffer.data(), data_size);
        const auto errCode = ZSTD_isError(decompressed_size);
        if (errCode) {
          const char *errInfo = ZSTD_getErrorName(decompressed_size);
          AERROR << "ZSTD Decompress Failed: errCode = " << errCode
                 << ", errInfo = " << std::string(errInfo);
        } else if (decompressed_size > 0 &&
                   decompressed_size <= maxUnCompresssBufferSize) {
          uncompressBuffer.resize(decompressed_size);
          if (protoMsg.ParseFromArray(uncompressBuffer.data(),
                                      uncompressBuffer.size())) {
            isSuccessed = true;
          }
        }
      }
    }
    break;
  }
  default: {
    break;
  }
  }

  return isSuccessed;
}

bool RSProtoCompress::decompressedFromFile(
    const std::string &inputFile,
    const robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT
        compressFormat,
    const size_t unCompressMsgSize,
    std::shared_ptr<google::protobuf::Message> &protoMsgPtr) {
  if (protoMsgPtr == nullptr) {
    return false;
  }

  return RSProtoCompress::decompressedFromFile(inputFile, compressFormat,
                                               unCompressMsgSize, *protoMsgPtr);
}

bool RSProtoCompress::decompressedFromFile(
    const std::string &inputFile,
    const robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT
        compressFormat,
    const size_t unCompressMsgSize, google::protobuf::Message *protoMsgPtr) {
  if (protoMsgPtr == nullptr) {
    return false;
  }

  return RSProtoCompress::decompressedFromFile(inputFile, compressFormat,
                                               unCompressMsgSize, *protoMsgPtr);
}

bool RSProtoCompress::decompressedFromString(
    const std::string &inputString,
    const robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT
        compressFormat,
    const size_t unCompressMsgSize, google::protobuf::Message &protoMsg) {
  protoMsg.Clear();

  bool isSuccessed = false;
  switch (compressFormat) {
  case robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_GZIP:
  case robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_ZLIB: {
    // 检查输入压缩数据长度是否支持: 防止越界
    if (inputString.size() > std::numeric_limits<int>::max()) {
      AERROR << "Compressed Message Size Too Big, Not Support: "
             << std::to_string(inputString.size());
      return false;
    }

    google::protobuf::io::ArrayInputStream inputStream(inputString.data(),
                                                       inputString.size());

    google::protobuf::io::GzipInputStream gzipStream(&inputStream);

    isSuccessed = protoMsg.ParseFromZeroCopyStream(&gzipStream);
    break;
  }
  case robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_LZ4: {
    // 防止数据越界
    // 检查解压缩数据量长度是否支持
    int maxUnCompresssBufferSize = 0;
    if (unCompressMsgSize > std::numeric_limits<int>::max() - 16 &&
        unCompressMsgSize <= std::numeric_limits<int>::max()) {
      maxUnCompresssBufferSize = std::numeric_limits<int>::max();
    } else if (unCompressMsgSize <= std::numeric_limits<int>::max() - 16) {
      maxUnCompresssBufferSize = (unCompressMsgSize / 16 + 1) * 16;
    } else {
      AERROR << "UnCompressed Message Size Too Big, Not Support: "
             << std::to_string(unCompressMsgSize);
      return false;
    }

    // 检查压缩数据量长度是否支持
    if (inputString.size() > std::numeric_limits<int>::max()) {
      AERROR << "Compress Message Size Too Big, Not Support: "
             << std::to_string(inputString.size());
      return false;
    }

    // LZ4解压缩
    std::vector<char> uncompressBuffer(maxUnCompresssBufferSize, 0);
    const int decompressed_size = LZ4_decompress_safe(
        static_cast<const char *>(inputString.data()), uncompressBuffer.data(),
        inputString.size(), maxUnCompresssBufferSize);
    if (decompressed_size > 0 &&
        decompressed_size <= maxUnCompresssBufferSize) {
      uncompressBuffer.resize(decompressed_size);
      if (protoMsg.ParseFromArray(uncompressBuffer.data(),
                                  uncompressBuffer.size())) {
        isSuccessed = true;
      }
    }
    break;
  }
  case robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_ZSTD: {
    // 防止数据越界
    // 检查解压缩数据量长度是否支持
    int maxUnCompresssBufferSize = 0;
    if (unCompressMsgSize > std::numeric_limits<int>::max() - 16 &&
        unCompressMsgSize <= std::numeric_limits<int>::max()) {
      maxUnCompresssBufferSize = std::numeric_limits<int>::max();
    } else if (unCompressMsgSize <= std::numeric_limits<int>::max() - 16) {
      maxUnCompresssBufferSize = (unCompressMsgSize / 16 + 1) * 16;
    } else {
      AERROR << "UnCompressed Message Size Too Big, Not Support: "
             << std::to_string(unCompressMsgSize);
      return false;
    }

    // 检查压缩数据量长度是否支持
    if (inputString.size() > std::numeric_limits<int>::max()) {
      AERROR << "Compress Message Size Too Big, Not Support: "
             << std::to_string(inputString.size());
      return false;
    }

    // ZSTD解压缩
    std::vector<char> uncompressBuffer(maxUnCompresssBufferSize, 0);
    size_t decompressed_size = ZSTD_decompress(
        uncompressBuffer.data(), maxUnCompresssBufferSize,
        const_cast<char *>(inputString.data()), inputString.size());
    const auto errCode = ZSTD_isError(decompressed_size);
    if (errCode) {
      const char *errInfo = ZSTD_getErrorName(decompressed_size);
      AERROR << "ZSTD Decompress Failed: errCode = " << errCode
             << ", errInfo = " << std::string(errInfo);
    } else if (decompressed_size > 0 &&
               decompressed_size <= maxUnCompresssBufferSize) {
      uncompressBuffer.resize(decompressed_size);
      if (protoMsg.ParseFromArray(uncompressBuffer.data(),
                                  uncompressBuffer.size())) {
        isSuccessed = true;
      }
    }

    break;
  }
  default: {
    break;
  }
  }

  return isSuccessed;
}

bool RSProtoCompress::decompressedFromString(
    const std::string &inputString,
    const robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT
        compressFormat,
    const size_t unCompressMsgSize,
    std::shared_ptr<google::protobuf::Message> &protoMsgPtr) {
  if (protoMsgPtr == nullptr) {
    return false;
  }
  return RSProtoCompress::decompressedFromString(
      inputString, compressFormat, unCompressMsgSize, *protoMsgPtr);
}

bool RSProtoCompress::decompressedFromString(
    const std::string &inputString,
    const robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT
        compressFormat,
    const size_t unCompressMsgSize, google::protobuf::Message *protoMsgPtr) {
  if (protoMsgPtr == nullptr) {
    return false;
  }
  return RSProtoCompress::decompressedFromString(
      inputString, compressFormat, unCompressMsgSize, *protoMsgPtr);
}

} // namespace compress
} // namespace proto
} // namespace robosense