
#include "hyper_vision/socketmanager/rsprotoparser.h"

namespace robosense {
namespace rs_hmi {
namespace hmi {

RSWebsocketProtoMsg::Ptr
RSProtoParser::protoParseMessage(const std::string &protoData) {
  if (protoData.empty()) {
    return nullptr;
  }

  int ret = 0;
  robosense::hmi_msgs::SelfDescribingMessage self_describing_message_;
  bool isSuccess = self_describing_message_.ParseFromArray(protoData.data(),
                                                           protoData.size());
  if (!isSuccess) {
    AERROR << "Parser Self Describing Message Failed !";
    return nullptr;
  }

  const std::string &message_topic_ = self_describing_message_.message_topic();
  const std::string &message_type_ = self_describing_message_.message_type();

  const google::protobuf::Descriptor *descriptor =
      google::protobuf::DescriptorPool::generated_pool()->FindMessageTypeByName(
          message_type_);
  if (descriptor == nullptr) {
    AERROR << "Find Message Type By Name Failed: message_type = "
           << message_type_ << ", message_topic = " << message_topic_;
    return nullptr;
  }

  google::protobuf::Message *msg =
      google::protobuf::MessageFactory::generated_factory()
          ->GetPrototype(descriptor)
          ->New();

  if (msg == nullptr) {
    AERROR << "Generated Protobuf Message Failed: message_type = "
           << message_type_ << ", message_topic = " << message_topic_;
    return nullptr;
  }

  const std::string &message_data = self_describing_message_.message_data();
  const size_t uncompressed_size = self_describing_message_.uncompressed_size();

  // 反序列化
  bool isDeSerializeSuccessed = true;
  switch (self_describing_message_.compress_format()) {
#if ENABLE_PROTOBUF_COMPRESS
  case robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_GZIP:
  case robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_ZLIB:
  case robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_LZ4:
  case robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_ZSTD: {
    isDeSerializeSuccessed =
        robosense::proto::compress::RSProtoCompress::decompressedFromString(
            message_data, self_describing_message_.compress_format(),
            uncompressed_size, msg);
    break;
  }
#endif // ENABLE_PROTOBUF_COMPRESS
  default: {
    isDeSerializeSuccessed = msg->ParseFromString(message_data);
    break;
  }
  }

  if (isDeSerializeSuccessed == false) {
    AERROR << "Parse Message Failed: message_type = " << message_type_
           << ", message_topic = " << message_topic_;
    return nullptr;
  }

  RSWebsocketProtoMsg::Ptr pSharedMsg(new RSWebsocketProtoMsg());
  pSharedMsg->message_topic_ = message_topic_;
  pSharedMsg->message_type_ = message_type_;
  pSharedMsg->message_.reset(msg);

  return pSharedMsg;
}

bool RSProtoParser::protoPackageMessage(
    const RSWebsocketProtoMsg::Ptr &msgPtr,
    const robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_FORMAT
        compressFormat,
    const uint32_t compressLevel, std::string &msgProtoString) {
  if (msgPtr == nullptr) {
    AERROR << "msgPtr is nullptr !";
    return false;
  } else if (msgPtr->message_ == nullptr) {
    AERROR << "msgPtr->message_ is nullptr !";
    return false;
  }

  const auto &message_ = msgPtr->message_;
  auto new_compressFormat = compressFormat; 
#if !ENABLE_SERIALIZE_COMPRESS_DEBUG
  // 正常处理模式
  size_t uncompressed_size = 0;
  bool isSerializeSuccessed = true;
  std::string protoMsgString;
  switch (compressFormat) {
#if ENABLE_PROTOBUF_COMPRESS
  case robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_GZIP:
  case robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_ZLIB:
  case robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_LZ4:
  case robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_ZSTD: {
    isSerializeSuccessed =
        robosense::proto::compress::RSProtoCompress::compressedToString(
            message_, protoMsgString, uncompressed_size, compressFormat,
            compressLevel);
    break;
  }
#else 
  case robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_GZIP:
  case robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_ZLIB:
  case robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_LZ4:
  case robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_ZSTD: {
    // 强制修改为非压缩模式
    isSerializeSuccessed = message_->SerializeToString(&protoMsgString);
    uncompressed_size = protoMsgString.size();
    new_compressFormat = robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_NOTHING; 
    break;
  }
#endif // ENABLE_PROTOBUF_COMPRESS
  default: {
    isSerializeSuccessed = message_->SerializeToString(&protoMsgString);
    uncompressed_size = protoMsgString.size();
    break;
  }
  }
#else
  // 调试处理模式: 对比压缩和非压缩时间
  size_t uncompressed_size = 0;
  bool isSerializeSuccessed = true;
  std::string protoMsgString;
  std::string protoMsgString2;
  const uint64_t compress_start_timestamp = UINT64_T_TIMESTAMP_NS;
  switch (compressFormat) {
  case robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_GZIP:
  case robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_ZLIB:
  case robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_LZ4:
  case robosense::proto_compress_msgs::RS_POST_DATA_COMPRESSION_ZSTD: {
    isSerializeSuccessed =
        robosense::proto::compress::RSProtoCompress::compressedToString(
            message_, protoMsgString, uncompressed_size, compressFormat,
            compressLevel);
    break;
  }
  default: {
    isSerializeSuccessed = message_->SerializeToString(&protoMsgString);
    uncompressed_size = protoMsgString.size();
    break;
  }
  }
  const uint64_t compress_mid_timestamp = UINT64_T_TIMESTAMP_NS;
  isSerializeSuccessed = message_->SerializeToString(&protoMsgString2);

  const uint64_t compress_end_timestamp = UINT64_T_TIMESTAMP_NS;

  AINFO << "compress timestamp diff = "
        << (compress_mid_timestamp - compress_start_timestamp)
        << "(ns), serialize timestamp diff = "
        << (compress_end_timestamp - compress_mid_timestamp)
        << "(ns), protoMsgString SIZE = " << protoMsgString.size()
        << ", protoMsgString2 SIZE = " << protoMsgString2.size();
#endif // ENABLE_SERIALIZE_COMPRESS_DEBUG

  if (!isSerializeSuccessed) {
    AERROR << "Inner Proto Message Serialize Failed !";
    return false;
  }

  msgProtoString.clear();
  robosense::hmi_msgs::SelfDescribingMessage msg;
  msg.set_message_topic(msgPtr->message_topic_);
  msg.set_message_type(msgPtr->message_type_);
  msg.set_message_data(protoMsgString);
  msg.set_send_hmi_time(UINT64_T_TIMESTAMP_NS);
  msg.set_compress_format(new_compressFormat);
  msg.set_compress_level(compressLevel);

  if (!msg.SerializeToString(&msgProtoString)) {
    AERROR << "Websocket Proto Message Serialize Failed !";
    return false;
  }

  return true;
}

} // namespace hmi
} // namespace rs_hmi
} // namespace robosense
