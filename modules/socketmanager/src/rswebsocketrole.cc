#include "hyper_vision/socketmanager/rswebsocketrole.h"

namespace robosense {
namespace rs_hmi {
namespace hmi {

RSWebsocketRole::RSWebsocketRole() {
  is_running_ = false;
  // proto_parser_.reset(new RSProtoParser());
}

RSWebsocketRole::~RSWebsocketRole() {
  // NOTHING TODO...
}

int RSWebsocketRole::sendProtoMessage(const RSWebsocketProtoMsg::Ptr &msgPtr) {
  if (!is_running_) {
    return -1;
  } else if (msgPtr == nullptr) {
    return -2;
  } else if (msgPtr->message_ == nullptr) {
    return -3;
  }
  // AERROR << "message_type = " << msgPtr->message_->GetTypeName();

  {
    std::lock_guard<std::mutex> lg(send_buffer_mtx_);
    const std::string &message_type = msgPtr->message_type_;
    // AERROR << "message_type = " << message_type;
    auto iterMap = websocket_send_controls_.find(message_type);
    bool isAddMessage = true;
    if (iterMap != websocket_send_controls_.end()) {
      // AERROR << "RUN HERE !";
      auto &websocketSendControl = iterMap->second;
      const auto op = websocketSendControl.checkWebsocketSend();
      switch (op) {
      case RSWebsocketSendControl::RS_WEBSOCKET_SEND_CONTROL_OP::
          RS_WEBSOCKET_SEND_CONTROL_NON: {
        break;
      }
      case RSWebsocketSendControl::RS_WEBSOCKET_SEND_CONTROL_OP::
          RS_WEBSOCKET_SEND_CONTROL_LOSS: {
        isAddMessage = false;
        AWARN << "message_type: " << message_type
              << ", WebsocketSendControl: Loss, Current Buffer Size = "
              << websocketSendControl.cur_buffer_size_;
        break;
      }
      case RSWebsocketSendControl::RS_WEBSOCKET_SEND_CONTROL_OP::
          RS_WEBSOCKET_SEND_CONTROL_LOSS2: {
        isAddMessage = false;
        AWARN << "message_type: " << message_type
              << ", WebsocketSendControl: Loss2, Current Buffer Size = "
              << websocketSendControl.cur_buffer_size_;
        break;
      }
      case RSWebsocketSendControl::RS_WEBSOCKET_SEND_CONTROL_OP::
          RS_WEBSOCKET_SEND_CONTROL_CLEAR: {
        AWARN << "message_type: " << message_type
              << ", WebsocketSendControl: Clear, Current Buffer Size = "
              << websocketSendControl.cur_buffer_size_;
        std::queue<RSWebsocketProtoMsg::Ptr> buffer;
        while (send_buffer_.size()) {
          const auto &msgPtr = send_buffer_.front();
          if (msgPtr != nullptr) {
            if (msgPtr->message_type_ != message_type) {
              buffer.push(msgPtr);
            }
          }
          send_buffer_.pop();
        }
        websocketSendControl.clearBufferSize();
        send_buffer_ = buffer;
        break;
      }
      }
    }

    if (isAddMessage) {
      send_buffer_.push(msgPtr);
      send_buffer_cond_.notify_one();
      if (iterMap != websocket_send_controls_.end()) {
        auto &websocketSendControl = iterMap->second;
        websocketSendControl.addBufferSize();
      }
    }
  }

  return 0;
}

int RSWebsocketRole::addWebsocketControl(
    const RSWebsocketSendControl &websocketSendControl) {
  const std::string &message_type = websocketSendControl.message_type_;
  if (websocket_send_controls_.find(message_type) !=
      websocket_send_controls_.end()) {
    return 0;
  }
  AINFO << "add websocket send control: message_type = " << message_type;
  websocketSendControl.printWebsocketSendControl();

  websocket_send_controls_[message_type] = websocketSendControl;

  return 0;
}

int RSWebsocketRole::outgoingMsgSize(
    size_t &totalOutgoingMsgSize,
    std::map<std::string, size_t> &connectionOutgoingSizes) {
  (void)(totalOutgoingMsgSize);
  (void)(connectionOutgoingSizes);
  return 0;
}

void RSWebsocketRole::runLocalExceptCallback(const int error_code,
                                             const std::string &error_info) {
  std::lock_guard<std::mutex> lg(exception_callback_mtx_);
  for (auto callback : exception_callbacks_) {
    if (callback != nullptr) {
      callback(error_code, error_info, websocket_endpoint_config_);
    }
  }
}

void RSWebsocketRole::websocketRecvWorkThread() {
  while (is_running_) {
    RSWebsocketProtoMsg::Ptr protoMsgPtr = nullptr;
    {
      std::unique_lock<std::mutex> lg(recv_buffer_mtx_);
      recv_buffer_cond_.wait(
          lg, [this] { return !is_running_ || !recv_buffer_.empty(); });
      if (!is_running_) {
        break;
      }
      protoMsgPtr = recv_buffer_.front();
      recv_buffer_.pop();
    }
    // AERROR << "RUN HERE";

    if (protoMsgPtr == nullptr) {
      AWARN << "Recv Proto Message is Nullptr !";
      continue;
    }

    runLocalRecvCallback(protoMsgPtr);
  }
}

void RSWebsocketRole::websocketSendWorkThread() {
  while (is_running_) {
    RSWebsocketProtoMsg::Ptr protoMsgPtr = nullptr;
    {
      std::unique_lock<std::mutex> lg(send_buffer_mtx_);
      send_buffer_cond_.wait(
          lg, [this] { return !is_running_ || !send_buffer_.empty(); });
      if (!is_running_) {
        break;
      }
      protoMsgPtr = send_buffer_.front();
      send_buffer_.pop();

      // 更新发送控制信息
      const std::string &message_type = protoMsgPtr->message_type_;
      auto iterMap = websocket_send_controls_.find(message_type);
      bool isAddMessage = true;
      if (iterMap != websocket_send_controls_.end()) {
        auto &websocketSendControl = iterMap->second;
        websocketSendControl.minusBufferSize();
      }
    }

    if (protoMsgPtr == nullptr) {
      AWARN << "Send Proto Message is Nullptr !";
      continue;
    } else if (protoMsgPtr->message_ == nullptr) {
      continue;
    }

    std::string msgString;
    if (!RSProtoParser::protoPackageMessage(
            protoMsgPtr, websocket_endpoint_config_.compress_format(),
            websocket_endpoint_config_.compress_level(), msgString)) {
      AERROR << "Websocket Proto Message Serialize Failed !";
      continue;
    }

    sendProtoMessage(msgString);
  }
}

int RSWebsocketRole::sendProtoMessage(const std::string &msg) {
  (void)(msg);
  return 0;
}

} // namespace hmi
} // namespace rs_hmi
} // namespace robosense
