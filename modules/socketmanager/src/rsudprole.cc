#include "hyper_vision/socketmanager/rsudprole.h"

namespace robosense {
namespace rs_hmi {
namespace hmi {

int RSUdpRole::sendProtoMessage(const RSWebsocketProtoMsg::Ptr &msgPtr) {
  if (is_running_ && msgPtr != nullptr &&
      _type == COMMUNICATION_IO_TYPE::COMMUNICATION_IO_SEND) {
    std::lock_guard<std::mutex> lg(send_buffer_mtx_);
    send_buffer_.push(msgPtr);
    send_buffer_cond_.notify_one();
  }
  return 0;
}

int RSUdpRole::initSndComm() {
  _type = COMMUNICATION_IO_TYPE::COMMUNICATION_IO_SEND;

  int ret = initAsynComm();
  if (ret != 0) {
    AERROR << "Initial Asyn Sender Communicater Failed: ret = " << ret;
    return -1;
  }

  return 0;
}

int RSUdpRole::initRcvComm() {
  _type = COMMUNICATION_IO_TYPE::COMMUNICATION_IO_RECV;

  int ret = initAsynComm();
  if (ret != 0) {
    AERROR << "Initial Asyn Receiver Communicater Failed: ret = " << ret;
    return -2;
  }

  return 0;
}

int RSUdpRole::initBothComm() {
  _type = COMMUNICATION_IO_TYPE::COMMUNICATION_IO_BOTH;

  int ret = initAsynComm();
  if (ret != 0) {
    AERROR << "Initial Asyn Receiver Communicater Failed: ret = " << ret;
    return -2;
  }

  return 0;
}

void RSUdpRole::updateRcvAvailIdx(const int idx) {
  std::lock_guard<std::mutex> lg(_recvBufferMtx);
  _recvAvailIdxs.push_back(idx);
  _recvAvailIdxsCond.notify_one();
}

RSBinaryBuffer::Ptr RSUdpRole::getRcvBinaryCell(int idx) {
  return _recvBinaryBuffers[idx];
}

int RSUdpRole::sendMessage(const RSRoboMsgHeader &msgHeader, const char *data,
                           const int length) {
  if (_sndAvailIdxs.size() == 0) {
    return 1;
  } else if ((data == nullptr && length != 0) ||
             (data != nullptr && length == 0)) {
    return 2;
  } else if (_udpMaxMsgSize < length + sizeof(RSRoboMsgHeader)) {
    return -1;
  } else if (_udpSocketPtr->is_open() == false) {
    int ret = initSocket();

    if (ret != 0) {
      return -2;
    }
  }

  int selectIdx = -1;
  {
    std::unique_lock<std::mutex> lg(_sndBufferMtx);
    _sndAvailIdxsCond.wait(lg, [&] { return _sndAvailIdxs.size() > 0; });
    selectIdx = _sndAvailIdxs[0];
    _sndAvailIdxs.erase(_sndAvailIdxs.begin());
  }

  // Copy Data
  RSBinaryBuffer::Ptr &selectBufferPtr = _sndBinaryBuffers[selectIdx];
  selectBufferPtr->initWithData(length, 0, data, msgHeader);

  // Send Data
  {
    std::lock_guard<std::mutex> lg(_mutexSocket);
    _udpSocketPtr->async_send_to(
        ASIO_BUFFER(selectBufferPtr->dataOffset(), selectBufferPtr->sndGap()),
        _defaultRemoteEndPoint,
        std::bind(&RSUdpRole::sendHandle, this, std::placeholders::_1,
                  std::placeholders::_2, std::ref(selectBufferPtr)));
  }

  return 0;
}

int RSUdpRole::sendMessage(const RSRoboMsgHeader &msgHeader, const char *data,
                           const int length, const std::string &remote_ip) {
  if (_sndAvailIdxs.size() == 0) {
    return 1;
  } else if ((data == nullptr && length != 0) ||
             (data != nullptr && length == 0)) {
    return 2;
  } else if (_udpMaxMsgSize < length + sizeof(RSRoboMsgHeader)) {
    return -1;
  } else if (_udpSocketPtr->is_open() == false) {
    int ret = initSocket();

    if (ret != 0) {
      return -2;
    }
  }

  int selectIdx = -1;
  {
    std::unique_lock<std::mutex> lg(_sndBufferMtx);
    _sndAvailIdxsCond.wait(lg, [&] { return _sndAvailIdxs.size() > 0; });
    selectIdx = _sndAvailIdxs[0];
    _sndAvailIdxs.erase(_sndAvailIdxs.begin());
  }

  // Copy Data
  RSBinaryBuffer::Ptr &selectBufferPtr = _sndBinaryBuffers[selectIdx];
  selectBufferPtr->initWithData(length, 0, data, msgHeader);

  // Send Data
  {
    std::lock_guard<std::mutex> lg(_mutexSocket);
    _udpSocketPtr->async_send_to(
        ASIO_BUFFER(selectBufferPtr->dataOffset(), selectBufferPtr->sndGap()),
        udp_endpoint(ASIO_IP_ADDRESS_FROM_STRING(remote_ip),
                     _defaultRemoteEndPoint.port()),
        std::bind(&RSUdpRole::sendHandle, this, std::placeholders::_1,
                  std::placeholders::_2, std::ref(selectBufferPtr)));
  }

  return 0;
}

void RSUdpRole::sendHandle(error_code ec, int snd_byte,
                           RSBinaryBuffer::Ptr &selectBufferPtr) {
  if (!ec && snd_byte > 0) {
    selectBufferPtr->update(snd_byte);

    if (selectBufferPtr->isSndSuccess()) {
      std::lock_guard<std::mutex> lg(_sndBufferMtx);
      _sndAvailIdxs.push_back(selectBufferPtr->index());
      // std::cout << "_sndMsgFrameCnt = " << (++_sndMsgFrameCnt) << std::endl;
    }
  } else {
    {
      std::lock_guard<std::mutex> lg(_mutexSocket);
      _udpSocketPtr->close();
    }

    {
      {
        std::lock_guard<std::mutex> lg(_sndBufferMtx);
        _sndAvailIdxs.clear();
        for (int i = 0; i < _udpBufferCellSize; ++i) {
          _sndAvailIdxs.push_back(i);
        }
      }
      _sndAvailIdxsCond.notify_one();
    }

    std::string errInfo =
        "Send Message Failed: boost::errorCnt = " + std::to_string(ec.value()) +
        ": boost::errorMsg = " + ec.message();
    AERROR << "errInfo = " << errInfo;
  }
}

void RSUdpRole::recvHandle(error_code ec, int rcv_byte,
                           RSBinaryBuffer::Ptr &selectBufferPtr) {
  if (!ec && rcv_byte > 0) {
    // std::cout << "_recvMsgFrameCnt = " << (++_recvMsgFrameCnt)
    //           << "==> avaiable = " << _recvAvailIdxs.size() << std::endl;
    selectBufferPtr->initWithoutData(rcv_byte, 0);
    {
      std::lock_guard<std::mutex> lg(_mutexProcess);
      _recvProcessIdxs.push_back(selectBufferPtr->index());
      _recvProcessIdxsCond.notify_one();
    }

    recvMessage();
  } else {
    {
      std::lock_guard<std::mutex> lg(_mutexSocket);
      _udpSocketPtr->close();
    }

    // 直接放入可使用缓冲区中
    {
      {
        std::lock_guard<std::mutex> lg(_recvBufferMtx);
        _recvAvailIdxs.clear();
        for (int i = 0; i < _udpBufferCellSize; ++i) {
          _recvAvailIdxs.push_back(i);
        }
      }
      _recvAvailIdxsCond.notify_one();
    }

    std::string errInfo = "Receive Message Failed: boost::errorCnt = " +
                          std::to_string(ec.value()) +
                          ": boost::errorMsg = " + ec.message();

    AERROR << "errInfo = " << errInfo;
  }
}

void RSUdpRole::recvMessage() {
  int selectIdx = -1;
  {
    std::unique_lock<std::mutex> lg(_recvBufferMtx);
    _recvAvailIdxsCond.wait(lg, [this] {
      return _recvAvailIdxs.size() > 0 || is_running_ == false;
    });

    if (is_running_ == false) {
      return;
    }

    selectIdx = _recvAvailIdxs[0];
    _recvAvailIdxs.erase(_recvAvailIdxs.begin());
  }

  RSBinaryBuffer::Ptr &selectBufferPtr = _recvBinaryBuffers[selectIdx];
  _udpSocketPtr->async_receive_from(
      ASIO_BUFFER(selectBufferPtr->data(), selectBufferPtr->totalSize()),
      _recvRemoteEndPoints[selectIdx],
      std::bind(&RSUdpRole::recvHandle, this, std::placeholders::_1,
                std::placeholders::_2, std::ref(selectBufferPtr)));
}

void RSUdpRole::processWorkThread() {
  while (is_running_) {
    int processIdx = -1;
    {
      std::unique_lock<std::mutex> lg(_mutexProcess);
      _recvProcessIdxsCond.wait(lg, [this] {
        return _recvProcessIdxs.size() > 0 || is_running_ == false;
      });
      if (is_running_ == false) {
        break;
      }
      processIdx = _recvProcessIdxs[0];
      _recvProcessIdxs.erase(_recvProcessIdxs.begin());
    }

    if (processIdx >= 0) {
      recvMessage(processIdx);
    }
  }
}

void RSUdpRole::recvMessage(const int recvIdx) {
  // std::cout << "this thread = " << std::this_thread::get_id() << std::endl;

  RSBinaryBuffer::Ptr binaryPtr = getRcvBinaryCell(recvIdx);
  RSRoboMsgHeader msgHeader =
      parseRoboMsgHeader(binaryPtr->data(), binaryPtr->contentSize());

  std::time_t curTime =
      (std::chrono::time_point_cast<std::chrono::milliseconds>(
           std::chrono::system_clock::now()))
          .time_since_epoch()
          .count();

#if ENABLE_DEBUG_RECV_MESSAGE_FUNC
  AERROR << "frame_id = " << msgHeader.msgFrameId
         << ", totalLen = " << msgHeader.msgTotalLen
         << ", localLen = " << msgHeader.msgLocalLen
         << ", curTime = " << curTime;
  updateRcvAvailIdx(recvIdx);
  return;
#endif // ENABLE_DEBUG_RECV_MESSAGE_FUNC

  if (msgHeader.msgTotalLen == msgHeader.msgLocalLen) {
    // 不需要累积处理场景
    RSWebsocketProtoMsg::Ptr protoMsgPtr = RSProtoParser::protoParseMessage(
        std::string(binaryPtr->data() + sizeof(RSRoboMsgHeader),
                    binaryPtr->contentSize() - sizeof(RSRoboMsgHeader)));
    if (protoMsgPtr) {
      // 回调返回
      {
        std::lock_guard<std::mutex> lg(_msgMutex);
        ++_udpReceiveMsgCnt_;
        ++_udpReceiveCnt_;
        _udpReceiveTotalSize_ +=
            binaryPtr->contentSize() - sizeof(RSRoboMsgHeader);
      }

      {
        protoMsgPtr->message_io_type_ =
            RS_WEBSOCKET_MESSAGE_IO_TYPE::RS_WEBSOCKET_RECEIVE;
        std::lock_guard<std::mutex> lg(recv_buffer_mtx_);
        recv_buffer_.push(protoMsgPtr);
        recv_buffer_cond_.notify_one();
      }
    } else {
      // 打印错误信息
      {
        std::lock_guard<std::mutex> lg(_msgMutex);
        ++_udpReceiveMsgCnt_;
        ++_udpReceiveCnt_;
        _udpReceiveTotalSize_ +=
            binaryPtr->contentSize() - sizeof(RSRoboMsgHeader);
      }
      AWARN << "Udp Receive SingleMessage Parser From Array Failed: "
               "frameId = "
            << msgHeader.msgFrameId;
      ++_udpReceiveParseErrorCnt_;
    }
    // Free Binary Cell
    updateRcvAvailIdx(recvIdx);

  } else {
    // 需要累积处理场景
    std::lock_guard<std::mutex> lg(_msgMutex);
    auto iterMap = _recvMessages.find(msgHeader.msgFrameId);
    if (iterMap == _recvMessages.end()) {
      RSReceiveMsgBuffer::Ptr recvMsg(
          new RSReceiveMsgBuffer(msgHeader.msgTotalLen, curTime));
      _recvMessages.insert(std::pair<int, RSReceiveMsgBuffer::Ptr>(
          msgHeader.msgFrameId, recvMsg));

      iterMap = _recvMessages.find(msgHeader.msgFrameId);
      ++_udpReceiveMsgCnt_;
    }

    ++_udpReceiveCnt_;
    _udpReceiveTotalSize_ += binaryPtr->contentSize() - sizeof(RSRoboMsgHeader);

    auto &recvMsg = iterMap->second;
    if (recvMsg != nullptr) {
      recvMsg->AddReceiveData(reinterpret_cast<const unsigned char *>(
                                  binaryPtr->data() + sizeof(RSRoboMsgHeader)),
                              msgHeader.msgLocalLen, msgHeader.msgOffset);
    }

    // Free Binary Cell
    updateRcvAvailIdx(recvIdx);

    // Check Receive Success
    for (auto iterMap = _recvMessages.begin();
         iterMap != _recvMessages.end();) {
      unsigned int frameId = iterMap->first;
      auto recvMsg = iterMap->second;
      if (recvMsg->checkIsComplete()) {
        RSWebsocketProtoMsg::Ptr protoMsgPtr =
            RSProtoParser::protoParseMessage(std::string(
                reinterpret_cast<const char *>(recvMsg->receiveBuffer_.data()),
                recvMsg->receiveBuffer_.size()));
        if (protoMsgPtr) {
          protoMsgPtr->message_io_type_ =
              RS_WEBSOCKET_MESSAGE_IO_TYPE::RS_WEBSOCKET_RECEIVE;
          std::lock_guard<std::mutex> lg(recv_buffer_mtx_);
          recv_buffer_.push(protoMsgPtr);
          recv_buffer_cond_.notify_one();
        } else {
          AWARN << "Udp Receive SingleMessage Parser From Array Failed: "
                   "frameId = "
                << frameId;
          ++_udpReceiveParseErrorCnt_;
        }

        iterMap = _recvMessages.erase(iterMap);
      } else if (recvMsg->checkIsTimeout(curTime, _udpRecvTimeoutThMs_)) {
        AWARN << "Udp Message Receive Timeout: Threshold = "
              << _udpRecvTimeoutThMs_ << "(ms): frameId = " << frameId
              << ", totalMsgSize_ = " << recvMsg->totalMsgSize_
              << ", curMsgSize_ = " << recvMsg->curMsgSize_;
        ++_udpReceiveTimeoutCnt_;
        iterMap = _recvMessages.erase(iterMap);
      } else {
        ++iterMap;
      }
    }
  }

  // 打印统计信息
  if (_udpReceiveMsgCnt_ % 10 == 0) {
    auto endTimestampNs = UINT64_T_TIMESTAMP_NS;
    double diffTime = (endTimestampNs - _udpStartTimestampNs_) * 1e-9;
    double dataSize = (_udpReceiveTotalSize_ * 1.0 / 1024 / 1024);

    AINFO << "_udpReceiveCnt_ = " << _udpReceiveCnt_
          << ", _udpReceiveMsgCnt_ = " << _udpReceiveMsgCnt_
          << ", _udpReceiveTimeoutCnt_ = " << _udpReceiveTimeoutCnt_
          << ", _udpReceiveParseErrorCnt_ = " << _udpReceiveParseErrorCnt_
          << ", _udpReceiveTimeoutCnt_(%) = "
          << (_udpReceiveTimeoutCnt_ * 100.0) / _udpReceiveMsgCnt_ << "(%)"
          << ", _udpReceiveParseErrorCnt_(%) = "
          << (_udpReceiveParseErrorCnt_ * 100.0) / _udpReceiveMsgCnt_
          << "(%), _udpReceiveTotalSize_(MB) = " << dataSize
          << "(MB), Receive Time(s) = " << diffTime
          << "(s), Receive Bandwidth(Mbps) = " << (dataSize * 8 / diffTime)
          << "(Mbps)";
  }
}

void RSUdpRole::ioServiceWorker() {
  //pthread_setname_np(pthread_self(), "ioServiceWorker");
  // apollo::cyber::scheduler::Instance()->SetInnerThreadAttr("ioServiceWorker");
  if (_ioServicePtr != nullptr) {
    _ioServicePtr->run();
  }
}

void RSUdpRole::udpSendWorkThread() {
  //pthread_setname_np(pthread_self(), "udpSendWorkThread");
  // apollo::cyber::scheduler::Instance()->SetInnerThreadAttr("udpSendWorkThread");
  while (is_running_) {
    RSWebsocketProtoMsg::Ptr sendMsg;
    uint32_t bufferedSize = 0;
    {
      std::unique_lock<std::mutex> lg(send_buffer_mtx_);
      send_buffer_cond_.wait(
          lg, [this] { return !is_running_ || !send_buffer_.empty(); });
      if (!is_running_) {
        break;
      }
      sendMsg = send_buffer_.front();
      send_buffer_.pop();
      bufferedSize = send_buffer_.size();
    }

    const uint64_t sendStartTimestampNs = UINT64_T_TIMESTAMP_NS;
    int ret = sendMessageUtil(sendMsg);
    const uint64_t sendFinishTimestampNs = UINT64_T_TIMESTAMP_NS;
    if (ret != 0) {
      AERROR << "Send Message Failed: ret = " << ret
             << ", bufferedSize = " << bufferedSize
             << ", sendStartTimestampNs = "
             << std::to_string(sendStartTimestampNs)
             << ", sendFinishTimestampNs = "
             << std::to_string(sendFinishTimestampNs) << ", diffTimestampNs = "
             << std::to_string(sendFinishTimestampNs - sendStartTimestampNs);
      continue;
    } else {
      AINFO << "Send Message Successed: bufferedSize = " << bufferedSize
            << ", sendStartTimestampNs = "
            << std::to_string(sendStartTimestampNs)
            << ", sendFinishTimestampNs = "
            << std::to_string(sendFinishTimestampNs) << ", diffTimestampNs = "
            << std::to_string(sendFinishTimestampNs - sendStartTimestampNs);
    }
  }
}

void RSUdpRole::udpRecvWorkThread() {
  while (is_running_) {
    RSWebsocketProtoMsg::Ptr protoMsgPtr = nullptr;
    {
      std::unique_lock<std::mutex> lg(recv_buffer_mtx_);
      recv_buffer_cond_.wait(
          lg, [this] { return !is_running_ || !recv_buffer_.empty(); });
      if (!is_running_) {
        break;
      }
      protoMsgPtr = recv_buffer_.front();
      recv_buffer_.pop();
    }
    // AERROR << "RUN HERE";

    if (protoMsgPtr == nullptr) {
      AWARN << "Recv Proto Message is Nullptr !";
      continue;
    }

    runLocalRecvCallback(protoMsgPtr);
  }
}

RSRoboMsgHeader RSUdpRole::parseRoboMsgHeader(const char *data,
                                              const int length) {
  RSRoboMsgHeader header;
  header.toHostEndianValue(data, length,
                           RS_DATA_ENDIAN_TYPE::RS_DATA_LITTLE_ENDIAN);

  return header;
}

} // namespace hmi
} // namespace rs_hmi
} // namespace robosense