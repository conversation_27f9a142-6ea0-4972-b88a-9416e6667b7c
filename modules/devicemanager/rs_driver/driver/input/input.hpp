/*********************************************************************************************************************
Copyright (c) 2020 RoboSense
All rights reserved

By downloading, copying, installing or using the software you agree to this license. If you do not agree to this
license, do not download, install, copy or use the software.

License Agreement
For RoboSense LiDAR SDK Library
(3-clause BSD License)

Redistribution and use in source and binary forms, with or without modification, are permitted provided that the
following conditions are met:

1. Redistributions of source code must retain the above copyright notice, this list of conditions and the following
disclaimer.

2. Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following
disclaimer in the documentation and/or other materials provided with the distribution.

3. Neither the names of the RoboSense, nor Suteng Innovation Technology, nor the names of other contributors may be used
to endorse or promote products derived from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES,
INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*********************************************************************************************************************/

#pragma once

#include <rs_driver/driver/driver_param.hpp>
#include <rs_driver/utility/buffer.hpp>
#include <rs_driver/common/error_code.hpp>

#include <functional>
#include <thread>
#include <cstring>
#include <atomic>

#define VLAN_HDR_LEN  4
#define ETH_HDR_LEN   42
#define ETH_LEN       (ETH_HDR_LEN + VLAN_HDR_LEN + 1500)
#define IP_LEN        65536 
#define UDP_HDR_LEN   8

namespace robosense
{
namespace lidar
{
class Input
{
public:
  Input(const RSInputParam& input_param);

  inline void regCallback(
      const std::function<void(const Error&)>& cb_excep,
      const std::function<std::shared_ptr<Buffer>(size_t)>& cb_get_pkt,
      const std::function<void(std::shared_ptr<Buffer>, bool)>& cb_put_pkt);

  inline void regCallback2(
      const std::function<std::shared_ptr<Buffer>(size_t)>& cb_get_pkt,
      const std::function<void(std::shared_ptr<Buffer>, bool)>& cb_put_pkt);

  inline void regCallback3(
      const std::function<std::shared_ptr<Buffer>(size_t)>& cb_get_pkt,
      const std::function<void(std::shared_ptr<Buffer>, bool)>& cb_put_pkt);

  virtual bool init() = 0;
  virtual bool start() = 0;
  virtual void stop();
  virtual int usb_transfer(uint8_t *data, int len, int *timeout, std::vector<uint8_t> &receive);
  virtual bool send_cmd(int req);

  bool get_start();
  void set_start(bool start_flag);
  bool get_recv();
  void set_recv(bool recv_flag);
  void start_recv();
  void stop_recv();
  virtual ~Input()
  {
  }

protected:
  inline void pushPacket(std::shared_ptr<Buffer> pkt, bool stuffed = true);
  inline void pushPacket2(std::shared_ptr<Buffer> pkt, bool stuffed = true);
  inline void pushPacket3(std::shared_ptr<Buffer> pkt, bool stuffed = true);

  RSInputParam input_param_;
  std::function<std::shared_ptr<Buffer>(size_t size)> cb_get_pkt_;
  std::function<void(std::shared_ptr<Buffer>, bool)> cb_put_pkt_;
  std::function<std::shared_ptr<Buffer>(size_t size)> cb_get_pkt_2_;
  std::function<void(std::shared_ptr<Buffer>, bool)> cb_put_pkt_2_;
  std::function<std::shared_ptr<Buffer>(size_t size)> cb_get_pkt_3_;
  std::function<void(std::shared_ptr<Buffer>, bool)> cb_put_pkt_3_;

  std::function<void(const Error&)> cb_excep_;
  std::thread recv_thread_;
  bool init_flag_;
  std::atomic<bool> start_flag_;
  std::atomic<bool> recv_flag_;
  std::atomic<bool> to_exit_recv_;
};

inline Input::Input(const RSInputParam& input_param)
  : input_param_(input_param), to_exit_recv_(false), 
  init_flag_(false), start_flag_(false), recv_flag_(true)
{
}

inline void Input::regCallback(
    const std::function<void(const Error&)>& cb_excep,
    const std::function<std::shared_ptr<Buffer>(size_t)>& cb_get_pkt,
    const std::function<void(std::shared_ptr<Buffer>, bool)>& cb_put_pkt)
{
  cb_excep_   = cb_excep;
  cb_get_pkt_ = cb_get_pkt;
  cb_put_pkt_ = cb_put_pkt;
}

inline void Input::regCallback2(
    const std::function<std::shared_ptr<Buffer>(size_t)>& cb_get_pkt,
    const std::function<void(std::shared_ptr<Buffer>, bool)>& cb_put_pkt)
{
  cb_get_pkt_2_ = cb_get_pkt;
  cb_put_pkt_2_ = cb_put_pkt;
}

inline void Input::regCallback3(
    const std::function<std::shared_ptr<Buffer>(size_t)>& cb_get_pkt,
    const std::function<void(std::shared_ptr<Buffer>, bool)>& cb_put_pkt)
{
  cb_get_pkt_3_ = cb_get_pkt;
  cb_put_pkt_3_ = cb_put_pkt;
}

inline void Input::stop()
{
  if (start_flag_.load())
  {
    to_exit_recv_.store(true);
    recv_thread_.join();

    start_flag_.store(false);
  }
}

inline void Input::pushPacket(std::shared_ptr<Buffer> pkt, bool stuffed)
{
  cb_put_pkt_(pkt, stuffed);
}

inline void Input::pushPacket2(std::shared_ptr<Buffer> pkt, bool stuffed)
{
  cb_put_pkt_2_(pkt, stuffed);
}

inline void Input::pushPacket3(std::shared_ptr<Buffer> pkt, bool stuffed)
{
  cb_put_pkt_3_(pkt, stuffed);
}

inline int Input::usb_transfer(uint8_t *data, int len, int *timeout, std::vector<uint8_t> &receive)
{
  return -1; // Default implementation, can be overridden by derived classes
}

inline bool Input::send_cmd(int req)
{
  return false;
}

inline bool Input::get_start() {
  return start_flag_.load();
}

inline void Input::set_start(bool start_flag) {
  start_flag_.store(start_flag);
}

inline bool Input::get_recv() {
  return recv_flag_;
}

inline void Input::set_recv(bool recv_flag) {
  recv_flag_.store(recv_flag);
}

inline void Input::start_recv() {
  to_exit_recv_.store(false);
}

inline void Input::stop_recv() {
  to_exit_recv_.store(true);
}

}  // namespace lidar
}  // namespace robosense
