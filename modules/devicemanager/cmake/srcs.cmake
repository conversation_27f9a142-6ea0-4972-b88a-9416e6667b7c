#========================
# libs
#========================

set(CMAKE_POSITION_INDEPENDENT_CODE ON)

set(CUR_SRCS "")
set(CUR_INCLUDES "include")

set(CUR_SUB_DIR "")
LIST(APPEND CUR_SUB_DIR include)
LIST(APPEND CUR_SUB_DIR src)
LIST(APPEND CUR_SUB_DIR rs_driver)

if(WIN32)
    add_definitions(-DWIN32_LEAN_AND_MEAN)
endif()

foreach (dir ${CUR_SUB_DIR})
    file(GLOB_RECURSE tmp_srcs ${dir}/*.cpp ${dir}/*.h)
    list(APPEND CUR_SRCS ${tmp_srcs})
endforeach ()

add_library(${CUR_LIB} STATIC
        ${CUR_SRCS}
        )

target_include_directories(${CUR_LIB}
        PUBLIC
        ${CUR_INCLUDES}
        ./
        ${CMAKE_CURRENT_BINARY_DIR}/../../third_party/libuvc
        )

if(WIN32)
        target_link_libraries(${CUR_LIB}
                PUBLIC
                common
                PRIVATE ws2_32
                usb-static 
                uvc-static 
                )
else()
        target_link_libraries(${CUR_LIB}
                PUBLIC
                common
                usb-static 
                uvc-static  
                )
endif(WIN32)

target_compile_definitions(${CUR_LIB} PRIVATE MODULE_NAME="device")

set(enable_test true)
if(enable_test)
        message("enable device test !")
        add_executable(devicemanager_test ./test/devicemanager_test.cpp)
        target_link_libraries(devicemanager_test device)

        add_executable(devicemanager_test2 ./test/devicemanager_test2.cpp)
        target_link_libraries(devicemanager_test2 device)

        add_executable(devicemanager_test3 ./test/devicemanager_test3.cpp)
        target_link_libraries(devicemanager_test3 device)

        add_executable(devicemanager_ota_test ./test/devicemanager_ota_test.cpp)
        target_link_libraries(devicemanager_ota_test device)

        add_executable(devicemanager_version_test ./test/devicemanager_version_test.cpp)
        target_link_libraries(devicemanager_version_test device)

        add_executable(devicemanager_calib_test ./test/devicemanager_calib_test.cpp)
        target_link_libraries(devicemanager_calib_test device nlohmann_json::nlohmann_json)

        add_executable(devicemanager_calib_writer ./test/devicemanager_calib_writer.cpp)
        target_link_libraries(devicemanager_calib_writer device nlohmann_json::nlohmann_json)

        add_executable(devicemanager_calib_reader ./test/devicemanager_calib_reader.cpp)
        target_link_libraries(devicemanager_calib_reader device nlohmann_json::nlohmann_json)
else() 
        message("disable device test !")
endif(enable_test)
