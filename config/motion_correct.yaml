
MOTION_CORRECT: true
USING_IMU_LINEAR_ACCELERATION: false # 使用IMU线加速度数据进行运动补偿
USING_ODOM_LINEAR_VELOCITY: false # 使用ODOM数据作为线速度补偿, 如果为true，则会覆盖IMU线加速度计算出来的位移
NUM_DRIFT: 100 # 计算零飘所需要的IMU数据数目
FRAME_TAIL: true # 将点补偿到点云帧尾时间
ORI_POINTS_TOPIC: /rslidar_points_origin #原始点云 + 运动矫正点云获取rgb
ORI_RGB_POINTS_TOPIC: /rslidar_points_origin_rgb #原始点云 + 运动矫正点云获取rgb
PROJ_IMG_TOPIC: /camera/image_raw/proj_motion
PROJECTION_ROOT: "" # "Q:\\Downloads\\super_sensor_sdk_v1\\log\\"  # 保存数据的位置
# PROJECTION_ROOT: /home/<USER>/Projects/super_sensor_sdk_v1/.cache/  # 保存数据的位置
CAMERA_LIDAR_TIME_DURA_THRES: 0.1 # s
meta_calib:
  include: calibration.yaml