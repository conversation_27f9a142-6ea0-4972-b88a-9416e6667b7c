enable_websocket_buffer_check: false 
websocket_buffer_check_timeout_th_ms: 100
render_config {
    websocket_task: {
        enable_task: true   
        task_socket_configs: { 
            support_message_topics: "/hmi/renderswitch"  
            support_message_topics: "/hmi/request" 
            support_message_topics: "/hmi/response"   
            task_socket_key: "websocket_server_29095" 
        }
        task_socket_configs: {
            task_socket_key: "websocket_server_29097" 
        }
        task_socket_configs: {
            task_socket_key: "udp_multicast_29096" 
        }
        task_socket_configs: {
            task_socket_key: "udp_multicast_29099" 
        }
        task_socket_configs: { 
            task_socket_key: "udp_p2p_29098" 
        }
    }
    enable_websocket_buffer: true 
    websocket_buffer_size: 5242880
    low_websocket_buffer_size: 524288
    websocket_cmd_socket_key: "websocket_server_29095" 
    websocket_socket_key: "websocket_server_29097"
    udp_multicast_socket_key: "udp_multicast_29096" 
    udp_double_multicast_socket_key: "udp_multicast_29099" 
    udp_p2p_socket_key: "udp_p2p_29098" 
    udp_2_sample_1_buffer_size: 3 
    udp_3_sample_1_buffer_size: 10 
    udp_loss_all_buffer_size: 10 
    max_send_render_buffer_size: 15 
}

hmi_socket_task_config {
    socket_task_configs: {
        websocket_task: {
            task_socket_key: "websocket_server_29095" 
            websocket_endpoint: {
                role_type: RS_WEBSOCKET_SERVER_ROLE  
                server_ip: "0.0.0.0"
                server_port: 29095
                run_thread_cnt: 4 
                send_thread_cnt: 4 
                server_client_timeout_ms: 20000 
                server_check_timeout_ms: 3000 
                compress_format: RS_POST_DATA_COMPRESSION_NOTHING
                compress_level: 9
            } 
            websocket_send_controls: {
                message_type: "robosense.acviewer_msgs.RSRender" 
                enable_loss: true
                loss_buffer_size: 3   
                loss_gap_size: 2 
                enable_loss2: true 
                loss_buffer_size2: 5 
                loss_gap_size2: 3 
                enable_clear: true 
                clear_buffer_size: 10 
            }
        }
    }
    socket_task_configs: {
        websocket_task: {
            task_socket_key: "websocket_server_29097" 
            websocket_endpoint: {
                role_type: RS_WEBSOCKET_SERVER_ROLE  
                server_ip: "0.0.0.0"
                server_port: 29097
                run_thread_cnt: 4 
                send_thread_cnt: 4 
                server_client_timeout_ms: 20000 
                server_check_timeout_ms: 3000 
                compress_format: RS_POST_DATA_COMPRESSION_NOTHING
                compress_level: 9
            } 
            websocket_send_controls: {
                message_type: "robosense.acviewer_msgs.RSRender" 
                enable_loss: true
                loss_buffer_size: 3   
                loss_gap_size: 2 
                enable_loss2: true 
                loss_buffer_size2: 5 
                loss_gap_size2: 3 
                enable_clear: true 
                clear_buffer_size: 10 
            }
        }
    }
    socket_task_configs: {
        udp_multicast_task: {
            task_socket_key: "udp_multicast_29096" 
            udp_multicast_endpoint: {
                role_type: RS_UDP_MULTICAST_SENDER_ROLE
                multicast_ip: "***********"
                multicast_port: 29096 
                multicast_host_ip: "************" 
                max_msg_size: 1400 
                buffer_cell_size: 10240 
                asio_run_thread_cnt: 4 
                asio_snd_thread_cnt: 1 
                asio_recv_thread_cnt: 1
                msg_timeout_th_ms: 100 
                asio_snd_retry_cnt: 3 
                asio_snd_retry_th_ms: 50 
                udp_control_config {
                    udp_control_type: RS_UDP_CONTROL_TOTAL_CONTROL_TIME
                    udp_total_control_time_ms: 60 
                    udp_total_control_single_time_ms: 2 
                    udp_data_control_size: 262144
                    udp_data_control_time_ms: 2
                }
                compress_format: RS_POST_DATA_COMPRESSION_NOTHING
                compress_level: 9
            }
        }    
    }
    socket_task_configs: {
        udp_multicast_task: {
            task_socket_key: "udp_multicast_29099" 
            udp_multicast_endpoint: {
                role_type: RS_UDP_MULTICAST_SENDER_ROLE
                multicast_ip: "***********"
                multicast_port: 29099
                multicast_host_ip: "************" 
                max_msg_size: 1400 
                buffer_cell_size: 10240 
                asio_run_thread_cnt: 4 
                asio_snd_thread_cnt: 1 
                asio_recv_thread_cnt: 1
                msg_timeout_th_ms: 100 
                asio_snd_retry_cnt: 3 
                asio_snd_retry_th_ms: 50 
                udp_control_config {
                    udp_control_type: RS_UDP_CONTROL_TOTAL_CONTROL_TIME
                    udp_total_control_time_ms: 60 
                    udp_total_control_single_time_ms: 2 
                    udp_data_control_size: 262144
                    udp_data_control_time_ms: 2
                } 
                compress_format: RS_POST_DATA_COMPRESSION_NOTHING
                compress_level: 9 
            }
        }
    }
    socket_task_configs: {
        udp_p2p_task: {
            task_socket_key: "udp_p2p_29098"
            udp_p2p_endpoint: {
                role_type: RS_UDP_P2P_SENDER_ROLE
                remote_ip: "0.0.0.0" 
                remote_port: 29098 
                max_msg_size: 1400 
                buffer_cell_size: 10240 
                asio_run_thread_cnt: 4 
                asio_snd_thread_cnt: 1 
                asio_recv_thread_cnt: 1
                msg_timeout_th_ms: 100 
                asio_snd_retry_cnt: 3 
                asio_snd_retry_th_ms: 50 
                socket_buffer_size: 262144
                udp_control_config {
                    udp_control_type: RS_UDP_CONTROL_TOTAL_CONTROL_TIME
                    udp_total_control_time_ms: 60 
                    udp_total_control_single_time_ms: 2 
                    udp_data_control_size: 262144
                    udp_data_control_time_ms: 2
                }
                compress_format: RS_POST_DATA_COMPRESSION_NOTHING
                compress_level: 9
            }
        }
    }
}